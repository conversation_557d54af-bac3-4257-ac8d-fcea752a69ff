2025-08-01 15:48:59,281 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49cda]
	(No symbol) [0x0x7ff65bf51679]
	(No symbol) [0x0x7ff65bf54a61]
	(No symbol) [0x0x7ff65bff1e4b]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:48:59,281 - INFO - 防检测机制设置完成
2025-08-01 15:48:59,282 - INFO - 浏览器驱动初始化成功
2025-08-01 15:48:59,288 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:48:59,291 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:48:59,294 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:48:59,297 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:48:59,302 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:48:59,305 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:48:59,311 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:48:59,314 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:48:59,318 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:48:59,321 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:48:59,325 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:48:59,329 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:48:59,332 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:48:59,335 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:48:59,339 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:48:59,342 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:48:59,346 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:48:59,349 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:48:59,353 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:48:59,353 - INFO - Cookies已加载
2025-08-01 15:48:59,354 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?channel=global_zx&spm=a21ug5.29159167.6452766900.2ndview_zxmore_click
2025-08-01 15:48:59,354 - INFO - 添加额外延迟: 1.4秒
2025-08-01 15:49:05,699 - INFO - 当前User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-01 15:49:05,699 - INFO - 确认：使用桌面版User-Agent
2025-08-01 15:49:05,703 - INFO - 桌面版User-Agent设置完成: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-01 15:49:06,093 - WARNING - 随机鼠标移动失败: Message: move target out of bounds
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65bffbe53]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:49:07,061 - WARNING - 检测到需要登录，请手动登录后继续
2025-08-01 15:49:07,139 - INFO - 调试截图已保存: debug_logs/login_required_screenshot_20250801_154907.png
2025-08-01 15:49:07,141 - INFO - 浏览器日志已保存: debug_logs/login_required_browser_log_20250801_154907.txt
2025-08-01 15:49:07,168 - INFO - 页面源码已保存: debug_logs/login_required_page_source_20250801_154907.html
2025-08-01 15:49:07,171 - INFO - 调试信息 - 当前URL: https://login.taobao.com/havanaone/login/login.htm?bizName=taobao&ttid=&redirectURL=https%3A%2F%2Fjingya-x.taobao.com%2Fwow%2Fz%2Ftfx%2Fstatic%2FdXCnMcpEntsE375tfHGp%3Fchannel%3Dglobal_zx%26spm%3Da21ug5.29159167.6452766900.2ndview_zxmore_click&sub=true
2025-08-01 15:49:07,171 - INFO - 调试信息 - 错误: 检测到需要登录
2025-08-01 15:49:07,171 - INFO - 请在打开的浏览器中完成登录，登录完成后点击'确认登录完成'按钮
2025-08-01 15:49:47,188 - ERROR - 验证登录状态失败: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49cda]
	(No symbol) [0x0x7ff65bf35f35]
	(No symbol) [0x0x7ff65bf5aabe]
	(No symbol) [0x0x7ff65bfcfeb5]
	(No symbol) [0x0x7ff65bff0432]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:49:47,191 - ERROR - 保存cookies失败: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65bf908bf]
	(No symbol) [0x0x7ff65bfc8792]
	(No symbol) [0x0x7ff65bfc3293]
	(No symbol) [0x0x7ff65bfc2359]
	(No symbol) [0x0x7ff65bf14b05]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	(No symbol) [0x0x7ff65bf13b00]
	GetHandleVerifier [0x0x7ff65c58bd88+4260984]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:49:49,219 - INFO - 浏览器已关闭
2025-08-01 15:49:56,958 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49cda]
	(No symbol) [0x0x7ff65bf51679]
	(No symbol) [0x0x7ff65bf54a61]
	(No symbol) [0x0x7ff65bff1e4b]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:49:56,958 - INFO - 防检测机制设置完成
2025-08-01 15:49:56,958 - INFO - 浏览器驱动初始化成功
2025-08-01 15:49:56,964 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:49:56,968 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:49:56,972 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:49:56,974 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:49:56,977 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:49:56,982 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:49:56,987 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:49:56,990 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:49:56,994 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:49:56,998 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:49:57,001 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:49:57,004 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:49:57,008 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:49:57,010 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:49:57,013 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:49:57,016 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:49:57,019 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:49:57,023 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:49:57,026 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:49:57,026 - INFO - Cookies已加载
2025-08-01 15:49:57,026 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?channel=global_zx&spm=a21ug5.29159167.6452766900.2ndview_zxmore_click
2025-08-01 15:50:01,638 - INFO - 当前User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-01 15:50:01,638 - INFO - 确认：使用桌面版User-Agent
2025-08-01 15:50:01,646 - INFO - 桌面版User-Agent设置完成: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-01 15:50:02,203 - INFO - 成功导航到目标页面
2025-08-01 15:50:02,204 - INFO - 登录成功！现在可以开始铺货任务
2025-08-01 15:50:06,254 - INFO - Cookies已保存
2025-08-01 15:50:08,526 - INFO - 浏览器已关闭
2025-08-01 15:50:08,527 - INFO - 以无界面(headless)模式启动Chrome，仅添加headless、no-sandbox、disable-dev-shm-usage参数
2025-08-01 15:50:10,552 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49cda]
	(No symbol) [0x0x7ff65bf51679]
	(No symbol) [0x0x7ff65bf54a61]
	(No symbol) [0x0x7ff65bff1e4b]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:50:10,553 - INFO - 防检测机制设置完成
2025-08-01 15:50:10,553 - INFO - 浏览器驱动初始化成功
2025-08-01 15:50:10,554 - INFO - 开始自动化铺货流程（无头模式）
2025-08-01 15:50:10,554 - INFO - 第一步：随机选择了【家居百货】分类页面
2025-08-01 15:50:10,554 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?cateName=%25E5%25AE%25B6%25E5%25B1%2585%25E7%2599%25BE%25E8%25B4%25A7&categoryList=50016349%2C50016348%2C50008163%2C21%2C50020808%2C122928002%2C122950001%2C122952001%2C50008164%2C50025705&keyword=&spm=a21ug5.29159167.9840561350.showIndustry
2025-08-01 15:50:13,798 - INFO - 第一步：点击登录按钮
2025-08-01 15:50:31,093 - INFO - 找到登录按钮，使用选择器: //*[@id='login-form']/div[6]/button
2025-08-01 15:50:32,118 - INFO - 准备点击登录按钮: 标签=button, 文本='登录', 选择器=//*[@id='login-form']/div[6]/button
2025-08-01 15:50:32,134 - INFO - 准备点击元素: tag=button, text='登录', class='fm-button fm-submit password-l', id=''
2025-08-01 15:50:33,330 - INFO - 尝试ActionChains点击...
2025-08-01 15:50:33,841 - INFO - ActionChains点击成功
2025-08-01 15:50:34,120 - INFO - 登录按钮点击成功
2025-08-01 15:50:34,120 - INFO - 登录按钮点击成功
2025-08-01 15:50:37,121 - INFO - 第二步：获取账号信息...
2025-08-01 15:50:37,122 - INFO - 正在跳转到个人中心页面: https://jingya.taobao.com/?v=2
2025-08-01 15:51:02,574 - INFO - 尝试获取账号名称，使用XPath: //*[@id='workbench-user-tool-btn']/div/div[2]
2025-08-01 15:51:02,587 - INFO - 成功获取账号信息: 恩瑞76店:青苹果
2025-08-01 15:51:02,587 - INFO - 获取到账号名称: 恩瑞76店:青苹果
2025-08-01 15:51:02,589 - INFO - GUI界面已更新账号显示: 恩瑞76店:青苹果
2025-08-01 15:51:02,590 - INFO - 第三步：开始读取60个商品信息...
2025-08-01 15:51:02,592 - INFO - 开始爬取商品信息，最大数量: 60
2025-08-01 15:51:02,592 - INFO - 使用目标URL: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?cateName=%25E5%25AE%25B6%25E5%25B1%2585%25E7%2599%25BE%25E8%25B4%25A7&categoryList=50016349%2C50016348%2C50008163%2C21%2C50020808%2C122928002%2C122950001%2C122952001%2C50008164%2C50025705&keyword=&spm=a21ug5.29159167.9840561350.showIndustry
2025-08-01 15:51:06,325 - INFO - 正在爬取第 1 页商品信息...
2025-08-01 15:51:08,337 - INFO - 找到商品容器，开始爬取商品，最大数量: 60
2025-08-01 15:51:08,351 - INFO - 找到商品项 1，检查元素信息...
2025-08-01 15:51:08,362 - INFO - 商品项 1 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:08,362 - INFO - 商品项 1 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:08,374 - INFO - 商品项 1 找到子元素，class: tfx-item
2025-08-01 15:51:08,378 - INFO - 商品项 1 直接获取到data-autolog: item_id=630604349715&path=search_goods_card&type=category
2025-08-01 15:51:08,378 - INFO - 商品项 1 data-autolog: item_id=630604349715&path=search_goods_card&type=category
2025-08-01 15:51:08,378 - INFO - 商品项 1 提取到item_id: 630604349715
2025-08-01 15:51:08,390 - INFO - 商品项 1 商品名称: 英国莱氏姆深层洁净洗衣液留香香味持久家用香水型（蓝色）
2025-08-01 15:51:08,402 - INFO - 找到商品项 2，检查元素信息...
2025-08-01 15:51:08,411 - INFO - 商品项 2 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:08,411 - INFO - 商品项 2 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:08,418 - INFO - 商品项 2 找到子元素，class: tfx-item
2025-08-01 15:51:08,420 - INFO - 商品项 2 直接获取到data-autolog: item_id=681787375440&path=search_goods_card&index=1&type=category
2025-08-01 15:51:08,420 - INFO - 商品项 2 data-autolog: item_id=681787375440&path=search_goods_card&index=1&type=category
2025-08-01 15:51:08,422 - INFO - 商品项 2 提取到item_id: 681787375440
2025-08-01 15:51:08,430 - INFO - 商品项 2 商品名称: 保税直供 拜耳Canesten凯妮汀衣物除菌液1L【 蓝色澳版，橙色港版
2025-08-01 15:51:08,442 - INFO - 找到商品项 3，检查元素信息...
2025-08-01 15:51:08,451 - INFO - 商品项 3 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:08,451 - INFO - 商品项 3 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:08,459 - INFO - 商品项 3 找到子元素，class: tfx-item
2025-08-01 15:51:08,463 - INFO - 商品项 3 直接获取到data-autolog: item_id=821067902127&path=search_goods_card&index=2&type=category
2025-08-01 15:51:08,463 - INFO - 商品项 3 data-autolog: item_id=821067902127&path=search_goods_card&index=2&type=category
2025-08-01 15:51:08,463 - INFO - 商品项 3 提取到item_id: 821067902127
2025-08-01 15:51:08,472 - INFO - 商品项 3 商品名称: 丹碧丝TAMPAX卫生棉条长导管式内置式纯棉月经棉条棉棒游泳卫生巾
2025-08-01 15:51:08,484 - INFO - 找到商品项 4，检查元素信息...
2025-08-01 15:51:08,494 - INFO - 商品项 4 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:08,494 - INFO - 商品项 4 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:08,502 - INFO - 商品项 4 找到子元素，class: tfx-item
2025-08-01 15:51:08,506 - INFO - 商品项 4 直接获取到data-autolog: item_id=846634805775&path=search_goods_card&index=3&type=category
2025-08-01 15:51:08,506 - INFO - 商品项 4 data-autolog: item_id=846634805775&path=search_goods_card&index=3&type=category
2025-08-01 15:51:08,507 - INFO - 商品项 4 提取到item_id: 846634805775
2025-08-01 15:51:08,515 - INFO - 商品项 4 商品名称: 【甄选】cow牛乳石碱原味/玫瑰花香 瓶装480ml
2025-08-01 15:51:08,526 - INFO - 找到商品项 5，检查元素信息...
2025-08-01 15:51:08,535 - INFO - 商品项 5 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:08,535 - INFO - 商品项 5 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:08,542 - INFO - 商品项 5 找到子元素，class: tfx-item
2025-08-01 15:51:08,546 - INFO - 商品项 5 直接获取到data-autolog: item_id=694650705326&path=search_goods_card&index=4&type=category
2025-08-01 15:51:08,546 - INFO - 商品项 5 data-autolog: item_id=694650705326&path=search_goods_card&index=4&type=category
2025-08-01 15:51:08,547 - INFO - 商品项 5 提取到item_id: 694650705326
2025-08-01 15:51:08,554 - INFO - 商品项 5 商品名称: 日本直采EBISU惠百施软毛宽头牙刷6列48孔舒适 颜色随机 P10
2025-08-01 15:51:08,567 - INFO - 找到商品项 6，检查元素信息...
2025-08-01 15:51:08,576 - INFO - 商品项 6 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:08,576 - INFO - 商品项 6 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:08,586 - INFO - 商品项 6 找到子元素，class: tfx-item
2025-08-01 15:51:08,676 - INFO - 商品项 6 直接获取到data-autolog: item_id=927913724907&path=search_goods_card&index=5&type=category
2025-08-01 15:51:08,676 - INFO - 商品项 6 data-autolog: item_id=927913724907&path=search_goods_card&index=5&type=category
2025-08-01 15:51:08,676 - INFO - 商品项 6 提取到item_id: 927913724907
2025-08-01 15:51:08,739 - INFO - 商品项 6 商品名称: 超威驱蚊蚊香10圈
2025-08-01 15:51:08,751 - INFO - 找到商品项 7，检查元素信息...
2025-08-01 15:51:08,761 - INFO - 商品项 7 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:08,761 - INFO - 商品项 7 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:08,768 - INFO - 商品项 7 找到子元素，class: tfx-item
2025-08-01 15:51:08,772 - INFO - 商品项 7 直接获取到data-autolog: item_id=644179310984&path=search_goods_card&index=6&type=category
2025-08-01 15:51:08,772 - INFO - 商品项 7 data-autolog: item_id=644179310984&path=search_goods_card&index=6&type=category
2025-08-01 15:51:08,772 - INFO - 商品项 7 提取到item_id: 644179310984
2025-08-01 15:51:08,779 - INFO - 商品项 7 商品名称: 库梅克斯皂液洗衣液除菌型香味持久留香家庭装
2025-08-01 15:51:08,796 - INFO - 找到商品项 8，检查元素信息...
2025-08-01 15:51:08,809 - INFO - 商品项 8 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:08,810 - INFO - 商品项 8 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:08,833 - INFO - 商品项 8 找到子元素，class: tfx-item
2025-08-01 15:51:08,836 - INFO - 商品项 8 直接获取到data-autolog: item_id=784715750417&path=search_goods_card&index=7&type=category
2025-08-01 15:51:08,836 - INFO - 商品项 8 data-autolog: item_id=784715750417&path=search_goods_card&index=7&type=category
2025-08-01 15:51:08,836 - INFO - 商品项 8 提取到item_id: 784715750417
2025-08-01 15:51:08,846 - INFO - 商品项 8 商品名称: 【渠道授权】日本EBISU惠百施软毛宽头牙刷6列高效舒适
2025-08-01 15:51:08,858 - INFO - 找到商品项 9，检查元素信息...
2025-08-01 15:51:08,867 - INFO - 商品项 9 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:08,867 - INFO - 商品项 9 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:08,873 - INFO - 商品项 9 找到子元素，class: tfx-item
2025-08-01 15:51:08,876 - INFO - 商品项 9 直接获取到data-autolog: item_id=778440875151&path=search_goods_card&index=8&type=category
2025-08-01 15:51:08,876 - INFO - 商品项 9 data-autolog: item_id=778440875151&path=search_goods_card&index=8&type=category
2025-08-01 15:51:08,877 - INFO - 商品项 9 提取到item_id: 778440875151
2025-08-01 15:51:08,887 - INFO - 商品项 9 商品名称: 勿动1
2025-08-01 15:51:08,897 - INFO - 找到商品项 10，检查元素信息...
2025-08-01 15:51:08,905 - INFO - 商品项 10 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:08,905 - INFO - 商品项 10 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:08,912 - INFO - 商品项 10 找到子元素，class: tfx-item
2025-08-01 15:51:08,914 - INFO - 商品项 10 直接获取到data-autolog: item_id=775343553518&path=search_goods_card&index=9&type=category
2025-08-01 15:51:08,914 - INFO - 商品项 10 data-autolog: item_id=775343553518&path=search_goods_card&index=9&type=category
2025-08-01 15:51:08,914 - INFO - 商品项 10 提取到item_id: 775343553518
2025-08-01 15:51:08,923 - INFO - 商品项 10 商品名称: 德国小红牙膏
2025-08-01 15:51:08,934 - INFO - 找到商品项 11，检查元素信息...
2025-08-01 15:51:08,942 - INFO - 商品项 11 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:08,943 - INFO - 商品项 11 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:08,951 - INFO - 商品项 11 找到子元素，class: tfx-item
2025-08-01 15:51:08,954 - INFO - 商品项 11 直接获取到data-autolog: item_id=773704801582&path=search_goods_card&index=10&type=category
2025-08-01 15:51:08,954 - INFO - 商品项 11 data-autolog: item_id=773704801582&path=search_goods_card&index=10&type=category
2025-08-01 15:51:08,954 - INFO - 商品项 11 提取到item_id: 773704801582
2025-08-01 15:51:08,963 - INFO - 商品项 11 商品名称: 【保税直发】泰国喜净Hygiene柔顺剂衣物防静电持久留香柔软剂
2025-08-01 15:51:08,976 - INFO - 找到商品项 12，检查元素信息...
2025-08-01 15:51:08,986 - INFO - 商品项 12 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:08,986 - INFO - 商品项 12 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:08,993 - INFO - 商品项 12 找到子元素，class: tfx-item
2025-08-01 15:51:08,996 - INFO - 商品项 12 直接获取到data-autolog: item_id=690522809098&path=search_goods_card&index=11&type=category
2025-08-01 15:51:08,996 - INFO - 商品项 12 data-autolog: item_id=690522809098&path=search_goods_card&index=11&type=category
2025-08-01 15:51:08,996 - INFO - 商品项 12 提取到item_id: 690522809098
2025-08-01 15:51:09,006 - INFO - 商品项 12 商品名称: 24年制 日本本土采 KAO/花王乐而雅超薄卫生巾生理期姨妈巾卫生棉
2025-08-01 15:51:09,017 - INFO - 找到商品项 13，检查元素信息...
2025-08-01 15:51:09,027 - INFO - 商品项 13 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:09,027 - INFO - 商品项 13 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:09,036 - INFO - 商品项 13 找到子元素，class: tfx-item
2025-08-01 15:51:09,039 - INFO - 商品项 13 直接获取到data-autolog: item_id=698669180031&path=search_goods_card&index=12&type=category
2025-08-01 15:51:09,039 - INFO - 商品项 13 data-autolog: item_id=698669180031&path=search_goods_card&index=12&type=category
2025-08-01 15:51:09,040 - INFO - 商品项 13 提取到item_id: 698669180031
2025-08-01 15:51:09,048 - INFO - 商品项 13 商品名称: 分销商 加V加拿大TAMPAX丹碧丝卫生棉条长导管式内置月经棉棒96支
2025-08-01 15:51:09,059 - INFO - 找到商品项 14，检查元素信息...
2025-08-01 15:51:09,069 - INFO - 商品项 14 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:09,069 - INFO - 商品项 14 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:09,074 - INFO - 商品项 14 找到子元素，class: tfx-item
2025-08-01 15:51:09,078 - INFO - 商品项 14 直接获取到data-autolog: item_id=710114820531&path=search_goods_card&index=13&type=category
2025-08-01 15:51:09,078 - INFO - 商品项 14 data-autolog: item_id=710114820531&path=search_goods_card&index=13&type=category
2025-08-01 15:51:09,079 - INFO - 商品项 14 提取到item_id: 710114820531
2025-08-01 15:51:09,087 - INFO - 商品项 14 商品名称: 【保税直发】泰国香蕉膏 防裂膏手足干裂脚后跟修复膏 20g*6瓶/盒
2025-08-01 15:51:09,097 - INFO - 找到商品项 15，检查元素信息...
2025-08-01 15:51:09,104 - INFO - 商品项 15 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:09,104 - INFO - 商品项 15 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:09,111 - INFO - 商品项 15 找到子元素，class: tfx-item
2025-08-01 15:51:09,114 - INFO - 商品项 15 直接获取到data-autolog: item_id=642497736652&path=search_goods_card&index=14&type=category
2025-08-01 15:51:09,114 - INFO - 商品项 15 data-autolog: item_id=642497736652&path=search_goods_card&index=14&type=category
2025-08-01 15:51:09,114 - INFO - 商品项 15 提取到item_id: 642497736652
2025-08-01 15:51:09,124 - INFO - 商品项 15 商品名称: 德国班奈特香水洗衣液3KG留香留香香味持久家用香水型
2025-08-01 15:51:09,136 - INFO - 找到商品项 16，检查元素信息...
2025-08-01 15:51:09,142 - INFO - 商品项 16 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:09,142 - INFO - 商品项 16 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:09,149 - INFO - 商品项 16 找到子元素，class: tfx-item
2025-08-01 15:51:09,152 - INFO - 商品项 16 直接获取到data-autolog: item_id=743986957239&path=search_goods_card&index=15&type=category
2025-08-01 15:51:09,152 - INFO - 商品项 16 data-autolog: item_id=743986957239&path=search_goods_card&index=15&type=category
2025-08-01 15:51:09,152 - INFO - 商品项 16 提取到item_id: 743986957239
2025-08-01 15:51:09,161 - INFO - 商品项 16 商品名称: 保税直供 拜耳出品Canesten凯妮汀衣物除菌液1L【港版】
2025-08-01 15:51:09,171 - INFO - 找到商品项 17，检查元素信息...
2025-08-01 15:51:09,178 - INFO - 商品项 17 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:09,179 - INFO - 商品项 17 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:09,186 - INFO - 商品项 17 找到子元素，class: tfx-item
2025-08-01 15:51:09,188 - INFO - 商品项 17 直接获取到data-autolog: item_id=703823332399&path=search_goods_card&index=16&type=category
2025-08-01 15:51:09,189 - INFO - 商品项 17 data-autolog: item_id=703823332399&path=search_goods_card&index=16&type=category
2025-08-01 15:51:09,189 - INFO - 商品项 17 提取到item_id: 703823332399
2025-08-01 15:51:09,198 - INFO - 商品项 17 商品名称: 意大利Marvis玛尔斯牙膏银色白色蓝色85ml [注意纸盒易折痕】
2025-08-01 15:51:09,207 - INFO - 找到商品项 18，检查元素信息...
2025-08-01 15:51:09,216 - INFO - 商品项 18 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:09,216 - INFO - 商品项 18 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:09,222 - INFO - 商品项 18 找到子元素，class: tfx-item
2025-08-01 15:51:09,226 - INFO - 商品项 18 直接获取到data-autolog: item_id=668810723647&path=search_goods_card&index=17&type=category
2025-08-01 15:51:09,226 - INFO - 商品项 18 data-autolog: item_id=668810723647&path=search_goods_card&index=17&type=category
2025-08-01 15:51:09,226 - INFO - 商品项 18 提取到item_id: 668810723647
2025-08-01 15:51:09,234 - INFO - 商品项 18 商品名称: 日本white conc沐浴露全身沐浴乳进口持久留香林允同款变白女
2025-08-01 15:51:09,244 - INFO - 找到商品项 19，检查元素信息...
2025-08-01 15:51:09,252 - INFO - 商品项 19 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:09,253 - INFO - 商品项 19 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:09,259 - INFO - 商品项 19 找到子元素，class: tfx-item
2025-08-01 15:51:09,261 - INFO - 商品项 19 直接获取到data-autolog: item_id=742357678329&path=search_goods_card&index=18&type=category
2025-08-01 15:51:09,261 - INFO - 商品项 19 data-autolog: item_id=742357678329&path=search_goods_card&index=18&type=category
2025-08-01 15:51:09,261 - INFO - 商品项 19 提取到item_id: 742357678329
2025-08-01 15:51:09,271 - INFO - 商品项 19 商品名称: 【保税直发】德国芭乐雅Balea沐浴露300ml 25.10
2025-08-01 15:51:09,281 - INFO - 找到商品项 20，检查元素信息...
2025-08-01 15:51:09,288 - INFO - 商品项 20 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:09,288 - INFO - 商品项 20 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:09,295 - INFO - 商品项 20 找到子元素，class: tfx-item
2025-08-01 15:51:09,297 - INFO - 商品项 20 直接获取到data-autolog: item_id=679559792422&path=search_goods_card&index=19&type=category
2025-08-01 15:51:09,297 - INFO - 商品项 20 data-autolog: item_id=679559792422&path=search_goods_card&index=19&type=category
2025-08-01 15:51:09,297 - INFO - 商品项 20 提取到item_id: 679559792422
2025-08-01 15:51:09,308 - INFO - 商品项 20 商品名称: Proraso 檀香泡沫300ML（泡沫类目）-2.0
2025-08-01 15:51:09,318 - INFO - 找到商品项 21，检查元素信息...
2025-08-01 15:51:09,326 - INFO - 商品项 21 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:09,326 - INFO - 商品项 21 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:09,333 - INFO - 商品项 21 找到子元素，class: tfx-item
2025-08-01 15:51:09,336 - INFO - 商品项 21 直接获取到data-autolog: item_id=755157258216&path=search_goods_card&index=20&type=category
2025-08-01 15:51:09,336 - INFO - 商品项 21 data-autolog: item_id=755157258216&path=search_goods_card&index=20&type=category
2025-08-01 15:51:09,336 - INFO - 商品项 21 提取到item_id: 755157258216
2025-08-01 15:51:09,363 - INFO - 商品项 21 商品名称: 【羽阳商贸淘宝仓】泰国hygiene衣物柔顺剂三色 大容量3402509000
2025-08-01 15:51:09,375 - INFO - 找到商品项 22，检查元素信息...
2025-08-01 15:51:09,382 - INFO - 商品项 22 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:09,382 - INFO - 商品项 22 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:09,391 - INFO - 商品项 22 找到子元素，class: tfx-item
2025-08-01 15:51:09,395 - INFO - 商品项 22 直接获取到data-autolog: item_id=762133318080&path=search_goods_card&index=21&type=category
2025-08-01 15:51:09,395 - INFO - 商品项 22 data-autolog: item_id=762133318080&path=search_goods_card&index=21&type=category
2025-08-01 15:51:09,395 - INFO - 商品项 22 提取到item_id: 762133318080
2025-08-01 15:51:09,404 - INFO - 商品项 22 商品名称: 美国Listerine李施德林口气清新片薄荷味(3*24片)/盒 2106909090
2025-08-01 15:51:17,581 - INFO - 找到商品项 23，检查元素信息...
2025-08-01 15:51:17,591 - INFO - 商品项 23 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:17,591 - INFO - 商品项 23 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:17,599 - INFO - 商品项 23 找到子元素，class: tfx-item
2025-08-01 15:51:17,602 - INFO - 商品项 23 直接获取到data-autolog: item_id=759842528968&path=search_goods_card&index=22&type=category
2025-08-01 15:51:17,602 - INFO - 商品项 23 data-autolog: item_id=759842528968&path=search_goods_card&index=22&type=category
2025-08-01 15:51:17,602 - INFO - 商品项 23 提取到item_id: 759842528968
2025-08-01 15:51:17,612 - INFO - 商品项 23 商品名称: 【保税现货】泰国DENTISTE丹师特牙膏薄荷持久清新口气去黄异味
2025-08-01 15:51:17,623 - INFO - 找到商品项 24，检查元素信息...
2025-08-01 15:51:17,632 - INFO - 商品项 24 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:17,632 - INFO - 商品项 24 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:17,642 - INFO - 商品项 24 找到子元素，class: tfx-item
2025-08-01 15:51:17,645 - INFO - 商品项 24 直接获取到data-autolog: item_id=740410737413&path=search_goods_card&index=23&type=category
2025-08-01 15:51:17,645 - INFO - 商品项 24 data-autolog: item_id=740410737413&path=search_goods_card&index=23&type=category
2025-08-01 15:51:17,645 - INFO - 商品项 24 提取到item_id: 740410737413
2025-08-01 15:51:17,655 - INFO - 商品项 24 商品名称: 第一三共CLEANDENTAL牙周护理牙膏50g 90g 100g 漱口水
2025-08-01 15:51:17,667 - INFO - 找到商品项 25，检查元素信息...
2025-08-01 15:51:17,673 - INFO - 商品项 25 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:17,673 - INFO - 商品项 25 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:17,700 - INFO - 商品项 25 找到子元素，class: tfx-item
2025-08-01 15:51:17,704 - INFO - 商品项 25 直接获取到data-autolog: item_id=778744140129&path=search_goods_card&index=24&type=category
2025-08-01 15:51:17,704 - INFO - 商品项 25 data-autolog: item_id=778744140129&path=search_goods_card&index=24&type=category
2025-08-01 15:51:17,704 - INFO - 商品项 25 提取到item_id: 778744140129
2025-08-01 15:51:17,713 - INFO - 商品项 25 商品名称: 花瓶1
2025-08-01 15:51:17,724 - INFO - 找到商品项 26，检查元素信息...
2025-08-01 15:51:17,731 - INFO - 商品项 26 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:17,732 - INFO - 商品项 26 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:17,738 - INFO - 商品项 26 找到子元素，class: tfx-item
2025-08-01 15:51:17,740 - INFO - 商品项 26 直接获取到data-autolog: item_id=680534455265&path=search_goods_card&index=25&type=category
2025-08-01 15:51:17,740 - INFO - 商品项 26 data-autolog: item_id=680534455265&path=search_goods_card&index=25&type=category
2025-08-01 15:51:17,741 - INFO - 商品项 26 提取到item_id: 680534455265
2025-08-01 15:51:17,750 - INFO - 商品项 26 商品名称: 美国Listerine李施德林口气清新片冰爽薄荷味肉桂味(3*24片)/盒
2025-08-01 15:51:17,760 - INFO - 找到商品项 27，检查元素信息...
2025-08-01 15:51:17,767 - INFO - 商品项 27 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:17,767 - INFO - 商品项 27 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:17,792 - INFO - 商品项 27 找到子元素，class: tfx-item
2025-08-01 15:51:17,794 - INFO - 商品项 27 直接获取到data-autolog: item_id=796223606931&path=search_goods_card&index=26&type=category
2025-08-01 15:51:17,795 - INFO - 商品项 27 data-autolog: item_id=796223606931&path=search_goods_card&index=26&type=category
2025-08-01 15:51:17,795 - INFO - 商品项 27 提取到item_id: 796223606931
2025-08-01 15:51:17,804 - INFO - 商品项 27 商品名称: 勿动3
2025-08-01 15:51:17,816 - INFO - 找到商品项 28，检查元素信息...
2025-08-01 15:51:17,823 - INFO - 商品项 28 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:17,823 - INFO - 商品项 28 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:17,831 - INFO - 商品项 28 找到子元素，class: tfx-item
2025-08-01 15:51:17,833 - INFO - 商品项 28 直接获取到data-autolog: item_id=805523184209&path=search_goods_card&index=27&type=category
2025-08-01 15:51:17,833 - INFO - 商品项 28 data-autolog: item_id=805523184209&path=search_goods_card&index=27&type=category
2025-08-01 15:51:17,834 - INFO - 商品项 28 提取到item_id: 805523184209
2025-08-01 15:51:17,843 - INFO - 商品项 28 商品名称: 【渠道授权】4支装 日本EBISU惠百施软毛宽头牙刷6列
2025-08-01 15:51:17,854 - INFO - 找到商品项 29，检查元素信息...
2025-08-01 15:51:17,862 - INFO - 商品项 29 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:17,862 - INFO - 商品项 29 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:17,872 - INFO - 商品项 29 找到子元素，class: tfx-item
2025-08-01 15:51:17,874 - INFO - 商品项 29 直接获取到data-autolog: item_id=679995147317&path=search_goods_card&index=28&type=category
2025-08-01 15:51:17,874 - INFO - 商品项 29 data-autolog: item_id=679995147317&path=search_goods_card&index=28&type=category
2025-08-01 15:51:17,875 - INFO - 商品项 29 提取到item_id: 679995147317
2025-08-01 15:51:17,887 - INFO - 商品项 29 商品名称: 日本white conc沐浴露全身沐浴乳进口持久留香林允同款变白女
2025-08-01 15:51:17,905 - INFO - 找到商品项 30，检查元素信息...
2025-08-01 15:51:17,913 - INFO - 商品项 30 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:17,914 - INFO - 商品项 30 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:17,921 - INFO - 商品项 30 找到子元素，class: tfx-item
2025-08-01 15:51:17,925 - INFO - 商品项 30 直接获取到data-autolog: item_id=751408783831&path=search_goods_card&index=29&type=category
2025-08-01 15:51:17,925 - INFO - 商品项 30 data-autolog: item_id=751408783831&path=search_goods_card&index=29&type=category
2025-08-01 15:51:17,925 - INFO - 商品项 30 提取到item_id: 751408783831
2025-08-01 15:51:17,934 - INFO - 商品项 30 商品名称: 日本white conc沐浴露全身沐浴乳进口持久留香林允同款
2025-08-01 15:51:17,946 - INFO - 找到商品项 31，检查元素信息...
2025-08-01 15:51:17,954 - INFO - 商品项 31 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:17,954 - INFO - 商品项 31 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:17,980 - INFO - 商品项 31 找到子元素，class: tfx-item
2025-08-01 15:51:17,984 - INFO - 商品项 31 直接获取到data-autolog: item_id=651574474225&path=search_goods_card&index=30&type=category
2025-08-01 15:51:17,984 - INFO - 商品项 31 data-autolog: item_id=651574474225&path=search_goods_card&index=30&type=category
2025-08-01 15:51:17,984 - INFO - 商品项 31 提取到item_id: 651574474225
2025-08-01 15:51:17,993 - INFO - 商品项 31 商品名称: 英国LAISHEMUM莱氏姆洗衣液3.1KG留香香味持久家用香水型品牌正品
2025-08-01 15:51:18,004 - INFO - 找到商品项 32，检查元素信息...
2025-08-01 15:51:18,013 - INFO - 商品项 32 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:18,013 - INFO - 商品项 32 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:18,019 - INFO - 商品项 32 找到子元素，class: tfx-item
2025-08-01 15:51:18,022 - INFO - 商品项 32 直接获取到data-autolog: item_id=706488285938&path=search_goods_card&index=31&type=category
2025-08-01 15:51:18,022 - INFO - 商品项 32 data-autolog: item_id=706488285938&path=search_goods_card&index=31&type=category
2025-08-01 15:51:18,022 - INFO - 商品项 32 提取到item_id: 706488285938
2025-08-01 15:51:18,030 - INFO - 商品项 32 商品名称: 强生婴儿沐浴露儿童沐浴乳滋润牛奶沐浴液洗浴露全家可用1000ml
2025-08-01 15:51:18,042 - INFO - 找到商品项 33，检查元素信息...
2025-08-01 15:51:18,049 - INFO - 商品项 33 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:18,049 - INFO - 商品项 33 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:18,057 - INFO - 商品项 33 找到子元素，class: tfx-item
2025-08-01 15:51:18,059 - INFO - 商品项 33 直接获取到data-autolog: item_id=739782656534&path=search_goods_card&index=32&type=category
2025-08-01 15:51:18,060 - INFO - 商品项 33 data-autolog: item_id=739782656534&path=search_goods_card&index=32&type=category
2025-08-01 15:51:18,060 - INFO - 商品项 33 提取到item_id: 739782656534
2025-08-01 15:51:18,069 - INFO - 商品项 33 商品名称: 德国益周适缓解牙龈出血牙肉红肿防蛀抗敏感去黄美白牙膏75ML
2025-08-01 15:51:18,079 - INFO - 找到商品项 34，检查元素信息...
2025-08-01 15:51:18,086 - INFO - 商品项 34 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:18,086 - INFO - 商品项 34 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:18,093 - INFO - 商品项 34 找到子元素，class: tfx-item
2025-08-01 15:51:18,095 - INFO - 商品项 34 直接获取到data-autolog: item_id=822594991780&path=search_goods_card&index=33&type=category
2025-08-01 15:51:18,095 - INFO - 商品项 34 data-autolog: item_id=822594991780&path=search_goods_card&index=33&type=category
2025-08-01 15:51:18,095 - INFO - 商品项 34 提取到item_id: 822594991780
2025-08-01 15:51:18,103 - INFO - 商品项 34 商品名称: 勿动9
2025-08-01 15:51:18,114 - INFO - 找到商品项 35，检查元素信息...
2025-08-01 15:51:18,122 - INFO - 商品项 35 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:18,122 - INFO - 商品项 35 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:18,128 - INFO - 商品项 35 找到子元素，class: tfx-item
2025-08-01 15:51:18,132 - INFO - 商品项 35 直接获取到data-autolog: item_id=686904390887&path=search_goods_card&index=34&type=category
2025-08-01 15:51:18,132 - INFO - 商品项 35 data-autolog: item_id=686904390887&path=search_goods_card&index=34&type=category
2025-08-01 15:51:18,133 - INFO - 商品项 35 提取到item_id: 686904390887
2025-08-01 15:51:18,141 - INFO - 商品项 35 商品名称: 新西兰国民品牌Glow Lab沐浴露900ml原装进口 多规格效期不一致
2025-08-01 15:51:18,152 - INFO - 找到商品项 36，检查元素信息...
2025-08-01 15:51:18,159 - INFO - 商品项 36 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:18,159 - INFO - 商品项 36 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:18,166 - INFO - 商品项 36 找到子元素，class: tfx-item
2025-08-01 15:51:18,169 - INFO - 商品项 36 直接获取到data-autolog: item_id=679319445210&path=search_goods_card&index=35&type=category
2025-08-01 15:51:18,169 - INFO - 商品项 36 data-autolog: item_id=679319445210&path=search_goods_card&index=35&type=category
2025-08-01 15:51:18,169 - INFO - 商品项 36 提取到item_id: 679319445210
2025-08-01 15:51:18,193 - INFO - 商品项 36 商品名称: 美国Tampax丹碧丝导管式卫生棉条超值装
2025-08-01 15:51:18,204 - INFO - 找到商品项 37，检查元素信息...
2025-08-01 15:51:18,214 - INFO - 商品项 37 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:18,214 - INFO - 商品项 37 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:18,221 - INFO - 商品项 37 找到子元素，class: tfx-item
2025-08-01 15:51:18,224 - INFO - 商品项 37 直接获取到data-autolog: item_id=831506717611&path=search_goods_card&index=36&type=category
2025-08-01 15:51:18,225 - INFO - 商品项 37 data-autolog: item_id=831506717611&path=search_goods_card&index=36&type=category
2025-08-01 15:51:18,225 - INFO - 商品项 37 提取到item_id: 831506717611
2025-08-01 15:51:18,232 - INFO - 商品项 37 商品名称: 【保税】第一三共cleandental牙膏牙垢牙周护理100g/50g
2025-08-01 15:51:18,242 - INFO - 找到商品项 38，检查元素信息...
2025-08-01 15:51:18,249 - INFO - 商品项 38 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:18,249 - INFO - 商品项 38 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:18,256 - INFO - 商品项 38 找到子元素，class: tfx-item
2025-08-01 15:51:18,259 - INFO - 商品项 38 直接获取到data-autolog: item_id=763435676681&path=search_goods_card&index=37&type=category
2025-08-01 15:51:18,259 - INFO - 商品项 38 data-autolog: item_id=763435676681&path=search_goods_card&index=37&type=category
2025-08-01 15:51:18,259 - INFO - 商品项 38 提取到item_id: 763435676681
2025-08-01 15:51:18,267 - INFO - 商品项 38 商品名称: 美国Tide to Go汰渍便携式衣物清洁剂去渍笔10ml 3402509000
2025-08-01 15:51:18,279 - INFO - 找到商品项 39，检查元素信息...
2025-08-01 15:51:18,287 - INFO - 商品项 39 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:18,287 - INFO - 商品项 39 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:18,294 - INFO - 商品项 39 找到子元素，class: tfx-item
2025-08-01 15:51:18,297 - INFO - 商品项 39 直接获取到data-autolog: item_id=738635978220&path=search_goods_card&index=38&type=category
2025-08-01 15:51:18,297 - INFO - 商品项 39 data-autolog: item_id=738635978220&path=search_goods_card&index=38&type=category
2025-08-01 15:51:18,297 - INFO - 商品项 39 提取到item_id: 738635978220
2025-08-01 15:51:18,305 - INFO - 商品项 39 商品名称: 【品牌直供】韩国爱茉莉麦迪安93牙膏 清洁清新口气亮白去黄3支30
2025-08-01 15:51:18,315 - INFO - 找到商品项 40，检查元素信息...
2025-08-01 15:51:18,324 - INFO - 商品项 40 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:18,324 - INFO - 商品项 40 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:18,332 - INFO - 商品项 40 找到子元素，class: tfx-item
2025-08-01 15:51:18,336 - INFO - 商品项 40 直接获取到data-autolog: item_id=679916637964&path=search_goods_card&index=39&type=category
2025-08-01 15:51:18,336 - INFO - 商品项 40 data-autolog: item_id=679916637964&path=search_goods_card&index=39&type=category
2025-08-01 15:51:18,336 - INFO - 商品项 40 提取到item_id: 679916637964
2025-08-01 15:51:18,347 - INFO - 商品项 40 商品名称: Proraso 燕麦泡沫300ML（泡沫类目）-2.0
2025-08-01 15:51:18,348 - INFO - 当前页面爬取完成，找到 40 个商品
2025-08-01 15:51:18,348 - INFO - 第 1 页找到 40 个商品，总计: 40
2025-08-01 15:51:18,376 - INFO - 找到下一页按钮: text='下一页', class='next-btn next-medium next-btn-normal next-pagination-item next-next'
2025-08-01 15:51:18,377 - INFO - 下一页按钮可点击，准备点击
2025-08-01 15:51:18,394 - INFO - 准备点击元素: tag=button, text='下一页', class='next-btn next-medium next-btn-', id=''
2025-08-01 15:51:19,698 - INFO - 尝试ActionChains点击...
2025-08-01 15:51:20,008 - INFO - ActionChains点击成功
2025-08-01 15:51:20,418 - INFO - 成功点击下一页按钮
2025-08-01 15:51:23,419 - INFO - 正在爬取第 2 页商品信息...
2025-08-01 15:51:25,425 - INFO - 找到商品容器，开始爬取商品，最大数量: 20
2025-08-01 15:51:25,455 - INFO - 找到商品项 1，检查元素信息...
2025-08-01 15:51:25,462 - INFO - 商品项 1 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:25,463 - INFO - 商品项 1 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:25,470 - INFO - 商品项 1 找到子元素，class: tfx-item
2025-08-01 15:51:25,473 - INFO - 商品项 1 直接获取到data-autolog: item_id=756909858622&path=search_goods_card&type=category
2025-08-01 15:51:25,473 - INFO - 商品项 1 data-autolog: item_id=756909858622&path=search_goods_card&type=category
2025-08-01 15:51:25,473 - INFO - 商品项 1 提取到item_id: 756909858622
2025-08-01 15:51:25,482 - INFO - 商品项 1 商品名称: 吉列Gillette锋速3剃须刀3层手动剃须刀头 链路齐全
2025-08-01 15:51:25,491 - INFO - 找到商品项 2，检查元素信息...
2025-08-01 15:51:25,500 - INFO - 商品项 2 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:25,500 - INFO - 商品项 2 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:25,509 - INFO - 商品项 2 找到子元素，class: tfx-item
2025-08-01 15:51:25,511 - INFO - 商品项 2 直接获取到data-autolog: item_id=679916877746&path=search_goods_card&index=1&type=category
2025-08-01 15:51:25,512 - INFO - 商品项 2 data-autolog: item_id=679916877746&path=search_goods_card&index=1&type=category
2025-08-01 15:51:25,512 - INFO - 商品项 2 提取到item_id: 679916877746
2025-08-01 15:51:25,521 - INFO - 商品项 2 商品名称: Proraso 芦荟剃须泡沫 300ML-2.0
2025-08-01 15:51:25,528 - INFO - 找到商品项 3，检查元素信息...
2025-08-01 15:51:25,537 - INFO - 商品项 3 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:25,537 - INFO - 商品项 3 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:25,544 - INFO - 商品项 3 找到子元素，class: tfx-item
2025-08-01 15:51:25,547 - INFO - 商品项 3 直接获取到data-autolog: item_id=679560394795&path=search_goods_card&index=2&type=category
2025-08-01 15:51:25,547 - INFO - 商品项 3 data-autolog: item_id=679560394795&path=search_goods_card&index=2&type=category
2025-08-01 15:51:25,547 - INFO - 商品项 3 提取到item_id: 679560394795
2025-08-01 15:51:25,555 - INFO - 商品项 3 商品名称: 意大利Marvis玛尔斯牙膏85ml
2025-08-01 15:51:25,564 - INFO - 找到商品项 4，检查元素信息...
2025-08-01 15:51:25,571 - INFO - 商品项 4 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:25,571 - INFO - 商品项 4 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:25,577 - INFO - 商品项 4 找到子元素，class: tfx-item
2025-08-01 15:51:25,580 - INFO - 商品项 4 直接获取到data-autolog: item_id=693959618019&path=search_goods_card&index=3&type=category
2025-08-01 15:51:25,580 - INFO - 商品项 4 data-autolog: item_id=693959618019&path=search_goods_card&index=3&type=category
2025-08-01 15:51:25,580 - INFO - 商品项 4 提取到item_id: 693959618019
2025-08-01 15:51:25,604 - INFO - 商品项 4 商品名称: 【保税直发】fancl药盒
2025-08-01 15:51:25,612 - INFO - 找到商品项 5，检查元素信息...
2025-08-01 15:51:25,619 - INFO - 商品项 5 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:25,619 - INFO - 商品项 5 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:25,627 - INFO - 商品项 5 找到子元素，class: tfx-item
2025-08-01 15:51:25,630 - INFO - 商品项 5 直接获取到data-autolog: item_id=816763140646&path=search_goods_card&index=4&type=category
2025-08-01 15:51:25,630 - INFO - 商品项 5 data-autolog: item_id=816763140646&path=search_goods_card&index=4&type=category
2025-08-01 15:51:25,630 - INFO - 商品项 5 提取到item_id: 816763140646
2025-08-01 15:51:25,639 - INFO - 商品项 5 商品名称: 进口Euthymol复古粉色牙膏持久清新口气清洁牙齿护龈去牙垢75ml
2025-08-01 15:51:25,646 - INFO - 找到商品项 6，检查元素信息...
2025-08-01 15:51:25,654 - INFO - 商品项 6 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:25,654 - INFO - 商品项 6 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:25,661 - INFO - 商品项 6 找到子元素，class: tfx-item
2025-08-01 15:51:25,665 - INFO - 商品项 6 直接获取到data-autolog: item_id=680234193283&path=search_goods_card&index=5&type=category
2025-08-01 15:51:25,665 - INFO - 商品项 6 data-autolog: item_id=680234193283&path=search_goods_card&index=5&type=category
2025-08-01 15:51:25,665 - INFO - 商品项 6 提取到item_id: 680234193283
2025-08-01 15:51:25,676 - INFO - 商品项 6 商品名称: 美国Playtex倍得适内置长导管卫生棉条运动款48支 可选
2025-08-01 15:51:25,686 - INFO - 找到商品项 7，检查元素信息...
2025-08-01 15:51:25,696 - INFO - 商品项 7 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:25,696 - INFO - 商品项 7 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:25,702 - INFO - 商品项 7 找到子元素，class: tfx-item
2025-08-01 15:51:25,706 - INFO - 商品项 7 直接获取到data-autolog: item_id=822251094523&path=search_goods_card&index=6&type=category
2025-08-01 15:51:25,706 - INFO - 商品项 7 data-autolog: item_id=822251094523&path=search_goods_card&index=6&type=category
2025-08-01 15:51:25,706 - INFO - 商品项 7 提取到item_id: 822251094523
2025-08-01 15:51:25,714 - INFO - 商品项 7 商品名称: 日本原装进口巴斯克林美肌泡澡浴盐系列600g/罐
2025-08-01 15:51:25,741 - INFO - 找到商品项 8，检查元素信息...
2025-08-01 15:51:25,749 - INFO - 商品项 8 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:25,749 - INFO - 商品项 8 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:25,757 - INFO - 商品项 8 找到子元素，class: tfx-item
2025-08-01 15:51:25,760 - INFO - 商品项 8 直接获取到data-autolog: item_id=727595139035&path=search_goods_card&index=7&type=category
2025-08-01 15:51:25,760 - INFO - 商品项 8 data-autolog: item_id=727595139035&path=search_goods_card&index=7&type=category
2025-08-01 15:51:25,760 - INFO - 商品项 8 提取到item_id: 727595139035
2025-08-01 15:51:25,768 - INFO - 商品项 8 商品名称: 【保税】花王牙膏美白护齿165g
2025-08-01 15:51:25,776 - INFO - 找到商品项 9，检查元素信息...
2025-08-01 15:51:25,784 - INFO - 商品项 9 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:25,784 - INFO - 商品项 9 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:25,792 - INFO - 商品项 9 找到子元素，class: tfx-item
2025-08-01 15:51:25,794 - INFO - 商品项 9 直接获取到data-autolog: item_id=824593169092&path=search_goods_card&index=8&type=category
2025-08-01 15:51:25,794 - INFO - 商品项 9 data-autolog: item_id=824593169092&path=search_goods_card&index=8&type=category
2025-08-01 15:51:25,794 - INFO - 商品项 9 提取到item_id: 824593169092
2025-08-01 15:51:25,803 - INFO - 商品项 9 商品名称: INSTANT SMILE白月光美白牙膜INSMILE牙齿美白亮白牙膏去黄牙凝胶
2025-08-01 15:51:25,809 - INFO - 找到商品项 10，检查元素信息...
2025-08-01 15:51:25,818 - INFO - 商品项 10 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:25,818 - INFO - 商品项 10 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:25,825 - INFO - 商品项 10 找到子元素，class: tfx-item
2025-08-01 15:51:25,828 - INFO - 商品项 10 直接获取到data-autolog: item_id=680475089390&path=search_goods_card&index=9&type=category
2025-08-01 15:51:25,828 - INFO - 商品项 10 data-autolog: item_id=680475089390&path=search_goods_card&index=9&type=category
2025-08-01 15:51:25,828 - INFO - 商品项 10 提取到item_id: 680475089390
2025-08-01 15:51:25,838 - INFO - 商品项 10 商品名称: 特价 日落琥珀26.05 欧舒丹香皂4*50G 3401110090
2025-08-01 15:51:25,847 - INFO - 找到商品项 11，检查元素信息...
2025-08-01 15:51:25,857 - INFO - 商品项 11 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:25,857 - INFO - 商品项 11 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:25,864 - INFO - 商品项 11 找到子元素，class: tfx-item
2025-08-01 15:51:25,868 - INFO - 商品项 11 直接获取到data-autolog: item_id=937604879458&path=search_goods_card&index=10&type=category
2025-08-01 15:51:25,868 - INFO - 商品项 11 data-autolog: item_id=937604879458&path=search_goods_card&index=10&type=category
2025-08-01 15:51:25,868 - INFO - 商品项 11 提取到item_id: 937604879458
2025-08-01 15:51:25,879 - INFO - 商品项 11 商品名称: 夏季大帽檐防紫外线空顶防晒太阳帽一个 米白色
2025-08-01 15:51:25,887 - INFO - 找到商品项 12，检查元素信息...
2025-08-01 15:51:25,897 - INFO - 商品项 12 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:25,898 - INFO - 商品项 12 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:25,907 - INFO - 商品项 12 找到子元素，class: tfx-item
2025-08-01 15:51:25,909 - INFO - 商品项 12 直接获取到data-autolog: item_id=678624292168&path=search_goods_card&index=11&type=category
2025-08-01 15:51:25,910 - INFO - 商品项 12 data-autolog: item_id=678624292168&path=search_goods_card&index=11&type=category
2025-08-01 15:51:25,910 - INFO - 商品项 12 提取到item_id: 678624292168
2025-08-01 15:51:25,918 - INFO - 商品项 12 商品名称: 加拿大Tampax丹碧丝卫生棉条导管式内置96支
2025-08-01 15:51:25,927 - INFO - 找到商品项 13，检查元素信息...
2025-08-01 15:51:25,936 - INFO - 商品项 13 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:25,936 - INFO - 商品项 13 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:25,959 - INFO - 商品项 13 找到子元素，class: tfx-item
2025-08-01 15:51:25,962 - INFO - 商品项 13 直接获取到data-autolog: item_id=678615972761&path=search_goods_card&index=12&type=category
2025-08-01 15:51:25,962 - INFO - 商品项 13 data-autolog: item_id=678615972761&path=search_goods_card&index=12&type=category
2025-08-01 15:51:25,962 - INFO - 商品项 13 提取到item_id: 678615972761
2025-08-01 15:51:25,971 - INFO - 商品项 13 商品名称: 美国BadAir Sponge甲醛装修汽车异味空气净化剂400g 3307490000
2025-08-01 15:51:25,979 - INFO - 找到商品项 14，检查元素信息...
2025-08-01 15:51:25,989 - INFO - 商品项 14 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:25,989 - INFO - 商品项 14 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:25,996 - INFO - 商品项 14 找到子元素，class: tfx-item
2025-08-01 15:51:26,001 - INFO - 商品项 14 直接获取到data-autolog: item_id=667871296860&path=search_goods_card&index=13&type=category
2025-08-01 15:51:26,001 - INFO - 商品项 14 data-autolog: item_id=667871296860&path=search_goods_card&index=13&type=category
2025-08-01 15:51:26,001 - INFO - 商品项 14 提取到item_id: 667871296860
2025-08-01 15:51:26,009 - INFO - 商品项 14 商品名称: 日本white conc沐浴露全身沐浴乳进口持久留香林允同款变白新
2025-08-01 15:51:26,019 - INFO - 找到商品项 15，检查元素信息...
2025-08-01 15:51:26,029 - INFO - 商品项 15 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:26,029 - INFO - 商品项 15 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:26,036 - INFO - 商品项 15 找到子元素，class: tfx-item
2025-08-01 15:51:26,040 - INFO - 商品项 15 直接获取到data-autolog: item_id=775364477598&path=search_goods_card&index=14&type=category
2025-08-01 15:51:26,041 - INFO - 商品项 15 data-autolog: item_id=775364477598&path=search_goods_card&index=14&type=category
2025-08-01 15:51:26,041 - INFO - 商品项 15 提取到item_id: 775364477598
2025-08-01 15:51:26,050 - INFO - 商品项 15 商品名称: 1-2-3-4支装德国小红牙膏
2025-08-01 15:51:26,060 - INFO - 找到商品项 16，检查元素信息...
2025-08-01 15:51:26,070 - INFO - 商品项 16 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:26,070 - INFO - 商品项 16 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:26,079 - INFO - 商品项 16 找到子元素，class: tfx-item
2025-08-01 15:51:26,082 - INFO - 商品项 16 直接获取到data-autolog: item_id=721466765064&path=search_goods_card&index=15&type=category
2025-08-01 15:51:26,082 - INFO - 商品项 16 data-autolog: item_id=721466765064&path=search_goods_card&index=15&type=category
2025-08-01 15:51:26,082 - INFO - 商品项 16 提取到item_id: 721466765064
2025-08-01 15:51:26,092 - INFO - 商品项 16 商品名称: 日本未来VAPE防蚊水喷雾宝宝液防叮防虫水婴儿防叮咬神器户外便携
2025-08-01 15:51:26,100 - INFO - 找到商品项 17，检查元素信息...
2025-08-01 15:51:26,109 - INFO - 商品项 17 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:26,109 - INFO - 商品项 17 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:26,115 - INFO - 商品项 17 找到子元素，class: tfx-item
2025-08-01 15:51:26,120 - INFO - 商品项 17 直接获取到data-autolog: item_id=646360607664&path=search_goods_card&index=16&type=category
2025-08-01 15:51:26,120 - INFO - 商品项 17 data-autolog: item_id=646360607664&path=search_goods_card&index=16&type=category
2025-08-01 15:51:26,120 - INFO - 商品项 17 提取到item_id: 646360607664
2025-08-01 15:51:26,127 - INFO - 商品项 17 商品名称: 莱氏姆柠檬洗洁精1KG去油家庭装家用按压瓶水果蔬菜英国品牌
2025-08-01 15:51:26,136 - INFO - 找到商品项 18，检查元素信息...
2025-08-01 15:51:26,143 - INFO - 商品项 18 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:26,143 - INFO - 商品项 18 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:26,151 - INFO - 商品项 18 找到子元素，class: tfx-item
2025-08-01 15:51:26,153 - INFO - 商品项 18 直接获取到data-autolog: item_id=832246402264&path=search_goods_card&index=17&type=category
2025-08-01 15:51:26,153 - INFO - 商品项 18 data-autolog: item_id=832246402264&path=search_goods_card&index=17&type=category
2025-08-01 15:51:26,153 - INFO - 商品项 18 提取到item_id: 832246402264
2025-08-01 15:51:26,162 - INFO - 商品项 18 商品名称: 【保税直发】喜净Hygiene衣物清新剂衣物护理祛皱柔顺喷雾220ml
2025-08-01 15:51:26,178 - INFO - 找到商品项 19，检查元素信息...
2025-08-01 15:51:26,188 - INFO - 商品项 19 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:26,189 - INFO - 商品项 19 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:26,196 - INFO - 商品项 19 找到子元素，class: tfx-item
2025-08-01 15:51:26,199 - INFO - 商品项 19 直接获取到data-autolog: item_id=738499396463&path=search_goods_card&index=18&type=category
2025-08-01 15:51:26,200 - INFO - 商品项 19 data-autolog: item_id=738499396463&path=search_goods_card&index=18&type=category
2025-08-01 15:51:26,200 - INFO - 商品项 19 提取到item_id: 738499396463
2025-08-01 15:51:26,215 - INFO - 商品项 19 商品名称: 德国Dr Beckmann贝克曼博士混洗衣吸色纸防染色串色布
2025-08-01 15:51:26,229 - INFO - 找到商品项 20，检查元素信息...
2025-08-01 15:51:26,241 - INFO - 商品项 20 - 元素标签: div, class: , 有data-autolog: False
2025-08-01 15:51:26,241 - INFO - 商品项 20 当前元素无data-autolog，查找子元素...
2025-08-01 15:51:26,247 - INFO - 商品项 20 找到子元素，class: tfx-item
2025-08-01 15:51:26,251 - INFO - 商品项 20 直接获取到data-autolog: item_id=702821195703&path=search_goods_card&index=19&type=category
2025-08-01 15:51:26,252 - INFO - 商品项 20 data-autolog: item_id=702821195703&path=search_goods_card&index=19&type=category
2025-08-01 15:51:26,252 - INFO - 商品项 20 提取到item_id: 702821195703
2025-08-01 15:51:26,260 - INFO - 商品项 20 商品名称: 韩国TOOSTY牙膏80g，联系vx或旺旺客服领取价格和上架资料
2025-08-01 15:51:26,260 - INFO - 已爬取到 20 个商品，停止当前页面爬取
2025-08-01 15:51:26,260 - INFO - 当前页面爬取完成，找到 20 个商品
2025-08-01 15:51:26,260 - INFO - 第 2 页找到 20 个商品，总计: 60
2025-08-01 15:51:26,261 - INFO - 已达到最大商品数量 60，停止爬取
2025-08-01 15:51:26,261 - INFO - 爬取完成，共找到 60 个商品
2025-08-01 15:51:26,261 - INFO - 成功读取到 60 个商品（目标60个），开始铺货任务
2025-08-01 15:51:26,261 - INFO - 第四步：开始处理商品...
2025-08-01 15:51:26,261 - INFO - 已读取 60 个商品，目标成功铺货 3 个商品
2025-08-01 15:51:26,261 - INFO - 将按顺序处理商品
2025-08-01 15:51:26,262 - INFO - 当前批次大小: 14
2025-08-01 15:51:26,262 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=630604349715
2025-08-01 15:51:29,542 - INFO - 页面加载完成
2025-08-01 15:51:29,542 - INFO - 模拟阅读行为，停顿 1.8 秒
2025-08-01 15:51:32,125 - INFO - 开始第 1 次铺货尝试...
2025-08-01 15:51:32,125 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-08-01 15:51:32,126 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-08-01 15:51:32,134 - INFO - span innerHTML: 立即铺货
2025-08-01 15:51:32,155 - INFO - 找到按钮，文本内容: '立即铺货'
2025-08-01 15:51:32,155 - INFO - 使用配置的XPath找到添加商品按钮
2025-08-01 15:51:32,167 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-08-01 15:51:33,616 - INFO - 尝试ActionChains点击...
2025-08-01 15:51:33,902 - INFO - ActionChains点击成功
2025-08-01 15:51:34,274 - INFO - 铺货按钮点击成功
2025-08-01 15:51:36,289 - INFO - 使用配置的XPath找到确认按钮
2025-08-01 15:51:36,301 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-08-01 15:51:37,625 - INFO - 尝试ActionChains点击...
2025-08-01 15:51:37,900 - INFO - ActionChains点击成功
2025-08-01 15:51:38,118 - INFO - 确认按钮点击成功
2025-08-01 15:51:40,119 - INFO - 验证铺货是否成功，检查span元素状态...
2025-08-01 15:51:40,126 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-08-01 15:51:40,126 - INFO - span仍为'立即铺货'，继续等待...
2025-08-01 15:51:41,136 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-08-01 15:51:41,138 - INFO - span仍为'立即铺货'，继续等待...
2025-08-01 15:51:42,147 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-08-01 15:51:42,147 - INFO - span仍为'立即铺货'，继续等待...
2025-08-01 15:51:43,155 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-08-01 15:51:43,155 - INFO - span仍为'立即铺货'，继续等待...
2025-08-01 15:51:44,163 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-08-01 15:51:44,163 - INFO - span仍为'立即铺货'，继续等待...
2025-08-01 15:51:45,172 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-08-01 15:51:45,172 - INFO - span仍为'立即铺货'，继续等待...
2025-08-01 15:51:46,180 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-08-01 15:51:46,180 - INFO - span仍为'立即铺货'，继续等待...
2025-08-01 15:51:47,188 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-08-01 15:51:47,188 - INFO - span仍为'立即铺货'，继续等待...
2025-08-01 15:51:48,197 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-08-01 15:51:48,197 - INFO - span仍为'立即铺货'，继续等待...
2025-08-01 15:51:49,204 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-08-01 15:51:49,204 - INFO - span仍为'立即铺货'，继续等待...
2025-08-01 15:51:50,205 - INFO - 等待超时，进行最后一次检查...
2025-08-01 15:51:50,213 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-08-01 15:51:50,213 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-08-01 15:51:50,213 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-08-01 15:51:50,213 - INFO - 检测是否出现滑块验证...
2025-08-01 15:51:50,221 - INFO - 在主文档中未找到滑块，检查iframe...
2025-08-01 15:51:50,229 - INFO - 找到 1 个iframe
2025-08-01 15:51:50,229 - INFO - 检查第 1 个iframe...
2025-08-01 15:51:50,249 - INFO - 在第 1 个iframe中检测到滑块验证，开始处理...
2025-08-01 15:51:50,250 - INFO - 检测到滑块验证，切换为手动处理模式...
2025-08-01 15:51:50,287 - INFO - 保存当前页面状态: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=630604349715
2025-08-01 15:51:52,572 - INFO - 关闭无头模式浏览器
2025-08-01 15:51:54,443 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49cda]
	(No symbol) [0x0x7ff65bf51679]
	(No symbol) [0x0x7ff65bf54a61]
	(No symbol) [0x0x7ff65bff1e4b]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:51:54,444 - INFO - 防检测机制设置完成
2025-08-01 15:51:54,444 - INFO - 浏览器驱动初始化成功
2025-08-01 15:51:54,444 - INFO - 启动有界面模式浏览器
2025-08-01 15:51:56,913 - INFO - 浏览器已切换为有界面模式，页面状态已恢复
2025-08-01 15:51:56,941 - INFO - 需要手动完成滑块验证
2025-08-01 15:51:56,941 - INFO - 第 1 个商品需要手动验证，正在切换到有界面模式...
2025-08-01 15:51:56,947 - INFO - 保存当前页面URL: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=630604349715
2025-08-01 15:51:56,952 - INFO - Cookies已保存
2025-08-01 15:51:56,952 - INFO - 已保存cookies
2025-08-01 15:51:59,246 - INFO - 已关闭无头浏览器
2025-08-01 15:52:01,048 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49cda]
	(No symbol) [0x0x7ff65bf51679]
	(No symbol) [0x0x7ff65bf54a61]
	(No symbol) [0x0x7ff65bff1e4b]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:01,049 - INFO - 防检测机制设置完成
2025-08-01 15:52:01,049 - INFO - 浏览器驱动初始化成功
2025-08-01 15:52:01,049 - INFO - 有界面模式浏览器初始化成功
2025-08-01 15:52:01,054 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:01,058 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:01,060 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:01,062 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:01,065 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:01,068 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:01,071 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:01,073 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:01,076 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:01,078 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:01,082 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:01,085 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:01,087 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:01,090 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:01,094 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:01,097 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:01,099 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:01,102 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:01,106 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:01,109 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:01,112 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:01,112 - INFO - Cookies已加载
2025-08-01 15:52:01,112 - INFO - 已加载cookies到有界面浏览器
2025-08-01 15:52:04,905 - INFO - 已导航回验证页面
2025-08-01 15:52:04,905 - INFO - 有界面模式下重新点击登录按钮...
2025-08-01 15:52:04,965 - INFO - 找到登录按钮，使用选择器: //*[@id='login-form']/div[6]/button
2025-08-01 15:52:05,990 - ERROR - 点击元素失败: Message: stale element reference: stale element not found
  (Session info: chrome=138.0.7204.169); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49cda]
	(No symbol) [0x0x7ff65bf600f4]
	(No symbol) [0x0x7ff65bf5ebc3]
	(No symbol) [0x0x7ff65bf523d9]
	(No symbol) [0x0x7ff65bf52553]
	(No symbol) [0x0x7ff65bf5028f]
	(No symbol) [0x0x7ff65bf54a61]
	(No symbol) [0x0x7ff65bff1e4b]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:05,990 - WARNING - safe_click方法点击登录按钮失败
2025-08-01 15:52:05,990 - WARNING - 有界面模式下登录按钮点击失败，但继续执行
2025-08-01 15:52:05,990 - INFO - 成功切换到有界面模式，等待用户完成验证
2025-08-01 15:52:05,994 - INFO - GUI窗口已显示到前台，请完成滑块验证
2025-08-01 15:52:55,304 - INFO - 用户完成手动验证，正在切换到无头模式...
2025-08-01 15:52:55,309 - INFO - 保存当前页面URL: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=630604349715
2025-08-01 15:52:55,316 - INFO - Cookies已保存
2025-08-01 15:52:55,316 - INFO - 已保存cookies
2025-08-01 15:52:55,522 - INFO - 重新处理第 1 个商品...
2025-08-01 15:52:55,522 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=630604349715
2025-08-01 15:52:55,567 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-08-01 15:52:55,568 - ERROR - 处理商品 630604349715 时出错: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65bf91366]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:55,568 - WARNING - 第 1 个商品处理失败（不计入成功数量）
2025-08-01 15:52:55,568 - INFO - 商品处理失败，立即处理下一个商品
2025-08-01 15:52:55,592 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=681787375440
2025-08-01 15:52:57,582 - INFO - 已关闭有界面浏览器
2025-08-01 15:52:57,583 - INFO - 以无界面(headless)模式启动Chrome，仅添加headless、no-sandbox、disable-dev-shm-usage参数
2025-08-01 15:52:59,553 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49cda]
	(No symbol) [0x0x7ff65bf51679]
	(No symbol) [0x0x7ff65bf54a61]
	(No symbol) [0x0x7ff65bff1e4b]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:59,553 - INFO - 防检测机制设置完成
2025-08-01 15:52:59,553 - INFO - 浏览器驱动初始化成功
2025-08-01 15:52:59,553 - INFO - 无头模式浏览器初始化成功
2025-08-01 15:52:59,557 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:59,560 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:59,564 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:59,567 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:59,569 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:59,572 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:59,576 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:59,579 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:59,582 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:59,585 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:59,587 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:59,590 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:59,593 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:59,596 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:59,599 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:59,602 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:59,606 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:59,608 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:59,611 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:59,613 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:59,617 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:59,619 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 15:52:59,619 - INFO - Cookies已加载
2025-08-01 15:52:59,619 - INFO - 已加载cookies到无头浏览器
2025-08-01 15:52:59,672 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000211CF0CDB90>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /session/2681d29061c3bcd459285d6117ed4c6f/url
2025-08-01 15:53:02,761 - INFO - 已导航回验证页面
2025-08-01 15:53:02,761 - INFO - 无头模式下重新点击登录按钮...
2025-08-01 15:53:02,782 - INFO - 找到登录按钮，使用选择器: //*[@id='login-form']/div[6]/button
2025-08-01 15:53:03,752 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000211CF0DD3D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /session/2681d29061c3bcd459285d6117ed4c6f/url
2025-08-01 15:53:03,816 - INFO - 准备点击登录按钮: 标签=button, 文本='登录', 选择器=//*[@id='login-form']/div[6]/button
2025-08-01 15:53:03,836 - INFO - 准备点击元素: tag=button, text='登录', class='fm-button fm-submit password-l', id=''
2025-08-01 15:53:05,294 - INFO - 尝试ActionChains点击...
2025-08-01 15:53:05,775 - INFO - ActionChains点击成功
2025-08-01 15:53:06,221 - INFO - 登录按钮点击成功
2025-08-01 15:53:06,221 - INFO - 无头模式下登录按钮点击成功
2025-08-01 15:53:07,850 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000211CF0DDB50>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /session/2681d29061c3bcd459285d6117ed4c6f/url
2025-08-01 15:53:09,222 - INFO - 成功切换到无头模式，继续自动化流程
2025-08-01 15:53:11,949 - ERROR - 处理商品 681787375440 时出错: HTTPConnectionPool(host='localhost', port=50167): Max retries exceeded with url: /session/2681d29061c3bcd459285d6117ed4c6f/url (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000211CF0DDAD0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-08-01 15:53:11,949 - WARNING - 第 2 个商品处理失败（不计入成功数量）
2025-08-01 15:53:11,949 - INFO - 商品处理失败，立即处理下一个商品
2025-08-01 15:53:11,961 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=821067902127
2025-08-01 15:53:15,037 - INFO - 页面加载完成
2025-08-01 15:53:15,037 - INFO - 模拟阅读行为，停顿 1.7 秒
2025-08-01 15:53:17,900 - INFO - 添加额外延迟: 1.3秒
2025-08-01 15:53:20,414 - INFO - 开始第 1 次铺货尝试...
2025-08-01 15:53:20,414 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-08-01 15:53:20,414 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-08-01 15:53:20,423 - INFO - span innerHTML: 立即铺货
2025-08-01 15:53:20,444 - INFO - 找到按钮，文本内容: '立即铺货'
2025-08-01 15:53:20,444 - INFO - 使用配置的XPath找到添加商品按钮
2025-08-01 15:53:20,459 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-08-01 15:53:21,720 - INFO - 尝试ActionChains点击...
2025-08-01 15:53:22,004 - INFO - ActionChains点击成功
2025-08-01 15:53:22,370 - INFO - 铺货按钮点击成功
2025-08-01 15:53:24,383 - INFO - 使用配置的XPath找到确认按钮
2025-08-01 15:53:24,393 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-08-01 15:53:25,438 - INFO - 尝试ActionChains点击...
2025-08-01 15:53:25,715 - INFO - ActionChains点击成功
2025-08-01 15:53:26,102 - INFO - 确认按钮点击成功
2025-08-01 15:53:28,102 - INFO - 验证铺货是否成功，检查span元素状态...
2025-08-01 15:53:28,110 - INFO - 第1次检查 - span innerHTML: 已铺货
2025-08-01 15:53:28,111 - INFO - 验证成功：span已变为'已铺货'，商品铺货成功
2025-08-01 15:53:28,111 - INFO - 第 3 个商品处理成功 (总成功: 1/3, 批次进度: 1/14)
2025-08-01 15:53:28,125 - INFO - 铺货成功，等待 4.0 秒后处理下一个商品
2025-08-01 15:53:32,093 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=846634805775
2025-08-01 15:53:35,798 - INFO - 页面加载完成
2025-08-01 15:53:35,799 - INFO - 模拟阅读行为，停顿 2.2 秒
2025-08-01 15:53:38,033 - INFO - 添加额外延迟: 1.8秒
2025-08-01 15:53:40,657 - INFO - 开始第 1 次铺货尝试...
2025-08-01 15:53:40,657 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-08-01 15:53:40,657 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-08-01 15:53:40,664 - INFO - span innerHTML: 立即铺货
2025-08-01 15:53:40,686 - INFO - 找到按钮，文本内容: '立即铺货'
2025-08-01 15:53:40,686 - INFO - 使用配置的XPath找到添加商品按钮
2025-08-01 15:53:40,699 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-08-01 15:53:41,992 - INFO - 尝试ActionChains点击...
2025-08-01 15:53:42,279 - INFO - ActionChains点击成功
2025-08-01 15:53:42,672 - INFO - 铺货按钮点击成功
2025-08-01 15:53:43,053 - INFO - Cookies已保存
2025-08-01 15:53:45,291 - INFO - 浏览器已关闭
