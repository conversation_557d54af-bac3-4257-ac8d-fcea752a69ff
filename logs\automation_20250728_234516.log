2025-07-28 23:45:19,217 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059cda]
	(No symbol) [0x0x7ff69a061679]
	(No symbol) [0x0x7ff69a064a61]
	(No symbol) [0x0x7ff69a101e4b]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:45:19,218 - INFO - 防检测机制设置完成
2025-07-28 23:45:19,218 - INFO - 浏览器驱动初始化成功
2025-07-28 23:45:19,224 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:45:19,232 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:45:19,235 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:45:19,240 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:45:19,244 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:45:19,247 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:45:19,250 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:45:19,254 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:45:19,258 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:45:19,261 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:45:19,264 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:45:19,268 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:45:19,271 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:45:19,273 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:45:19,275 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:45:19,293 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:45:19,296 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:45:19,298 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:45:19,301 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:45:19,311 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:45:19,311 - INFO - Cookies已加载
2025-07-28 23:45:19,311 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?channel=global_zx&spm=a21ug5.29159167.6452766900.2ndview_zxmore_click
2025-07-28 23:45:24,842 - INFO - 当前User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-28 23:45:24,842 - INFO - 确认：使用桌面版User-Agent
2025-07-28 23:45:24,848 - INFO - 桌面版User-Agent设置完成: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-28 23:45:26,719 - INFO - 成功导航到目标页面
2025-07-28 23:45:26,720 - INFO - 登录成功！现在可以开始铺货任务
2025-07-28 23:45:28,882 - INFO - Cookies已保存
2025-07-28 23:45:31,132 - INFO - 浏览器已关闭
2025-07-28 23:45:32,537 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059cda]
	(No symbol) [0x0x7ff69a061679]
	(No symbol) [0x0x7ff69a064a61]
	(No symbol) [0x0x7ff69a101e4b]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:45:32,537 - INFO - 防检测机制设置完成
2025-07-28 23:45:32,537 - INFO - 浏览器驱动初始化成功
2025-07-28 23:45:32,537 - INFO - 开始自动化铺货流程（有界面模式）
2025-07-28 23:45:32,538 - INFO - 第一步：正在导航到目标页面...
2025-07-28 23:45:36,350 - INFO - 第一步：点击登录按钮
2025-07-28 23:45:36,400 - INFO - 找到登录按钮，使用选择器: //*[@id='login-form']/div[6]/button
2025-07-28 23:45:37,429 - INFO - 准备点击登录按钮: 标签=button, 文本='登录', 选择器=//*[@id='login-form']/div[6]/button
2025-07-28 23:45:37,444 - INFO - 准备点击元素: tag=button, text='登录', class='fm-button fm-submit password-l', id=''
2025-07-28 23:45:38,835 - INFO - 尝试ActionChains点击...
2025-07-28 23:45:39,243 - INFO - ActionChains点击成功
2025-07-28 23:45:39,652 - INFO - 登录按钮点击成功
2025-07-28 23:45:39,652 - INFO - 登录按钮点击成功
2025-07-28 23:45:42,653 - INFO - 第二步：获取账号信息...
2025-07-28 23:45:42,653 - INFO - 正在跳转到个人中心页面: https://jingya.taobao.com/?v=2
2025-07-28 23:46:08,000 - INFO - 尝试获取账号名称，使用XPath: //*[@id='workbench-user-tool-btn']/div/div[2]
2025-07-28 23:46:08,014 - INFO - 成功获取账号信息: 卓祥22店:冰淇淋
2025-07-28 23:46:08,015 - INFO - 获取到账号名称: 卓祥22店:冰淇淋
2025-07-28 23:46:08,015 - INFO - GUI界面已更新账号显示: 卓祥22店:冰淇淋
2025-07-28 23:46:08,015 - INFO - 第三步：开始读取20个商品信息...
2025-07-28 23:46:08,017 - INFO - 开始爬取商品信息，最大数量: 20
2025-07-28 23:46:11,621 - INFO - 正在爬取第 1 页商品信息...
2025-07-28 23:46:13,630 - INFO - 找到商品容器，开始爬取商品，最大数量: 20
2025-07-28 23:46:13,643 - INFO - 找到商品项 1，检查元素信息...
2025-07-28 23:46:13,655 - INFO - 商品项 1 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:46:13,658 - INFO - 商品项 1 直接获取到data-autolog: item_id=681778175573&path=search_goods_card&type=search
2025-07-28 23:46:13,659 - INFO - 商品项 1 data-autolog: item_id=681778175573&path=search_goods_card&type=search
2025-07-28 23:46:13,659 - INFO - 商品项 1 提取到item_id: 681778175573
2025-07-28 23:46:13,672 - INFO - 商品项 1 商品名称: 保税直供 Dove多芬磨砂膏石榴籽乳木果风味身体磨砂膏298g
2025-07-28 23:46:13,684 - INFO - 找到商品项 2，检查元素信息...
2025-07-28 23:46:13,693 - INFO - 商品项 2 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:46:13,697 - INFO - 商品项 2 直接获取到data-autolog: item_id=690841537159&path=search_goods_card&index=1&type=search
2025-07-28 23:46:13,697 - INFO - 商品项 2 data-autolog: item_id=690841537159&path=search_goods_card&index=1&type=search
2025-07-28 23:46:13,697 - INFO - 商品项 2 提取到item_id: 690841537159
2025-07-28 23:46:13,705 - INFO - 商品项 2 商品名称: 保税直供 凡士林Vaseline烟酰胺身体乳400ml 725ml无压泵款200ml
2025-07-28 23:46:17,810 - INFO - 找到商品项 3，检查元素信息...
2025-07-28 23:46:17,820 - INFO - 商品项 3 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:46:17,823 - INFO - 商品项 3 直接获取到data-autolog: item_id=789447097664&path=search_goods_card&index=2&type=search
2025-07-28 23:46:17,823 - INFO - 商品项 3 data-autolog: item_id=789447097664&path=search_goods_card&index=2&type=search
2025-07-28 23:46:17,823 - INFO - 商品项 3 提取到item_id: 789447097664
2025-07-28 23:46:17,832 - INFO - 商品项 3 商品名称: 【甄选】珂润Curel泡沫洗面奶150ml
2025-07-28 23:46:17,843 - INFO - 找到商品项 4，检查元素信息...
2025-07-28 23:46:17,850 - INFO - 商品项 4 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:46:17,854 - INFO - 商品项 4 直接获取到data-autolog: item_id=694682843417&path=search_goods_card&index=3&type=search
2025-07-28 23:46:17,854 - INFO - 商品项 4 data-autolog: item_id=694682843417&path=search_goods_card&index=3&type=search
2025-07-28 23:46:17,854 - INFO - 商品项 4 提取到item_id: 694682843417
2025-07-28 23:46:17,862 - INFO - 商品项 4 商品名称: 【可开授权】日台混发Fino发膜美容液护发素 发膜230g
2025-07-28 23:46:17,873 - INFO - 找到商品项 5，检查元素信息...
2025-07-28 23:46:17,882 - INFO - 商品项 5 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:46:17,885 - INFO - 商品项 5 直接获取到data-autolog: item_id=681082809892&path=search_goods_card&index=4&type=search
2025-07-28 23:46:17,885 - INFO - 商品项 5 data-autolog: item_id=681082809892&path=search_goods_card&index=4&type=search
2025-07-28 23:46:17,885 - INFO - 商品项 5 提取到item_id: 681082809892
2025-07-28 23:46:17,894 - INFO - 商品项 5 商品名称: 保税直供 Dove多芬大白碗润肤身体乳300ml 【1//2//3罐】
2025-07-28 23:46:17,904 - INFO - 找到商品项 6，检查元素信息...
2025-07-28 23:46:17,912 - INFO - 商品项 6 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:46:17,915 - INFO - 商品项 6 直接获取到data-autolog: item_id=694675776881&path=search_goods_card&index=5&type=search
2025-07-28 23:46:17,915 - INFO - 商品项 6 data-autolog: item_id=694675776881&path=search_goods_card&index=5&type=search
2025-07-28 23:46:17,915 - INFO - 商品项 6 提取到item_id: 694675776881
2025-07-28 23:46:17,925 - INFO - 商品项 6 商品名称: 保税直供 Pantene潘婷护发素三分钟奇迹多效修护发膜级护发素
2025-07-28 23:46:17,935 - INFO - 找到商品项 7，检查元素信息...
2025-07-28 23:46:17,943 - INFO - 商品项 7 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:46:17,946 - INFO - 商品项 7 直接获取到data-autolog: item_id=680729304287&path=search_goods_card&index=6&type=search
2025-07-28 23:46:17,946 - INFO - 商品项 7 data-autolog: item_id=680729304287&path=search_goods_card&index=6&type=search
2025-07-28 23:46:17,946 - INFO - 商品项 7 提取到item_id: 680729304287
2025-07-28 23:46:17,955 - INFO - 商品项 7 商品名称: 保税直供 多芬dove空气感无硅油 洗发水480g 护发素480g护发临期
2025-07-28 23:46:17,968 - INFO - 找到商品项 8，检查元素信息...
2025-07-28 23:46:17,976 - INFO - 商品项 8 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:46:17,979 - INFO - 商品项 8 直接获取到data-autolog: item_id=710355107054&path=search_goods_card&index=7&type=search
2025-07-28 23:46:17,979 - INFO - 商品项 8 data-autolog: item_id=710355107054&path=search_goods_card&index=7&type=search
2025-07-28 23:46:17,980 - INFO - 商品项 8 提取到item_id: 710355107054
2025-07-28 23:46:18,003 - INFO - 商品项 8 商品名称: 保税直供 Dove多芬洗发水护发素空气感洗发水480g 【护发素临期
2025-07-28 23:46:18,014 - INFO - 找到商品项 9，检查元素信息...
2025-07-28 23:46:18,022 - INFO - 商品项 9 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:46:18,024 - INFO - 商品项 9 直接获取到data-autolog: item_id=740463658412&path=search_goods_card&index=8&type=search
2025-07-28 23:46:18,024 - INFO - 商品项 9 data-autolog: item_id=740463658412&path=search_goods_card&index=8&type=search
2025-07-28 23:46:18,024 - INFO - 商品项 9 提取到item_id: 740463658412
2025-07-28 23:46:18,034 - INFO - 商品项 9 商品名称: 【主推链接】花王cape空气感定型喷雾50g/180g 编码：3305300000
2025-07-28 23:46:18,044 - INFO - 找到商品项 10，检查元素信息...
2025-07-28 23:46:18,053 - INFO - 商品项 10 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:46:18,056 - INFO - 商品项 10 直接获取到data-autolog: item_id=821067902127&path=search_goods_card&index=9&type=search
2025-07-28 23:46:18,056 - INFO - 商品项 10 data-autolog: item_id=821067902127&path=search_goods_card&index=9&type=search
2025-07-28 23:46:18,056 - INFO - 商品项 10 提取到item_id: 821067902127
2025-07-28 23:46:18,064 - INFO - 商品项 10 商品名称: 丹碧丝TAMPAX卫生棉条长导管式内置式纯棉月经棉条棉棒游泳卫生巾
2025-07-28 23:46:18,074 - INFO - 找到商品项 11，检查元素信息...
2025-07-28 23:46:18,083 - INFO - 商品项 11 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:46:18,085 - INFO - 商品项 11 直接获取到data-autolog: item_id=789835300215&path=search_goods_card&index=10&type=search
2025-07-28 23:46:18,085 - INFO - 商品项 11 data-autolog: item_id=789835300215&path=search_goods_card&index=10&type=search
2025-07-28 23:46:18,085 - INFO - 商品项 11 提取到item_id: 789835300215
2025-07-28 23:46:18,092 - INFO - 商品项 11 商品名称: 【保税】雅漾喷雾爽肤水300ml
2025-07-28 23:46:18,103 - INFO - 找到商品项 12，检查元素信息...
2025-07-28 23:46:18,111 - INFO - 商品项 12 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:46:18,114 - INFO - 商品项 12 直接获取到data-autolog: item_id=843747605382&path=search_goods_card&index=11&type=search
2025-07-28 23:46:18,114 - INFO - 商品项 12 data-autolog: item_id=843747605382&path=search_goods_card&index=11&type=search
2025-07-28 23:46:18,114 - INFO - 商品项 12 提取到item_id: 843747605382
2025-07-28 23:46:18,123 - INFO - 商品项 12 商品名称: 倩碧紫胖子卸妆膏125ml/瓶
2025-07-28 23:46:18,134 - INFO - 找到商品项 13，检查元素信息...
2025-07-28 23:46:18,141 - INFO - 商品项 13 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:46:18,144 - INFO - 商品项 13 直接获取到data-autolog: item_id=675816539149&path=search_goods_card&index=12&type=search
2025-07-28 23:46:18,144 - INFO - 商品项 13 data-autolog: item_id=675816539149&path=search_goods_card&index=12&type=search
2025-07-28 23:46:18,144 - INFO - 商品项 13 提取到item_id: 675816539149
2025-07-28 23:46:18,152 - INFO - 商品项 13 商品名称: 新加坡Vicopyl维可保益生菌(Pylopass/拒绝幽门菌/抑口臭/调肠胃)
2025-07-28 23:46:18,163 - INFO - 找到商品项 14，检查元素信息...
2025-07-28 23:46:18,170 - INFO - 商品项 14 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:46:18,174 - INFO - 商品项 14 直接获取到data-autolog: item_id=846634805775&path=search_goods_card&index=13&type=search
2025-07-28 23:46:18,174 - INFO - 商品项 14 data-autolog: item_id=846634805775&path=search_goods_card&index=13&type=search
2025-07-28 23:46:18,174 - INFO - 商品项 14 提取到item_id: 846634805775
2025-07-28 23:46:18,195 - INFO - 商品项 14 商品名称: 【甄选】cow牛乳石碱原味/玫瑰花香 瓶装480ml
2025-07-28 23:46:18,206 - INFO - 找到商品项 15，检查元素信息...
2025-07-28 23:46:18,213 - INFO - 商品项 15 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:46:18,215 - INFO - 商品项 15 直接获取到data-autolog: item_id=852510575114&path=search_goods_card&index=14&type=search
2025-07-28 23:46:18,217 - INFO - 商品项 15 data-autolog: item_id=852510575114&path=search_goods_card&index=14&type=search
2025-07-28 23:46:18,217 - INFO - 商品项 15 提取到item_id: 852510575114
2025-07-28 23:46:18,224 - INFO - 商品项 15 商品名称: Nars 纳斯 红壳蜜粉饼16g（含粉扑）、Nars纳斯裸光蜜粉饼10g
2025-07-28 23:46:18,235 - INFO - 找到商品项 16，检查元素信息...
2025-07-28 23:46:18,243 - INFO - 商品项 16 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:46:18,245 - INFO - 商品项 16 直接获取到data-autolog: item_id=743442966826&path=search_goods_card&index=15&type=search
2025-07-28 23:46:18,245 - INFO - 商品项 16 data-autolog: item_id=743442966826&path=search_goods_card&index=15&type=search
2025-07-28 23:46:18,245 - INFO - 商品项 16 提取到item_id: 743442966826
2025-07-28 23:46:18,254 - INFO - 商品项 16 商品名称: ESI卡路里拜拜片白芸豆阻断片大餐救星膳食纤维身材热控管理
2025-07-28 23:46:18,275 - INFO - 找到商品项 17，检查元素信息...
2025-07-28 23:46:18,284 - INFO - 商品项 17 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:46:18,287 - INFO - 商品项 17 直接获取到data-autolog: item_id=848934676698&path=search_goods_card&index=16&type=search
2025-07-28 23:46:18,287 - INFO - 商品项 17 data-autolog: item_id=848934676698&path=search_goods_card&index=16&type=search
2025-07-28 23:46:18,287 - INFO - 商品项 17 提取到item_id: 848934676698
2025-07-28 23:46:18,296 - INFO - 商品项 17 商品名称: Estee Lauder 雅诗兰黛持妆粉底液30ml 2C0 /1w1/1C1
2025-07-28 23:46:18,308 - INFO - 找到商品项 18，检查元素信息...
2025-07-28 23:46:18,316 - INFO - 商品项 18 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:46:18,318 - INFO - 商品项 18 直接获取到data-autolog: item_id=693758717931&path=search_goods_card&index=17&type=search
2025-07-28 23:46:18,318 - INFO - 商品项 18 data-autolog: item_id=693758717931&path=search_goods_card&index=17&type=search
2025-07-28 23:46:18,319 - INFO - 商品项 18 提取到item_id: 693758717931
2025-07-28 23:46:18,327 - INFO - 商品项 18 商品名称: 保税直供 资生堂fino高效渗透发膜 230g （1/2/3罐）日版
2025-07-28 23:46:18,339 - INFO - 找到商品项 19，检查元素信息...
2025-07-28 23:46:18,347 - INFO - 商品项 19 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:46:18,350 - INFO - 商品项 19 直接获取到data-autolog: item_id=691142128229&path=search_goods_card&index=18&type=search
2025-07-28 23:46:18,350 - INFO - 商品项 19 data-autolog: item_id=691142128229&path=search_goods_card&index=18&type=search
2025-07-28 23:46:18,350 - INFO - 商品项 19 提取到item_id: 691142128229
2025-07-28 23:46:18,358 - INFO - 商品项 19 商品名称: 保税 日版Dove多芬洁面洗面奶新版 正装瓶150ml 补充袋125ml 甄选
2025-07-28 23:46:18,369 - INFO - 找到商品项 20，检查元素信息...
2025-07-28 23:46:18,378 - INFO - 商品项 20 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:46:18,381 - INFO - 商品项 20 直接获取到data-autolog: item_id=690739721578&path=search_goods_card&index=19&type=search
2025-07-28 23:46:18,381 - INFO - 商品项 20 data-autolog: item_id=690739721578&path=search_goods_card&index=19&type=search
2025-07-28 23:46:18,382 - INFO - 商品项 20 提取到item_id: 690739721578
2025-07-28 23:46:18,390 - INFO - 商品项 20 商品名称: 保税直供 SUNSILK夏士莲椰子洗发水//护发素450ml 泰产
2025-07-28 23:46:18,391 - INFO - 已爬取到 20 个商品，停止当前页面爬取
2025-07-28 23:46:18,391 - INFO - 当前页面爬取完成，找到 20 个商品
2025-07-28 23:46:18,391 - INFO - 第 1 页找到 20 个商品，总计: 20
2025-07-28 23:46:18,391 - INFO - 已达到最大商品数量 20，停止爬取
2025-07-28 23:46:18,391 - INFO - 爬取完成，共找到 20 个商品
2025-07-28 23:46:18,392 - INFO - 成功读取到 20 个商品（目标20个），开始铺货任务
2025-07-28 23:46:18,392 - INFO - 第四步：开始处理商品...
2025-07-28 23:46:18,403 - INFO - 已读取 20 个商品，目标成功铺货 3 个商品
2025-07-28 23:46:18,403 - INFO - 当前批次大小: 12
2025-07-28 23:46:18,404 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=681778175573
2025-07-28 23:46:22,063 - INFO - 页面加载完成
2025-07-28 23:46:22,063 - INFO - 模拟阅读行为，停顿 1.2 秒
2025-07-28 23:46:23,883 - INFO - 开始第 1 次铺货尝试...
2025-07-28 23:46:23,883 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:46:23,883 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:46:23,894 - INFO - span innerHTML: 立即铺货
2025-07-28 23:46:23,931 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-28 23:46:23,931 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-28 23:46:23,943 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-28 23:46:25,156 - INFO - 尝试ActionChains点击...
2025-07-28 23:46:25,446 - INFO - ActionChains点击成功
2025-07-28 23:46:25,818 - INFO - 铺货按钮点击成功
2025-07-28 23:46:27,848 - INFO - 使用配置的XPath找到确认按钮
2025-07-28 23:46:27,861 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-28 23:46:28,871 - INFO - 尝试ActionChains点击...
2025-07-28 23:46:29,139 - INFO - ActionChains点击成功
2025-07-28 23:46:29,532 - INFO - 确认按钮点击成功
2025-07-28 23:46:31,532 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-28 23:46:31,540 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-28 23:46:31,540 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:46:32,550 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-28 23:46:32,550 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:46:33,558 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-28 23:46:33,558 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:46:34,566 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-28 23:46:34,566 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:46:35,575 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-07-28 23:46:35,575 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:46:36,584 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-07-28 23:46:36,584 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:46:37,617 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-07-28 23:46:37,617 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:46:38,625 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-07-28 23:46:38,626 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:46:39,661 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-07-28 23:46:39,661 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:46:40,671 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-07-28 23:46:40,671 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:46:41,672 - INFO - 等待超时，进行最后一次检查...
2025-07-28 23:46:41,678 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-07-28 23:46:41,678 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-07-28 23:46:41,678 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-07-28 23:46:41,678 - INFO - 检测是否出现滑块验证...
2025-07-28 23:46:41,686 - INFO - 在主文档中未找到滑块，检查iframe...
2025-07-28 23:46:41,692 - INFO - 找到 1 个iframe
2025-07-28 23:46:41,692 - INFO - 检查第 1 个iframe...
2025-07-28 23:46:41,716 - INFO - 在第 1 个iframe中检测到滑块验证，开始处理...
2025-07-28 23:46:41,716 - INFO - 开始处理滑块验证...
2025-07-28 23:46:41,729 - INFO - 滑块容器宽度: 300px
2025-07-28 23:46:41,729 - INFO - 计算拖拽距离: 282px (比例: 0.94)
2025-07-28 23:46:41,729 - INFO - 开始简洁滑块操作...
2025-07-28 23:46:41,729 - INFO - 移动鼠标到滑块...
2025-07-28 23:46:41,729 - INFO - 按住滑块...
2025-07-28 23:46:41,729 - INFO - 拖拽滑块 282px...
2025-07-28 23:46:41,729 - INFO - 释放滑块...
2025-07-28 23:46:41,730 - INFO - 执行滑块操作...
2025-07-28 23:46:45,442 - INFO - 简洁滑块操作完成
2025-07-28 23:46:45,446 - INFO - 滑块验证成功，滑块已消失
2025-07-28 23:46:45,447 - INFO - 滑块验证处理成功，准备重试铺货
2025-07-28 23:46:47,448 - INFO - 开始第 2 次铺货尝试...
2025-07-28 23:46:47,448 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:46:47,448 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:46:47,457 - INFO - span innerHTML: 立即铺货
2025-07-28 23:46:57,789 - WARNING - 等待元素可点击超时: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:46:57,789 - WARNING - 使用配置的XPath未找到添加商品按钮
2025-07-28 23:46:57,789 - ERROR - 未找到铺货按钮
2025-07-28 23:46:57,789 - WARNING - 第 1 个商品处理失败（不计入成功数量）
2025-07-28 23:46:57,789 - INFO - 商品处理失败，立即处理下一个商品
2025-07-28 23:46:57,791 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=690841537159
2025-07-28 23:47:01,045 - INFO - 页面加载完成
2025-07-28 23:47:01,045 - INFO - 模拟阅读行为，停顿 2.3 秒
2025-07-28 23:47:04,156 - INFO - 添加额外延迟: 2.9秒
2025-07-28 23:47:07,944 - INFO - 开始第 1 次铺货尝试...
2025-07-28 23:47:07,945 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:47:07,945 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:47:07,952 - INFO - span innerHTML: 立即铺货
2025-07-28 23:47:07,973 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-28 23:47:07,973 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-28 23:47:07,987 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-28 23:47:09,464 - INFO - 尝试ActionChains点击...
2025-07-28 23:47:09,752 - INFO - ActionChains点击成功
2025-07-28 23:47:10,116 - INFO - 铺货按钮点击成功
2025-07-28 23:47:12,143 - INFO - 使用配置的XPath找到确认按钮
2025-07-28 23:47:12,156 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-28 23:47:13,611 - INFO - 尝试ActionChains点击...
2025-07-28 23:47:13,878 - INFO - ActionChains点击成功
2025-07-28 23:47:14,364 - INFO - 确认按钮点击成功
2025-07-28 23:47:16,364 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-28 23:47:16,372 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-28 23:47:16,372 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:47:17,381 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-28 23:47:17,381 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:47:18,388 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-28 23:47:18,388 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:47:19,395 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-28 23:47:19,395 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:47:20,404 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-07-28 23:47:20,406 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:47:21,413 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-07-28 23:47:21,413 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:47:22,422 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-07-28 23:47:22,422 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:47:23,430 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-07-28 23:47:23,430 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:47:24,438 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-07-28 23:47:24,438 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:47:25,446 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-07-28 23:47:25,446 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:47:26,447 - INFO - 等待超时，进行最后一次检查...
2025-07-28 23:47:26,454 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-07-28 23:47:26,454 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-07-28 23:47:26,454 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-07-28 23:47:26,454 - INFO - 检测是否出现滑块验证...
2025-07-28 23:47:26,476 - INFO - 在主文档中未找到滑块，检查iframe...
2025-07-28 23:47:26,481 - INFO - 找到 1 个iframe
2025-07-28 23:47:26,481 - INFO - 检查第 1 个iframe...
2025-07-28 23:47:26,501 - INFO - 在第 1 个iframe中检测到滑块验证，开始处理...
2025-07-28 23:47:26,501 - INFO - 开始处理滑块验证...
2025-07-28 23:47:26,515 - INFO - 滑块容器宽度: 300px
2025-07-28 23:47:26,515 - INFO - 计算拖拽距离: 276px (比例: 0.92)
2025-07-28 23:47:26,515 - INFO - 开始简洁滑块操作...
2025-07-28 23:47:26,515 - INFO - 移动鼠标到滑块...
2025-07-28 23:47:26,515 - INFO - 按住滑块...
2025-07-28 23:47:26,515 - INFO - 拖拽滑块 276px...
2025-07-28 23:47:26,516 - INFO - 释放滑块...
2025-07-28 23:47:26,516 - INFO - 执行滑块操作...
2025-07-28 23:47:30,174 - INFO - 简洁滑块操作完成
2025-07-28 23:47:30,180 - INFO - 滑块验证成功，滑块已消失
2025-07-28 23:47:30,181 - INFO - 滑块验证处理成功，准备重试铺货
2025-07-28 23:47:32,182 - INFO - 开始第 2 次铺货尝试...
2025-07-28 23:47:32,182 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:47:32,183 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:47:32,191 - INFO - span innerHTML: 立即铺货
2025-07-28 23:47:42,404 - WARNING - 等待元素可点击超时: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:47:42,404 - WARNING - 使用配置的XPath未找到添加商品按钮
2025-07-28 23:47:42,404 - ERROR - 未找到铺货按钮
2025-07-28 23:47:42,404 - WARNING - 第 2 个商品处理失败（不计入成功数量）
2025-07-28 23:47:42,405 - INFO - 商品处理失败，立即处理下一个商品
2025-07-28 23:47:42,418 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=789447097664
2025-07-28 23:47:46,181 - INFO - 页面加载完成
2025-07-28 23:47:46,181 - INFO - 模拟阅读行为，停顿 1.8 秒
2025-07-28 23:47:50,192 - INFO - 开始第 1 次铺货尝试...
2025-07-28 23:47:50,192 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:47:50,193 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:47:50,199 - INFO - span innerHTML: 立即铺货
2025-07-28 23:47:50,224 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-28 23:47:50,224 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-28 23:47:50,239 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-28 23:47:51,271 - INFO - 尝试ActionChains点击...
2025-07-28 23:47:51,566 - INFO - ActionChains点击成功
2025-07-28 23:47:52,035 - INFO - 铺货按钮点击成功
2025-07-28 23:47:54,050 - INFO - 使用配置的XPath找到确认按钮
2025-07-28 23:47:54,065 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-28 23:47:55,462 - INFO - 尝试ActionChains点击...
2025-07-28 23:47:55,728 - INFO - ActionChains点击成功
2025-07-28 23:47:56,216 - INFO - 确认按钮点击成功
2025-07-28 23:47:58,217 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-28 23:47:58,226 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-28 23:47:58,226 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:47:59,234 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-28 23:47:59,234 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:48:00,242 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-28 23:48:00,242 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:48:01,251 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-28 23:48:01,251 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:48:02,260 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-07-28 23:48:02,261 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:48:03,268 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-07-28 23:48:03,268 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:48:04,294 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-07-28 23:48:04,294 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:48:05,302 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-07-28 23:48:05,302 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:48:06,309 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-07-28 23:48:06,310 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:48:07,317 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-07-28 23:48:07,317 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:48:08,318 - INFO - 等待超时，进行最后一次检查...
2025-07-28 23:48:08,327 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-07-28 23:48:08,327 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-07-28 23:48:08,327 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-07-28 23:48:08,327 - INFO - 检测是否出现滑块验证...
2025-07-28 23:48:08,335 - INFO - 在主文档中未找到滑块，检查iframe...
2025-07-28 23:48:08,343 - INFO - 找到 1 个iframe
2025-07-28 23:48:08,343 - INFO - 检查第 1 个iframe...
2025-07-28 23:48:08,363 - INFO - 在第 1 个iframe中检测到滑块验证，开始处理...
2025-07-28 23:48:08,363 - INFO - 开始处理滑块验证...
2025-07-28 23:48:08,376 - INFO - 滑块容器宽度: 300px
2025-07-28 23:48:08,376 - INFO - 计算拖拽距离: 278px (比例: 0.93)
2025-07-28 23:48:08,376 - INFO - 开始简洁滑块操作...
2025-07-28 23:48:08,376 - INFO - 移动鼠标到滑块...
2025-07-28 23:48:08,376 - INFO - 按住滑块...
2025-07-28 23:48:08,376 - INFO - 拖拽滑块 278px...
2025-07-28 23:48:08,377 - INFO - 释放滑块...
2025-07-28 23:48:08,377 - INFO - 执行滑块操作...
2025-07-28 23:48:12,011 - INFO - 简洁滑块操作完成
2025-07-28 23:48:12,016 - INFO - 滑块验证成功，滑块已消失
2025-07-28 23:48:12,018 - INFO - 滑块验证处理成功，准备重试铺货
2025-07-28 23:48:14,019 - INFO - 开始第 2 次铺货尝试...
2025-07-28 23:48:14,019 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:48:14,019 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:48:14,027 - INFO - span innerHTML: 立即铺货
2025-07-28 23:48:14,046 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-28 23:48:14,046 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-28 23:48:14,060 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-28 23:48:15,124 - INFO - 尝试ActionChains点击...
2025-07-28 23:48:15,396 - INFO - ActionChains点击成功
2025-07-28 23:48:15,636 - INFO - 铺货按钮点击成功
2025-07-28 23:48:17,651 - INFO - 使用配置的XPath找到确认按钮
2025-07-28 23:48:17,664 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-28 23:48:19,167 - INFO - 尝试ActionChains点击...
2025-07-28 23:48:19,436 - INFO - ActionChains点击成功
2025-07-28 23:48:19,640 - INFO - 确认按钮点击成功
2025-07-28 23:48:21,641 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-28 23:48:21,649 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-28 23:48:21,649 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:48:22,657 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-28 23:48:22,657 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:48:23,665 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-28 23:48:23,665 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:48:24,673 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-28 23:48:24,674 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:48:25,682 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-07-28 23:48:25,682 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:48:26,690 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-07-28 23:48:26,691 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:48:27,699 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-07-28 23:48:27,699 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:48:28,706 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-07-28 23:48:28,706 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:48:29,716 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-07-28 23:48:29,716 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:48:30,723 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-07-28 23:48:30,723 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:48:31,724 - INFO - 等待超时，进行最后一次检查...
2025-07-28 23:48:31,732 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-07-28 23:48:31,732 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-07-28 23:48:31,732 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-07-28 23:48:31,732 - INFO - 检测是否出现滑块验证...
2025-07-28 23:48:31,737 - INFO - 在主文档中未找到滑块，检查iframe...
2025-07-28 23:48:31,742 - INFO - 找到 1 个iframe
2025-07-28 23:48:31,742 - INFO - 检查第 1 个iframe...
2025-07-28 23:48:31,758 - INFO - 在所有iframe中都未检测到滑块验证
2025-07-28 23:48:31,758 - INFO - 未检测到滑块验证或处理失败
2025-07-28 23:48:31,759 - WARNING - 第 3 个商品处理失败（不计入成功数量）
2025-07-28 23:48:31,759 - INFO - 商品处理失败，立即处理下一个商品
2025-07-28 23:48:31,759 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=694682843417
2025-07-28 23:48:35,390 - INFO - 页面加载完成
2025-07-28 23:48:35,390 - INFO - 模拟阅读行为，停顿 1.5 秒
2025-07-28 23:48:39,068 - INFO - 开始第 1 次铺货尝试...
2025-07-28 23:48:39,070 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:48:39,070 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:48:39,092 - INFO - span innerHTML: 立即铺货
2025-07-28 23:48:39,109 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-28 23:48:39,109 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-28 23:48:39,123 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-28 23:48:40,556 - INFO - 尝试ActionChains点击...
2025-07-28 23:48:40,843 - INFO - ActionChains点击成功
2025-07-28 23:48:41,239 - INFO - 铺货按钮点击成功
2025-07-28 23:48:43,252 - INFO - 使用配置的XPath找到确认按钮
2025-07-28 23:48:43,266 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-28 23:48:44,397 - INFO - 尝试ActionChains点击...
2025-07-28 23:48:44,667 - INFO - ActionChains点击成功
2025-07-28 23:48:45,094 - INFO - 确认按钮点击成功
2025-07-28 23:48:47,095 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-28 23:48:47,108 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-28 23:48:47,109 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:48:48,117 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-28 23:48:48,117 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:48:49,124 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-28 23:48:49,124 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:48:50,144 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-28 23:48:50,144 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:48:51,163 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-07-28 23:48:51,163 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:48:52,204 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-07-28 23:48:52,205 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:48:53,215 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-07-28 23:48:53,215 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:48:54,226 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-07-28 23:48:54,226 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:48:55,235 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-07-28 23:48:55,236 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:48:56,244 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-07-28 23:48:56,244 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:48:57,245 - INFO - 等待超时，进行最后一次检查...
2025-07-28 23:48:57,254 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-07-28 23:48:57,254 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-07-28 23:48:57,255 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-07-28 23:48:57,255 - INFO - 检测是否出现滑块验证...
2025-07-28 23:48:57,261 - INFO - 在主文档中未找到滑块，检查iframe...
2025-07-28 23:48:57,266 - INFO - 找到 1 个iframe
2025-07-28 23:48:57,266 - INFO - 检查第 1 个iframe...
2025-07-28 23:48:57,286 - INFO - 在第 1 个iframe中检测到滑块验证，开始处理...
2025-07-28 23:48:57,286 - INFO - 开始处理滑块验证...
2025-07-28 23:48:57,306 - INFO - 滑块容器宽度: 300px
2025-07-28 23:48:57,306 - INFO - 计算拖拽距离: 264px (比例: 0.88)
2025-07-28 23:48:57,306 - INFO - 开始简洁滑块操作...
2025-07-28 23:48:57,307 - INFO - 移动鼠标到滑块...
2025-07-28 23:48:57,307 - INFO - 按住滑块...
2025-07-28 23:48:57,307 - INFO - 拖拽滑块 264px...
2025-07-28 23:48:57,307 - INFO - 释放滑块...
2025-07-28 23:48:57,307 - INFO - 执行滑块操作...
2025-07-28 23:49:00,979 - INFO - 简洁滑块操作完成
2025-07-28 23:49:00,983 - INFO - 滑块验证成功，滑块已消失
2025-07-28 23:49:00,984 - INFO - 滑块验证处理成功，准备重试铺货
2025-07-28 23:49:02,985 - INFO - 开始第 2 次铺货尝试...
2025-07-28 23:49:02,985 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:49:02,985 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:49:02,994 - INFO - span innerHTML: 立即铺货
2025-07-28 23:49:03,013 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-28 23:49:03,013 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-28 23:49:03,026 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-28 23:49:04,444 - INFO - 尝试ActionChains点击...
2025-07-28 23:49:04,705 - INFO - ActionChains点击成功
2025-07-28 23:49:04,979 - INFO - 铺货按钮点击成功
2025-07-28 23:49:06,994 - INFO - 使用配置的XPath找到确认按钮
2025-07-28 23:49:07,006 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-28 23:49:08,233 - INFO - 尝试ActionChains点击...
2025-07-28 23:49:08,495 - INFO - ActionChains点击成功
2025-07-28 23:49:08,833 - INFO - 确认按钮点击成功
2025-07-28 23:49:10,834 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-28 23:49:10,842 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-28 23:49:10,842 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:49:11,850 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-28 23:49:11,850 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:49:12,857 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-28 23:49:12,858 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:49:13,865 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-28 23:49:13,865 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:49:14,873 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-07-28 23:49:14,873 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:49:15,881 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-07-28 23:49:15,881 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:49:16,890 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-07-28 23:49:16,891 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:49:17,898 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-07-28 23:49:17,898 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:49:18,923 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-07-28 23:49:18,923 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:49:19,931 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-07-28 23:49:19,931 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:49:20,932 - INFO - 等待超时，进行最后一次检查...
2025-07-28 23:49:20,939 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-07-28 23:49:20,939 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-07-28 23:49:20,939 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-07-28 23:49:20,939 - INFO - 检测是否出现滑块验证...
2025-07-28 23:49:20,953 - INFO - 在主文档中未找到滑块，检查iframe...
2025-07-28 23:49:20,961 - INFO - 找到 1 个iframe
2025-07-28 23:49:20,961 - INFO - 检查第 1 个iframe...
2025-07-28 23:49:20,983 - INFO - 在所有iframe中都未检测到滑块验证
2025-07-28 23:49:20,983 - INFO - 未检测到滑块验证或处理失败
2025-07-28 23:49:20,984 - WARNING - 第 4 个商品处理失败（不计入成功数量）
2025-07-28 23:49:20,984 - INFO - 商品处理失败，立即处理下一个商品
2025-07-28 23:49:20,984 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=681082809892
2025-07-28 23:49:24,663 - INFO - 页面加载完成
2025-07-28 23:49:24,664 - INFO - 模拟阅读行为，停顿 2.6 秒
2025-07-28 23:49:28,066 - INFO - 开始第 1 次铺货尝试...
2025-07-28 23:49:28,066 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:49:28,066 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:49:28,072 - INFO - span innerHTML: 立即铺货
2025-07-28 23:49:28,111 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-28 23:49:28,111 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-28 23:49:28,124 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-28 23:49:29,467 - INFO - 尝试ActionChains点击...
2025-07-28 23:49:29,760 - INFO - ActionChains点击成功
2025-07-28 23:49:29,994 - INFO - 铺货按钮点击成功
2025-07-28 23:49:32,005 - INFO - 使用配置的XPath找到确认按钮
2025-07-28 23:49:32,017 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-28 23:49:33,071 - INFO - 尝试ActionChains点击...
2025-07-28 23:49:33,341 - INFO - ActionChains点击成功
2025-07-28 23:49:33,736 - INFO - 确认按钮点击成功
2025-07-28 23:49:35,737 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-28 23:49:35,745 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-28 23:49:35,746 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:49:36,754 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-28 23:49:36,755 - INFO - span仍为'立即铺货'，继续等待...
