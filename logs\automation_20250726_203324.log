2025-07-26 20:33:27,560 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9cda]
	(No symbol) [0x0x7ff7243d1679]
	(No symbol) [0x0x7ff7243d4a61]
	(No symbol) [0x0x7ff724471e4b]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:33:27,560 - INFO - 防检测机制设置完成
2025-07-26 20:33:27,561 - INFO - 浏览器驱动初始化成功
2025-07-26 20:33:27,567 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9b0c]
	(No symbol) [0x0x7ff724485b46]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:33:27,571 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9b0c]
	(No symbol) [0x0x7ff724485b46]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:33:27,574 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9b0c]
	(No symbol) [0x0x7ff724485b46]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:33:27,579 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9b0c]
	(No symbol) [0x0x7ff724485b46]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:33:27,586 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9b0c]
	(No symbol) [0x0x7ff724485b46]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:33:27,592 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9b0c]
	(No symbol) [0x0x7ff724485b46]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:33:27,596 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9b0c]
	(No symbol) [0x0x7ff724485b46]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:33:27,600 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9b0c]
	(No symbol) [0x0x7ff724485b46]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:33:27,607 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9b0c]
	(No symbol) [0x0x7ff724485b46]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:33:27,611 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9b0c]
	(No symbol) [0x0x7ff724485b46]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:33:27,617 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9b0c]
	(No symbol) [0x0x7ff724485b46]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:33:27,622 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9b0c]
	(No symbol) [0x0x7ff724485b46]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:33:27,626 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9b0c]
	(No symbol) [0x0x7ff724485b46]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:33:27,631 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9b0c]
	(No symbol) [0x0x7ff724485b46]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:33:27,636 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9b0c]
	(No symbol) [0x0x7ff724485b46]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:33:27,639 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9b0c]
	(No symbol) [0x0x7ff724485b46]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:33:27,644 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9b0c]
	(No symbol) [0x0x7ff724485b46]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:33:27,647 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9b0c]
	(No symbol) [0x0x7ff724485b46]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:33:27,650 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9b0c]
	(No symbol) [0x0x7ff724485b46]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:33:27,654 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9b0c]
	(No symbol) [0x0x7ff724485b46]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:33:27,654 - INFO - Cookies已加载
2025-07-26 20:33:27,655 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?channel=global_zx&spm=a21ug5.29159167.6452766900.2ndview_zxmore_click
2025-07-26 20:33:32,770 - INFO - 当前User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-26 20:33:32,770 - INFO - 确认：使用桌面版User-Agent
2025-07-26 20:33:32,773 - INFO - 桌面版User-Agent设置完成: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-26 20:33:35,172 - INFO - 成功导航到目标页面
2025-07-26 20:33:35,173 - INFO - 登录成功！现在可以开始铺货任务
2025-07-26 20:33:43,268 - INFO - 开始测试仓库移除功能...
2025-07-26 20:33:43,268 - INFO - 开始执行批量管理操作...
2025-07-26 20:33:43,269 - INFO - 正在导航到管理页面: https://myseller.taobao.com/home.htm/SellManage/all?current=1&pageSize=20
2025-07-26 20:33:44,367 - INFO - 页面加载完成
2025-07-26 20:33:46,066 - INFO - 准备点击元素: tag=span, text='', class='next-checkbox', id=''
2025-07-26 20:33:47,531 - INFO - 尝试ActionChains点击...
2025-07-26 20:33:47,885 - INFO - ActionChains点击成功
2025-07-26 20:33:48,344 - INFO - 已点击全选复选框
2025-07-26 20:33:48,344 - INFO - 等待 1.2 秒
2025-07-26 20:33:49,535 - INFO - 准备点击元素: tag=button, text='批量删除', class='next-btn next-medium next-btn-', id=''
2025-07-26 20:33:50,510 - INFO - 尝试ActionChains点击...
2025-07-26 20:33:50,515 - WARNING - ActionChains点击失败: Message: stale element reference: stale element not found in the current frame
  (Session info: chrome=138.0.7204.158); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9cda]
	(No symbol) [0x0x7ff7243d1679]
	(No symbol) [0x0x7ff7243d471c]
	(No symbol) [0x0x7ff7243d47ef]
	(No symbol) [0x0x7ff724427a64]
	(No symbol) [0x0x7ff724426c9b]
	(No symbol) [0x0x7ff72447c23d]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:33:50,515 - INFO - 尝试直接点击...
2025-07-26 20:33:50,519 - WARNING - 直接点击失败: Message: stale element reference: stale element not found in the current frame
  (Session info: chrome=138.0.7204.158); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9cda]
	(No symbol) [0x0x7ff7243d1679]
	(No symbol) [0x0x7ff7243d471c]
	(No symbol) [0x0x7ff7243d47ef]
	(No symbol) [0x0x7ff72442289d]
	(No symbol) [0x0x7ff724413633]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724412f76]
	(No symbol) [0x0x7ff724448ae0]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:33:50,519 - INFO - 尝试JavaScript点击...
2025-07-26 20:33:50,525 - WARNING - JavaScript点击失败: Message: stale element reference: stale element not found in the current frame
  (Session info: chrome=138.0.7204.158); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9cda]
	(No symbol) [0x0x7ff7243d1679]
	(No symbol) [0x0x7ff7243d4a61]
	(No symbol) [0x0x7ff724471e4b]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:33:50,525 - INFO - 尝试坐标点击...
2025-07-26 20:33:50,530 - WARNING - 坐标点击失败: Message: stale element reference: stale element not found in the current frame
  (Session info: chrome=138.0.7204.158); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9cda]
	(No symbol) [0x0x7ff7243d1679]
	(No symbol) [0x0x7ff7243d471c]
	(No symbol) [0x0x7ff7243d47ef]
	(No symbol) [0x0x7ff72441b3c3]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724412f76]
	(No symbol) [0x0x7ff724448ae0]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:33:50,536 - ERROR - 所有点击方式都失败
2025-07-26 20:33:50,537 - INFO - 操作失败，2秒后重试 (尝试 1/2)
2025-07-26 20:33:52,568 - INFO - 准备点击元素: tag=button, text='批量上架', class='next-btn next-medium next-btn-', id=''
2025-07-26 20:33:54,018 - INFO - 尝试ActionChains点击...
2025-07-26 20:33:54,293 - INFO - ActionChains点击成功
2025-07-26 20:33:54,620 - INFO - 已点击批量编辑按钮
2025-07-26 20:33:54,620 - INFO - 等待 2.6 秒
2025-07-26 20:34:07,396 - WARNING - 等待元素可点击超时: //*[@id="qn-worbench-container"]/div[2]/div[2]/div/div[2]/button
2025-07-26 20:34:07,396 - INFO - 操作失败，2秒后重试 (尝试 1/2)
2025-07-26 20:34:09,872 - INFO - Cookies已保存
2025-07-26 20:34:10,173 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-07-26 20:34:12,184 - INFO - 浏览器已关闭
