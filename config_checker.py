#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置检查和修复工具
用于验证和修复配置文件
"""

import json
import os
import sys

def check_config():
    """检查配置文件"""
    print("正在检查配置文件...")
    
    config_file = "config.json"
    
    # 检查配置文件是否存在
    if not os.path.exists(config_file):
        print("❌ 配置文件不存在，正在创建默认配置...")
        create_default_config()
        print("✅ 默认配置文件已创建")
        return True
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 检查必要的配置项
        required_keys = [
            "min_interval", "max_interval", "max_products",
            "batch_size_min", "batch_size_max", "target_url",
            "manage_url", "xpaths"
        ]
        
        missing_keys = []
        for key in required_keys:
            if key not in config:
                missing_keys.append(key)
        
        if missing_keys:
            print(f"❌ 配置文件缺少必要项: {', '.join(missing_keys)}")
            print("正在修复配置文件...")
            fix_config(config)
            print("✅ 配置文件已修复")
        else:
            print("✅ 配置文件检查通过")
        
        # 检查XPath配置
        if "xpaths" in config:
            xpath_keys = [
                "product_list_container", "add_product_button", "confirm_button",
                "select_all_checkbox", "batch_edit_button", "final_confirm_button"
            ]
            
            missing_xpaths = []
            for key in xpath_keys:
                if key not in config["xpaths"]:
                    missing_xpaths.append(key)
            
            if missing_xpaths:
                print(f"⚠️  XPath配置缺少项: {', '.join(missing_xpaths)}")
                print("请根据实际页面结构更新这些XPath")
            else:
                print("✅ XPath配置完整")
        
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ 配置文件格式错误: {e}")
        print("正在重新创建配置文件...")
        create_default_config()
        print("✅ 配置文件已重新创建")
        return True
    
    except Exception as e:
        print(f"❌ 检查配置文件时出错: {e}")
        return False

def create_default_config():
    """创建默认配置文件"""
    default_config = {
        "min_interval": 3,
        "max_interval": 8,
        "max_products": 50,
        "batch_size_min": 10,
        "batch_size_max": 15,
        "chrome_driver_path": "./chromedriver.exe",
        "user_data_dir": "./chrome_user_data",
        "target_url": "https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?channel=global_zx&spm=a21ug5.29159167.6452766900.2ndview_zxmore_click",
        "manage_url": "https://myseller.taobao.com/home.htm/SellManage/all?current=1&pageSize=20",
        "xpaths": {
            "product_list_container": "//*[@id=\"root\"]/div[2]/div/div/div/div[4]/div[1]",
            "add_product_button": "//*[@id=\"root\"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button",
            "confirm_button": "/html/body/div[8]/div[2]/div[2]/button",
            "select_all_checkbox": "//*[@id=\"sell-manage-wrap\"]/div[4]/div/div[6]/div[1]/table/thead/tr/th[1]/div/label/span",
            "batch_edit_button": "//*[@id=\"sell-manage-wrap\"]/div[4]/div/div[3]/div[1]/div[3]/button",
            "final_confirm_button": "//*[@id=\"qn-worbench-container\"]/div[2]/div[2]/div/div[2]/button"
        }
    }
    
    with open("config.json", 'w', encoding='utf-8') as f:
        json.dump(default_config, f, indent=4, ensure_ascii=False)

def fix_config(config):
    """修复配置文件"""
    # 添加缺少的基本配置
    defaults = {
        "min_interval": 3,
        "max_interval": 8,
        "max_products": 50,
        "batch_size_min": 10,
        "batch_size_max": 15,
        "chrome_driver_path": "./chromedriver.exe",
        "user_data_dir": "./chrome_user_data",
        "target_url": "https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?channel=global_zx&spm=a21ug5.29159167.6452766900.2ndview_zxmore_click",
        "manage_url": "https://myseller.taobao.com/home.htm/SellManage/all?current=1&pageSize=20"
    }
    
    for key, value in defaults.items():
        if key not in config:
            config[key] = value
    
    # 添加缺少的XPath配置
    if "xpaths" not in config:
        config["xpaths"] = {}
    
    xpath_defaults = {
        "product_list_container": "//*[@id=\"root\"]/div[2]/div/div/div/div[4]/div[1]",
        "add_product_button": "//*[@id=\"root\"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button",
        "confirm_button": "/html/body/div[8]/div[2]/div[2]/button",
        "select_all_checkbox": "//*[@id=\"sell-manage-wrap\"]/div[4]/div/div[6]/div[1]/table/thead/tr/th[1]/div/label/span",
        "batch_edit_button": "//*[@id=\"sell-manage-wrap\"]/div[4]/div/div[3]/div[1]/div[3]/button",
        "final_confirm_button": "//*[@id=\"qn-worbench-container\"]/div[2]/div[2]/div/div[2]/button"
    }
    
    for key, value in xpath_defaults.items():
        if key not in config["xpaths"]:
            config["xpaths"][key] = value
    
    # 保存修复后的配置
    with open("config.json", 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=4, ensure_ascii=False)

def check_dependencies():
    """检查依赖包"""
    print("正在检查依赖包...")
    
    required_packages = [
        "selenium", "webdriver-manager", "requests", 
        "beautifulsoup4", "lxml", "Pillow"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装依赖包:")
        print("pip install -r requirements.txt")
        return False
    else:
        print("✅ 依赖包检查通过")
        return True

def check_files():
    """检查必要文件"""
    print("正在检查必要文件...")
    
    required_files = [
        "main.py", "automation_core.py", "requirements.txt"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        return False
    else:
        print("✅ 必要文件检查通过")
        return True

def main():
    """主函数"""
    print("=" * 50)
    print("淘宝铺货自动化工具 - 配置检查器")
    print("=" * 50)
    print()
    
    # 检查必要文件
    if not check_files():
        print("\n❌ 文件检查失败，请确保所有必要文件都存在")
        input("按回车键退出...")
        return
    
    # 检查依赖包
    if not check_dependencies():
        print("\n❌ 依赖包检查失败")
        input("按回车键退出...")
        return
    
    # 检查配置文件
    if not check_config():
        print("\n❌ 配置检查失败")
        input("按回车键退出...")
        return
    
    print("\n✅ 所有检查都通过，程序可以正常运行")
    print("\n使用说明:")
    print("1. 运行 python main.py 启动程序")
    print("2. 或者双击 start.bat 启动程序")
    print("3. 首次使用请先点击'登录淘宝'完成登录")
    print("4. 然后点击'开始铺货'开始自动化任务")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
