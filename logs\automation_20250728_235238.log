2025-07-28 23:52:41,186 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059cda]
	(No symbol) [0x0x7ff69a061679]
	(No symbol) [0x0x7ff69a064a61]
	(No symbol) [0x0x7ff69a101e4b]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:52:41,186 - INFO - 防检测机制设置完成
2025-07-28 23:52:41,186 - INFO - 浏览器驱动初始化成功
2025-07-28 23:52:41,190 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:52:41,194 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:52:41,197 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:52:41,200 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:52:41,203 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:52:41,210 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:52:41,215 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:52:41,218 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:52:41,223 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:52:41,225 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:52:41,228 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:52:41,230 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:52:41,234 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:52:41,238 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:52:41,241 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:52:41,244 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:52:41,247 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:52:41,251 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:52:41,254 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:52:41,256 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:52:41,257 - INFO - Cookies已加载
2025-07-28 23:52:41,257 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?channel=global_zx&spm=a21ug5.29159167.6452766900.2ndview_zxmore_click
2025-07-28 23:52:41,257 - INFO - 添加额外延迟: 1.4秒
2025-07-28 23:52:46,490 - INFO - 当前User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-28 23:52:46,490 - INFO - 确认：使用桌面版User-Agent
2025-07-28 23:52:46,493 - INFO - 桌面版User-Agent设置完成: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-28 23:52:47,413 - INFO - 成功导航到目标页面
2025-07-28 23:52:47,413 - INFO - 登录成功！现在可以开始铺货任务
2025-07-28 23:52:49,130 - INFO - Cookies已保存
2025-07-28 23:52:51,391 - INFO - 浏览器已关闭
2025-07-28 23:52:52,786 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059cda]
	(No symbol) [0x0x7ff69a061679]
	(No symbol) [0x0x7ff69a064a61]
	(No symbol) [0x0x7ff69a101e4b]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:52:52,786 - INFO - 防检测机制设置完成
2025-07-28 23:52:52,786 - INFO - 浏览器驱动初始化成功
2025-07-28 23:52:52,787 - INFO - 开始自动化铺货流程（有界面模式）
2025-07-28 23:52:52,787 - INFO - 第一步：正在导航到目标页面...
2025-07-28 23:52:56,480 - INFO - 第一步：点击登录按钮
2025-07-28 23:52:56,499 - INFO - 找到登录按钮，使用选择器: //*[@id='login-form']/div[6]/button
2025-07-28 23:52:57,521 - INFO - 准备点击登录按钮: 标签=button, 文本='登录', 选择器=//*[@id='login-form']/div[6]/button
2025-07-28 23:52:57,536 - INFO - 准备点击元素: tag=button, text='登录', class='fm-button fm-submit password-l', id=''
2025-07-28 23:52:58,725 - INFO - 尝试ActionChains点击...
2025-07-28 23:52:59,198 - INFO - ActionChains点击成功
2025-07-28 23:52:59,471 - INFO - 登录按钮点击成功
2025-07-28 23:52:59,471 - INFO - 登录按钮点击成功
2025-07-28 23:53:02,472 - INFO - 第二步：获取账号信息...
2025-07-28 23:53:02,472 - INFO - 正在跳转到个人中心页面: https://jingya.taobao.com/?v=2
2025-07-28 23:53:27,419 - INFO - 尝试获取账号名称，使用XPath: //*[@id='workbench-user-tool-btn']/div/div[2]
2025-07-28 23:53:27,445 - INFO - 成功获取账号信息: 卓祥22店:冰淇淋
2025-07-28 23:53:27,445 - INFO - 获取到账号名称: 卓祥22店:冰淇淋
2025-07-28 23:53:27,445 - INFO - GUI界面已更新账号显示: 卓祥22店:冰淇淋
2025-07-28 23:53:27,445 - INFO - 第三步：开始读取20个商品信息...
2025-07-28 23:53:27,447 - INFO - 开始爬取商品信息，最大数量: 20
2025-07-28 23:53:30,532 - INFO - 正在爬取第 1 页商品信息...
2025-07-28 23:53:32,540 - INFO - 找到商品容器，开始爬取商品，最大数量: 20
2025-07-28 23:53:32,550 - INFO - 找到商品项 1，检查元素信息...
2025-07-28 23:53:32,561 - INFO - 商品项 1 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:53:32,564 - INFO - 商品项 1 直接获取到data-autolog: item_id=681778175573&path=search_goods_card&type=search
2025-07-28 23:53:32,564 - INFO - 商品项 1 data-autolog: item_id=681778175573&path=search_goods_card&type=search
2025-07-28 23:53:32,564 - INFO - 商品项 1 提取到item_id: 681778175573
2025-07-28 23:53:32,576 - INFO - 商品项 1 商品名称: 保税直供 Dove多芬磨砂膏石榴籽乳木果风味身体磨砂膏298g
2025-07-28 23:53:32,608 - INFO - 找到商品项 2，检查元素信息...
2025-07-28 23:53:32,616 - INFO - 商品项 2 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:53:32,619 - INFO - 商品项 2 直接获取到data-autolog: item_id=690841537159&path=search_goods_card&index=1&type=search
2025-07-28 23:53:32,619 - INFO - 商品项 2 data-autolog: item_id=690841537159&path=search_goods_card&index=1&type=search
2025-07-28 23:53:32,619 - INFO - 商品项 2 提取到item_id: 690841537159
2025-07-28 23:53:32,627 - INFO - 商品项 2 商品名称: 保税直供 凡士林Vaseline烟酰胺身体乳400ml 725ml无压泵款200ml
2025-07-28 23:53:32,639 - INFO - 找到商品项 3，检查元素信息...
2025-07-28 23:53:32,648 - INFO - 商品项 3 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:53:32,652 - INFO - 商品项 3 直接获取到data-autolog: item_id=789447097664&path=search_goods_card&index=2&type=search
2025-07-28 23:53:32,653 - INFO - 商品项 3 data-autolog: item_id=789447097664&path=search_goods_card&index=2&type=search
2025-07-28 23:53:32,653 - INFO - 商品项 3 提取到item_id: 789447097664
2025-07-28 23:53:32,662 - INFO - 商品项 3 商品名称: 【甄选】珂润Curel泡沫洗面奶150ml
2025-07-28 23:53:32,672 - INFO - 找到商品项 4，检查元素信息...
2025-07-28 23:53:32,680 - INFO - 商品项 4 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:53:32,682 - INFO - 商品项 4 直接获取到data-autolog: item_id=694682843417&path=search_goods_card&index=3&type=search
2025-07-28 23:53:32,683 - INFO - 商品项 4 data-autolog: item_id=694682843417&path=search_goods_card&index=3&type=search
2025-07-28 23:53:32,683 - INFO - 商品项 4 提取到item_id: 694682843417
2025-07-28 23:53:32,692 - INFO - 商品项 4 商品名称: 【可开授权】日台混发Fino发膜美容液护发素 发膜230g
2025-07-28 23:53:32,703 - INFO - 找到商品项 5，检查元素信息...
2025-07-28 23:53:32,710 - INFO - 商品项 5 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:53:32,714 - INFO - 商品项 5 直接获取到data-autolog: item_id=681082809892&path=search_goods_card&index=4&type=search
2025-07-28 23:53:32,714 - INFO - 商品项 5 data-autolog: item_id=681082809892&path=search_goods_card&index=4&type=search
2025-07-28 23:53:32,714 - INFO - 商品项 5 提取到item_id: 681082809892
2025-07-28 23:53:32,722 - INFO - 商品项 5 商品名称: 保税直供 Dove多芬大白碗润肤身体乳300ml 【1//2//3罐】
2025-07-28 23:53:32,734 - INFO - 找到商品项 6，检查元素信息...
2025-07-28 23:53:32,741 - INFO - 商品项 6 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:53:32,744 - INFO - 商品项 6 直接获取到data-autolog: item_id=694675776881&path=search_goods_card&index=5&type=search
2025-07-28 23:53:32,745 - INFO - 商品项 6 data-autolog: item_id=694675776881&path=search_goods_card&index=5&type=search
2025-07-28 23:53:32,745 - INFO - 商品项 6 提取到item_id: 694675776881
2025-07-28 23:53:32,834 - INFO - 商品项 6 商品名称: 保税直供 Pantene潘婷护发素三分钟奇迹多效修护发膜级护发素
2025-07-28 23:53:32,854 - INFO - 找到商品项 7，检查元素信息...
2025-07-28 23:53:32,864 - INFO - 商品项 7 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:53:32,867 - INFO - 商品项 7 直接获取到data-autolog: item_id=680729304287&path=search_goods_card&index=6&type=search
2025-07-28 23:53:32,867 - INFO - 商品项 7 data-autolog: item_id=680729304287&path=search_goods_card&index=6&type=search
2025-07-28 23:53:32,868 - INFO - 商品项 7 提取到item_id: 680729304287
2025-07-28 23:53:32,878 - INFO - 商品项 7 商品名称: 保税直供 多芬dove空气感无硅油 洗发水480g 护发素480g护发临期
2025-07-28 23:53:32,890 - INFO - 找到商品项 8，检查元素信息...
2025-07-28 23:53:32,898 - INFO - 商品项 8 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:53:32,904 - INFO - 商品项 8 直接获取到data-autolog: item_id=710355107054&path=search_goods_card&index=7&type=search
2025-07-28 23:53:32,905 - INFO - 商品项 8 data-autolog: item_id=710355107054&path=search_goods_card&index=7&type=search
2025-07-28 23:53:32,905 - INFO - 商品项 8 提取到item_id: 710355107054
2025-07-28 23:53:32,916 - INFO - 商品项 8 商品名称: 保税直供 Dove多芬洗发水护发素空气感洗发水480g 【护发素临期
2025-07-28 23:53:32,926 - INFO - 找到商品项 9，检查元素信息...
2025-07-28 23:53:32,935 - INFO - 商品项 9 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:53:32,937 - INFO - 商品项 9 直接获取到data-autolog: item_id=740463658412&path=search_goods_card&index=8&type=search
2025-07-28 23:53:32,937 - INFO - 商品项 9 data-autolog: item_id=740463658412&path=search_goods_card&index=8&type=search
2025-07-28 23:53:32,937 - INFO - 商品项 9 提取到item_id: 740463658412
2025-07-28 23:53:32,947 - INFO - 商品项 9 商品名称: 【主推链接】花王cape空气感定型喷雾50g/180g 编码：3305300000
2025-07-28 23:53:32,958 - INFO - 找到商品项 10，检查元素信息...
2025-07-28 23:53:32,964 - INFO - 商品项 10 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:53:32,967 - INFO - 商品项 10 直接获取到data-autolog: item_id=821067902127&path=search_goods_card&index=9&type=search
2025-07-28 23:53:32,967 - INFO - 商品项 10 data-autolog: item_id=821067902127&path=search_goods_card&index=9&type=search
2025-07-28 23:53:32,968 - INFO - 商品项 10 提取到item_id: 821067902127
2025-07-28 23:53:32,978 - INFO - 商品项 10 商品名称: 丹碧丝TAMPAX卫生棉条长导管式内置式纯棉月经棉条棉棒游泳卫生巾
2025-07-28 23:53:32,989 - INFO - 找到商品项 11，检查元素信息...
2025-07-28 23:53:32,997 - INFO - 商品项 11 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:53:33,001 - INFO - 商品项 11 直接获取到data-autolog: item_id=789835300215&path=search_goods_card&index=10&type=search
2025-07-28 23:53:33,001 - INFO - 商品项 11 data-autolog: item_id=789835300215&path=search_goods_card&index=10&type=search
2025-07-28 23:53:33,001 - INFO - 商品项 11 提取到item_id: 789835300215
2025-07-28 23:53:33,021 - INFO - 商品项 11 商品名称: 【保税】雅漾喷雾爽肤水300ml
2025-07-28 23:53:33,032 - INFO - 找到商品项 12，检查元素信息...
2025-07-28 23:53:33,043 - INFO - 商品项 12 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:53:33,046 - INFO - 商品项 12 直接获取到data-autolog: item_id=843747605382&path=search_goods_card&index=11&type=search
2025-07-28 23:53:33,046 - INFO - 商品项 12 data-autolog: item_id=843747605382&path=search_goods_card&index=11&type=search
2025-07-28 23:53:33,046 - INFO - 商品项 12 提取到item_id: 843747605382
2025-07-28 23:53:33,056 - INFO - 商品项 12 商品名称: 倩碧紫胖子卸妆膏125ml/瓶
2025-07-28 23:53:33,068 - INFO - 找到商品项 13，检查元素信息...
2025-07-28 23:53:33,077 - INFO - 商品项 13 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:53:33,081 - INFO - 商品项 13 直接获取到data-autolog: item_id=675816539149&path=search_goods_card&index=12&type=search
2025-07-28 23:53:33,081 - INFO - 商品项 13 data-autolog: item_id=675816539149&path=search_goods_card&index=12&type=search
2025-07-28 23:53:33,081 - INFO - 商品项 13 提取到item_id: 675816539149
2025-07-28 23:53:33,093 - INFO - 商品项 13 商品名称: 新加坡Vicopyl维可保益生菌(Pylopass/拒绝幽门菌/抑口臭/调肠胃)
2025-07-28 23:53:33,106 - INFO - 找到商品项 14，检查元素信息...
2025-07-28 23:53:33,115 - INFO - 商品项 14 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:53:33,118 - INFO - 商品项 14 直接获取到data-autolog: item_id=846634805775&path=search_goods_card&index=13&type=search
2025-07-28 23:53:33,118 - INFO - 商品项 14 data-autolog: item_id=846634805775&path=search_goods_card&index=13&type=search
2025-07-28 23:53:33,118 - INFO - 商品项 14 提取到item_id: 846634805775
2025-07-28 23:53:33,149 - INFO - 商品项 14 商品名称: 【甄选】cow牛乳石碱原味/玫瑰花香 瓶装480ml
2025-07-28 23:53:33,160 - INFO - 找到商品项 15，检查元素信息...
2025-07-28 23:53:33,169 - INFO - 商品项 15 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:53:33,172 - INFO - 商品项 15 直接获取到data-autolog: item_id=852510575114&path=search_goods_card&index=14&type=search
2025-07-28 23:53:33,173 - INFO - 商品项 15 data-autolog: item_id=852510575114&path=search_goods_card&index=14&type=search
2025-07-28 23:53:33,173 - INFO - 商品项 15 提取到item_id: 852510575114
2025-07-28 23:53:33,182 - INFO - 商品项 15 商品名称: Nars 纳斯 红壳蜜粉饼16g（含粉扑）、Nars纳斯裸光蜜粉饼10g
2025-07-28 23:53:33,195 - INFO - 找到商品项 16，检查元素信息...
2025-07-28 23:53:33,204 - INFO - 商品项 16 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:53:33,207 - INFO - 商品项 16 直接获取到data-autolog: item_id=743442966826&path=search_goods_card&index=15&type=search
2025-07-28 23:53:33,207 - INFO - 商品项 16 data-autolog: item_id=743442966826&path=search_goods_card&index=15&type=search
2025-07-28 23:53:33,208 - INFO - 商品项 16 提取到item_id: 743442966826
2025-07-28 23:53:33,228 - INFO - 商品项 16 商品名称: ESI卡路里拜拜片白芸豆阻断片大餐救星膳食纤维身材热控管理
2025-07-28 23:53:33,240 - INFO - 找到商品项 17，检查元素信息...
2025-07-28 23:53:33,248 - INFO - 商品项 17 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:53:33,251 - INFO - 商品项 17 直接获取到data-autolog: item_id=848934676698&path=search_goods_card&index=16&type=search
2025-07-28 23:53:33,251 - INFO - 商品项 17 data-autolog: item_id=848934676698&path=search_goods_card&index=16&type=search
2025-07-28 23:53:33,251 - INFO - 商品项 17 提取到item_id: 848934676698
2025-07-28 23:53:33,260 - INFO - 商品项 17 商品名称: Estee Lauder 雅诗兰黛持妆粉底液30ml 2C0 /1w1/1C1
2025-07-28 23:53:33,271 - INFO - 找到商品项 18，检查元素信息...
2025-07-28 23:53:33,278 - INFO - 商品项 18 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:53:33,282 - INFO - 商品项 18 直接获取到data-autolog: item_id=693758717931&path=search_goods_card&index=17&type=search
2025-07-28 23:53:33,283 - INFO - 商品项 18 data-autolog: item_id=693758717931&path=search_goods_card&index=17&type=search
2025-07-28 23:53:33,283 - INFO - 商品项 18 提取到item_id: 693758717931
2025-07-28 23:53:33,292 - INFO - 商品项 18 商品名称: 保税直供 资生堂fino高效渗透发膜 230g （1/2/3罐）日版
2025-07-28 23:53:33,303 - INFO - 找到商品项 19，检查元素信息...
2025-07-28 23:53:33,311 - INFO - 商品项 19 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:53:33,313 - INFO - 商品项 19 直接获取到data-autolog: item_id=691142128229&path=search_goods_card&index=18&type=search
2025-07-28 23:53:33,314 - INFO - 商品项 19 data-autolog: item_id=691142128229&path=search_goods_card&index=18&type=search
2025-07-28 23:53:33,314 - INFO - 商品项 19 提取到item_id: 691142128229
2025-07-28 23:53:33,323 - INFO - 商品项 19 商品名称: 保税 日版Dove多芬洁面洗面奶新版 正装瓶150ml 补充袋125ml 甄选
2025-07-28 23:53:33,336 - INFO - 找到商品项 20，检查元素信息...
2025-07-28 23:53:33,344 - INFO - 商品项 20 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:53:33,347 - INFO - 商品项 20 直接获取到data-autolog: item_id=690739721578&path=search_goods_card&index=19&type=search
2025-07-28 23:53:33,347 - INFO - 商品项 20 data-autolog: item_id=690739721578&path=search_goods_card&index=19&type=search
2025-07-28 23:53:33,347 - INFO - 商品项 20 提取到item_id: 690739721578
2025-07-28 23:53:33,356 - INFO - 商品项 20 商品名称: 保税直供 SUNSILK夏士莲椰子洗发水//护发素450ml 泰产
2025-07-28 23:53:33,357 - INFO - 已爬取到 20 个商品，停止当前页面爬取
2025-07-28 23:53:33,357 - INFO - 当前页面爬取完成，找到 20 个商品
2025-07-28 23:53:33,357 - INFO - 第 1 页找到 20 个商品，总计: 20
2025-07-28 23:53:33,357 - INFO - 已达到最大商品数量 20，停止爬取
2025-07-28 23:53:33,357 - INFO - 爬取完成，共找到 20 个商品
2025-07-28 23:53:33,357 - INFO - 成功读取到 20 个商品（目标20个），开始铺货任务
2025-07-28 23:53:33,357 - INFO - 第四步：开始处理商品...
2025-07-28 23:53:33,368 - INFO - 已读取 20 个商品，目标成功铺货 3 个商品
2025-07-28 23:53:33,368 - INFO - 当前批次大小: 11
2025-07-28 23:53:33,368 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=681778175573
2025-07-28 23:53:36,982 - INFO - 页面加载完成
2025-07-28 23:53:36,983 - INFO - 模拟阅读行为，停顿 2.5 秒
2025-07-28 23:53:41,537 - INFO - 开始第 1 次铺货尝试...
2025-07-28 23:53:41,538 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:53:41,538 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:53:41,548 - INFO - span innerHTML: 立即铺货
2025-07-28 23:53:41,569 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-28 23:53:41,569 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-28 23:53:41,584 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-28 23:53:43,033 - INFO - 尝试ActionChains点击...
2025-07-28 23:53:43,328 - INFO - ActionChains点击成功
2025-07-28 23:53:43,786 - INFO - 铺货按钮点击成功
2025-07-28 23:53:45,800 - INFO - 使用配置的XPath找到确认按钮
2025-07-28 23:53:45,812 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-28 23:53:47,049 - INFO - 尝试ActionChains点击...
2025-07-28 23:53:47,320 - INFO - ActionChains点击成功
2025-07-28 23:53:47,763 - INFO - 确认按钮点击成功
2025-07-28 23:53:49,764 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-28 23:53:49,772 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-28 23:53:49,772 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:53:50,779 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-28 23:53:50,779 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:53:51,788 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-28 23:53:51,788 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:53:52,797 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-28 23:53:52,797 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:53:53,805 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-07-28 23:53:53,805 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:53:54,813 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-07-28 23:53:54,814 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:53:55,822 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-07-28 23:53:55,822 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:53:56,830 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-07-28 23:53:56,830 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:53:57,839 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-07-28 23:53:57,839 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:53:58,847 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-07-28 23:53:58,847 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:53:59,848 - INFO - 等待超时，进行最后一次检查...
2025-07-28 23:53:59,860 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-07-28 23:53:59,860 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-07-28 23:53:59,860 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-07-28 23:53:59,860 - INFO - 检测是否出现滑块验证...
2025-07-28 23:53:59,869 - INFO - 在主文档中未找到滑块，检查iframe...
2025-07-28 23:53:59,875 - INFO - 找到 1 个iframe
2025-07-28 23:53:59,875 - INFO - 检查第 1 个iframe...
2025-07-28 23:53:59,896 - INFO - 在第 1 个iframe中检测到滑块验证，开始处理...
2025-07-28 23:53:59,897 - INFO - 开始处理滑块验证...
2025-07-28 23:53:59,910 - INFO - 滑块容器宽度: 300px
2025-07-28 23:53:59,910 - INFO - 计算拖拽距离: 267px (比例: 0.89)
2025-07-28 23:53:59,910 - INFO - 开始慢速匀速滑块操作...
2025-07-28 23:53:59,910 - INFO - 构建慢速滑块操作链，拖拽距离: 267px
2025-07-28 23:53:59,911 - INFO - 慢速匀速滑块操作失败: ActionChains.move_by_offset() got an unexpected keyword argument 'duration'
2025-07-28 23:53:59,911 - INFO - 尝试分段慢速拖拽...
2025-07-28 23:54:05,561 - INFO - 分段慢速拖拽完成
2025-07-28 23:54:05,566 - INFO - 滑块验证成功，滑块已消失
2025-07-28 23:54:05,567 - INFO - 滑块验证处理成功，准备重试铺货
2025-07-28 23:54:07,568 - INFO - 开始第 2 次铺货尝试...
2025-07-28 23:54:07,568 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:54:07,568 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:54:07,575 - INFO - span innerHTML: 立即铺货
2025-07-28 23:54:17,773 - WARNING - 等待元素可点击超时: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:54:17,774 - WARNING - 使用配置的XPath未找到添加商品按钮
2025-07-28 23:54:17,774 - ERROR - 未找到铺货按钮
2025-07-28 23:54:17,774 - WARNING - 第 1 个商品处理失败（不计入成功数量）
2025-07-28 23:54:17,774 - INFO - 商品处理失败，立即处理下一个商品
2025-07-28 23:54:17,788 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=690841537159
2025-07-28 23:54:21,401 - INFO - 页面加载完成
2025-07-28 23:54:21,401 - INFO - 模拟阅读行为，停顿 1.6 秒
2025-07-28 23:54:24,285 - INFO - 开始第 1 次铺货尝试...
2025-07-28 23:54:24,285 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:54:24,285 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:54:24,292 - INFO - span innerHTML: 立即铺货
2025-07-28 23:54:24,312 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-28 23:54:24,312 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-28 23:54:24,327 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-28 23:54:25,480 - INFO - 尝试ActionChains点击...
2025-07-28 23:54:25,774 - INFO - ActionChains点击成功
2025-07-28 23:54:26,003 - INFO - 铺货按钮点击成功
2025-07-28 23:54:28,019 - INFO - 使用配置的XPath找到确认按钮
2025-07-28 23:54:28,031 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-28 23:54:29,120 - INFO - 尝试ActionChains点击...
2025-07-28 23:54:29,391 - INFO - ActionChains点击成功
2025-07-28 23:54:29,821 - INFO - 确认按钮点击成功
2025-07-28 23:54:31,822 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-28 23:54:31,831 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-28 23:54:31,831 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:54:32,842 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-28 23:54:32,842 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:54:33,850 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-28 23:54:33,850 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:54:34,858 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-28 23:54:34,859 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:54:35,868 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-07-28 23:54:35,868 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:54:36,875 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-07-28 23:54:36,875 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:54:37,884 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-07-28 23:54:37,884 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:54:38,893 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-07-28 23:54:38,893 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:54:39,900 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-07-28 23:54:39,900 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:54:40,910 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-07-28 23:54:40,910 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:54:41,912 - INFO - 等待超时，进行最后一次检查...
2025-07-28 23:54:41,919 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-07-28 23:54:41,919 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-07-28 23:54:41,920 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-07-28 23:54:41,920 - INFO - 检测是否出现滑块验证...
2025-07-28 23:54:41,928 - INFO - 在主文档中未找到滑块，检查iframe...
2025-07-28 23:54:41,935 - INFO - 找到 1 个iframe
2025-07-28 23:54:41,936 - INFO - 检查第 1 个iframe...
2025-07-28 23:54:41,955 - INFO - 在第 1 个iframe中检测到滑块验证，开始处理...
2025-07-28 23:54:41,956 - INFO - 开始处理滑块验证...
2025-07-28 23:54:41,983 - INFO - 滑块容器宽度: 300px
2025-07-28 23:54:41,983 - INFO - 计算拖拽距离: 273px (比例: 0.91)
2025-07-28 23:54:41,983 - INFO - 开始慢速匀速滑块操作...
2025-07-28 23:54:41,984 - INFO - 构建慢速滑块操作链，拖拽距离: 273px
2025-07-28 23:54:41,984 - INFO - 慢速匀速滑块操作失败: ActionChains.move_by_offset() got an unexpected keyword argument 'duration'
2025-07-28 23:54:41,984 - INFO - 尝试分段慢速拖拽...
2025-07-28 23:54:47,527 - INFO - 分段慢速拖拽完成
2025-07-28 23:54:47,533 - INFO - 滑块验证成功，滑块已消失
2025-07-28 23:54:47,535 - INFO - 滑块验证处理成功，准备重试铺货
2025-07-28 23:54:49,536 - INFO - 开始第 2 次铺货尝试...
2025-07-28 23:54:49,536 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:54:49,536 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:54:49,544 - INFO - span innerHTML: 立即铺货
2025-07-28 23:54:59,770 - WARNING - 等待元素可点击超时: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:54:59,770 - WARNING - 使用配置的XPath未找到添加商品按钮
2025-07-28 23:54:59,770 - ERROR - 未找到铺货按钮
2025-07-28 23:54:59,770 - WARNING - 第 2 个商品处理失败（不计入成功数量）
2025-07-28 23:54:59,771 - INFO - 商品处理失败，立即处理下一个商品
2025-07-28 23:54:59,783 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=789447097664
2025-07-28 23:55:03,477 - INFO - 页面加载完成
2025-07-28 23:55:03,477 - INFO - 模拟阅读行为，停顿 1.0 秒
2025-07-28 23:55:05,856 - INFO - 开始第 1 次铺货尝试...
2025-07-28 23:55:05,856 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:55:05,856 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:55:05,863 - INFO - span innerHTML: 立即铺货
2025-07-28 23:55:05,880 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-28 23:55:05,880 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-28 23:55:05,894 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-28 23:55:06,913 - INFO - 尝试ActionChains点击...
2025-07-28 23:55:07,206 - INFO - ActionChains点击成功
2025-07-28 23:55:07,552 - INFO - 铺货按钮点击成功
2025-07-28 23:55:09,566 - INFO - 使用配置的XPath找到确认按钮
2025-07-28 23:55:09,594 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-28 23:55:10,949 - INFO - 尝试ActionChains点击...
2025-07-28 23:55:11,222 - INFO - ActionChains点击成功
2025-07-28 23:55:11,689 - INFO - 确认按钮点击成功
2025-07-28 23:55:13,689 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-28 23:55:13,699 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-28 23:55:13,699 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:55:14,707 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-28 23:55:14,707 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:55:15,715 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-28 23:55:15,715 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:55:16,722 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-28 23:55:16,723 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:55:17,730 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-07-28 23:55:17,730 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:55:18,738 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-07-28 23:55:18,739 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:55:19,745 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-07-28 23:55:19,746 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:55:20,754 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-07-28 23:55:20,754 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:55:21,763 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-07-28 23:55:21,763 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:55:22,770 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-07-28 23:55:22,770 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:55:23,772 - INFO - 等待超时，进行最后一次检查...
2025-07-28 23:55:23,780 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-07-28 23:55:23,780 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-07-28 23:55:23,780 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-07-28 23:55:23,780 - INFO - 检测是否出现滑块验证...
2025-07-28 23:55:23,788 - INFO - 在主文档中未找到滑块，检查iframe...
2025-07-28 23:55:23,795 - INFO - 找到 1 个iframe
2025-07-28 23:55:23,795 - INFO - 检查第 1 个iframe...
2025-07-28 23:55:23,818 - INFO - 在第 1 个iframe中检测到滑块验证，开始处理...
2025-07-28 23:55:23,818 - INFO - 开始处理滑块验证...
2025-07-28 23:55:23,833 - INFO - 滑块容器宽度: 300px
2025-07-28 23:55:23,833 - INFO - 计算拖拽距离: 270px (比例: 0.90)
2025-07-28 23:55:23,833 - INFO - 开始慢速匀速滑块操作...
2025-07-28 23:55:23,833 - INFO - 构建慢速滑块操作链，拖拽距离: 270px
2025-07-28 23:55:23,833 - INFO - 慢速匀速滑块操作失败: ActionChains.move_by_offset() got an unexpected keyword argument 'duration'
2025-07-28 23:55:23,833 - INFO - 尝试分段慢速拖拽...
2025-07-28 23:55:29,410 - INFO - 分段慢速拖拽完成
2025-07-28 23:55:29,415 - INFO - 滑块验证成功，滑块已消失
2025-07-28 23:55:29,417 - INFO - 滑块验证处理成功，准备重试铺货
2025-07-28 23:55:31,418 - INFO - 开始第 2 次铺货尝试...
2025-07-28 23:55:31,418 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:55:31,418 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:55:31,440 - INFO - span innerHTML: 立即铺货
2025-07-28 23:55:31,481 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-28 23:55:31,481 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-28 23:55:31,492 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-28 23:55:32,629 - INFO - 尝试ActionChains点击...
2025-07-28 23:55:32,896 - INFO - ActionChains点击成功
2025-07-28 23:55:33,218 - INFO - 铺货按钮点击成功
2025-07-28 23:55:35,228 - INFO - 使用配置的XPath找到确认按钮
2025-07-28 23:55:35,241 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-28 23:55:36,404 - INFO - 尝试ActionChains点击...
2025-07-28 23:55:36,664 - INFO - ActionChains点击成功
2025-07-28 23:55:37,001 - INFO - 确认按钮点击成功
2025-07-28 23:55:39,002 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-28 23:55:39,009 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-28 23:55:39,009 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:55:40,019 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-28 23:55:40,019 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:55:41,026 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-28 23:55:41,026 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:55:42,035 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-28 23:55:42,035 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:55:43,043 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-07-28 23:55:43,043 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:55:44,050 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-07-28 23:55:44,051 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:55:45,075 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-07-28 23:55:45,075 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:55:46,083 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-07-28 23:55:46,083 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:55:47,092 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-07-28 23:55:47,092 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:55:48,100 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-07-28 23:55:48,100 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:55:49,102 - INFO - 等待超时，进行最后一次检查...
2025-07-28 23:55:49,109 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-07-28 23:55:49,109 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-07-28 23:55:49,110 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-07-28 23:55:49,110 - INFO - 检测是否出现滑块验证...
2025-07-28 23:55:49,116 - INFO - 在主文档中未找到滑块，检查iframe...
2025-07-28 23:55:49,119 - INFO - 找到 1 个iframe
2025-07-28 23:55:49,120 - INFO - 检查第 1 个iframe...
2025-07-28 23:55:49,164 - INFO - 在所有iframe中都未检测到滑块验证
2025-07-28 23:55:49,164 - INFO - 未检测到滑块验证或处理失败
2025-07-28 23:55:49,164 - WARNING - 第 3 个商品处理失败（不计入成功数量）
2025-07-28 23:55:49,164 - INFO - 商品处理失败，立即处理下一个商品
2025-07-28 23:55:49,166 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=694682843417
