2025-07-26 15:24:45,348 - INFO - 使用固定桌面版User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-26 15:24:46,757 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9cda]
	(No symbol) [0x0x7ff7a71f1679]
	(No symbol) [0x0x7ff7a71f4a61]
	(No symbol) [0x0x7ff7a7291e4b]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:24:46,763 - INFO - 防检测机制设置完成
2025-07-26 15:24:46,802 - INFO - 浏览器窗口已最大化
2025-07-26 15:24:46,909 - INFO - 已尝试进入全屏模式（F11）
2025-07-26 15:24:46,943 - INFO - 已通过JavaScript请求全屏
2025-07-26 15:24:46,955 - INFO - 浏览器窗口最终尺寸: 2560x1440
2025-07-26 15:24:46,958 - INFO - 浏览器驱动初始化成功
2025-07-26 15:24:46,974 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:24:46,981 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:24:46,990 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:24:47,000 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:24:47,009 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:24:47,016 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:24:47,025 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:24:47,033 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:24:47,042 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:24:47,050 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:24:47,057 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:24:47,067 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:24:47,074 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:24:47,082 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:24:47,090 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:24:47,098 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:24:47,106 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:24:47,117 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:24:47,125 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:24:47,135 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:24:47,142 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:24:47,147 - INFO - Cookies已加载
2025-07-26 15:24:47,159 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?channel=global_zx&spm=a21ug5.29159167.6452766900.2ndview_zxmore_click
2025-07-26 15:24:51,697 - INFO - 当前User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-26 15:24:51,711 - INFO - 确认：使用桌面版User-Agent
2025-07-26 15:24:51,735 - INFO - 桌面版User-Agent设置完成: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-26 15:24:52,408 - WARNING - 随机鼠标移动失败: Message: move target out of bounds
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a729be53]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:24:52,423 - WARNING - 检测到需要登录，请手动登录后继续
2025-07-26 15:24:52,437 - INFO - 请在打开的浏览器中完成登录，登录完成后点击'确认登录完成'按钮
2025-07-26 15:25:02,474 - INFO - Cookies已保存
2025-07-26 15:25:02,477 - INFO - 登录成功！现在可以开始铺货任务
2025-07-26 15:25:19,210 - INFO - 开始自动化铺货流程
2025-07-26 15:25:19,218 - INFO - 正在返回主页面...
2025-07-26 15:25:23,166 - INFO - 已返回主页面
2025-07-26 15:25:23,188 - INFO - 找到 40 个直接子div
2025-07-26 15:25:23,213 - INFO - 商品 1: 位置={'x': 686, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=681778175573&path=search_goods_card&type=s
2025-07-26 15:25:23,237 - INFO - 商品 2: 位置={'x': 923, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=690841537159&path=search_goods_card&index=
2025-07-26 15:25:23,267 - INFO - 商品 3: 位置={'x': 1160, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=789447097664&path=search_goods_card&index=
2025-07-26 15:25:23,292 - INFO - 商品 4: 位置={'x': 1397, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=694682843417&path=search_goods_card&index=
2025-07-26 15:25:23,323 - INFO - 商品 5: 位置={'x': 1634, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=681082809892&path=search_goods_card&index=
2025-07-26 15:25:23,355 - INFO - 商品 6: 位置={'x': 686, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=694675776881&path=search_goods_card&index=
2025-07-26 15:25:23,389 - INFO - 商品 7: 位置={'x': 923, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=680729304287&path=search_goods_card&index=
2025-07-26 15:25:23,426 - INFO - 商品 8: 位置={'x': 1160, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=710355107054&path=search_goods_card&index=
2025-07-26 15:25:23,460 - INFO - 商品 9: 位置={'x': 1397, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=740463658412&path=search_goods_card&index=
2025-07-26 15:25:23,510 - INFO - 商品 10: 位置={'x': 1634, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=821067902127&path=search_goods_card&index=
2025-07-26 15:25:23,549 - INFO - 商品 11: 位置={'x': 686, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=789835300215&path=search_goods_card&index=
2025-07-26 15:25:23,585 - INFO - 商品 12: 位置={'x': 923, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=843747605382&path=search_goods_card&index=
2025-07-26 15:25:23,622 - INFO - 商品 13: 位置={'x': 1160, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=675816539149&path=search_goods_card&index=
2025-07-26 15:25:23,659 - INFO - 商品 14: 位置={'x': 1397, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=846634805775&path=search_goods_card&index=
2025-07-26 15:25:23,696 - INFO - 商品 15: 位置={'x': 1634, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=852510575114&path=search_goods_card&index=
2025-07-26 15:25:23,733 - INFO - 商品 16: 位置={'x': 686, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=743442966826&path=search_goods_card&index=
2025-07-26 15:25:23,770 - INFO - 商品 17: 位置={'x': 923, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=848934676698&path=search_goods_card&index=
2025-07-26 15:25:23,808 - INFO - 商品 18: 位置={'x': 1160, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=693758717931&path=search_goods_card&index=
2025-07-26 15:25:23,846 - INFO - 商品 19: 位置={'x': 1397, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=691142128229&path=search_goods_card&index=
2025-07-26 15:25:23,883 - INFO - 商品 20: 位置={'x': 1634, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=690739721578&path=search_goods_card&index=
2025-07-26 15:25:23,923 - INFO - 商品 21: 位置={'x': 686, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=691918824108&path=search_goods_card&index=
2025-07-26 15:25:23,958 - INFO - 商品 22: 位置={'x': 923, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=875045022715&path=search_goods_card&index=
2025-07-26 15:25:24,002 - INFO - 商品 23: 位置={'x': 1160, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=873911671570&path=search_goods_card&index=
2025-07-26 15:25:24,050 - INFO - 商品 24: 位置={'x': 1397, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=722937163980&path=search_goods_card&index=
2025-07-26 15:25:24,091 - INFO - 商品 25: 位置={'x': 1634, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=739090443937&path=search_goods_card&index=
2025-07-26 15:25:24,136 - INFO - 商品 26: 位置={'x': 686, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=777862601010&path=search_goods_card&index=
2025-07-26 15:25:24,175 - INFO - 商品 27: 位置={'x': 923, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=686870174450&path=search_goods_card&index=
2025-07-26 15:25:24,215 - INFO - 商品 28: 位置={'x': 1160, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=814542665480&path=search_goods_card&index=
2025-07-26 15:25:24,254 - INFO - 商品 29: 位置={'x': 1397, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=852625718015&path=search_goods_card&index=
2025-07-26 15:25:24,291 - INFO - 商品 30: 位置={'x': 1634, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=681472049289&path=search_goods_card&index=
2025-07-26 15:25:24,331 - INFO - 商品 31: 位置={'x': 686, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=683142617961&path=search_goods_card&index=
2025-07-26 15:25:24,371 - INFO - 商品 32: 位置={'x': 923, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=763659951211&path=search_goods_card&index=
2025-07-26 15:25:24,411 - INFO - 商品 33: 位置={'x': 1160, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=675478966678&path=search_goods_card&index=
2025-07-26 15:25:24,451 - INFO - 商品 34: 位置={'x': 1397, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=800365611234&path=search_goods_card&index=
2025-07-26 15:25:24,489 - INFO - 商品 35: 位置={'x': 1634, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=896721954982&path=search_goods_card&index=
2025-07-26 15:25:24,529 - INFO - 商品 36: 位置={'x': 686, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=795389643023&path=search_goods_card&index=
2025-07-26 15:25:24,569 - INFO - 商品 37: 位置={'x': 923, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=689584396900&path=search_goods_card&index=
2025-07-26 15:25:24,609 - INFO - 商品 38: 位置={'x': 1160, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=788516102627&path=search_goods_card&index=
2025-07-26 15:25:24,647 - INFO - 商品 39: 位置={'x': 1397, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=825025224622&path=search_goods_card&index=
2025-07-26 15:25:24,688 - INFO - 商品 40: 位置={'x': 1634, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=842638336638&path=search_goods_card&index=
2025-07-26 15:25:24,721 - INFO - 找到 40 个有效的商品容器
2025-07-26 15:25:24,751 - INFO - 找到 40 个商品，将处理 20 个
2025-07-26 15:25:24,785 - INFO - 当前批次大小: 15
2025-07-26 15:25:24,813 - INFO - 开始处理第 1/40 个商品
2025-07-26 15:25:24,842 - INFO - 找到 40 个直接子div
2025-07-26 15:25:24,881 - INFO - 商品 1: 位置={'x': 686, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=681778175573&path=search_goods_card&type=s
2025-07-26 15:25:24,915 - INFO - 商品 2: 位置={'x': 923, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=690841537159&path=search_goods_card&index=
2025-07-26 15:25:24,950 - INFO - 商品 3: 位置={'x': 1160, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=789447097664&path=search_goods_card&index=
2025-07-26 15:25:24,987 - INFO - 商品 4: 位置={'x': 1397, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=694682843417&path=search_goods_card&index=
2025-07-26 15:25:25,023 - INFO - 商品 5: 位置={'x': 1634, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=681082809892&path=search_goods_card&index=
2025-07-26 15:25:25,058 - INFO - 商品 6: 位置={'x': 686, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=694675776881&path=search_goods_card&index=
2025-07-26 15:25:25,092 - INFO - 商品 7: 位置={'x': 923, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=680729304287&path=search_goods_card&index=
2025-07-26 15:25:25,128 - INFO - 商品 8: 位置={'x': 1160, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=710355107054&path=search_goods_card&index=
2025-07-26 15:25:25,165 - INFO - 商品 9: 位置={'x': 1397, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=740463658412&path=search_goods_card&index=
2025-07-26 15:25:25,207 - INFO - 商品 10: 位置={'x': 1634, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=821067902127&path=search_goods_card&index=
2025-07-26 15:25:25,331 - INFO - 商品 11: 位置={'x': 686, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=789835300215&path=search_goods_card&index=
2025-07-26 15:25:25,373 - INFO - 商品 12: 位置={'x': 923, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=843747605382&path=search_goods_card&index=
2025-07-26 15:25:25,412 - INFO - 商品 13: 位置={'x': 1160, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=675816539149&path=search_goods_card&index=
2025-07-26 15:25:25,455 - INFO - 商品 14: 位置={'x': 1397, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=846634805775&path=search_goods_card&index=
2025-07-26 15:25:25,491 - INFO - 商品 15: 位置={'x': 1634, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=852510575114&path=search_goods_card&index=
2025-07-26 15:25:25,531 - INFO - 商品 16: 位置={'x': 686, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=743442966826&path=search_goods_card&index=
2025-07-26 15:25:25,567 - INFO - 商品 17: 位置={'x': 923, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=848934676698&path=search_goods_card&index=
2025-07-26 15:25:25,606 - INFO - 商品 18: 位置={'x': 1160, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=693758717931&path=search_goods_card&index=
2025-07-26 15:25:25,643 - INFO - 商品 19: 位置={'x': 1397, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=691142128229&path=search_goods_card&index=
2025-07-26 15:25:25,680 - INFO - 商品 20: 位置={'x': 1634, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=690739721578&path=search_goods_card&index=
2025-07-26 15:25:25,720 - INFO - 商品 21: 位置={'x': 686, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=691918824108&path=search_goods_card&index=
2025-07-26 15:25:25,756 - INFO - 商品 22: 位置={'x': 923, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=875045022715&path=search_goods_card&index=
2025-07-26 15:25:25,794 - INFO - 商品 23: 位置={'x': 1160, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=873911671570&path=search_goods_card&index=
2025-07-26 15:25:25,831 - INFO - 商品 24: 位置={'x': 1397, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=722937163980&path=search_goods_card&index=
2025-07-26 15:25:25,872 - INFO - 商品 25: 位置={'x': 1634, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=739090443937&path=search_goods_card&index=
2025-07-26 15:25:25,935 - INFO - 商品 26: 位置={'x': 686, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=777862601010&path=search_goods_card&index=
2025-07-26 15:25:25,975 - INFO - 商品 27: 位置={'x': 923, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=686870174450&path=search_goods_card&index=
2025-07-26 15:25:26,014 - INFO - 商品 28: 位置={'x': 1160, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=814542665480&path=search_goods_card&index=
2025-07-26 15:25:26,053 - INFO - 商品 29: 位置={'x': 1397, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=852625718015&path=search_goods_card&index=
2025-07-26 15:25:26,093 - INFO - 商品 30: 位置={'x': 1634, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=681472049289&path=search_goods_card&index=
2025-07-26 15:25:26,130 - INFO - 商品 31: 位置={'x': 686, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=683142617961&path=search_goods_card&index=
2025-07-26 15:25:26,167 - INFO - 商品 32: 位置={'x': 923, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=763659951211&path=search_goods_card&index=
2025-07-26 15:25:26,226 - INFO - 商品 33: 位置={'x': 1160, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=675478966678&path=search_goods_card&index=
2025-07-26 15:25:26,263 - INFO - 商品 34: 位置={'x': 1397, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=800365611234&path=search_goods_card&index=
2025-07-26 15:25:26,302 - INFO - 商品 35: 位置={'x': 1634, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=896721954982&path=search_goods_card&index=
2025-07-26 15:25:26,340 - INFO - 商品 36: 位置={'x': 686, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=795389643023&path=search_goods_card&index=
2025-07-26 15:25:26,377 - INFO - 商品 37: 位置={'x': 923, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=689584396900&path=search_goods_card&index=
2025-07-26 15:25:26,414 - INFO - 商品 38: 位置={'x': 1160, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=788516102627&path=search_goods_card&index=
2025-07-26 15:25:26,453 - INFO - 商品 39: 位置={'x': 1397, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=825025224622&path=search_goods_card&index=
2025-07-26 15:25:26,490 - INFO - 商品 40: 位置={'x': 1634, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=842638336638&path=search_goods_card&index=
2025-07-26 15:25:26,523 - INFO - 找到 40 个有效的商品容器
2025-07-26 15:25:26,550 - WARNING - 警告：第 1 个元素可能不是商品项，class=''
2025-07-26 15:25:26,593 - INFO - 商品 1 信息: 文本='保税直供 Dove多芬磨砂膏石榴籽乳木果风味身体磨砂膏298g
采购价¥35.00销量11.2万+件', 位置={'x': 686, 'y': 737}
2025-07-26 15:25:28,135 - INFO - 商品 1 位置: {'x': 686, 'y': 737}, 尺寸: {'height': 347, 'width': 226}
2025-07-26 15:25:28,160 - INFO - 模拟阅读行为，停顿 2.7 秒
2025-07-26 15:25:32,052 - INFO - 点击前标签页数量: 1
2025-07-26 15:25:32,076 - INFO - 尝试策略: ActionChains点击
2025-07-26 15:25:34,281 - INFO - 点击后标签页数量: 2
2025-07-26 15:25:34,296 - INFO - ActionChains点击成功，新标签页已打开
2025-07-26 15:25:34,312 - INFO - 已点击第 1 个商品，等待页面响应...
2025-07-26 15:25:41,338 - WARNING - 等待新标签页超时，未检测到新标签页
2025-07-26 15:25:41,352 - WARNING - 未检测到新标签页，可能是页面内跳转
2025-07-26 15:25:44,364 - INFO - 找到 0 个iframe
2025-07-26 15:25:44,375 - INFO - 未找到包含目标元素的iframe，保持在主文档
2025-07-26 15:25:44,386 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-26 15:25:54,537 - WARNING - 等待元素可点击超时: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-26 15:25:54,552 - INFO - 尝试选择器: //*[@id='root']//button[contains(@class, 'btn') or contains(@class, 'button')]
2025-07-26 15:25:54,576 - INFO - 找到元素，文本: '筛选'
2025-07-26 15:25:54,593 - INFO - 找到元素，文本: '重置'
2025-07-26 15:25:54,611 - INFO - 找到元素，文本: '上一页'
2025-07-26 15:25:54,626 - INFO - 找到元素，文本: '1'
2025-07-26 15:25:54,644 - INFO - 找到元素，文本: '2'
2025-07-26 15:25:54,658 - INFO - 找到元素，文本: '3'
2025-07-26 15:25:54,673 - INFO - 找到元素，文本: '4'
2025-07-26 15:25:54,688 - INFO - 找到元素，文本: '44'
2025-07-26 15:25:54,703 - INFO - 找到元素，文本: '下一页'
2025-07-26 15:25:54,718 - INFO - 找到元素，文本: '确定'
2025-07-26 15:25:54,729 - INFO - 尝试选择器: //*[@id='root']//div[3]//button
2025-07-26 15:25:54,748 - INFO - 找到元素，文本: '筛选'
2025-07-26 15:25:54,764 - INFO - 找到元素，文本: '重置'
2025-07-26 15:25:54,777 - INFO - 尝试选择器: //*[@id='root']//div[contains(@class, 'action') or contains(@class, 'btn')]//button
2025-07-26 15:25:54,795 - INFO - 尝试选择器: //button[contains(text(), '立即铺货') or contains(text(), '立即') or contains(text(), '铺货')]
2025-07-26 15:25:54,810 - INFO - 尝试选择器: //button[contains(text(), '添加') or contains(text(), '加入')]
2025-07-26 15:25:54,828 - INFO - 尝试选择器: //a[contains(text(), '立即铺货') or contains(text(), '立即') or contains(text(), '铺货')]
2025-07-26 15:25:54,843 - INFO - 尝试选择器: //a[contains(text(), '添加') or contains(text(), '加入')]
2025-07-26 15:25:54,862 - INFO - 尝试选择器: //button[contains(@class, 'primary') or contains(@class, 'main')]
2025-07-26 15:25:54,895 - INFO - 找到元素，文本: '筛选'
2025-07-26 15:25:54,907 - INFO - 尝试选择器: //button[contains(@class, 'submit') or contains(@class, 'confirm')]
2025-07-26 15:25:54,923 - INFO - 尝试选择器: //div[contains(@class, 'action')]//button
2025-07-26 15:25:54,938 - INFO - 尝试选择器: //div[contains(@class, 'btn')]//button
2025-07-26 15:25:54,954 - INFO - 尝试查找页面中的所有按钮...
2025-07-26 15:25:54,976 - INFO - 找到 10 个可点击元素
2025-07-26 15:25:54,995 - INFO - 元素 1: 文本='筛选', class='next-btn next-medium next-btn-primary', id=''
2025-07-26 15:25:55,006 - INFO - 找到匹配的按钮: 文本='筛选', class='next-btn next-medium next-btn-primary'
2025-07-26 15:25:55,025 - INFO - 准备点击元素: tag=button, text='筛选', class='next-btn next-medium next-btn-', id=''
2025-07-26 15:25:56,505 - INFO - 尝试ActionChains点击...
2025-07-26 15:26:01,831 - INFO - ActionChains点击成功
2025-07-26 15:26:02,053 - INFO - 已点击添加商品按钮，等待确认对话框...
2025-07-26 15:26:10,156 - WARNING - 等待元素可点击超时: /html/body/div[8]/div[2]/div[2]/button
2025-07-26 15:26:12,205 - WARNING - 等待元素可点击超时: //button[contains(text(), '确认') or contains(text(), '确定') or contains(text(), '提交')]
2025-07-26 15:26:14,245 - WARNING - 等待元素可点击超时: //button[contains(@class, 'confirm') or contains(@class, 'submit')]
2025-07-26 15:26:16,287 - WARNING - 等待元素可点击超时: //div[contains(@class, 'dialog')]//button[last()]
2025-07-26 15:26:18,630 - WARNING - 等待元素可点击超时: //div[contains(@class, 'modal')]//button[contains(@class, 'primary')]
2025-07-26 15:26:20,755 - WARNING - 等待元素可点击超时: //button[@type='submit']
2025-07-26 15:26:22,857 - WARNING - 等待元素可点击超时: //a[contains(text(), '确认') or contains(text(), '确定')]
2025-07-26 15:26:24,906 - WARNING - 等待元素可点击超时: //*[@role='button'][contains(text(), '确认') or contains(text(), '确定')]
2025-07-26 15:26:25,089 - INFO - 通过文本内容找到确认按钮: 确定
2025-07-26 15:26:25,118 - INFO - 准备点击元素: tag=button, text='确定', class='next-btn next-medium next-btn-', id=''
2025-07-26 15:26:26,143 - INFO - 尝试ActionChains点击...
2025-07-26 15:26:31,425 - INFO - ActionChains点击成功
2025-07-26 15:26:31,929 - INFO - 商品添加成功，返回主页面
2025-07-26 15:26:34,248 - INFO - 第 1 个商品处理成功 (总成功: 1, 批次进度: 1/15)
2025-07-26 15:26:34,260 - INFO - 等待 6.0 秒后处理下一个商品
2025-07-26 15:26:40,240 - INFO - 开始处理第 2/40 个商品
2025-07-26 15:26:40,266 - INFO - 找到 40 个直接子div
2025-07-26 15:26:40,296 - INFO - 商品 1: 位置={'x': 686, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=681778175573&path=search_goods_card&type=s
2025-07-26 15:26:40,331 - INFO - 商品 2: 位置={'x': 923, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=690841537159&path=search_goods_card&index=
2025-07-26 15:26:40,371 - INFO - 商品 3: 位置={'x': 1160, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=789447097664&path=search_goods_card&index=
2025-07-26 15:26:40,409 - INFO - 商品 4: 位置={'x': 1397, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=694682843417&path=search_goods_card&index=
2025-07-26 15:26:40,450 - INFO - 商品 5: 位置={'x': 1634, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=681082809892&path=search_goods_card&index=
2025-07-26 15:26:40,486 - INFO - 商品 6: 位置={'x': 686, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=694675776881&path=search_goods_card&index=
2025-07-26 15:26:40,529 - INFO - 商品 7: 位置={'x': 923, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=680729304287&path=search_goods_card&index=
2025-07-26 15:26:40,569 - INFO - 商品 8: 位置={'x': 1160, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=710355107054&path=search_goods_card&index=
2025-07-26 15:26:40,617 - INFO - 商品 9: 位置={'x': 1397, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=740463658412&path=search_goods_card&index=
2025-07-26 15:26:40,661 - INFO - 商品 10: 位置={'x': 1634, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=821067902127&path=search_goods_card&index=
2025-07-26 15:26:40,706 - INFO - 商品 11: 位置={'x': 686, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=789835300215&path=search_goods_card&index=
2025-07-26 15:26:40,748 - INFO - 商品 12: 位置={'x': 923, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=843747605382&path=search_goods_card&index=
2025-07-26 15:26:40,790 - INFO - 商品 13: 位置={'x': 1160, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=675816539149&path=search_goods_card&index=
2025-07-26 15:26:40,842 - INFO - 商品 14: 位置={'x': 1397, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=846634805775&path=search_goods_card&index=
2025-07-26 15:26:40,885 - INFO - 商品 15: 位置={'x': 1634, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=852510575114&path=search_goods_card&index=
2025-07-26 15:26:40,929 - INFO - 商品 16: 位置={'x': 686, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=743442966826&path=search_goods_card&index=
2025-07-26 15:26:40,974 - INFO - 商品 17: 位置={'x': 923, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=848934676698&path=search_goods_card&index=
2025-07-26 15:26:41,021 - INFO - 商品 18: 位置={'x': 1160, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=693758717931&path=search_goods_card&index=
2025-07-26 15:26:41,071 - INFO - 商品 19: 位置={'x': 1397, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=691142128229&path=search_goods_card&index=
2025-07-26 15:26:41,119 - INFO - 商品 20: 位置={'x': 1634, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=690739721578&path=search_goods_card&index=
2025-07-26 15:26:41,163 - INFO - 商品 21: 位置={'x': 686, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=691918824108&path=search_goods_card&index=
2025-07-26 15:26:41,210 - INFO - 商品 22: 位置={'x': 923, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=875045022715&path=search_goods_card&index=
2025-07-26 15:26:41,263 - INFO - 商品 23: 位置={'x': 1160, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=873911671570&path=search_goods_card&index=
2025-07-26 15:26:41,305 - INFO - 商品 24: 位置={'x': 1397, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=722937163980&path=search_goods_card&index=
2025-07-26 15:26:41,354 - INFO - 商品 25: 位置={'x': 1634, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=739090443937&path=search_goods_card&index=
2025-07-26 15:26:41,407 - INFO - 商品 26: 位置={'x': 686, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=777862601010&path=search_goods_card&index=
2025-07-26 15:26:41,465 - INFO - 商品 27: 位置={'x': 923, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=686870174450&path=search_goods_card&index=
2025-07-26 15:26:41,513 - INFO - 商品 28: 位置={'x': 1160, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=814542665480&path=search_goods_card&index=
2025-07-26 15:26:41,557 - INFO - 商品 29: 位置={'x': 1397, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=852625718015&path=search_goods_card&index=
2025-07-26 15:26:41,602 - INFO - 商品 30: 位置={'x': 1634, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=681472049289&path=search_goods_card&index=
2025-07-26 15:26:41,646 - INFO - 商品 31: 位置={'x': 686, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=683142617961&path=search_goods_card&index=
2025-07-26 15:26:41,690 - INFO - 商品 32: 位置={'x': 923, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=763659951211&path=search_goods_card&index=
2025-07-26 15:26:41,733 - INFO - 商品 33: 位置={'x': 1160, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=675478966678&path=search_goods_card&index=
2025-07-26 15:26:41,780 - INFO - 商品 34: 位置={'x': 1397, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=800365611234&path=search_goods_card&index=
2025-07-26 15:26:41,822 - INFO - 商品 35: 位置={'x': 1634, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=896721954982&path=search_goods_card&index=
2025-07-26 15:26:41,865 - INFO - 商品 36: 位置={'x': 686, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=795389643023&path=search_goods_card&index=
2025-07-26 15:26:41,915 - INFO - 商品 37: 位置={'x': 923, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=689584396900&path=search_goods_card&index=
2025-07-26 15:26:41,973 - INFO - 商品 38: 位置={'x': 1160, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=788516102627&path=search_goods_card&index=
2025-07-26 15:26:42,029 - INFO - 商品 39: 位置={'x': 1397, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=825025224622&path=search_goods_card&index=
2025-07-26 15:26:42,075 - INFO - 商品 40: 位置={'x': 1634, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=842638336638&path=search_goods_card&index=
2025-07-26 15:26:42,113 - INFO - 找到 40 个有效的商品容器
2025-07-26 15:26:42,140 - WARNING - 警告：第 2 个元素可能不是商品项，class=''
2025-07-26 15:26:42,194 - INFO - 商品 2 信息: 文本='保税直供 凡士林Vaseline烟酰胺身体乳400ml 725ml无压泵款200ml
采购价¥19.', 位置={'x': 923, 'y': 737}
2025-07-26 15:26:43,738 - INFO - 商品 2 位置: {'x': 922, 'y': 737}, 尺寸: {'height': 347, 'width': 226}
2025-07-26 15:26:43,767 - INFO - 模拟阅读行为，停顿 1.8 秒
2025-07-26 15:26:46,328 - INFO - 点击前标签页数量: 3
2025-07-26 15:26:46,350 - INFO - 尝试策略: ActionChains点击
2025-07-26 15:26:52,825 - INFO - 点击后标签页数量: 4
2025-07-26 15:26:52,840 - INFO - ActionChains点击成功，新标签页已打开
2025-07-26 15:26:52,857 - INFO - 已点击第 2 个商品，等待页面响应...
2025-07-26 15:26:59,883 - WARNING - 等待新标签页超时，未检测到新标签页
2025-07-26 15:26:59,897 - WARNING - 未检测到新标签页，可能是页面内跳转
2025-07-26 15:27:02,915 - INFO - 找到 0 个iframe
2025-07-26 15:27:02,927 - INFO - 未找到包含目标元素的iframe，保持在主文档
2025-07-26 15:27:02,942 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-26 15:27:13,129 - WARNING - 等待元素可点击超时: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-26 15:27:13,144 - INFO - 尝试选择器: //*[@id='root']//button[contains(@class, 'btn') or contains(@class, 'button')]
2025-07-26 15:27:13,172 - INFO - 找到元素，文本: '筛选'
2025-07-26 15:27:13,194 - INFO - 找到元素，文本: '重置'
2025-07-26 15:27:13,215 - INFO - 找到元素，文本: '上一页'
2025-07-26 15:27:13,235 - INFO - 找到元素，文本: '1'
2025-07-26 15:27:13,257 - INFO - 找到元素，文本: '2'
2025-07-26 15:27:13,274 - INFO - 找到元素，文本: '3'
2025-07-26 15:27:13,293 - INFO - 找到元素，文本: '4'
2025-07-26 15:27:13,312 - INFO - 找到元素，文本: '44'
2025-07-26 15:27:13,331 - INFO - 找到元素，文本: '下一页'
2025-07-26 15:27:13,350 - INFO - 找到元素，文本: '确定'
2025-07-26 15:27:13,361 - INFO - 尝试选择器: //*[@id='root']//div[3]//button
2025-07-26 15:27:13,386 - INFO - 找到元素，文本: '筛选'
2025-07-26 15:27:13,407 - INFO - 找到元素，文本: '重置'
2025-07-26 15:27:13,420 - INFO - 尝试选择器: //*[@id='root']//div[contains(@class, 'action') or contains(@class, 'btn')]//button
2025-07-26 15:27:13,441 - INFO - 尝试选择器: //button[contains(text(), '立即铺货') or contains(text(), '立即') or contains(text(), '铺货')]
2025-07-26 15:27:13,459 - INFO - 尝试选择器: //button[contains(text(), '添加') or contains(text(), '加入')]
2025-07-26 15:27:13,477 - INFO - 尝试选择器: //a[contains(text(), '立即铺货') or contains(text(), '立即') or contains(text(), '铺货')]
2025-07-26 15:27:13,495 - INFO - 尝试选择器: //a[contains(text(), '添加') or contains(text(), '加入')]
2025-07-26 15:27:13,519 - INFO - 尝试选择器: //button[contains(@class, 'primary') or contains(@class, 'main')]
2025-07-26 15:27:13,543 - INFO - 找到元素，文本: '筛选'
2025-07-26 15:27:13,556 - INFO - 尝试选择器: //button[contains(@class, 'submit') or contains(@class, 'confirm')]
2025-07-26 15:27:13,570 - INFO - 尝试选择器: //div[contains(@class, 'action')]//button
2025-07-26 15:27:13,586 - INFO - 尝试选择器: //div[contains(@class, 'btn')]//button
2025-07-26 15:27:13,600 - INFO - 尝试查找页面中的所有按钮...
2025-07-26 15:27:13,619 - INFO - 找到 10 个可点击元素
2025-07-26 15:27:13,644 - INFO - 元素 1: 文本='筛选', class='next-btn next-medium next-btn-primary', id=''
2025-07-26 15:27:13,655 - INFO - 找到匹配的按钮: 文本='筛选', class='next-btn next-medium next-btn-primary'
2025-07-26 15:27:13,681 - INFO - 准备点击元素: tag=button, text='筛选', class='next-btn next-medium next-btn-', id=''
2025-07-26 15:27:14,946 - INFO - 尝试ActionChains点击...
2025-07-26 15:27:20,253 - INFO - ActionChains点击成功
2025-07-26 15:27:20,651 - INFO - 已点击添加商品按钮，等待确认对话框...
2025-07-26 15:27:28,751 - WARNING - 等待元素可点击超时: /html/body/div[8]/div[2]/div[2]/button
2025-07-26 15:27:30,803 - WARNING - 等待元素可点击超时: //button[contains(text(), '确认') or contains(text(), '确定') or contains(text(), '提交')]
2025-07-26 15:27:32,854 - WARNING - 等待元素可点击超时: //button[contains(@class, 'confirm') or contains(@class, 'submit')]
2025-07-26 15:27:34,913 - WARNING - 等待元素可点击超时: //div[contains(@class, 'dialog')]//button[last()]
2025-07-26 15:27:36,962 - WARNING - 等待元素可点击超时: //div[contains(@class, 'modal')]//button[contains(@class, 'primary')]
2025-07-26 15:27:39,010 - WARNING - 等待元素可点击超时: //button[@type='submit']
2025-07-26 15:27:41,070 - WARNING - 等待元素可点击超时: //a[contains(text(), '确认') or contains(text(), '确定')]
2025-07-26 15:27:43,135 - WARNING - 等待元素可点击超时: //*[@role='button'][contains(text(), '确认') or contains(text(), '确定')]
2025-07-26 15:27:43,325 - INFO - 通过文本内容找到确认按钮: 确定
2025-07-26 15:27:43,355 - INFO - 准备点击元素: tag=button, text='确定', class='next-btn next-medium next-btn-', id=''
2025-07-26 15:27:44,648 - INFO - 尝试ActionChains点击...
2025-07-26 15:27:49,926 - INFO - ActionChains点击成功
2025-07-26 15:27:50,354 - INFO - 商品添加成功，返回主页面
2025-07-26 15:27:52,384 - INFO - 第 2 个商品处理成功 (总成功: 2, 批次进度: 2/15)
2025-07-26 15:27:52,397 - INFO - 等待 7.2 秒后处理下一个商品
2025-07-26 15:27:59,621 - INFO - 开始处理第 3/40 个商品
2025-07-26 15:27:59,654 - INFO - 找到 0 个直接子div
2025-07-26 15:27:59,673 - INFO - 找到 0 个有效的商品容器
2025-07-26 15:27:59,683 - ERROR - 无法获取商品列表
2025-07-26 15:27:59,694 - WARNING - 第 3 个商品处理失败
2025-07-26 15:27:59,707 - INFO - 等待 4.8 秒后处理下一个商品
2025-07-26 15:28:04,552 - INFO - 开始处理第 4/40 个商品
2025-07-26 15:28:04,570 - INFO - 找到 0 个直接子div
2025-07-26 15:28:04,576 - INFO - 找到 0 个有效的商品容器
2025-07-26 15:28:04,586 - ERROR - 无法获取商品列表
2025-07-26 15:28:04,592 - WARNING - 第 4 个商品处理失败
2025-07-26 15:28:04,598 - INFO - 等待 5.1 秒后处理下一个商品
2025-07-26 15:28:09,741 - INFO - 开始处理第 5/40 个商品
2025-07-26 15:28:09,762 - INFO - 找到 0 个直接子div
2025-07-26 15:28:09,766 - INFO - 找到 0 个有效的商品容器
2025-07-26 15:28:09,772 - ERROR - 无法获取商品列表
2025-07-26 15:28:09,775 - WARNING - 第 5 个商品处理失败
2025-07-26 15:28:09,779 - INFO - 等待 6.8 秒后处理下一个商品
2025-07-26 15:28:16,536 - INFO - 开始处理第 6/40 个商品
2025-07-26 15:28:16,546 - ERROR - 获取商品列表失败: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9cda]
	(No symbol) [0x0x7ff7a71d5f35]
	(No symbol) [0x0x7ff7a71faabe]
	(No symbol) [0x0x7ff7a726feb5]
	(No symbol) [0x0x7ff7a7290432]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:28:16,553 - ERROR - 无法获取商品列表
2025-07-26 15:28:16,561 - WARNING - 第 6 个商品处理失败
2025-07-26 15:28:16,567 - INFO - 等待 6.0 秒后处理下一个商品
2025-07-26 15:28:22,535 - INFO - 开始处理第 7/40 个商品
2025-07-26 15:28:22,543 - ERROR - 获取商品列表失败: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72308bf]
	(No symbol) [0x0x7ff7a7268792]
	(No symbol) [0x0x7ff7a7263293]
	(No symbol) [0x0x7ff7a7262359]
	(No symbol) [0x0x7ff7a71b4b05]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	(No symbol) [0x0x7ff7a71b3b00]
	GetHandleVerifier [0x0x7ff7a782bd88+4260984]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:28:22,551 - ERROR - 无法获取商品列表
2025-07-26 15:28:22,559 - WARNING - 第 7 个商品处理失败
2025-07-26 15:28:22,566 - INFO - 等待 3.6 秒后处理下一个商品
2025-07-26 15:28:26,208 - INFO - 开始处理第 8/40 个商品
2025-07-26 15:28:26,215 - ERROR - 获取商品列表失败: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72308bf]
	(No symbol) [0x0x7ff7a7268792]
	(No symbol) [0x0x7ff7a7263293]
	(No symbol) [0x0x7ff7a7262359]
	(No symbol) [0x0x7ff7a71b4b05]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	(No symbol) [0x0x7ff7a71b3b00]
	GetHandleVerifier [0x0x7ff7a782bd88+4260984]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:28:26,224 - ERROR - 无法获取商品列表
2025-07-26 15:28:26,230 - WARNING - 第 8 个商品处理失败
2025-07-26 15:28:26,238 - INFO - 等待 3.8 秒后处理下一个商品
2025-07-26 15:28:30,093 - INFO - 开始处理第 9/40 个商品
2025-07-26 15:28:30,100 - ERROR - 获取商品列表失败: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72308bf]
	(No symbol) [0x0x7ff7a7268792]
	(No symbol) [0x0x7ff7a7263293]
	(No symbol) [0x0x7ff7a7262359]
	(No symbol) [0x0x7ff7a71b4b05]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	(No symbol) [0x0x7ff7a71b3b00]
	GetHandleVerifier [0x0x7ff7a782bd88+4260984]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:28:30,112 - ERROR - 无法获取商品列表
2025-07-26 15:28:30,121 - WARNING - 第 9 个商品处理失败
2025-07-26 15:28:30,127 - INFO - 等待 4.1 秒后处理下一个商品
2025-07-26 15:28:34,259 - INFO - 开始处理第 10/40 个商品
2025-07-26 15:28:34,267 - ERROR - 获取商品列表失败: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72308bf]
	(No symbol) [0x0x7ff7a7268792]
	(No symbol) [0x0x7ff7a7263293]
	(No symbol) [0x0x7ff7a7262359]
	(No symbol) [0x0x7ff7a71b4b05]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	(No symbol) [0x0x7ff7a71b3b00]
	GetHandleVerifier [0x0x7ff7a782bd88+4260984]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:28:34,274 - ERROR - 无法获取商品列表
2025-07-26 15:28:34,294 - WARNING - 第 10 个商品处理失败
2025-07-26 15:28:34,303 - INFO - 等待 5.3 秒后处理下一个商品
2025-07-26 15:28:39,638 - INFO - 开始处理第 11/40 个商品
2025-07-26 15:28:39,645 - ERROR - 获取商品列表失败: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72308bf]
	(No symbol) [0x0x7ff7a7268792]
	(No symbol) [0x0x7ff7a7263293]
	(No symbol) [0x0x7ff7a7262359]
	(No symbol) [0x0x7ff7a71b4b05]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	(No symbol) [0x0x7ff7a71b3b00]
	GetHandleVerifier [0x0x7ff7a782bd88+4260984]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:28:39,657 - ERROR - 无法获取商品列表
2025-07-26 15:28:39,664 - WARNING - 第 11 个商品处理失败
2025-07-26 15:28:39,668 - INFO - 等待 6.3 秒后处理下一个商品
2025-07-26 15:28:45,974 - INFO - 开始处理第 12/40 个商品
2025-07-26 15:28:45,980 - ERROR - 获取商品列表失败: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72308bf]
	(No symbol) [0x0x7ff7a7268792]
	(No symbol) [0x0x7ff7a7263293]
	(No symbol) [0x0x7ff7a7262359]
	(No symbol) [0x0x7ff7a71b4b05]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	(No symbol) [0x0x7ff7a71b3b00]
	GetHandleVerifier [0x0x7ff7a782bd88+4260984]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:28:45,989 - ERROR - 无法获取商品列表
2025-07-26 15:28:45,996 - WARNING - 第 12 个商品处理失败
2025-07-26 15:28:46,004 - INFO - 等待 5.2 秒后处理下一个商品
2025-07-26 15:28:51,221 - INFO - 开始处理第 13/40 个商品
2025-07-26 15:28:51,228 - ERROR - 获取商品列表失败: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72308bf]
	(No symbol) [0x0x7ff7a7268792]
	(No symbol) [0x0x7ff7a7263293]
	(No symbol) [0x0x7ff7a7262359]
	(No symbol) [0x0x7ff7a71b4b05]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	(No symbol) [0x0x7ff7a71b3b00]
	GetHandleVerifier [0x0x7ff7a782bd88+4260984]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:28:51,239 - ERROR - 无法获取商品列表
2025-07-26 15:28:51,247 - WARNING - 第 13 个商品处理失败
2025-07-26 15:28:51,257 - INFO - 等待 7.6 秒后处理下一个商品
2025-07-26 15:28:58,865 - INFO - 开始处理第 14/40 个商品
2025-07-26 15:28:58,871 - ERROR - 获取商品列表失败: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72308bf]
	(No symbol) [0x0x7ff7a7268792]
	(No symbol) [0x0x7ff7a7263293]
	(No symbol) [0x0x7ff7a7262359]
	(No symbol) [0x0x7ff7a71b4b05]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	(No symbol) [0x0x7ff7a71b3b00]
	GetHandleVerifier [0x0x7ff7a782bd88+4260984]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:28:58,883 - ERROR - 无法获取商品列表
2025-07-26 15:28:58,892 - WARNING - 第 14 个商品处理失败
2025-07-26 15:28:58,898 - INFO - 等待 3.8 秒后处理下一个商品
2025-07-26 15:29:02,719 - INFO - 开始处理第 15/40 个商品
2025-07-26 15:29:02,728 - ERROR - 获取商品列表失败: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72308bf]
	(No symbol) [0x0x7ff7a7268792]
	(No symbol) [0x0x7ff7a7263293]
	(No symbol) [0x0x7ff7a7262359]
	(No symbol) [0x0x7ff7a71b4b05]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	(No symbol) [0x0x7ff7a71b3b00]
	GetHandleVerifier [0x0x7ff7a782bd88+4260984]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:29:02,737 - ERROR - 无法获取商品列表
2025-07-26 15:29:02,745 - WARNING - 第 15 个商品处理失败
2025-07-26 15:29:02,752 - INFO - 等待 5.2 秒后处理下一个商品
2025-07-26 15:29:07,940 - INFO - 开始处理第 16/40 个商品
2025-07-26 15:29:07,949 - ERROR - 获取商品列表失败: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72308bf]
	(No symbol) [0x0x7ff7a7268792]
	(No symbol) [0x0x7ff7a7263293]
	(No symbol) [0x0x7ff7a7262359]
	(No symbol) [0x0x7ff7a71b4b05]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	(No symbol) [0x0x7ff7a71b3b00]
	GetHandleVerifier [0x0x7ff7a782bd88+4260984]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:29:07,958 - ERROR - 无法获取商品列表
2025-07-26 15:29:07,963 - WARNING - 第 16 个商品处理失败
2025-07-26 15:29:07,971 - INFO - 等待 5.0 秒后处理下一个商品
2025-07-26 15:29:12,999 - INFO - 开始处理第 17/40 个商品
2025-07-26 15:29:13,006 - ERROR - 获取商品列表失败: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72308bf]
	(No symbol) [0x0x7ff7a7268792]
	(No symbol) [0x0x7ff7a7263293]
	(No symbol) [0x0x7ff7a7262359]
	(No symbol) [0x0x7ff7a71b4b05]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	(No symbol) [0x0x7ff7a71b3b00]
	GetHandleVerifier [0x0x7ff7a782bd88+4260984]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:29:13,015 - ERROR - 无法获取商品列表
2025-07-26 15:29:13,023 - WARNING - 第 17 个商品处理失败
2025-07-26 15:29:13,028 - INFO - 等待 6.9 秒后处理下一个商品
