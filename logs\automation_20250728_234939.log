2025-07-28 23:49:42,297 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059cda]
	(No symbol) [0x0x7ff69a061679]
	(No symbol) [0x0x7ff69a064a61]
	(No symbol) [0x0x7ff69a101e4b]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:49:42,298 - INFO - 防检测机制设置完成
2025-07-28 23:49:42,298 - INFO - 浏览器驱动初始化成功
2025-07-28 23:49:42,314 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:49:42,325 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:49:42,328 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:49:42,334 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:49:42,337 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:49:42,339 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:49:42,344 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:49:42,348 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:49:42,350 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:49:42,353 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:49:42,355 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:49:42,358 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:49:42,362 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:49:42,365 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:49:42,367 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:49:42,370 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:49:42,374 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:49:42,377 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:49:42,381 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:49:42,384 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:49:42,385 - INFO - Cookies已加载
2025-07-28 23:49:42,385 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?channel=global_zx&spm=a21ug5.29159167.6452766900.2ndview_zxmore_click
2025-07-28 23:49:48,042 - INFO - 当前User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-28 23:49:48,042 - INFO - 确认：使用桌面版User-Agent
2025-07-28 23:49:48,045 - INFO - 桌面版User-Agent设置完成: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-28 23:49:49,602 - INFO - 成功导航到目标页面
2025-07-28 23:49:49,603 - INFO - 登录成功！现在可以开始铺货任务
2025-07-28 23:49:52,169 - INFO - Cookies已保存
2025-07-28 23:49:54,425 - INFO - 浏览器已关闭
2025-07-28 23:49:55,804 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059cda]
	(No symbol) [0x0x7ff69a061679]
	(No symbol) [0x0x7ff69a064a61]
	(No symbol) [0x0x7ff69a101e4b]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:49:55,804 - INFO - 防检测机制设置完成
2025-07-28 23:49:55,804 - INFO - 浏览器驱动初始化成功
2025-07-28 23:49:55,805 - INFO - 开始自动化铺货流程（有界面模式）
2025-07-28 23:49:55,805 - INFO - 第一步：正在导航到目标页面...
2025-07-28 23:49:59,485 - INFO - 第一步：点击登录按钮
2025-07-28 23:49:59,506 - INFO - 找到登录按钮，使用选择器: //*[@id='login-form']/div[6]/button
2025-07-28 23:50:00,528 - INFO - 准备点击登录按钮: 标签=button, 文本='登录', 选择器=//*[@id='login-form']/div[6]/button
2025-07-28 23:50:00,541 - INFO - 准备点击元素: tag=button, text='登录', class='fm-button fm-submit password-l', id=''
2025-07-28 23:50:01,974 - INFO - 尝试ActionChains点击...
2025-07-28 23:50:02,381 - INFO - ActionChains点击成功
2025-07-28 23:50:02,704 - INFO - 登录按钮点击成功
2025-07-28 23:50:02,704 - INFO - 登录按钮点击成功
2025-07-28 23:50:05,705 - INFO - 第二步：获取账号信息...
2025-07-28 23:50:05,705 - INFO - 正在跳转到个人中心页面: https://jingya.taobao.com/?v=2
2025-07-28 23:50:30,452 - INFO - 尝试获取账号名称，使用XPath: //*[@id='workbench-user-tool-btn']/div/div[2]
2025-07-28 23:50:30,464 - INFO - 成功获取账号信息: 卓祥22店:冰淇淋
2025-07-28 23:50:30,464 - INFO - 获取到账号名称: 卓祥22店:冰淇淋
2025-07-28 23:50:30,465 - INFO - GUI界面已更新账号显示: 卓祥22店:冰淇淋
2025-07-28 23:50:30,466 - INFO - 第三步：开始读取20个商品信息...
2025-07-28 23:50:30,468 - INFO - 开始爬取商品信息，最大数量: 20
2025-07-28 23:50:33,549 - INFO - 正在爬取第 1 页商品信息...
2025-07-28 23:50:35,556 - INFO - 找到商品容器，开始爬取商品，最大数量: 20
2025-07-28 23:50:35,567 - INFO - 找到商品项 1，检查元素信息...
2025-07-28 23:50:35,578 - INFO - 商品项 1 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:50:35,581 - INFO - 商品项 1 直接获取到data-autolog: item_id=681778175573&path=search_goods_card&type=search
2025-07-28 23:50:35,581 - INFO - 商品项 1 data-autolog: item_id=681778175573&path=search_goods_card&type=search
2025-07-28 23:50:35,581 - INFO - 商品项 1 提取到item_id: 681778175573
2025-07-28 23:50:35,594 - INFO - 商品项 1 商品名称: 保税直供 Dove多芬磨砂膏石榴籽乳木果风味身体磨砂膏298g
2025-07-28 23:50:35,606 - INFO - 找到商品项 2，检查元素信息...
2025-07-28 23:50:35,613 - INFO - 商品项 2 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:50:35,616 - INFO - 商品项 2 直接获取到data-autolog: item_id=690841537159&path=search_goods_card&index=1&type=search
2025-07-28 23:50:35,616 - INFO - 商品项 2 data-autolog: item_id=690841537159&path=search_goods_card&index=1&type=search
2025-07-28 23:50:35,616 - INFO - 商品项 2 提取到item_id: 690841537159
2025-07-28 23:50:35,625 - INFO - 商品项 2 商品名称: 保税直供 凡士林Vaseline烟酰胺身体乳400ml 725ml无压泵款200ml
2025-07-28 23:50:35,636 - INFO - 找到商品项 3，检查元素信息...
2025-07-28 23:50:35,645 - INFO - 商品项 3 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:50:35,648 - INFO - 商品项 3 直接获取到data-autolog: item_id=789447097664&path=search_goods_card&index=2&type=search
2025-07-28 23:50:35,648 - INFO - 商品项 3 data-autolog: item_id=789447097664&path=search_goods_card&index=2&type=search
2025-07-28 23:50:35,648 - INFO - 商品项 3 提取到item_id: 789447097664
2025-07-28 23:50:35,657 - INFO - 商品项 3 商品名称: 【甄选】珂润Curel泡沫洗面奶150ml
2025-07-28 23:50:35,669 - INFO - 找到商品项 4，检查元素信息...
2025-07-28 23:50:35,676 - INFO - 商品项 4 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:50:35,679 - INFO - 商品项 4 直接获取到data-autolog: item_id=694682843417&path=search_goods_card&index=3&type=search
2025-07-28 23:50:35,680 - INFO - 商品项 4 data-autolog: item_id=694682843417&path=search_goods_card&index=3&type=search
2025-07-28 23:50:35,680 - INFO - 商品项 4 提取到item_id: 694682843417
2025-07-28 23:50:35,688 - INFO - 商品项 4 商品名称: 【可开授权】日台混发Fino发膜美容液护发素 发膜230g
2025-07-28 23:50:35,700 - INFO - 找到商品项 5，检查元素信息...
2025-07-28 23:50:35,708 - INFO - 商品项 5 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:50:35,711 - INFO - 商品项 5 直接获取到data-autolog: item_id=681082809892&path=search_goods_card&index=4&type=search
2025-07-28 23:50:35,711 - INFO - 商品项 5 data-autolog: item_id=681082809892&path=search_goods_card&index=4&type=search
2025-07-28 23:50:35,711 - INFO - 商品项 5 提取到item_id: 681082809892
2025-07-28 23:50:35,720 - INFO - 商品项 5 商品名称: 保税直供 Dove多芬大白碗润肤身体乳300ml 【1//2//3罐】
2025-07-28 23:50:35,731 - INFO - 找到商品项 6，检查元素信息...
2025-07-28 23:50:35,738 - INFO - 商品项 6 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:50:35,742 - INFO - 商品项 6 直接获取到data-autolog: item_id=694675776881&path=search_goods_card&index=5&type=search
2025-07-28 23:50:35,742 - INFO - 商品项 6 data-autolog: item_id=694675776881&path=search_goods_card&index=5&type=search
2025-07-28 23:50:35,742 - INFO - 商品项 6 提取到item_id: 694675776881
2025-07-28 23:50:35,751 - INFO - 商品项 6 商品名称: 保税直供 Pantene潘婷护发素三分钟奇迹多效修护发膜级护发素
2025-07-28 23:50:35,846 - INFO - 找到商品项 7，检查元素信息...
2025-07-28 23:50:35,862 - INFO - 商品项 7 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:50:35,867 - INFO - 商品项 7 直接获取到data-autolog: item_id=680729304287&path=search_goods_card&index=6&type=search
2025-07-28 23:50:35,868 - INFO - 商品项 7 data-autolog: item_id=680729304287&path=search_goods_card&index=6&type=search
2025-07-28 23:50:35,868 - INFO - 商品项 7 提取到item_id: 680729304287
2025-07-28 23:50:35,879 - INFO - 商品项 7 商品名称: 保税直供 多芬dove空气感无硅油 洗发水480g 护发素480g护发临期
2025-07-28 23:50:35,891 - INFO - 找到商品项 8，检查元素信息...
2025-07-28 23:50:35,900 - INFO - 商品项 8 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:50:35,903 - INFO - 商品项 8 直接获取到data-autolog: item_id=710355107054&path=search_goods_card&index=7&type=search
2025-07-28 23:50:35,903 - INFO - 商品项 8 data-autolog: item_id=710355107054&path=search_goods_card&index=7&type=search
2025-07-28 23:50:35,904 - INFO - 商品项 8 提取到item_id: 710355107054
2025-07-28 23:50:35,913 - INFO - 商品项 8 商品名称: 保税直供 Dove多芬洗发水护发素空气感洗发水480g 【护发素临期
2025-07-28 23:50:35,927 - INFO - 找到商品项 9，检查元素信息...
2025-07-28 23:50:35,935 - INFO - 商品项 9 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:50:35,938 - INFO - 商品项 9 直接获取到data-autolog: item_id=740463658412&path=search_goods_card&index=8&type=search
2025-07-28 23:50:35,938 - INFO - 商品项 9 data-autolog: item_id=740463658412&path=search_goods_card&index=8&type=search
2025-07-28 23:50:35,938 - INFO - 商品项 9 提取到item_id: 740463658412
2025-07-28 23:50:35,962 - INFO - 商品项 9 商品名称: 【主推链接】花王cape空气感定型喷雾50g/180g 编码：3305300000
2025-07-28 23:50:35,973 - INFO - 找到商品项 10，检查元素信息...
2025-07-28 23:50:35,981 - INFO - 商品项 10 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:50:35,984 - INFO - 商品项 10 直接获取到data-autolog: item_id=821067902127&path=search_goods_card&index=9&type=search
2025-07-28 23:50:35,984 - INFO - 商品项 10 data-autolog: item_id=821067902127&path=search_goods_card&index=9&type=search
2025-07-28 23:50:35,984 - INFO - 商品项 10 提取到item_id: 821067902127
2025-07-28 23:50:35,994 - INFO - 商品项 10 商品名称: 丹碧丝TAMPAX卫生棉条长导管式内置式纯棉月经棉条棉棒游泳卫生巾
2025-07-28 23:50:36,005 - INFO - 找到商品项 11，检查元素信息...
2025-07-28 23:50:36,014 - INFO - 商品项 11 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:50:36,017 - INFO - 商品项 11 直接获取到data-autolog: item_id=789835300215&path=search_goods_card&index=10&type=search
2025-07-28 23:50:36,017 - INFO - 商品项 11 data-autolog: item_id=789835300215&path=search_goods_card&index=10&type=search
2025-07-28 23:50:36,017 - INFO - 商品项 11 提取到item_id: 789835300215
2025-07-28 23:50:36,028 - INFO - 商品项 11 商品名称: 【保税】雅漾喷雾爽肤水300ml
2025-07-28 23:50:36,040 - INFO - 找到商品项 12，检查元素信息...
2025-07-28 23:50:36,047 - INFO - 商品项 12 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:50:36,050 - INFO - 商品项 12 直接获取到data-autolog: item_id=843747605382&path=search_goods_card&index=11&type=search
2025-07-28 23:50:36,050 - INFO - 商品项 12 data-autolog: item_id=843747605382&path=search_goods_card&index=11&type=search
2025-07-28 23:50:36,050 - INFO - 商品项 12 提取到item_id: 843747605382
2025-07-28 23:50:36,076 - INFO - 商品项 12 商品名称: 倩碧紫胖子卸妆膏125ml/瓶
2025-07-28 23:50:36,088 - INFO - 找到商品项 13，检查元素信息...
2025-07-28 23:50:36,097 - INFO - 商品项 13 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:50:36,100 - INFO - 商品项 13 直接获取到data-autolog: item_id=675816539149&path=search_goods_card&index=12&type=search
2025-07-28 23:50:36,100 - INFO - 商品项 13 data-autolog: item_id=675816539149&path=search_goods_card&index=12&type=search
2025-07-28 23:50:36,100 - INFO - 商品项 13 提取到item_id: 675816539149
2025-07-28 23:50:36,109 - INFO - 商品项 13 商品名称: 新加坡Vicopyl维可保益生菌(Pylopass/拒绝幽门菌/抑口臭/调肠胃)
2025-07-28 23:50:36,121 - INFO - 找到商品项 14，检查元素信息...
2025-07-28 23:50:36,127 - INFO - 商品项 14 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:50:36,130 - INFO - 商品项 14 直接获取到data-autolog: item_id=846634805775&path=search_goods_card&index=13&type=search
2025-07-28 23:50:36,130 - INFO - 商品项 14 data-autolog: item_id=846634805775&path=search_goods_card&index=13&type=search
2025-07-28 23:50:36,130 - INFO - 商品项 14 提取到item_id: 846634805775
2025-07-28 23:50:36,139 - INFO - 商品项 14 商品名称: 【甄选】cow牛乳石碱原味/玫瑰花香 瓶装480ml
2025-07-28 23:50:36,151 - INFO - 找到商品项 15，检查元素信息...
2025-07-28 23:50:36,158 - INFO - 商品项 15 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:50:36,162 - INFO - 商品项 15 直接获取到data-autolog: item_id=852510575114&path=search_goods_card&index=14&type=search
2025-07-28 23:50:36,162 - INFO - 商品项 15 data-autolog: item_id=852510575114&path=search_goods_card&index=14&type=search
2025-07-28 23:50:36,162 - INFO - 商品项 15 提取到item_id: 852510575114
2025-07-28 23:50:36,170 - INFO - 商品项 15 商品名称: Nars 纳斯 红壳蜜粉饼16g（含粉扑）、Nars纳斯裸光蜜粉饼10g
2025-07-28 23:50:36,182 - INFO - 找到商品项 16，检查元素信息...
2025-07-28 23:50:36,191 - INFO - 商品项 16 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:50:36,193 - INFO - 商品项 16 直接获取到data-autolog: item_id=743442966826&path=search_goods_card&index=15&type=search
2025-07-28 23:50:36,193 - INFO - 商品项 16 data-autolog: item_id=743442966826&path=search_goods_card&index=15&type=search
2025-07-28 23:50:36,193 - INFO - 商品项 16 提取到item_id: 743442966826
2025-07-28 23:50:36,203 - INFO - 商品项 16 商品名称: ESI卡路里拜拜片白芸豆阻断片大餐救星膳食纤维身材热控管理
2025-07-28 23:50:36,215 - INFO - 找到商品项 17，检查元素信息...
2025-07-28 23:50:36,221 - INFO - 商品项 17 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:50:36,224 - INFO - 商品项 17 直接获取到data-autolog: item_id=848934676698&path=search_goods_card&index=16&type=search
2025-07-28 23:50:36,224 - INFO - 商品项 17 data-autolog: item_id=848934676698&path=search_goods_card&index=16&type=search
2025-07-28 23:50:36,224 - INFO - 商品项 17 提取到item_id: 848934676698
2025-07-28 23:50:36,234 - INFO - 商品项 17 商品名称: Estee Lauder 雅诗兰黛持妆粉底液30ml 2C0 /1w1/1C1
2025-07-28 23:50:36,245 - INFO - 找到商品项 18，检查元素信息...
2025-07-28 23:50:36,253 - INFO - 商品项 18 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:50:36,257 - INFO - 商品项 18 直接获取到data-autolog: item_id=693758717931&path=search_goods_card&index=17&type=search
2025-07-28 23:50:36,257 - INFO - 商品项 18 data-autolog: item_id=693758717931&path=search_goods_card&index=17&type=search
2025-07-28 23:50:36,257 - INFO - 商品项 18 提取到item_id: 693758717931
2025-07-28 23:50:36,265 - INFO - 商品项 18 商品名称: 保税直供 资生堂fino高效渗透发膜 230g （1/2/3罐）日版
2025-07-28 23:50:36,276 - INFO - 找到商品项 19，检查元素信息...
2025-07-28 23:50:36,284 - INFO - 商品项 19 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:50:36,286 - INFO - 商品项 19 直接获取到data-autolog: item_id=691142128229&path=search_goods_card&index=18&type=search
2025-07-28 23:50:36,286 - INFO - 商品项 19 data-autolog: item_id=691142128229&path=search_goods_card&index=18&type=search
2025-07-28 23:50:36,286 - INFO - 商品项 19 提取到item_id: 691142128229
2025-07-28 23:50:36,296 - INFO - 商品项 19 商品名称: 保税 日版Dove多芬洁面洗面奶新版 正装瓶150ml 补充袋125ml 甄选
2025-07-28 23:50:36,306 - INFO - 找到商品项 20，检查元素信息...
2025-07-28 23:50:36,313 - INFO - 商品项 20 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:50:36,317 - INFO - 商品项 20 直接获取到data-autolog: item_id=690739721578&path=search_goods_card&index=19&type=search
2025-07-28 23:50:36,317 - INFO - 商品项 20 data-autolog: item_id=690739721578&path=search_goods_card&index=19&type=search
2025-07-28 23:50:36,317 - INFO - 商品项 20 提取到item_id: 690739721578
2025-07-28 23:50:36,341 - INFO - 商品项 20 商品名称: 保税直供 SUNSILK夏士莲椰子洗发水//护发素450ml 泰产
2025-07-28 23:50:36,342 - INFO - 已爬取到 20 个商品，停止当前页面爬取
2025-07-28 23:50:36,342 - INFO - 当前页面爬取完成，找到 20 个商品
2025-07-28 23:50:36,342 - INFO - 第 1 页找到 20 个商品，总计: 20
2025-07-28 23:50:36,342 - INFO - 已达到最大商品数量 20，停止爬取
2025-07-28 23:50:36,342 - INFO - 爬取完成，共找到 20 个商品
2025-07-28 23:50:36,342 - INFO - 成功读取到 20 个商品（目标20个），开始铺货任务
2025-07-28 23:50:36,342 - INFO - 第四步：开始处理商品...
2025-07-28 23:50:36,353 - INFO - 已读取 20 个商品，目标成功铺货 3 个商品
2025-07-28 23:50:36,354 - INFO - 当前批次大小: 15
2025-07-28 23:50:36,354 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=681778175573
2025-07-28 23:50:39,464 - INFO - 页面加载完成
2025-07-28 23:50:39,465 - INFO - 模拟阅读行为，停顿 2.3 秒
2025-07-28 23:50:42,480 - INFO - 添加额外延迟: 1.6秒
2025-07-28 23:50:44,702 - INFO - 开始第 1 次铺货尝试...
2025-07-28 23:50:44,702 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:50:44,703 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:50:44,713 - INFO - span innerHTML: 立即铺货
2025-07-28 23:50:44,734 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-28 23:50:44,734 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-28 23:50:44,748 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-28 23:50:46,131 - INFO - 尝试ActionChains点击...
2025-07-28 23:50:46,426 - INFO - ActionChains点击成功
2025-07-28 23:50:46,805 - INFO - 铺货按钮点击成功
2025-07-28 23:50:48,820 - INFO - 使用配置的XPath找到确认按钮
2025-07-28 23:50:48,836 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-28 23:50:49,898 - INFO - 尝试ActionChains点击...
2025-07-28 23:50:50,178 - INFO - ActionChains点击成功
2025-07-28 23:50:50,605 - INFO - 确认按钮点击成功
2025-07-28 23:50:52,606 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-28 23:50:52,614 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-28 23:50:52,614 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:50:53,621 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-28 23:50:53,621 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:50:54,629 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-28 23:50:54,630 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:50:55,638 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-28 23:50:55,638 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:50:56,647 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-07-28 23:50:56,647 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:50:57,655 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-07-28 23:50:57,655 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:50:58,662 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-07-28 23:50:58,662 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:50:59,671 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-07-28 23:50:59,671 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:51:00,681 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-07-28 23:51:00,681 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:51:01,689 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-07-28 23:51:01,689 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:51:02,690 - INFO - 等待超时，进行最后一次检查...
2025-07-28 23:51:02,699 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-07-28 23:51:02,699 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-07-28 23:51:02,699 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-07-28 23:51:02,699 - INFO - 检测是否出现滑块验证...
2025-07-28 23:51:02,707 - INFO - 在主文档中未找到滑块，检查iframe...
2025-07-28 23:51:02,714 - INFO - 找到 1 个iframe
2025-07-28 23:51:02,715 - INFO - 检查第 1 个iframe...
2025-07-28 23:51:02,736 - INFO - 在第 1 个iframe中检测到滑块验证，开始处理...
2025-07-28 23:51:02,737 - INFO - 开始处理滑块验证...
2025-07-28 23:51:02,750 - INFO - 滑块容器宽度: 300px
2025-07-28 23:51:02,750 - INFO - 计算拖拽距离: 268px (比例: 0.89)
2025-07-28 23:51:02,750 - INFO - 开始匀速滑块操作...
2025-07-28 23:51:02,751 - INFO - 构建滑块操作链，拖拽距离: 268px
2025-07-28 23:51:02,751 - INFO - 执行匀速滑块拖拽...
2025-07-28 23:51:06,432 - INFO - 匀速滑块操作完成
2025-07-28 23:51:06,436 - INFO - 滑块验证成功，滑块已消失
2025-07-28 23:51:06,437 - INFO - 滑块验证处理成功，准备重试铺货
2025-07-28 23:51:08,438 - INFO - 开始第 2 次铺货尝试...
2025-07-28 23:51:08,438 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:51:08,438 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:51:08,447 - INFO - span innerHTML: 立即铺货
2025-07-28 23:51:18,676 - WARNING - 等待元素可点击超时: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:51:18,677 - WARNING - 使用配置的XPath未找到添加商品按钮
2025-07-28 23:51:18,677 - ERROR - 未找到铺货按钮
2025-07-28 23:51:18,677 - WARNING - 第 1 个商品处理失败（不计入成功数量）
2025-07-28 23:51:18,677 - INFO - 商品处理失败，立即处理下一个商品
2025-07-28 23:51:18,691 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=690841537159
2025-07-28 23:51:22,276 - INFO - 页面加载完成
2025-07-28 23:51:22,276 - INFO - 模拟阅读行为，停顿 2.2 秒
2025-07-28 23:51:25,239 - INFO - 开始第 1 次铺货尝试...
2025-07-28 23:51:25,239 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:51:25,239 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:51:25,246 - INFO - span innerHTML: 立即铺货
2025-07-28 23:51:25,266 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-28 23:51:25,267 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-28 23:51:25,281 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-28 23:51:26,350 - INFO - 尝试ActionChains点击...
2025-07-28 23:51:26,636 - INFO - ActionChains点击成功
2025-07-28 23:51:26,908 - INFO - 铺货按钮点击成功
2025-07-28 23:51:28,924 - INFO - 使用配置的XPath找到确认按钮
2025-07-28 23:51:28,936 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-28 23:51:30,215 - INFO - 尝试ActionChains点击...
2025-07-28 23:51:30,482 - INFO - ActionChains点击成功
2025-07-28 23:51:30,863 - INFO - 确认按钮点击成功
2025-07-28 23:51:32,863 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-28 23:51:32,872 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-28 23:51:32,872 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:51:33,880 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-28 23:51:33,880 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:51:34,888 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-28 23:51:34,888 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:51:35,896 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-28 23:51:35,896 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:51:36,904 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-07-28 23:51:36,904 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:51:37,913 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-07-28 23:51:37,913 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:51:38,922 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-07-28 23:51:38,922 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:51:39,930 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-07-28 23:51:39,930 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:51:40,938 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-07-28 23:51:40,939 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:51:41,946 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-07-28 23:51:41,946 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:51:42,947 - INFO - 等待超时，进行最后一次检查...
2025-07-28 23:51:42,954 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-07-28 23:51:42,954 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-07-28 23:51:42,954 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-07-28 23:51:42,955 - INFO - 检测是否出现滑块验证...
2025-07-28 23:51:42,965 - INFO - 在主文档中未找到滑块，检查iframe...
2025-07-28 23:51:42,971 - INFO - 找到 1 个iframe
2025-07-28 23:51:42,971 - INFO - 检查第 1 个iframe...
2025-07-28 23:51:42,992 - INFO - 在第 1 个iframe中检测到滑块验证，开始处理...
2025-07-28 23:51:42,992 - INFO - 开始处理滑块验证...
2025-07-28 23:51:43,005 - INFO - 滑块容器宽度: 300px
2025-07-28 23:51:43,005 - INFO - 计算拖拽距离: 264px (比例: 0.88)
2025-07-28 23:51:43,006 - INFO - 开始匀速滑块操作...
2025-07-28 23:51:43,006 - INFO - 构建滑块操作链，拖拽距离: 264px
2025-07-28 23:51:43,006 - INFO - 执行匀速滑块拖拽...
2025-07-28 23:51:46,635 - INFO - 匀速滑块操作完成
2025-07-28 23:51:46,639 - INFO - 滑块验证成功，滑块已消失
2025-07-28 23:51:46,640 - INFO - 滑块验证处理成功，准备重试铺货
2025-07-28 23:51:48,642 - INFO - 开始第 2 次铺货尝试...
2025-07-28 23:51:48,642 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:51:48,642 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:51:48,650 - INFO - span innerHTML: 立即铺货
2025-07-28 23:51:58,855 - WARNING - 等待元素可点击超时: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:51:58,855 - WARNING - 使用配置的XPath未找到添加商品按钮
2025-07-28 23:51:58,855 - ERROR - 未找到铺货按钮
2025-07-28 23:51:58,855 - WARNING - 第 2 个商品处理失败（不计入成功数量）
2025-07-28 23:51:58,856 - INFO - 商品处理失败，立即处理下一个商品
2025-07-28 23:51:58,867 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=789447097664
2025-07-28 23:52:01,989 - INFO - 页面加载完成
2025-07-28 23:52:01,989 - INFO - 模拟阅读行为，停顿 2.6 秒
