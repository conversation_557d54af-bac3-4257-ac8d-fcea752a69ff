2025-07-26 13:59:52,817 - INFO - 浏览器驱动初始化成功
2025-07-26 13:59:52,865 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 13:59:52,877 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 13:59:52,890 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 13:59:52,903 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 13:59:52,912 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 13:59:52,921 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 13:59:52,931 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 13:59:52,939 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 13:59:52,948 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 13:59:52,960 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 13:59:52,967 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 13:59:52,974 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 13:59:52,984 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 13:59:52,995 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 13:59:53,005 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 13:59:53,014 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 13:59:53,024 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 13:59:53,034 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 13:59:53,043 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 13:59:53,052 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 13:59:53,060 - INFO - Cookies已加载
2025-07-26 13:59:53,073 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?channel=global_zx&spm=a21ug5.29159167.6452766900.2ndview_zxmore_click
2025-07-26 13:59:57,385 - INFO - 成功导航到目标页面
2025-07-26 13:59:57,393 - INFO - 登录成功！现在可以开始铺货任务
2025-07-26 14:00:02,311 - INFO - 开始测试仓库移除功能...
2025-07-26 14:00:02,319 - INFO - 开始执行批量管理操作...
2025-07-26 14:00:02,328 - INFO - 正在导航到管理页面: https://myseller.taobao.com/home.htm/SellManage/all?current=1&pageSize=20
2025-07-26 14:00:04,076 - INFO - 页面加载完成
2025-07-26 14:00:07,438 - INFO - 已点击全选复选框
2025-07-26 14:00:07,450 - INFO - 等待 2.8 秒
2025-07-26 14:00:10,835 - INFO - 已点击批量编辑按钮
2025-07-26 14:00:10,846 - INFO - 等待 1.6 秒
2025-07-26 14:00:13,015 - INFO - 已关闭弹窗: //div[contains(@class, 'close')]
2025-07-26 14:00:13,586 - INFO - 批量管理操作完成
2025-07-26 14:00:16,594 - INFO - 仓库移除测试成功完成！
2025-07-26 14:02:49,369 - INFO - 开始自动化铺货流程
2025-07-26 14:02:49,379 - INFO - 正在返回主页面...
2025-07-26 14:02:53,024 - INFO - 已返回主页面
2025-07-26 14:02:58,548 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到 160 个有效商品项
2025-07-26 14:02:59,115 - INFO - 去重后找到 160 个唯一商品项
2025-07-26 14:02:59,131 - INFO - 商品 1: class='tfx-item', data-spm='search_goods_card_tfx_item_0', 位置={'x': 358, 'y': 737}
2025-07-26 14:02:59,150 - INFO - 商品 2: class='tfx-item--top', data-spm='无data-spm', 位置={'x': 358, 'y': 738}
2025-07-26 14:02:59,169 - INFO - 商品 3: class='tfx-item__info', data-spm='无data-spm', 位置={'x': 358, 'y': 958}
2025-07-26 14:02:59,188 - INFO - 商品 4: class='tfx-item__title', data-spm='无data-spm', 位置={'x': 364, 'y': 966}
2025-07-26 14:02:59,208 - INFO - 商品 5: class='tfx-item', data-spm='search_goods_card_tfx_item_1', 位置={'x': 595, 'y': 737}
2025-07-26 14:02:59,218 - INFO - 找到 160 个商品，将处理 20 个
2025-07-26 14:02:59,229 - INFO - 当前批次大小: 11
2025-07-26 14:02:59,239 - INFO - 开始处理第 1/160 个商品
2025-07-26 14:03:04,465 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到 160 个有效商品项
2025-07-26 14:03:05,072 - INFO - 去重后找到 160 个唯一商品项
2025-07-26 14:03:05,092 - INFO - 商品 1: class='tfx-item', data-spm='search_goods_card_tfx_item_0', 位置={'x': 358, 'y': 737}
2025-07-26 14:03:05,111 - INFO - 商品 2: class='tfx-item--top', data-spm='无data-spm', 位置={'x': 358, 'y': 738}
2025-07-26 14:03:05,130 - INFO - 商品 3: class='tfx-item__info', data-spm='无data-spm', 位置={'x': 358, 'y': 958}
2025-07-26 14:03:05,148 - INFO - 商品 4: class='tfx-item__title', data-spm='无data-spm', 位置={'x': 364, 'y': 966}
2025-07-26 14:03:05,172 - INFO - 商品 5: class='tfx-item', data-spm='search_goods_card_tfx_item_1', 位置={'x': 595, 'y': 737}
2025-07-26 14:03:05,189 - INFO - 第 1 个商品已铺货，跳过
2025-07-26 14:03:05,201 - WARNING - 第 1 个商品处理失败
2025-07-26 14:03:05,213 - INFO - 等待 7.5 秒后处理下一个商品
2025-07-26 14:03:12,749 - INFO - 开始处理第 2/160 个商品
2025-07-26 14:03:18,010 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到 160 个有效商品项
2025-07-26 14:03:18,599 - INFO - 去重后找到 160 个唯一商品项
2025-07-26 14:03:18,619 - INFO - 商品 1: class='tfx-item', data-spm='search_goods_card_tfx_item_0', 位置={'x': 358, 'y': 737}
2025-07-26 14:03:18,639 - INFO - 商品 2: class='tfx-item--top', data-spm='无data-spm', 位置={'x': 358, 'y': 738}
2025-07-26 14:03:18,661 - INFO - 商品 3: class='tfx-item__info', data-spm='无data-spm', 位置={'x': 358, 'y': 958}
2025-07-26 14:03:18,680 - INFO - 商品 4: class='tfx-item__title', data-spm='无data-spm', 位置={'x': 364, 'y': 966}
2025-07-26 14:03:18,702 - INFO - 商品 5: class='tfx-item', data-spm='search_goods_card_tfx_item_1', 位置={'x': 595, 'y': 737}
2025-07-26 14:03:18,715 - INFO - 第 2 个商品已铺货，跳过
2025-07-26 14:03:18,728 - WARNING - 第 2 个商品处理失败
2025-07-26 14:03:18,740 - INFO - 等待 5.0 秒后处理下一个商品
2025-07-26 14:03:23,707 - INFO - 开始处理第 3/160 个商品
2025-07-26 14:03:29,087 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到 160 个有效商品项
2025-07-26 14:03:29,699 - INFO - 去重后找到 160 个唯一商品项
2025-07-26 14:03:29,719 - INFO - 商品 1: class='tfx-item', data-spm='search_goods_card_tfx_item_0', 位置={'x': 358, 'y': 737}
2025-07-26 14:03:29,739 - INFO - 商品 2: class='tfx-item--top', data-spm='无data-spm', 位置={'x': 358, 'y': 738}
2025-07-26 14:03:29,757 - INFO - 商品 3: class='tfx-item__info', data-spm='无data-spm', 位置={'x': 358, 'y': 958}
2025-07-26 14:03:29,778 - INFO - 商品 4: class='tfx-item__title', data-spm='无data-spm', 位置={'x': 364, 'y': 966}
2025-07-26 14:03:29,799 - INFO - 商品 5: class='tfx-item', data-spm='search_goods_card_tfx_item_1', 位置={'x': 595, 'y': 737}
2025-07-26 14:03:29,836 - INFO - 商品 3 信息: 文本='保税直供 Dove多芬磨砂膏石榴籽乳木果风味身体磨砂膏298g
采购价¥35.00销量11.2万+件', 位置={'x': 358, 'y': 958}
2025-07-26 14:03:32,984 - INFO - 使用普通点击成功点击第 3 个商品
2025-07-26 14:03:32,998 - INFO - 已点击第 3 个商品，等待页面响应...
2025-07-26 14:03:37,014 - INFO - 已切换到新标签页
2025-07-26 14:03:37,024 - INFO - 页面加载完成
2025-07-26 14:03:37,088 - INFO - 找到 0 个iframe
2025-07-26 14:03:37,099 - INFO - 未找到包含目标元素的iframe，保持在主文档
2025-07-26 14:03:42,284 - WARNING - 等待元素可点击超时: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-26 14:03:44,327 - WARNING - 等待元素可点击超时: //button[contains(text(), '添加') or contains(text(), '加入') or contains(text(), '铺货')]
2025-07-26 14:03:46,385 - WARNING - 等待元素可点击超时: //button[contains(@class, 'add') or contains(@class, 'submit')]
2025-07-26 14:03:48,458 - WARNING - 等待元素可点击超时: //a[contains(text(), '添加') or contains(text(), '加入') or contains(text(), '铺货')]
2025-07-26 14:03:50,517 - WARNING - 等待元素可点击超时: //div[contains(@class, 'add-btn')]//button
2025-07-26 14:03:52,606 - WARNING - 等待元素可点击超时: //button[contains(@class, 'primary')]
2025-07-26 14:03:54,647 - WARNING - 等待元素可点击超时: //*[@role='button'][contains(text(), '添加') or contains(text(), '加入')]
2025-07-26 14:03:56,690 - WARNING - 等待元素可点击超时: //button[contains(text(), '立即')]
2025-07-26 14:03:58,737 - WARNING - 等待元素可点击超时: //a[contains(text(), '立即')]
2025-07-26 14:03:58,788 - WARNING - 未能找到添加商品按钮
2025-07-26 14:03:58,802 - INFO - 操作失败，2秒后重试 (尝试 1/2)
2025-07-26 14:04:01,788 - INFO - 用户请求停止自动化流程
2025-07-26 14:04:05,959 - WARNING - 等待元素可点击超时: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-26 14:04:08,028 - WARNING - 等待元素可点击超时: //button[contains(text(), '添加') or contains(text(), '加入') or contains(text(), '铺货')]
2025-07-26 14:04:10,098 - WARNING - 等待元素可点击超时: //button[contains(@class, 'add') or contains(@class, 'submit')]
2025-07-26 14:04:12,183 - WARNING - 等待元素可点击超时: //a[contains(text(), '添加') or contains(text(), '加入') or contains(text(), '铺货')]
2025-07-26 14:04:14,235 - WARNING - 等待元素可点击超时: //div[contains(@class, 'add-btn')]//button
2025-07-26 14:04:16,345 - WARNING - 等待元素可点击超时: //button[contains(@class, 'primary')]
2025-07-26 14:04:18,422 - WARNING - 等待元素可点击超时: //*[@role='button'][contains(text(), '添加') or contains(text(), '加入')]
2025-07-26 14:04:20,503 - WARNING - 等待元素可点击超时: //button[contains(text(), '立即')]
2025-07-26 14:04:22,577 - WARNING - 等待元素可点击超时: //a[contains(text(), '立即')]
2025-07-26 14:04:22,631 - WARNING - 未能找到添加商品按钮
2025-07-26 14:04:22,645 - ERROR - 操作失败，已达到最大重试次数 (2)
2025-07-26 14:04:22,663 - ERROR - 无法找到或点击添加商品按钮
2025-07-26 14:04:22,741 - INFO - 已关闭当前标签页并切换回主页面
2025-07-26 14:04:22,757 - WARNING - 第 3 个商品处理失败
2025-07-26 14:04:22,772 - INFO - 等待 7.0 秒后处理下一个商品
2025-07-26 14:04:29,823 - INFO - 用户停止了流程
2025-07-26 14:04:29,838 - INFO - 自动化流程已停止
