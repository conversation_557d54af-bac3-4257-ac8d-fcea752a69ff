2025-07-26 14:48:36,027 - INFO - 使用固定桌面版User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-26 14:48:37,422 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9cda]
	(No symbol) [0x0x7ff7a71f1679]
	(No symbol) [0x0x7ff7a71f4a61]
	(No symbol) [0x0x7ff7a7291e4b]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:48:37,430 - INFO - 防检测机制设置完成
2025-07-26 14:48:37,438 - INFO - 浏览器驱动初始化成功
2025-07-26 14:48:37,453 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:48:37,468 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:48:37,477 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:48:37,488 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:48:37,495 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:48:37,511 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:48:37,520 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:48:37,533 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:48:37,542 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:48:37,551 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:48:37,560 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:48:37,569 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:48:37,582 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:48:37,593 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:48:37,602 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:48:37,610 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:48:37,620 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:48:37,631 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:48:37,641 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:48:37,650 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:48:37,659 - INFO - Cookies已加载
2025-07-26 14:48:37,674 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?channel=global_zx&spm=a21ug5.29159167.6452766900.2ndview_zxmore_click
2025-07-26 14:48:37,684 - INFO - 添加额外延迟: 2.0秒
2025-07-26 14:48:45,621 - INFO - 当前User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-26 14:48:45,638 - INFO - 确认：使用桌面版User-Agent
2025-07-26 14:48:45,657 - INFO - 桌面版User-Agent设置完成: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-26 14:48:45,711 - INFO - 成功导航到目标页面
2025-07-26 14:48:45,728 - INFO - 登录成功！现在可以开始铺货任务
2025-07-26 14:48:49,095 - INFO - 开始自动化铺货流程
2025-07-26 14:48:49,118 - INFO - 正在返回主页面...
2025-07-26 14:48:53,007 - INFO - 已返回主页面
2025-07-26 14:48:53,034 - INFO - 找到 40 个直接子div
2025-07-26 14:48:53,079 - INFO - 商品 1: 位置={'x': 306, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=681778175573&path=search_goods_card&type=s
2025-07-26 14:48:53,112 - INFO - 商品 2: 位置={'x': 543, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=690841537159&path=search_goods_card&index=
2025-07-26 14:48:53,150 - INFO - 商品 3: 位置={'x': 780, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=789447097664&path=search_goods_card&index=
2025-07-26 14:48:53,206 - INFO - 商品 4: 位置={'x': 1017, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=694682843417&path=search_goods_card&index=
2025-07-26 14:48:53,242 - INFO - 商品 5: 位置={'x': 1254, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=681082809892&path=search_goods_card&index=
2025-07-26 14:48:53,280 - INFO - 商品 6: 位置={'x': 306, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=694675776881&path=search_goods_card&index=
2025-07-26 14:48:53,324 - INFO - 商品 7: 位置={'x': 543, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=680729304287&path=search_goods_card&index=
2025-07-26 14:48:53,365 - INFO - 商品 8: 位置={'x': 780, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=710355107054&path=search_goods_card&index=
2025-07-26 14:48:53,409 - INFO - 商品 9: 位置={'x': 1017, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=740463658412&path=search_goods_card&index=
2025-07-26 14:48:53,450 - INFO - 商品 10: 位置={'x': 1254, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=821067902127&path=search_goods_card&index=
2025-07-26 14:48:53,496 - INFO - 商品 11: 位置={'x': 306, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=789835300215&path=search_goods_card&index=
2025-07-26 14:48:53,557 - INFO - 商品 12: 位置={'x': 543, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=843747605382&path=search_goods_card&index=
2025-07-26 14:48:53,599 - INFO - 商品 13: 位置={'x': 780, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=675816539149&path=search_goods_card&index=
2025-07-26 14:48:53,641 - INFO - 商品 14: 位置={'x': 1017, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=846634805775&path=search_goods_card&index=
2025-07-26 14:48:53,681 - INFO - 商品 15: 位置={'x': 1254, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=852510575114&path=search_goods_card&index=
2025-07-26 14:48:53,742 - INFO - 商品 16: 位置={'x': 306, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=743442966826&path=search_goods_card&index=
2025-07-26 14:48:53,785 - INFO - 商品 17: 位置={'x': 543, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=848934676698&path=search_goods_card&index=
2025-07-26 14:48:53,826 - INFO - 商品 18: 位置={'x': 780, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=693758717931&path=search_goods_card&index=
2025-07-26 14:48:53,867 - INFO - 商品 19: 位置={'x': 1017, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=691142128229&path=search_goods_card&index=
2025-07-26 14:48:53,910 - INFO - 商品 20: 位置={'x': 1254, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=690739721578&path=search_goods_card&index=
2025-07-26 14:48:53,955 - INFO - 商品 21: 位置={'x': 306, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=691918824108&path=search_goods_card&index=
2025-07-26 14:48:53,996 - INFO - 商品 22: 位置={'x': 543, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=875045022715&path=search_goods_card&index=
2025-07-26 14:48:54,038 - INFO - 商品 23: 位置={'x': 780, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=873911671570&path=search_goods_card&index=
2025-07-26 14:48:54,080 - INFO - 商品 24: 位置={'x': 1017, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=722937163980&path=search_goods_card&index=
2025-07-26 14:48:54,120 - INFO - 商品 25: 位置={'x': 1254, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=739090443937&path=search_goods_card&index=
2025-07-26 14:48:54,163 - INFO - 商品 26: 位置={'x': 306, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=777862601010&path=search_goods_card&index=
2025-07-26 14:48:54,220 - INFO - 商品 27: 位置={'x': 543, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=686870174450&path=search_goods_card&index=
2025-07-26 14:48:54,268 - INFO - 商品 28: 位置={'x': 780, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=814542665480&path=search_goods_card&index=
2025-07-26 14:48:54,310 - INFO - 商品 29: 位置={'x': 1017, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=852625718015&path=search_goods_card&index=
2025-07-26 14:48:54,352 - INFO - 商品 30: 位置={'x': 1254, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=681472049289&path=search_goods_card&index=
2025-07-26 14:48:54,409 - INFO - 商品 31: 位置={'x': 306, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=683142617961&path=search_goods_card&index=
2025-07-26 14:48:54,455 - INFO - 商品 32: 位置={'x': 543, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=763659951211&path=search_goods_card&index=
2025-07-26 14:48:54,498 - INFO - 商品 33: 位置={'x': 780, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=675478966678&path=search_goods_card&index=
2025-07-26 14:48:54,539 - INFO - 商品 34: 位置={'x': 1017, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=800365611234&path=search_goods_card&index=
2025-07-26 14:48:54,583 - INFO - 商品 35: 位置={'x': 1254, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=896721954982&path=search_goods_card&index=
2025-07-26 14:48:54,625 - INFO - 商品 36: 位置={'x': 306, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=795389643023&path=search_goods_card&index=
2025-07-26 14:48:54,666 - INFO - 商品 37: 位置={'x': 543, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=689584396900&path=search_goods_card&index=
2025-07-26 14:48:54,709 - INFO - 商品 38: 位置={'x': 780, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=788516102627&path=search_goods_card&index=
2025-07-26 14:48:54,752 - INFO - 商品 39: 位置={'x': 1017, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=825025224622&path=search_goods_card&index=
2025-07-26 14:48:54,795 - INFO - 商品 40: 位置={'x': 1254, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=842638336638&path=search_goods_card&index=
2025-07-26 14:48:54,831 - INFO - 找到 40 个有效的商品容器
2025-07-26 14:48:54,860 - INFO - 找到 40 个商品，将处理 20 个
2025-07-26 14:48:54,890 - INFO - 当前批次大小: 13
2025-07-26 14:48:54,914 - INFO - 开始处理第 1/40 个商品
2025-07-26 14:48:54,941 - INFO - 找到 40 个直接子div
2025-07-26 14:48:54,982 - INFO - 商品 1: 位置={'x': 306, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=681778175573&path=search_goods_card&type=s
2025-07-26 14:48:55,018 - INFO - 商品 2: 位置={'x': 543, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=690841537159&path=search_goods_card&index=
2025-07-26 14:48:55,057 - INFO - 商品 3: 位置={'x': 780, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=789447097664&path=search_goods_card&index=
2025-07-26 14:48:55,165 - INFO - 商品 4: 位置={'x': 1017, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=694682843417&path=search_goods_card&index=
2025-07-26 14:48:55,215 - INFO - 商品 5: 位置={'x': 1254, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=681082809892&path=search_goods_card&index=
2025-07-26 14:48:55,279 - INFO - 商品 6: 位置={'x': 306, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=694675776881&path=search_goods_card&index=
2025-07-26 14:48:55,322 - INFO - 商品 7: 位置={'x': 543, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=680729304287&path=search_goods_card&index=
2025-07-26 14:48:55,367 - INFO - 商品 8: 位置={'x': 780, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=710355107054&path=search_goods_card&index=
2025-07-26 14:48:55,415 - INFO - 商品 9: 位置={'x': 1017, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=740463658412&path=search_goods_card&index=
2025-07-26 14:48:55,469 - INFO - 商品 10: 位置={'x': 1254, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=821067902127&path=search_goods_card&index=
2025-07-26 14:48:55,520 - INFO - 商品 11: 位置={'x': 306, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=789835300215&path=search_goods_card&index=
2025-07-26 14:48:55,570 - INFO - 商品 12: 位置={'x': 543, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=843747605382&path=search_goods_card&index=
2025-07-26 14:48:55,618 - INFO - 商品 13: 位置={'x': 780, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=675816539149&path=search_goods_card&index=
2025-07-26 14:48:55,666 - INFO - 商品 14: 位置={'x': 1017, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=846634805775&path=search_goods_card&index=
2025-07-26 14:48:55,712 - INFO - 商品 15: 位置={'x': 1254, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=852510575114&path=search_goods_card&index=
2025-07-26 14:48:55,759 - INFO - 商品 16: 位置={'x': 306, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=743442966826&path=search_goods_card&index=
2025-07-26 14:48:55,802 - INFO - 商品 17: 位置={'x': 543, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=848934676698&path=search_goods_card&index=
2025-07-26 14:48:55,849 - INFO - 商品 18: 位置={'x': 780, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=693758717931&path=search_goods_card&index=
2025-07-26 14:48:55,895 - INFO - 商品 19: 位置={'x': 1017, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=691142128229&path=search_goods_card&index=
2025-07-26 14:48:55,947 - INFO - 商品 20: 位置={'x': 1254, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=690739721578&path=search_goods_card&index=
2025-07-26 14:48:55,992 - INFO - 商品 21: 位置={'x': 306, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=691918824108&path=search_goods_card&index=
2025-07-26 14:48:56,040 - INFO - 商品 22: 位置={'x': 543, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=875045022715&path=search_goods_card&index=
2025-07-26 14:48:56,086 - INFO - 商品 23: 位置={'x': 780, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=873911671570&path=search_goods_card&index=
2025-07-26 14:48:56,129 - INFO - 商品 24: 位置={'x': 1017, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=722937163980&path=search_goods_card&index=
2025-07-26 14:48:56,172 - INFO - 商品 25: 位置={'x': 1254, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=739090443937&path=search_goods_card&index=
2025-07-26 14:48:56,218 - INFO - 商品 26: 位置={'x': 306, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=777862601010&path=search_goods_card&index=
2025-07-26 14:48:56,263 - INFO - 商品 27: 位置={'x': 543, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=686870174450&path=search_goods_card&index=
2025-07-26 14:48:56,305 - INFO - 商品 28: 位置={'x': 780, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=814542665480&path=search_goods_card&index=
2025-07-26 14:48:56,348 - INFO - 商品 29: 位置={'x': 1017, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=852625718015&path=search_goods_card&index=
2025-07-26 14:48:56,394 - INFO - 商品 30: 位置={'x': 1254, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=681472049289&path=search_goods_card&index=
2025-07-26 14:48:56,439 - INFO - 商品 31: 位置={'x': 306, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=683142617961&path=search_goods_card&index=
2025-07-26 14:48:56,480 - INFO - 商品 32: 位置={'x': 543, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=763659951211&path=search_goods_card&index=
2025-07-26 14:48:56,547 - INFO - 商品 33: 位置={'x': 780, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=675478966678&path=search_goods_card&index=
2025-07-26 14:48:56,589 - INFO - 商品 34: 位置={'x': 1017, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=800365611234&path=search_goods_card&index=
2025-07-26 14:48:56,630 - INFO - 商品 35: 位置={'x': 1254, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=896721954982&path=search_goods_card&index=
2025-07-26 14:48:56,670 - INFO - 商品 36: 位置={'x': 306, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=795389643023&path=search_goods_card&index=
2025-07-26 14:48:56,712 - INFO - 商品 37: 位置={'x': 543, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=689584396900&path=search_goods_card&index=
2025-07-26 14:48:56,756 - INFO - 商品 38: 位置={'x': 780, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=788516102627&path=search_goods_card&index=
2025-07-26 14:48:56,798 - INFO - 商品 39: 位置={'x': 1017, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=825025224622&path=search_goods_card&index=
2025-07-26 14:48:56,842 - INFO - 商品 40: 位置={'x': 1254, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=842638336638&path=search_goods_card&index=
2025-07-26 14:48:56,879 - INFO - 找到 40 个有效的商品容器
2025-07-26 14:48:56,907 - WARNING - 警告：第 1 个元素可能不是商品项，class=''
2025-07-26 14:48:56,952 - INFO - 商品 1 信息: 文本='保税直供 Dove多芬磨砂膏石榴籽乳木果风味身体磨砂膏298g
采购价¥35.00销量11.2万+件', 位置={'x': 306, 'y': 737}
2025-07-26 14:48:58,495 - INFO - 商品 1 位置: {'x': 306, 'y': 737}, 尺寸: {'height': 347, 'width': 226}
2025-07-26 14:48:58,518 - INFO - 模拟阅读行为，停顿 1.2 秒
2025-07-26 14:49:00,757 - WARNING - 人性化鼠标移动失败: Message: move target out of bounds
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a729be53]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:49:01,536 - INFO - 成功点击第 1 个商品
2025-07-26 14:49:01,542 - INFO - 已点击第 1 个商品，等待页面响应...
2025-07-26 14:49:05,549 - WARNING - 未检测到新标签页
2025-07-26 14:49:05,559 - WARNING - 未检测到新标签页，可能是页面内跳转
2025-07-26 14:49:08,570 - INFO - 找到 0 个iframe
2025-07-26 14:49:08,576 - INFO - 未找到包含目标元素的iframe，保持在主文档
2025-07-26 14:49:13,650 - WARNING - 等待元素可点击超时: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-26 14:49:15,686 - WARNING - 等待元素可点击超时: //button[contains(text(), '添加') or contains(text(), '加入') or contains(text(), '铺货')]
2025-07-26 14:49:17,753 - WARNING - 等待元素可点击超时: //button[contains(@class, 'add') or contains(@class, 'submit')]
2025-07-26 14:49:19,805 - WARNING - 等待元素可点击超时: //a[contains(text(), '添加') or contains(text(), '加入') or contains(text(), '铺货')]
2025-07-26 14:49:21,866 - WARNING - 等待元素可点击超时: //div[contains(@class, 'add-btn')]//button
2025-07-26 14:49:21,911 - INFO - 使用选择器找到添加商品按钮: //button[contains(@class, 'primary')]
2025-07-26 14:49:24,193 - INFO - 已点击添加商品按钮，等待确认对话框...
2025-07-26 14:49:32,280 - WARNING - 等待元素可点击超时: /html/body/div[8]/div[2]/div[2]/button
2025-07-26 14:49:34,321 - WARNING - 等待元素可点击超时: //button[contains(text(), '确认') or contains(text(), '确定') or contains(text(), '提交')]
2025-07-26 14:49:36,358 - WARNING - 等待元素可点击超时: //button[contains(@class, 'confirm') or contains(@class, 'submit')]
2025-07-26 14:49:38,419 - WARNING - 等待元素可点击超时: //div[contains(@class, 'dialog')]//button[last()]
2025-07-26 14:49:40,469 - WARNING - 等待元素可点击超时: //div[contains(@class, 'modal')]//button[contains(@class, 'primary')]
2025-07-26 14:49:42,517 - WARNING - 等待元素可点击超时: //button[@type='submit']
2025-07-26 14:49:44,561 - WARNING - 等待元素可点击超时: //a[contains(text(), '确认') or contains(text(), '确定')]
2025-07-26 14:49:46,608 - WARNING - 等待元素可点击超时: //*[@role='button'][contains(text(), '确认') or contains(text(), '确定')]
2025-07-26 14:49:46,732 - INFO - 通过文本内容找到确认按钮: 确定
2025-07-26 14:49:47,588 - WARNING - 人性化鼠标移动失败: Message: move target out of bounds
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a729be53]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:49:48,020 - INFO - 商品添加成功，返回主页面
2025-07-26 14:49:50,166 - INFO - 第 1 个商品处理成功 (总成功: 1, 批次进度: 1/13)
2025-07-26 14:49:50,173 - INFO - 等待 7.7 秒后处理下一个商品
2025-07-26 14:49:57,874 - INFO - 开始处理第 2/40 个商品
2025-07-26 14:49:57,897 - INFO - 找到 40 个直接子div
2025-07-26 14:49:57,927 - INFO - 商品 1: 位置={'x': 306, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=681778175573&path=search_goods_card&type=s
2025-07-26 14:49:57,956 - INFO - 商品 2: 位置={'x': 543, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=690841537159&path=search_goods_card&index=
2025-07-26 14:49:57,988 - INFO - 商品 3: 位置={'x': 780, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=789447097664&path=search_goods_card&index=
2025-07-26 14:49:58,019 - INFO - 商品 4: 位置={'x': 1017, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=694682843417&path=search_goods_card&index=
2025-07-26 14:49:58,057 - INFO - 商品 5: 位置={'x': 1254, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=681082809892&path=search_goods_card&index=
2025-07-26 14:49:58,094 - INFO - 商品 6: 位置={'x': 306, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=694675776881&path=search_goods_card&index=
2025-07-26 14:49:58,140 - INFO - 商品 7: 位置={'x': 543, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=680729304287&path=search_goods_card&index=
2025-07-26 14:49:58,205 - INFO - 商品 8: 位置={'x': 780, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=710355107054&path=search_goods_card&index=
2025-07-26 14:49:58,251 - INFO - 商品 9: 位置={'x': 1017, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=740463658412&path=search_goods_card&index=
2025-07-26 14:49:58,291 - INFO - 商品 10: 位置={'x': 1254, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=821067902127&path=search_goods_card&index=
2025-07-26 14:49:58,334 - INFO - 商品 11: 位置={'x': 306, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=789835300215&path=search_goods_card&index=
2025-07-26 14:49:58,378 - INFO - 商品 12: 位置={'x': 543, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=843747605382&path=search_goods_card&index=
2025-07-26 14:49:58,422 - INFO - 商品 13: 位置={'x': 780, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=675816539149&path=search_goods_card&index=
2025-07-26 14:49:58,465 - INFO - 商品 14: 位置={'x': 1017, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=846634805775&path=search_goods_card&index=
2025-07-26 14:49:58,510 - INFO - 商品 15: 位置={'x': 1254, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=852510575114&path=search_goods_card&index=
2025-07-26 14:49:58,557 - INFO - 商品 16: 位置={'x': 306, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=743442966826&path=search_goods_card&index=
2025-07-26 14:49:58,604 - INFO - 商品 17: 位置={'x': 543, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=848934676698&path=search_goods_card&index=
2025-07-26 14:49:58,649 - INFO - 商品 18: 位置={'x': 780, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=693758717931&path=search_goods_card&index=
2025-07-26 14:49:58,691 - INFO - 商品 19: 位置={'x': 1017, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=691142128229&path=search_goods_card&index=
2025-07-26 14:49:58,734 - INFO - 商品 20: 位置={'x': 1254, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=690739721578&path=search_goods_card&index=
2025-07-26 14:49:58,775 - INFO - 商品 21: 位置={'x': 306, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=691918824108&path=search_goods_card&index=
2025-07-26 14:49:58,819 - INFO - 商品 22: 位置={'x': 543, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=875045022715&path=search_goods_card&index=
2025-07-26 14:49:58,865 - INFO - 商品 23: 位置={'x': 780, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=873911671570&path=search_goods_card&index=
2025-07-26 14:49:58,910 - INFO - 商品 24: 位置={'x': 1017, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=722937163980&path=search_goods_card&index=
2025-07-26 14:49:58,952 - INFO - 商品 25: 位置={'x': 1254, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=739090443937&path=search_goods_card&index=
2025-07-26 14:49:58,995 - INFO - 商品 26: 位置={'x': 306, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=777862601010&path=search_goods_card&index=
2025-07-26 14:49:59,037 - INFO - 商品 27: 位置={'x': 543, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=686870174450&path=search_goods_card&index=
2025-07-26 14:49:59,082 - INFO - 商品 28: 位置={'x': 780, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=814542665480&path=search_goods_card&index=
2025-07-26 14:49:59,127 - INFO - 商品 29: 位置={'x': 1017, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=852625718015&path=search_goods_card&index=
2025-07-26 14:49:59,173 - INFO - 商品 30: 位置={'x': 1254, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=681472049289&path=search_goods_card&index=
2025-07-26 14:49:59,218 - INFO - 商品 31: 位置={'x': 306, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=683142617961&path=search_goods_card&index=
2025-07-26 14:49:59,258 - INFO - 商品 32: 位置={'x': 543, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=763659951211&path=search_goods_card&index=
2025-07-26 14:49:59,301 - INFO - 商品 33: 位置={'x': 780, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=675478966678&path=search_goods_card&index=
2025-07-26 14:49:59,347 - INFO - 商品 34: 位置={'x': 1017, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=800365611234&path=search_goods_card&index=
2025-07-26 14:49:59,389 - INFO - 商品 35: 位置={'x': 1254, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=896721954982&path=search_goods_card&index=
2025-07-26 14:49:59,433 - INFO - 商品 36: 位置={'x': 306, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=795389643023&path=search_goods_card&index=
2025-07-26 14:49:59,477 - INFO - 商品 37: 位置={'x': 543, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=689584396900&path=search_goods_card&index=
2025-07-26 14:49:59,530 - INFO - 商品 38: 位置={'x': 780, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=788516102627&path=search_goods_card&index=
2025-07-26 14:49:59,571 - INFO - 商品 39: 位置={'x': 1017, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=825025224622&path=search_goods_card&index=
2025-07-26 14:49:59,610 - INFO - 商品 40: 位置={'x': 1254, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=842638336638&path=search_goods_card&index=
2025-07-26 14:49:59,645 - INFO - 找到 40 个有效的商品容器
2025-07-26 14:49:59,673 - WARNING - 警告：第 2 个元素可能不是商品项，class=''
2025-07-26 14:49:59,718 - INFO - 商品 2 信息: 文本='保税直供 凡士林Vaseline烟酰胺身体乳400ml 725ml无压泵款200ml
采购价¥19.', 位置={'x': 543, 'y': 737}
2025-07-26 14:50:01,263 - INFO - 商品 2 位置: {'x': 542, 'y': 737}, 尺寸: {'height': 347, 'width': 226}
2025-07-26 14:50:01,288 - INFO - 模拟阅读行为，停顿 2.7 秒
2025-07-26 14:50:05,868 - WARNING - 人性化鼠标移动失败: Message: move target out of bounds
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a729be53]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:50:06,922 - INFO - 成功点击第 2 个商品
2025-07-26 14:50:06,926 - INFO - 已点击第 2 个商品，等待页面响应...
2025-07-26 14:50:10,935 - INFO - 已切换到新标签页
2025-07-26 14:50:10,947 - INFO - 页面加载完成
2025-07-26 14:50:10,957 - INFO - 模拟阅读行为，停顿 1.1 秒
2025-07-26 14:50:14,418 - INFO - 找到 0 个iframe
2025-07-26 14:50:14,427 - INFO - 未找到包含目标元素的iframe，保持在主文档
2025-07-26 14:50:15,563 - WARNING - 随机鼠标移动失败: Message: move target out of bounds
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a729be53]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:50:17,770 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-26 14:50:17,776 - INFO - 模拟阅读行为，停顿 2.2 秒
2025-07-26 14:50:21,580 - WARNING - 人性化鼠标移动失败: Message: move target out of bounds
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a729be53]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:50:22,047 - INFO - 已点击添加商品按钮，等待确认对话框...
2025-07-26 14:50:22,054 - INFO - 等待确认对话框出现，停顿 3.8 秒
2025-07-26 14:50:30,977 - WARNING - 等待元素可点击超时: /html/body/div[8]/div[2]/div[2]/button
2025-07-26 14:50:33,017 - WARNING - 等待元素可点击超时: //button[contains(text(), '确认') or contains(text(), '确定') or contains(text(), '提交')]
2025-07-26 14:50:35,058 - WARNING - 等待元素可点击超时: //button[contains(@class, 'confirm') or contains(@class, 'submit')]
2025-07-26 14:50:37,119 - WARNING - 等待元素可点击超时: //div[contains(@class, 'dialog')]//button[last()]
2025-07-26 14:50:39,160 - WARNING - 等待元素可点击超时: //div[contains(@class, 'modal')]//button[contains(@class, 'primary')]
2025-07-26 14:50:41,201 - WARNING - 等待元素可点击超时: //button[@type='submit']
2025-07-26 14:50:43,242 - WARNING - 等待元素可点击超时: //a[contains(text(), '确认') or contains(text(), '确定')]
2025-07-26 14:50:45,326 - WARNING - 等待元素可点击超时: //*[@role='button'][contains(text(), '确认') or contains(text(), '确定')]
2025-07-26 14:50:45,385 - WARNING - 未能找到确认按钮
2025-07-26 14:50:45,402 - INFO - 操作失败，2秒后重试 (尝试 1/2)
2025-07-26 14:50:52,497 - WARNING - 等待元素可点击超时: /html/body/div[8]/div[2]/div[2]/button
2025-07-26 14:50:54,542 - WARNING - 等待元素可点击超时: //button[contains(text(), '确认') or contains(text(), '确定') or contains(text(), '提交')]
2025-07-26 14:50:56,717 - WARNING - 等待元素可点击超时: //button[contains(@class, 'confirm') or contains(@class, 'submit')]
2025-07-26 14:50:58,763 - WARNING - 等待元素可点击超时: //div[contains(@class, 'dialog')]//button[last()]
2025-07-26 14:51:00,806 - WARNING - 等待元素可点击超时: //div[contains(@class, 'modal')]//button[contains(@class, 'primary')]
2025-07-26 14:51:02,846 - WARNING - 等待元素可点击超时: //button[@type='submit']
2025-07-26 14:51:04,883 - WARNING - 等待元素可点击超时: //a[contains(text(), '确认') or contains(text(), '确定')]
2025-07-26 14:51:06,924 - WARNING - 等待元素可点击超时: //*[@role='button'][contains(text(), '确认') or contains(text(), '确定')]
2025-07-26 14:51:06,968 - WARNING - 未能找到确认按钮
2025-07-26 14:51:06,984 - ERROR - 操作失败，已达到最大重试次数 (2)
2025-07-26 14:51:07,004 - ERROR - 无法找到或点击确认按钮
2025-07-26 14:51:07,074 - INFO - 已关闭当前标签页并切换回主页面
2025-07-26 14:51:07,091 - WARNING - 第 2 个商品处理失败
2025-07-26 14:51:07,110 - INFO - 等待 6.7 秒后处理下一个商品
2025-07-26 14:51:13,855 - INFO - 开始处理第 3/40 个商品
2025-07-26 14:51:13,882 - INFO - 找到 40 个直接子div
2025-07-26 14:51:13,915 - INFO - 商品 1: 位置={'x': 306, 'y': 737}, 尺寸={'height': 347, 'width': 225}, ID=item_id=681778175573&path=search_goods_card&type=s
2025-07-26 14:51:13,947 - INFO - 商品 2: 位置={'x': 542, 'y': 737}, 尺寸={'height': 347, 'width': 226}, ID=item_id=690841537159&path=search_goods_card&index=
2025-07-26 14:51:13,979 - INFO - 商品 3: 位置={'x': 780, 'y': 737}, 尺寸={'height': 347, 'width': 225}, ID=item_id=789447097664&path=search_goods_card&index=
2025-07-26 14:51:14,013 - INFO - 商品 4: 位置={'x': 1018, 'y': 737}, 尺寸={'height': 347, 'width': 225}, ID=item_id=694682843417&path=search_goods_card&index=
2025-07-26 14:51:14,049 - INFO - 商品 5: 位置={'x': 1254, 'y': 737}, 尺寸={'height': 347, 'width': 225}, ID=item_id=681082809892&path=search_goods_card&index=
2025-07-26 14:51:14,086 - INFO - 商品 6: 位置={'x': 306, 'y': 1096}, 尺寸={'height': 341, 'width': 225}, ID=item_id=694675776881&path=search_goods_card&index=
2025-07-26 14:51:14,124 - INFO - 商品 7: 位置={'x': 542, 'y': 1096}, 尺寸={'height': 341, 'width': 226}, ID=item_id=680729304287&path=search_goods_card&index=
2025-07-26 14:51:14,164 - INFO - 商品 8: 位置={'x': 780, 'y': 1096}, 尺寸={'height': 341, 'width': 225}, ID=item_id=710355107054&path=search_goods_card&index=
2025-07-26 14:51:14,207 - INFO - 商品 9: 位置={'x': 1018, 'y': 1096}, 尺寸={'height': 341, 'width': 225}, ID=item_id=740463658412&path=search_goods_card&index=
2025-07-26 14:51:14,250 - INFO - 商品 10: 位置={'x': 1254, 'y': 1096}, 尺寸={'height': 341, 'width': 225}, ID=item_id=821067902127&path=search_goods_card&index=
2025-07-26 14:51:14,295 - INFO - 商品 11: 位置={'x': 306, 'y': 1449}, 尺寸={'height': 341, 'width': 225}, ID=item_id=789835300215&path=search_goods_card&index=
2025-07-26 14:51:14,337 - INFO - 商品 12: 位置={'x': 542, 'y': 1449}, 尺寸={'height': 341, 'width': 226}, ID=item_id=843747605382&path=search_goods_card&index=
2025-07-26 14:51:14,381 - INFO - 商品 13: 位置={'x': 780, 'y': 1449}, 尺寸={'height': 341, 'width': 225}, ID=item_id=675816539149&path=search_goods_card&index=
2025-07-26 14:51:14,428 - INFO - 商品 14: 位置={'x': 1018, 'y': 1449}, 尺寸={'height': 341, 'width': 225}, ID=item_id=846634805775&path=search_goods_card&index=
2025-07-26 14:51:14,474 - INFO - 商品 15: 位置={'x': 1254, 'y': 1449}, 尺寸={'height': 341, 'width': 225}, ID=item_id=852510575114&path=search_goods_card&index=
2025-07-26 14:51:14,516 - INFO - 商品 16: 位置={'x': 306, 'y': 1802}, 尺寸={'height': 341, 'width': 225}, ID=item_id=743442966826&path=search_goods_card&index=
2025-07-26 14:51:14,562 - INFO - 商品 17: 位置={'x': 542, 'y': 1802}, 尺寸={'height': 341, 'width': 226}, ID=item_id=848934676698&path=search_goods_card&index=
2025-07-26 14:51:14,604 - INFO - 商品 18: 位置={'x': 780, 'y': 1802}, 尺寸={'height': 341, 'width': 225}, ID=item_id=693758717931&path=search_goods_card&index=
2025-07-26 14:51:14,646 - INFO - 商品 19: 位置={'x': 1018, 'y': 1802}, 尺寸={'height': 341, 'width': 225}, ID=item_id=691142128229&path=search_goods_card&index=
2025-07-26 14:51:14,690 - INFO - 商品 20: 位置={'x': 1254, 'y': 1802}, 尺寸={'height': 341, 'width': 225}, ID=item_id=690739721578&path=search_goods_card&index=
2025-07-26 14:51:14,733 - INFO - 商品 21: 位置={'x': 306, 'y': 2155}, 尺寸={'height': 341, 'width': 225}, ID=item_id=691918824108&path=search_goods_card&index=
2025-07-26 14:51:14,778 - INFO - 商品 22: 位置={'x': 542, 'y': 2155}, 尺寸={'height': 341, 'width': 226}, ID=item_id=875045022715&path=search_goods_card&index=
2025-07-26 14:51:14,820 - INFO - 商品 23: 位置={'x': 780, 'y': 2155}, 尺寸={'height': 341, 'width': 225}, ID=item_id=873911671570&path=search_goods_card&index=
2025-07-26 14:51:14,863 - INFO - 商品 24: 位置={'x': 1018, 'y': 2155}, 尺寸={'height': 341, 'width': 225}, ID=item_id=722937163980&path=search_goods_card&index=
2025-07-26 14:51:14,914 - INFO - 商品 25: 位置={'x': 1254, 'y': 2155}, 尺寸={'height': 341, 'width': 225}, ID=item_id=739090443937&path=search_goods_card&index=
2025-07-26 14:51:14,958 - INFO - 商品 26: 位置={'x': 306, 'y': 2508}, 尺寸={'height': 341, 'width': 225}, ID=item_id=777862601010&path=search_goods_card&index=
2025-07-26 14:51:15,001 - INFO - 商品 27: 位置={'x': 542, 'y': 2508}, 尺寸={'height': 341, 'width': 226}, ID=item_id=686870174450&path=search_goods_card&index=
2025-07-26 14:51:15,042 - INFO - 商品 28: 位置={'x': 780, 'y': 2508}, 尺寸={'height': 341, 'width': 225}, ID=item_id=814542665480&path=search_goods_card&index=
2025-07-26 14:51:15,085 - INFO - 商品 29: 位置={'x': 1018, 'y': 2508}, 尺寸={'height': 341, 'width': 225}, ID=item_id=852625718015&path=search_goods_card&index=
2025-07-26 14:51:15,128 - INFO - 商品 30: 位置={'x': 1254, 'y': 2508}, 尺寸={'height': 341, 'width': 225}, ID=item_id=681472049289&path=search_goods_card&index=
2025-07-26 14:51:15,171 - INFO - 商品 31: 位置={'x': 306, 'y': 2861}, 尺寸={'height': 341, 'width': 225}, ID=item_id=683142617961&path=search_goods_card&index=
2025-07-26 14:51:15,212 - INFO - 商品 32: 位置={'x': 542, 'y': 2861}, 尺寸={'height': 341, 'width': 226}, ID=item_id=763659951211&path=search_goods_card&index=
2025-07-26 14:51:15,255 - INFO - 商品 33: 位置={'x': 780, 'y': 2861}, 尺寸={'height': 341, 'width': 225}, ID=item_id=675478966678&path=search_goods_card&index=
2025-07-26 14:51:15,299 - INFO - 商品 34: 位置={'x': 1018, 'y': 2861}, 尺寸={'height': 341, 'width': 225}, ID=item_id=800365611234&path=search_goods_card&index=
2025-07-26 14:51:15,342 - INFO - 商品 35: 位置={'x': 1254, 'y': 2861}, 尺寸={'height': 341, 'width': 225}, ID=item_id=896721954982&path=search_goods_card&index=
2025-07-26 14:51:15,384 - INFO - 商品 36: 位置={'x': 306, 'y': 3214}, 尺寸={'height': 341, 'width': 225}, ID=item_id=795389643023&path=search_goods_card&index=
2025-07-26 14:51:15,427 - INFO - 商品 37: 位置={'x': 542, 'y': 3214}, 尺寸={'height': 341, 'width': 226}, ID=item_id=689584396900&path=search_goods_card&index=
2025-07-26 14:51:15,472 - INFO - 商品 38: 位置={'x': 780, 'y': 3214}, 尺寸={'height': 341, 'width': 225}, ID=item_id=788516102627&path=search_goods_card&index=
2025-07-26 14:51:15,516 - INFO - 商品 39: 位置={'x': 1018, 'y': 3214}, 尺寸={'height': 341, 'width': 225}, ID=item_id=825025224622&path=search_goods_card&index=
2025-07-26 14:51:15,561 - INFO - 商品 40: 位置={'x': 1254, 'y': 3214}, 尺寸={'height': 341, 'width': 225}, ID=item_id=842638336638&path=search_goods_card&index=
2025-07-26 14:51:15,598 - INFO - 找到 40 个有效的商品容器
2025-07-26 14:51:15,630 - WARNING - 警告：第 3 个元素可能不是商品项，class=''
2025-07-26 14:51:15,677 - INFO - 商品 3 信息: 文本='【甄选】珂润Curel泡沫洗面奶150ml
采购价¥52.00销量7.9万+件
佣金率5.50%|1', 位置={'x': 780, 'y': 737}
2025-07-26 14:51:17,212 - INFO - 商品 3 位置: {'x': 780, 'y': 737}, 尺寸: {'height': 347, 'width': 226}
2025-07-26 14:51:17,235 - INFO - 模拟阅读行为，停顿 1.4 秒
2025-07-26 14:51:20,687 - WARNING - 人性化鼠标移动失败: Message: move target out of bounds
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a729be53]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:51:21,540 - INFO - 成功点击第 3 个商品
2025-07-26 14:51:21,545 - INFO - 已点击第 3 个商品，等待页面响应...
2025-07-26 14:51:25,553 - INFO - 已切换到新标签页
2025-07-26 14:51:25,560 - INFO - 页面加载完成
2025-07-26 14:51:25,568 - INFO - 模拟阅读行为，停顿 2.3 秒
2025-07-26 14:51:27,900 - INFO - 添加额外延迟: 1.2秒
2025-07-26 14:51:30,650 - INFO - 找到 0 个iframe
2025-07-26 14:51:30,660 - INFO - 未找到包含目标元素的iframe，保持在主文档
2025-07-26 14:51:30,694 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-26 14:51:30,719 - INFO - 模拟阅读行为，停顿 1.2 秒
2025-07-26 14:51:34,398 - WARNING - 人性化鼠标移动失败: Message: move target out of bounds
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a729be53]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:51:34,680 - INFO - 已点击添加商品按钮，等待确认对话框...
2025-07-26 14:51:34,690 - INFO - 等待确认对话框出现，停顿 3.8 秒
2025-07-26 14:51:43,657 - WARNING - 等待元素可点击超时: /html/body/div[8]/div[2]/div[2]/button
2025-07-26 14:51:45,725 - WARNING - 等待元素可点击超时: //button[contains(text(), '确认') or contains(text(), '确定') or contains(text(), '提交')]
2025-07-26 14:51:47,774 - WARNING - 等待元素可点击超时: //button[contains(@class, 'confirm') or contains(@class, 'submit')]
2025-07-26 14:51:49,844 - WARNING - 等待元素可点击超时: //div[contains(@class, 'dialog')]//button[last()]
2025-07-26 14:51:51,939 - WARNING - 等待元素可点击超时: //div[contains(@class, 'modal')]//button[contains(@class, 'primary')]
2025-07-26 14:51:53,986 - WARNING - 等待元素可点击超时: //button[@type='submit']
2025-07-26 14:51:56,041 - WARNING - 等待元素可点击超时: //a[contains(text(), '确认') or contains(text(), '确定')]
2025-07-26 14:51:58,098 - WARNING - 等待元素可点击超时: //*[@role='button'][contains(text(), '确认') or contains(text(), '确定')]
2025-07-26 14:51:58,174 - WARNING - 未能找到确认按钮
2025-07-26 14:51:58,183 - INFO - 操作失败，2秒后重试 (尝试 1/2)
2025-07-26 14:52:05,294 - INFO - 用户请求停止自动化流程
2025-07-26 14:52:05,372 - WARNING - 等待元素可点击超时: /html/body/div[8]/div[2]/div[2]/button
2025-07-26 14:52:06,239 - INFO - Cookies已保存
2025-07-26 14:52:06,514 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-07-26 14:52:08,532 - INFO - 浏览器已关闭
