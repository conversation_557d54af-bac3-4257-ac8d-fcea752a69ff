2025-07-29 00:05:48,456 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059cda]
	(No symbol) [0x0x7ff69a061679]
	(No symbol) [0x0x7ff69a064a61]
	(No symbol) [0x0x7ff69a101e4b]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:05:48,457 - INFO - 防检测机制设置完成
2025-07-29 00:05:48,457 - INFO - 浏览器驱动初始化成功
2025-07-29 00:05:48,467 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:05:48,470 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:05:48,478 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:05:48,482 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:05:48,487 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:05:48,491 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:05:48,495 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:05:48,497 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:05:48,500 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:05:48,503 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:05:48,505 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:05:48,508 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:05:48,510 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:05:48,514 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:05:48,517 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:05:48,519 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:05:48,522 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:05:48,528 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:05:48,530 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:05:48,534 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:05:48,535 - INFO - Cookies已加载
2025-07-29 00:05:48,535 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?channel=global_zx&spm=a21ug5.29159167.6452766900.2ndview_zxmore_click
2025-07-29 00:05:48,535 - INFO - 添加额外延迟: 2.3秒
2025-07-29 00:05:56,859 - INFO - 当前User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-29 00:05:56,860 - INFO - 确认：使用桌面版User-Agent
2025-07-29 00:05:56,896 - INFO - 桌面版User-Agent设置完成: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-29 00:05:59,374 - INFO - 成功导航到目标页面
2025-07-29 00:05:59,375 - INFO - 登录成功！现在可以开始铺货任务
2025-07-29 00:06:01,426 - INFO - Cookies已保存
2025-07-29 00:06:03,698 - INFO - 浏览器已关闭
2025-07-29 00:06:05,093 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059cda]
	(No symbol) [0x0x7ff69a061679]
	(No symbol) [0x0x7ff69a064a61]
	(No symbol) [0x0x7ff69a101e4b]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:06:05,093 - INFO - 防检测机制设置完成
2025-07-29 00:06:05,093 - INFO - 浏览器驱动初始化成功
2025-07-29 00:06:05,093 - INFO - 开始自动化铺货流程（有界面模式）
2025-07-29 00:06:05,093 - INFO - 第一步：正在导航到目标页面...
2025-07-29 00:06:09,559 - INFO - 第一步：点击登录按钮
2025-07-29 00:06:09,579 - INFO - 找到登录按钮，使用选择器: //*[@id='login-form']/div[6]/button
2025-07-29 00:06:10,601 - INFO - 准备点击登录按钮: 标签=button, 文本='登录', 选择器=//*[@id='login-form']/div[6]/button
2025-07-29 00:06:10,614 - INFO - 准备点击元素: tag=button, text='登录', class='fm-button fm-submit password-l', id=''
2025-07-29 00:06:11,717 - INFO - 尝试ActionChains点击...
2025-07-29 00:06:12,137 - INFO - ActionChains点击成功
2025-07-29 00:06:12,625 - INFO - 登录按钮点击成功
2025-07-29 00:06:12,625 - INFO - 登录按钮点击成功
2025-07-29 00:06:15,626 - INFO - 第二步：获取账号信息...
2025-07-29 00:06:15,626 - INFO - 正在跳转到个人中心页面: https://jingya.taobao.com/?v=2
2025-07-29 00:06:40,423 - INFO - 尝试获取账号名称，使用XPath: //*[@id='workbench-user-tool-btn']/div/div[2]
2025-07-29 00:06:40,436 - INFO - 成功获取账号信息: 卓祥22店:冰淇淋
2025-07-29 00:06:40,437 - INFO - 获取到账号名称: 卓祥22店:冰淇淋
2025-07-29 00:06:40,438 - INFO - 第三步：开始读取20个商品信息...
2025-07-29 00:06:40,438 - INFO - GUI界面已更新账号显示: 卓祥22店:冰淇淋
2025-07-29 00:06:40,439 - INFO - 开始爬取商品信息，最大数量: 20
2025-07-29 00:06:44,577 - INFO - 正在爬取第 1 页商品信息...
2025-07-29 00:06:46,585 - INFO - 找到商品容器，开始爬取商品，最大数量: 20
2025-07-29 00:06:46,596 - INFO - 找到商品项 1，检查元素信息...
2025-07-29 00:06:46,605 - INFO - 商品项 1 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:06:46,609 - INFO - 商品项 1 直接获取到data-autolog: item_id=681778175573&path=search_goods_card&type=search
2025-07-29 00:06:46,609 - INFO - 商品项 1 data-autolog: item_id=681778175573&path=search_goods_card&type=search
2025-07-29 00:06:46,610 - INFO - 商品项 1 提取到item_id: 681778175573
2025-07-29 00:06:46,621 - INFO - 商品项 1 商品名称: 保税直供 Dove多芬磨砂膏石榴籽乳木果风味身体磨砂膏298g
2025-07-29 00:06:46,632 - INFO - 找到商品项 2，检查元素信息...
2025-07-29 00:06:46,640 - INFO - 商品项 2 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:06:46,643 - INFO - 商品项 2 直接获取到data-autolog: item_id=690841537159&path=search_goods_card&index=1&type=search
2025-07-29 00:06:46,643 - INFO - 商品项 2 data-autolog: item_id=690841537159&path=search_goods_card&index=1&type=search
2025-07-29 00:06:46,643 - INFO - 商品项 2 提取到item_id: 690841537159
2025-07-29 00:06:46,727 - INFO - 商品项 2 商品名称: 保税直供 凡士林Vaseline烟酰胺身体乳400ml 725ml无压泵款200ml
2025-07-29 00:06:46,741 - INFO - 找到商品项 3，检查元素信息...
2025-07-29 00:06:46,762 - INFO - 商品项 3 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:06:46,767 - INFO - 商品项 3 直接获取到data-autolog: item_id=789447097664&path=search_goods_card&index=2&type=search
2025-07-29 00:06:46,767 - INFO - 商品项 3 data-autolog: item_id=789447097664&path=search_goods_card&index=2&type=search
2025-07-29 00:06:46,767 - INFO - 商品项 3 提取到item_id: 789447097664
2025-07-29 00:06:46,778 - INFO - 商品项 3 商品名称: 【甄选】珂润Curel泡沫洗面奶150ml
2025-07-29 00:06:46,791 - INFO - 找到商品项 4，检查元素信息...
2025-07-29 00:06:46,800 - INFO - 商品项 4 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:06:46,802 - INFO - 商品项 4 直接获取到data-autolog: item_id=694682843417&path=search_goods_card&index=3&type=search
2025-07-29 00:06:46,802 - INFO - 商品项 4 data-autolog: item_id=694682843417&path=search_goods_card&index=3&type=search
2025-07-29 00:06:46,802 - INFO - 商品项 4 提取到item_id: 694682843417
2025-07-29 00:06:46,814 - INFO - 商品项 4 商品名称: 【可开授权】日台混发Fino发膜美容液护发素 发膜230g
2025-07-29 00:06:46,826 - INFO - 找到商品项 5，检查元素信息...
2025-07-29 00:06:46,832 - INFO - 商品项 5 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:06:46,836 - INFO - 商品项 5 直接获取到data-autolog: item_id=681082809892&path=search_goods_card&index=4&type=search
2025-07-29 00:06:46,836 - INFO - 商品项 5 data-autolog: item_id=681082809892&path=search_goods_card&index=4&type=search
2025-07-29 00:06:46,836 - INFO - 商品项 5 提取到item_id: 681082809892
2025-07-29 00:06:46,845 - INFO - 商品项 5 商品名称: 保税直供 Dove多芬大白碗润肤身体乳300ml 【1//2//3罐】
2025-07-29 00:06:46,855 - INFO - 找到商品项 6，检查元素信息...
2025-07-29 00:06:46,863 - INFO - 商品项 6 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:06:46,866 - INFO - 商品项 6 直接获取到data-autolog: item_id=694675776881&path=search_goods_card&index=5&type=search
2025-07-29 00:06:46,866 - INFO - 商品项 6 data-autolog: item_id=694675776881&path=search_goods_card&index=5&type=search
2025-07-29 00:06:46,866 - INFO - 商品项 6 提取到item_id: 694675776881
2025-07-29 00:06:46,876 - INFO - 商品项 6 商品名称: 保税直供 Pantene潘婷护发素三分钟奇迹多效修护发膜级护发素
2025-07-29 00:06:46,886 - INFO - 找到商品项 7，检查元素信息...
2025-07-29 00:06:46,898 - INFO - 商品项 7 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:06:46,901 - INFO - 商品项 7 直接获取到data-autolog: item_id=680729304287&path=search_goods_card&index=6&type=search
2025-07-29 00:06:46,902 - INFO - 商品项 7 data-autolog: item_id=680729304287&path=search_goods_card&index=6&type=search
2025-07-29 00:06:46,902 - INFO - 商品项 7 提取到item_id: 680729304287
2025-07-29 00:06:46,911 - INFO - 商品项 7 商品名称: 保税直供 多芬dove空气感无硅油 洗发水480g 护发素480g护发临期
2025-07-29 00:06:46,927 - INFO - 找到商品项 8，检查元素信息...
2025-07-29 00:06:46,947 - INFO - 商品项 8 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:06:46,951 - INFO - 商品项 8 直接获取到data-autolog: item_id=710355107054&path=search_goods_card&index=7&type=search
2025-07-29 00:06:46,951 - INFO - 商品项 8 data-autolog: item_id=710355107054&path=search_goods_card&index=7&type=search
2025-07-29 00:06:46,951 - INFO - 商品项 8 提取到item_id: 710355107054
2025-07-29 00:06:46,961 - INFO - 商品项 8 商品名称: 保税直供 Dove多芬洗发水护发素空气感洗发水480g 【护发素临期
2025-07-29 00:06:46,972 - INFO - 找到商品项 9，检查元素信息...
2025-07-29 00:06:46,979 - INFO - 商品项 9 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:06:46,984 - INFO - 商品项 9 直接获取到data-autolog: item_id=740463658412&path=search_goods_card&index=8&type=search
2025-07-29 00:06:46,984 - INFO - 商品项 9 data-autolog: item_id=740463658412&path=search_goods_card&index=8&type=search
2025-07-29 00:06:46,984 - INFO - 商品项 9 提取到item_id: 740463658412
2025-07-29 00:06:46,992 - INFO - 商品项 9 商品名称: 【主推链接】花王cape空气感定型喷雾50g/180g 编码：3305300000
2025-07-29 00:06:47,004 - INFO - 找到商品项 10，检查元素信息...
2025-07-29 00:06:47,013 - INFO - 商品项 10 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:06:47,015 - INFO - 商品项 10 直接获取到data-autolog: item_id=821067902127&path=search_goods_card&index=9&type=search
2025-07-29 00:06:47,015 - INFO - 商品项 10 data-autolog: item_id=821067902127&path=search_goods_card&index=9&type=search
2025-07-29 00:06:47,016 - INFO - 商品项 10 提取到item_id: 821067902127
2025-07-29 00:06:47,026 - INFO - 商品项 10 商品名称: 丹碧丝TAMPAX卫生棉条长导管式内置式纯棉月经棉条棉棒游泳卫生巾
2025-07-29 00:06:47,037 - INFO - 找到商品项 11，检查元素信息...
2025-07-29 00:06:47,045 - INFO - 商品项 11 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:06:47,047 - INFO - 商品项 11 直接获取到data-autolog: item_id=789835300215&path=search_goods_card&index=10&type=search
2025-07-29 00:06:47,047 - INFO - 商品项 11 data-autolog: item_id=789835300215&path=search_goods_card&index=10&type=search
2025-07-29 00:06:47,048 - INFO - 商品项 11 提取到item_id: 789835300215
2025-07-29 00:06:47,056 - INFO - 商品项 11 商品名称: 【保税】雅漾喷雾爽肤水300ml
2025-07-29 00:06:47,069 - INFO - 找到商品项 12，检查元素信息...
2025-07-29 00:06:47,077 - INFO - 商品项 12 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:06:47,081 - INFO - 商品项 12 直接获取到data-autolog: item_id=843747605382&path=search_goods_card&index=11&type=search
2025-07-29 00:06:47,081 - INFO - 商品项 12 data-autolog: item_id=843747605382&path=search_goods_card&index=11&type=search
2025-07-29 00:06:47,081 - INFO - 商品项 12 提取到item_id: 843747605382
2025-07-29 00:06:47,091 - INFO - 商品项 12 商品名称: 倩碧紫胖子卸妆膏125ml/瓶
2025-07-29 00:06:47,104 - INFO - 找到商品项 13，检查元素信息...
2025-07-29 00:06:47,111 - INFO - 商品项 13 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:06:47,114 - INFO - 商品项 13 直接获取到data-autolog: item_id=675816539149&path=search_goods_card&index=12&type=search
2025-07-29 00:06:47,114 - INFO - 商品项 13 data-autolog: item_id=675816539149&path=search_goods_card&index=12&type=search
2025-07-29 00:06:47,114 - INFO - 商品项 13 提取到item_id: 675816539149
2025-07-29 00:06:47,124 - INFO - 商品项 13 商品名称: 新加坡Vicopyl维可保益生菌(Pylopass/拒绝幽门菌/抑口臭/调肠胃)
2025-07-29 00:06:47,135 - INFO - 找到商品项 14，检查元素信息...
2025-07-29 00:06:47,145 - INFO - 商品项 14 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:06:47,147 - INFO - 商品项 14 直接获取到data-autolog: item_id=846634805775&path=search_goods_card&index=13&type=search
2025-07-29 00:06:47,148 - INFO - 商品项 14 data-autolog: item_id=846634805775&path=search_goods_card&index=13&type=search
2025-07-29 00:06:47,148 - INFO - 商品项 14 提取到item_id: 846634805775
2025-07-29 00:06:47,156 - INFO - 商品项 14 商品名称: 【甄选】cow牛乳石碱原味/玫瑰花香 瓶装480ml
2025-07-29 00:06:47,166 - INFO - 找到商品项 15，检查元素信息...
2025-07-29 00:06:47,175 - INFO - 商品项 15 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:06:47,178 - INFO - 商品项 15 直接获取到data-autolog: item_id=852510575114&path=search_goods_card&index=14&type=search
2025-07-29 00:06:47,178 - INFO - 商品项 15 data-autolog: item_id=852510575114&path=search_goods_card&index=14&type=search
2025-07-29 00:06:47,178 - INFO - 商品项 15 提取到item_id: 852510575114
2025-07-29 00:06:47,187 - INFO - 商品项 15 商品名称: Nars 纳斯 红壳蜜粉饼16g（含粉扑）、Nars纳斯裸光蜜粉饼10g
2025-07-29 00:06:47,199 - INFO - 找到商品项 16，检查元素信息...
2025-07-29 00:06:47,206 - INFO - 商品项 16 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:06:47,209 - INFO - 商品项 16 直接获取到data-autolog: item_id=743442966826&path=search_goods_card&index=15&type=search
2025-07-29 00:06:47,209 - INFO - 商品项 16 data-autolog: item_id=743442966826&path=search_goods_card&index=15&type=search
2025-07-29 00:06:47,209 - INFO - 商品项 16 提取到item_id: 743442966826
2025-07-29 00:06:47,218 - INFO - 商品项 16 商品名称: ESI卡路里拜拜片白芸豆阻断片大餐救星膳食纤维身材热控管理
2025-07-29 00:06:47,229 - INFO - 找到商品项 17，检查元素信息...
2025-07-29 00:06:47,238 - INFO - 商品项 17 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:06:47,242 - INFO - 商品项 17 直接获取到data-autolog: item_id=848934676698&path=search_goods_card&index=16&type=search
2025-07-29 00:06:47,242 - INFO - 商品项 17 data-autolog: item_id=848934676698&path=search_goods_card&index=16&type=search
2025-07-29 00:06:47,242 - INFO - 商品项 17 提取到item_id: 848934676698
2025-07-29 00:06:47,251 - INFO - 商品项 17 商品名称: Estee Lauder 雅诗兰黛持妆粉底液30ml 2C0 /1w1/1C1
2025-07-29 00:06:47,261 - INFO - 找到商品项 18，检查元素信息...
2025-07-29 00:06:47,270 - INFO - 商品项 18 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:06:47,273 - INFO - 商品项 18 直接获取到data-autolog: item_id=693758717931&path=search_goods_card&index=17&type=search
2025-07-29 00:06:47,273 - INFO - 商品项 18 data-autolog: item_id=693758717931&path=search_goods_card&index=17&type=search
2025-07-29 00:06:47,273 - INFO - 商品项 18 提取到item_id: 693758717931
2025-07-29 00:06:47,282 - INFO - 商品项 18 商品名称: 保税直供 资生堂fino高效渗透发膜 230g （1/2/3罐）日版
2025-07-29 00:06:47,295 - INFO - 找到商品项 19，检查元素信息...
2025-07-29 00:06:47,303 - INFO - 商品项 19 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:06:47,306 - INFO - 商品项 19 直接获取到data-autolog: item_id=691142128229&path=search_goods_card&index=18&type=search
2025-07-29 00:06:47,306 - INFO - 商品项 19 data-autolog: item_id=691142128229&path=search_goods_card&index=18&type=search
2025-07-29 00:06:47,306 - INFO - 商品项 19 提取到item_id: 691142128229
2025-07-29 00:06:47,315 - INFO - 商品项 19 商品名称: 保税 日版Dove多芬洁面洗面奶新版 正装瓶150ml 补充袋125ml 甄选
2025-07-29 00:06:47,326 - INFO - 找到商品项 20，检查元素信息...
2025-07-29 00:06:47,336 - INFO - 商品项 20 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:06:47,338 - INFO - 商品项 20 直接获取到data-autolog: item_id=690739721578&path=search_goods_card&index=19&type=search
2025-07-29 00:06:47,338 - INFO - 商品项 20 data-autolog: item_id=690739721578&path=search_goods_card&index=19&type=search
2025-07-29 00:06:47,338 - INFO - 商品项 20 提取到item_id: 690739721578
2025-07-29 00:06:47,347 - INFO - 商品项 20 商品名称: 保税直供 SUNSILK夏士莲椰子洗发水//护发素450ml 泰产
2025-07-29 00:06:47,348 - INFO - 已爬取到 20 个商品，停止当前页面爬取
2025-07-29 00:06:47,348 - INFO - 当前页面爬取完成，找到 20 个商品
2025-07-29 00:06:47,348 - INFO - 第 1 页找到 20 个商品，总计: 20
2025-07-29 00:06:47,348 - INFO - 已达到最大商品数量 20，停止爬取
2025-07-29 00:06:47,348 - INFO - 爬取完成，共找到 20 个商品
2025-07-29 00:06:47,349 - INFO - 成功读取到 20 个商品（目标20个），开始铺货任务
2025-07-29 00:06:47,349 - INFO - 第四步：开始处理商品...
2025-07-29 00:06:47,359 - INFO - 已读取 20 个商品，目标成功铺货 3 个商品
2025-07-29 00:06:47,360 - INFO - 当前批次大小: 12
2025-07-29 00:06:47,360 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=681778175573
2025-07-29 00:06:50,935 - INFO - 页面加载完成
2025-07-29 00:06:50,935 - INFO - 模拟阅读行为，停顿 2.5 秒
2025-07-29 00:06:55,567 - INFO - 开始第 1 次铺货尝试...
2025-07-29 00:06:55,567 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-29 00:06:55,567 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-29 00:06:55,579 - INFO - span innerHTML: 立即铺货
2025-07-29 00:06:55,612 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-29 00:06:55,612 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-29 00:06:55,626 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-29 00:06:56,717 - INFO - 尝试ActionChains点击...
2025-07-29 00:06:57,002 - INFO - ActionChains点击成功
2025-07-29 00:06:57,372 - INFO - 铺货按钮点击成功
2025-07-29 00:06:59,386 - INFO - 使用配置的XPath找到确认按钮
2025-07-29 00:06:59,400 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-29 00:07:00,744 - INFO - 尝试ActionChains点击...
2025-07-29 00:07:01,011 - INFO - ActionChains点击成功
2025-07-29 00:07:01,262 - INFO - 确认按钮点击成功
2025-07-29 00:07:03,263 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-29 00:07:03,271 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-29 00:07:03,271 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:07:04,280 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-29 00:07:04,280 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:07:05,290 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-29 00:07:05,290 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:07:06,298 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-29 00:07:06,299 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:07:07,308 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-07-29 00:07:07,308 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:07:08,314 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-07-29 00:07:08,314 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:07:09,322 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-07-29 00:07:09,322 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:07:10,331 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-07-29 00:07:10,331 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:07:11,339 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-07-29 00:07:11,339 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:07:12,349 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-07-29 00:07:12,349 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:07:13,350 - INFO - 等待超时，进行最后一次检查...
2025-07-29 00:07:13,359 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-07-29 00:07:13,359 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-07-29 00:07:13,359 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-07-29 00:07:13,360 - INFO - 检测是否出现滑块验证...
2025-07-29 00:07:13,366 - INFO - 在主文档中未找到滑块，检查iframe...
2025-07-29 00:07:13,374 - INFO - 找到 1 个iframe
2025-07-29 00:07:13,374 - INFO - 检查第 1 个iframe...
2025-07-29 00:07:13,394 - INFO - 在第 1 个iframe中检测到滑块验证，开始处理...
2025-07-29 00:07:13,394 - INFO - 开始处理滑块验证...
2025-07-29 00:07:13,407 - INFO - 滑块容器宽度: 300px
2025-07-29 00:07:13,407 - INFO - 计算拖拽距离: 279px (比例: 0.93)
2025-07-29 00:07:13,407 - INFO - 开始匀速不分段滑块操作...
2025-07-29 00:07:13,407 - INFO - 尝试drag_and_drop_by_offset，拖拽距离: 279px
2025-07-29 00:07:14,122 - INFO - drag_and_drop_by_offset执行完成
2025-07-29 00:07:17,123 - INFO - 匀速不分段滑块操作完成
2025-07-29 00:07:17,127 - INFO - 滑块验证成功，滑块已消失
2025-07-29 00:07:17,128 - INFO - 滑块验证处理成功，准备重试铺货
2025-07-29 00:07:19,129 - INFO - 开始第 2 次铺货尝试...
2025-07-29 00:07:19,129 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-29 00:07:19,129 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-29 00:07:19,138 - INFO - span innerHTML: 立即铺货
2025-07-29 00:07:29,342 - WARNING - 等待元素可点击超时: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-29 00:07:29,342 - WARNING - 使用配置的XPath未找到添加商品按钮
2025-07-29 00:07:29,343 - ERROR - 未找到铺货按钮
2025-07-29 00:07:29,344 - WARNING - 第 1 个商品处理失败（不计入成功数量）
2025-07-29 00:07:29,345 - INFO - 商品处理失败，立即处理下一个商品
2025-07-29 00:07:29,356 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=690841537159
2025-07-29 00:07:32,980 - INFO - 页面加载完成
2025-07-29 00:07:32,980 - INFO - 模拟阅读行为，停顿 1.0 秒
2025-07-29 00:07:35,426 - INFO - 开始第 1 次铺货尝试...
2025-07-29 00:07:35,426 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-29 00:07:35,426 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-29 00:07:35,434 - INFO - span innerHTML: 立即铺货
2025-07-29 00:07:35,455 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-29 00:07:35,455 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-29 00:07:35,470 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-29 00:07:36,637 - INFO - 尝试ActionChains点击...
2025-07-29 00:07:36,922 - INFO - ActionChains点击成功
2025-07-29 00:07:37,184 - INFO - 铺货按钮点击成功
2025-07-29 00:07:39,198 - INFO - 使用配置的XPath找到确认按钮
2025-07-29 00:07:39,212 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-29 00:07:40,419 - INFO - 尝试ActionChains点击...
2025-07-29 00:07:40,698 - INFO - ActionChains点击成功
2025-07-29 00:07:41,081 - INFO - 确认按钮点击成功
2025-07-29 00:07:43,082 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-29 00:07:43,090 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-29 00:07:43,091 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:07:44,099 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-29 00:07:44,099 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:07:45,108 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-29 00:07:45,108 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:07:46,115 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-29 00:07:46,115 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:07:47,123 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-07-29 00:07:47,123 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:07:48,133 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-07-29 00:07:48,133 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:07:49,140 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-07-29 00:07:49,140 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:07:50,149 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-07-29 00:07:50,151 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:07:51,158 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-07-29 00:07:51,159 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:07:52,167 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-07-29 00:07:52,167 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:07:53,168 - INFO - 等待超时，进行最后一次检查...
2025-07-29 00:07:53,176 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-07-29 00:07:53,176 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-07-29 00:07:53,176 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-07-29 00:07:53,177 - INFO - 检测是否出现滑块验证...
2025-07-29 00:07:53,183 - INFO - 在主文档中未找到滑块，检查iframe...
2025-07-29 00:07:53,191 - INFO - 找到 1 个iframe
2025-07-29 00:07:53,192 - INFO - 检查第 1 个iframe...
2025-07-29 00:07:53,213 - INFO - 在第 1 个iframe中检测到滑块验证，开始处理...
2025-07-29 00:07:53,213 - INFO - 开始处理滑块验证...
2025-07-29 00:07:53,225 - INFO - 滑块容器宽度: 300px
2025-07-29 00:07:53,226 - INFO - 计算拖拽距离: 278px (比例: 0.93)
2025-07-29 00:07:53,226 - INFO - 开始匀速不分段滑块操作...
2025-07-29 00:07:53,226 - INFO - 尝试drag_and_drop_by_offset，拖拽距离: 278px
2025-07-29 00:07:53,868 - INFO - drag_and_drop_by_offset执行完成
2025-07-29 00:07:56,869 - INFO - 匀速不分段滑块操作完成
2025-07-29 00:07:56,873 - INFO - 滑块验证成功，滑块已消失
2025-07-29 00:07:56,874 - INFO - 滑块验证处理成功，准备重试铺货
2025-07-29 00:07:58,875 - INFO - 开始第 2 次铺货尝试...
2025-07-29 00:07:58,875 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-29 00:07:58,875 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-29 00:07:58,884 - INFO - span innerHTML: 立即铺货
2025-07-29 00:08:09,073 - WARNING - 等待元素可点击超时: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-29 00:08:09,073 - WARNING - 使用配置的XPath未找到添加商品按钮
2025-07-29 00:08:09,073 - ERROR - 未找到铺货按钮
2025-07-29 00:08:09,074 - WARNING - 第 2 个商品处理失败（不计入成功数量）
2025-07-29 00:08:09,074 - INFO - 商品处理失败，立即处理下一个商品
2025-07-29 00:08:09,087 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=789447097664
2025-07-29 00:08:12,829 - INFO - 页面加载完成
2025-07-29 00:08:12,829 - INFO - 模拟阅读行为，停顿 2.4 秒
2025-07-29 00:08:17,034 - INFO - 开始第 1 次铺货尝试...
2025-07-29 00:08:17,035 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-29 00:08:17,035 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-29 00:08:17,044 - INFO - span innerHTML: 立即铺货
2025-07-29 00:08:17,064 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-29 00:08:17,065 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-29 00:08:17,080 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-29 00:08:18,390 - INFO - 尝试ActionChains点击...
2025-07-29 00:08:18,673 - INFO - ActionChains点击成功
2025-07-29 00:08:19,025 - INFO - 铺货按钮点击成功
2025-07-29 00:08:21,040 - INFO - 使用配置的XPath找到确认按钮
2025-07-29 00:08:21,054 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-29 00:08:22,249 - INFO - 尝试ActionChains点击...
2025-07-29 00:08:22,527 - INFO - ActionChains点击成功
2025-07-29 00:08:22,766 - INFO - 确认按钮点击成功
2025-07-29 00:08:24,767 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-29 00:08:24,776 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-29 00:08:24,777 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:08:25,785 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-29 00:08:25,785 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:08:26,792 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-29 00:08:26,793 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:08:27,803 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-29 00:08:27,803 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:08:28,809 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-07-29 00:08:28,809 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:08:29,819 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-07-29 00:08:29,819 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:08:30,826 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-07-29 00:08:30,826 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:08:31,835 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-07-29 00:08:31,835 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:08:32,842 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-07-29 00:08:32,843 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:08:33,849 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-07-29 00:08:33,849 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:08:34,851 - INFO - 等待超时，进行最后一次检查...
2025-07-29 00:08:34,860 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-07-29 00:08:34,860 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-07-29 00:08:34,860 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-07-29 00:08:34,860 - INFO - 检测是否出现滑块验证...
2025-07-29 00:08:34,869 - INFO - 在主文档中未找到滑块，检查iframe...
2025-07-29 00:08:34,875 - INFO - 找到 1 个iframe
2025-07-29 00:08:34,876 - INFO - 检查第 1 个iframe...
2025-07-29 00:08:34,898 - INFO - 在第 1 个iframe中检测到滑块验证，开始处理...
2025-07-29 00:08:34,898 - INFO - 开始处理滑块验证...
2025-07-29 00:08:34,910 - INFO - 滑块容器宽度: 300px
2025-07-29 00:08:34,910 - INFO - 计算拖拽距离: 268px (比例: 0.89)
2025-07-29 00:08:34,910 - INFO - 开始匀速不分段滑块操作...
2025-07-29 00:08:34,910 - INFO - 尝试drag_and_drop_by_offset，拖拽距离: 268px
2025-07-29 00:08:35,575 - INFO - drag_and_drop_by_offset执行完成
2025-07-29 00:08:38,576 - INFO - 匀速不分段滑块操作完成
2025-07-29 00:08:38,581 - INFO - 滑块验证成功，滑块已消失
2025-07-29 00:08:38,584 - INFO - 滑块验证处理成功，准备重试铺货
2025-07-29 00:08:40,584 - INFO - 开始第 2 次铺货尝试...
2025-07-29 00:08:40,584 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-29 00:08:40,584 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-29 00:08:40,593 - INFO - span innerHTML: 立即铺货
2025-07-29 00:08:50,775 - WARNING - 等待元素可点击超时: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-29 00:08:50,775 - WARNING - 使用配置的XPath未找到添加商品按钮
2025-07-29 00:08:50,775 - ERROR - 未找到铺货按钮
2025-07-29 00:08:50,775 - WARNING - 第 3 个商品处理失败（不计入成功数量）
2025-07-29 00:08:50,776 - INFO - 商品处理失败，立即处理下一个商品
2025-07-29 00:08:50,786 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=694682843417
2025-07-29 00:08:54,399 - INFO - 页面加载完成
2025-07-29 00:08:54,399 - INFO - 模拟阅读行为，停顿 2.4 秒
2025-07-29 00:08:57,626 - INFO - 开始第 1 次铺货尝试...
2025-07-29 00:08:57,627 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-29 00:08:57,627 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-29 00:08:57,636 - INFO - span innerHTML: 立即铺货
2025-07-29 00:08:57,659 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-29 00:08:57,659 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-29 00:08:57,674 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-29 00:08:59,007 - INFO - 尝试ActionChains点击...
2025-07-29 00:08:59,305 - INFO - ActionChains点击成功
2025-07-29 00:08:59,640 - INFO - 铺货按钮点击成功
2025-07-29 00:09:01,655 - INFO - 使用配置的XPath找到确认按钮
2025-07-29 00:09:01,666 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-29 00:09:03,116 - INFO - 尝试ActionChains点击...
2025-07-29 00:09:03,393 - INFO - ActionChains点击成功
2025-07-29 00:09:03,660 - INFO - 确认按钮点击成功
2025-07-29 00:09:05,660 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-29 00:09:05,668 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-29 00:09:05,668 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:09:06,676 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-29 00:09:06,676 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:09:07,685 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-29 00:09:07,686 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:09:08,696 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-29 00:09:08,696 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:09:09,704 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-07-29 00:09:09,704 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:09:10,713 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-07-29 00:09:10,714 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:09:11,721 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-07-29 00:09:11,721 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:09:12,729 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-07-29 00:09:12,729 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:09:13,738 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-07-29 00:09:13,738 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:09:14,746 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-07-29 00:09:14,746 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:09:15,747 - INFO - 等待超时，进行最后一次检查...
2025-07-29 00:09:15,755 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-07-29 00:09:15,756 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-07-29 00:09:15,756 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-07-29 00:09:15,756 - INFO - 检测是否出现滑块验证...
2025-07-29 00:09:15,764 - INFO - 在主文档中未找到滑块，检查iframe...
2025-07-29 00:09:15,770 - INFO - 找到 1 个iframe
2025-07-29 00:09:15,770 - INFO - 检查第 1 个iframe...
2025-07-29 00:09:15,794 - INFO - 在第 1 个iframe中检测到滑块验证，开始处理...
2025-07-29 00:09:15,794 - INFO - 开始处理滑块验证...
2025-07-29 00:09:15,808 - INFO - 滑块容器宽度: 300px
2025-07-29 00:09:15,808 - INFO - 计算拖拽距离: 273px (比例: 0.91)
2025-07-29 00:09:15,808 - INFO - 开始匀速不分段滑块操作...
2025-07-29 00:09:15,808 - INFO - 尝试drag_and_drop_by_offset，拖拽距离: 273px
2025-07-29 00:09:16,443 - INFO - drag_and_drop_by_offset执行完成
2025-07-29 00:09:19,445 - INFO - 匀速不分段滑块操作完成
2025-07-29 00:09:19,449 - INFO - 滑块验证成功，滑块已消失
2025-07-29 00:09:19,449 - INFO - 滑块验证处理成功，准备重试铺货
2025-07-29 00:09:21,450 - INFO - 开始第 2 次铺货尝试...
2025-07-29 00:09:21,450 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-29 00:09:21,450 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-29 00:09:21,460 - INFO - span innerHTML: 立即铺货
2025-07-29 00:09:31,640 - WARNING - 等待元素可点击超时: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-29 00:09:31,641 - WARNING - 使用配置的XPath未找到添加商品按钮
2025-07-29 00:09:31,641 - ERROR - 未找到铺货按钮
2025-07-29 00:09:31,641 - WARNING - 第 4 个商品处理失败（不计入成功数量）
2025-07-29 00:09:31,641 - INFO - 商品处理失败，立即处理下一个商品
2025-07-29 00:09:31,653 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=681082809892
2025-07-29 00:09:35,272 - INFO - 页面加载完成
2025-07-29 00:09:35,272 - INFO - 模拟阅读行为，停顿 2.6 秒
2025-07-29 00:09:38,918 - INFO - 开始第 1 次铺货尝试...
2025-07-29 00:09:38,919 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-29 00:09:38,919 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-29 00:09:38,926 - INFO - span innerHTML: 立即铺货
2025-07-29 00:09:38,948 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-29 00:09:38,948 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-29 00:09:38,965 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-29 00:09:40,108 - INFO - 尝试ActionChains点击...
2025-07-29 00:09:40,397 - INFO - ActionChains点击成功
2025-07-29 00:09:40,697 - INFO - 铺货按钮点击成功
2025-07-29 00:09:42,713 - INFO - 使用配置的XPath找到确认按钮
2025-07-29 00:09:42,727 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-29 00:09:44,118 - INFO - 尝试ActionChains点击...
2025-07-29 00:09:44,393 - INFO - ActionChains点击成功
2025-07-29 00:09:44,826 - INFO - 确认按钮点击成功
2025-07-29 00:09:46,826 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-29 00:09:46,836 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-29 00:09:46,836 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:09:47,845 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-29 00:09:47,845 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:09:48,853 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-29 00:09:48,853 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:09:49,861 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-29 00:09:49,861 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:09:50,869 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-07-29 00:09:50,869 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:09:51,878 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-07-29 00:09:51,879 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:09:52,887 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-07-29 00:09:52,887 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:09:53,898 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-07-29 00:09:53,898 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:09:54,905 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-07-29 00:09:54,905 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:09:55,916 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-07-29 00:09:55,916 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:09:56,916 - INFO - 等待超时，进行最后一次检查...
2025-07-29 00:09:56,923 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-07-29 00:09:56,923 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-07-29 00:09:56,924 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-07-29 00:09:56,924 - INFO - 检测是否出现滑块验证...
2025-07-29 00:09:56,935 - INFO - 在主文档中未找到滑块，检查iframe...
2025-07-29 00:09:56,942 - INFO - 找到 1 个iframe
2025-07-29 00:09:56,942 - INFO - 检查第 1 个iframe...
2025-07-29 00:09:56,963 - INFO - 在第 1 个iframe中检测到滑块验证，开始处理...
2025-07-29 00:09:56,964 - INFO - 开始处理滑块验证...
2025-07-29 00:09:56,978 - INFO - 滑块容器宽度: 300px
2025-07-29 00:09:56,979 - INFO - 计算拖拽距离: 265px (比例: 0.88)
2025-07-29 00:09:56,979 - INFO - 开始匀速不分段滑块操作...
2025-07-29 00:09:56,979 - INFO - 尝试drag_and_drop_by_offset，拖拽距离: 265px
2025-07-29 00:09:57,604 - INFO - drag_and_drop_by_offset执行完成
2025-07-29 00:10:00,605 - INFO - 匀速不分段滑块操作完成
2025-07-29 00:10:00,608 - INFO - 滑块验证成功，滑块已消失
2025-07-29 00:10:00,609 - INFO - 滑块验证处理成功，准备重试铺货
2025-07-29 00:10:02,611 - INFO - 开始第 2 次铺货尝试...
2025-07-29 00:10:02,611 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-29 00:10:02,611 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-29 00:10:02,620 - INFO - span innerHTML: 立即铺货
2025-07-29 00:10:12,801 - WARNING - 等待元素可点击超时: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-29 00:10:12,801 - WARNING - 使用配置的XPath未找到添加商品按钮
2025-07-29 00:10:12,801 - ERROR - 未找到铺货按钮
2025-07-29 00:10:12,802 - WARNING - 第 5 个商品处理失败（不计入成功数量）
2025-07-29 00:10:12,802 - INFO - 商品处理失败，立即处理下一个商品
2025-07-29 00:10:12,802 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=694675776881
2025-07-29 00:10:15,917 - INFO - 页面加载完成
2025-07-29 00:10:15,918 - INFO - 模拟阅读行为，停顿 1.2 秒
2025-07-29 00:10:17,086 - INFO - 添加额外延迟: 1.1秒
2025-07-29 00:10:18,744 - INFO - 开始第 1 次铺货尝试...
2025-07-29 00:10:18,744 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-29 00:10:18,744 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-29 00:10:18,750 - INFO - span innerHTML: 立即铺货
2025-07-29 00:10:18,772 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-29 00:10:18,773 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-29 00:10:18,789 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-29 00:10:20,058 - INFO - 尝试ActionChains点击...
2025-07-29 00:10:20,347 - INFO - ActionChains点击成功
2025-07-29 00:10:20,616 - INFO - 铺货按钮点击成功
2025-07-29 00:10:22,631 - INFO - 使用配置的XPath找到确认按钮
2025-07-29 00:10:22,643 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-29 00:10:23,747 - INFO - 尝试ActionChains点击...
2025-07-29 00:10:24,030 - INFO - ActionChains点击成功
2025-07-29 00:10:24,276 - INFO - 确认按钮点击成功
