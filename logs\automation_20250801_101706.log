2025-08-01 10:17:12,450 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49cda]
	(No symbol) [0x0x7ff65bf51679]
	(No symbol) [0x0x7ff65bf54a61]
	(No symbol) [0x0x7ff65bff1e4b]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 10:17:12,450 - INFO - 防检测机制设置完成
2025-08-01 10:17:12,450 - INFO - 浏览器驱动初始化成功
2025-08-01 10:17:12,455 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 10:17:12,459 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 10:17:12,462 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 10:17:12,466 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 10:17:12,472 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 10:17:12,475 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 10:17:12,478 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 10:17:12,480 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 10:17:12,488 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 10:17:12,491 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 10:17:12,493 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 10:17:12,497 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 10:17:12,502 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 10:17:12,504 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 10:17:12,507 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 10:17:12,511 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 10:17:12,515 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 10:17:12,518 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 10:17:12,520 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 10:17:12,524 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 10:17:12,524 - INFO - Cookies已加载
2025-08-01 10:17:12,525 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?channel=global_zx&spm=a21ug5.29159167.6452766900.2ndview_zxmore_click
2025-08-01 10:17:19,140 - INFO - 当前User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-01 10:17:19,141 - INFO - 确认：使用桌面版User-Agent
2025-08-01 10:17:19,144 - INFO - 桌面版User-Agent设置完成: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-01 10:17:20,245 - WARNING - 随机鼠标移动失败: Message: move target out of bounds
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65bffbe53]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 10:17:21,674 - INFO - 成功导航到目标页面
2025-08-01 10:17:21,675 - INFO - 登录成功！现在可以开始铺货任务
2025-08-01 10:17:23,798 - INFO - Cookies已保存
2025-08-01 10:17:26,083 - INFO - 浏览器已关闭
2025-08-01 10:17:26,084 - INFO - 以无界面(headless)模式启动Chrome，仅添加headless、no-sandbox、disable-dev-shm-usage参数
2025-08-01 10:17:28,114 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49cda]
	(No symbol) [0x0x7ff65bf51679]
	(No symbol) [0x0x7ff65bf54a61]
	(No symbol) [0x0x7ff65bff1e4b]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 10:17:28,114 - INFO - 防检测机制设置完成
2025-08-01 10:17:28,114 - INFO - 浏览器驱动初始化成功
2025-08-01 10:17:28,114 - INFO - 开始自动化铺货流程（无头模式）
2025-08-01 10:17:28,114 - INFO - 第一步：随机选择了【宠物园艺】分类页面
2025-08-01 10:17:28,114 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?cateName=%25E5%25AE%25A0%25E7%2589%25A9%25E5%259B%25AD%25E8%2589%25BA&categoryList=50007216%2C29%2C124466001&keyword=&spm=a21ug5.29159167.9840561350.showIndustry
2025-08-01 10:17:32,529 - INFO - 第一步：点击登录按钮
2025-08-01 10:17:32,574 - INFO - 找到登录按钮，使用选择器: //*[@id='login-form']/div[6]/button
2025-08-01 10:17:33,602 - INFO - 准备点击登录按钮: 标签=button, 文本='登录', 选择器=//*[@id='login-form']/div[6]/button
2025-08-01 10:17:33,616 - INFO - 准备点击元素: tag=button, text='登录', class='fm-button fm-submit password-l', id=''
2025-08-01 10:17:34,947 - INFO - 尝试ActionChains点击...
2025-08-01 10:17:39,037 - INFO - ActionChains点击成功
2025-08-01 10:17:39,466 - INFO - 登录按钮点击成功
2025-08-01 10:17:39,466 - INFO - 登录按钮点击成功
2025-08-01 10:17:42,467 - INFO - 第二步：获取账号信息...
2025-08-01 10:17:42,467 - INFO - 正在跳转到个人中心页面: https://jingya.taobao.com/?v=2
2025-08-01 10:17:48,655 - INFO - 用户请求停止自动化流程
2025-08-01 10:17:55,004 - ERROR - 获取账号名称时出错: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-08-01 10:17:55,005 - WARNING - 未能获取账号名称
