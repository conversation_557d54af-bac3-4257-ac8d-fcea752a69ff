2025-07-28 23:36:05,594 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059cda]
	(No symbol) [0x0x7ff69a061679]
	(No symbol) [0x0x7ff69a064a61]
	(No symbol) [0x0x7ff69a101e4b]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:36:05,594 - INFO - 防检测机制设置完成
2025-07-28 23:36:05,594 - INFO - 浏览器驱动初始化成功
2025-07-28 23:36:05,600 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:36:05,604 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:36:05,607 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:36:05,612 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:36:05,616 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:36:05,623 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:36:05,626 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:36:05,629 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:36:05,631 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:36:05,634 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:36:05,638 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:36:05,641 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:36:05,645 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:36:05,647 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:36:05,652 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:36:05,656 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:36:05,659 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:36:05,663 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:36:05,667 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:36:05,670 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:36:05,670 - INFO - Cookies已加载
2025-07-28 23:36:05,670 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?channel=global_zx&spm=a21ug5.29159167.6452766900.2ndview_zxmore_click
2025-07-28 23:36:11,830 - INFO - 当前User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-28 23:36:11,830 - INFO - 确认：使用桌面版User-Agent
2025-07-28 23:36:11,834 - INFO - 桌面版User-Agent设置完成: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-28 23:36:12,557 - WARNING - 随机鼠标移动失败: Message: move target out of bounds
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a10be53]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:36:12,561 - INFO - 成功导航到目标页面
2025-07-28 23:36:12,561 - INFO - 登录成功！现在可以开始铺货任务
2025-07-28 23:36:14,731 - INFO - Cookies已保存
2025-07-28 23:36:16,988 - INFO - 浏览器已关闭
2025-07-28 23:36:18,376 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059cda]
	(No symbol) [0x0x7ff69a061679]
	(No symbol) [0x0x7ff69a064a61]
	(No symbol) [0x0x7ff69a101e4b]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:36:18,376 - INFO - 防检测机制设置完成
2025-07-28 23:36:18,376 - INFO - 浏览器驱动初始化成功
2025-07-28 23:36:18,376 - INFO - 开始自动化铺货流程（有界面模式）
2025-07-28 23:36:18,376 - INFO - 第一步：正在导航到目标页面...
2025-07-28 23:36:22,126 - INFO - 第一步：点击登录按钮
2025-07-28 23:36:38,682 - INFO - 找到登录按钮，使用选择器: //*[@id='login-form']/div[6]/button
2025-07-28 23:36:39,715 - INFO - 准备点击登录按钮: 标签=button, 文本='登录', 选择器=//*[@id='login-form']/div[6]/button
2025-07-28 23:36:39,738 - INFO - 准备点击元素: tag=button, text='登录', class='fm-button fm-submit password-l', id=''
2025-07-28 23:36:41,083 - INFO - 尝试ActionChains点击...
2025-07-28 23:36:41,485 - INFO - ActionChains点击成功
2025-07-28 23:36:41,825 - INFO - 登录按钮点击成功
2025-07-28 23:36:41,825 - INFO - 登录按钮点击成功
2025-07-28 23:36:44,826 - INFO - 第二步：获取账号信息...
2025-07-28 23:36:44,826 - INFO - 正在跳转到个人中心页面: https://jingya.taobao.com/?v=2
2025-07-28 23:37:09,876 - INFO - 尝试获取账号名称，使用XPath: //*[@id='workbench-user-tool-btn']/div/div[2]
2025-07-28 23:37:09,889 - INFO - 成功获取账号信息: 卓祥22店:冰淇淋
2025-07-28 23:37:09,889 - INFO - 获取到账号名称: 卓祥22店:冰淇淋
2025-07-28 23:37:09,890 - INFO - GUI界面已更新账号显示: 卓祥22店:冰淇淋
2025-07-28 23:37:09,890 - INFO - 第三步：开始读取20个商品信息...
2025-07-28 23:37:09,894 - INFO - 开始爬取商品信息，最大数量: 20
2025-07-28 23:37:14,060 - INFO - 正在爬取第 1 页商品信息...
2025-07-28 23:37:16,069 - INFO - 找到商品容器，开始爬取商品，最大数量: 20
2025-07-28 23:37:16,082 - INFO - 找到商品项 1，检查元素信息...
2025-07-28 23:37:16,093 - INFO - 商品项 1 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:37:16,096 - INFO - 商品项 1 直接获取到data-autolog: item_id=681778175573&path=search_goods_card&type=search
2025-07-28 23:37:16,097 - INFO - 商品项 1 data-autolog: item_id=681778175573&path=search_goods_card&type=search
2025-07-28 23:37:16,097 - INFO - 商品项 1 提取到item_id: 681778175573
2025-07-28 23:37:16,109 - INFO - 商品项 1 商品名称: 保税直供 Dove多芬磨砂膏石榴籽乳木果风味身体磨砂膏298g
2025-07-28 23:37:16,121 - INFO - 找到商品项 2，检查元素信息...
2025-07-28 23:37:16,129 - INFO - 商品项 2 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:37:16,131 - INFO - 商品项 2 直接获取到data-autolog: item_id=690841537159&path=search_goods_card&index=1&type=search
2025-07-28 23:37:16,131 - INFO - 商品项 2 data-autolog: item_id=690841537159&path=search_goods_card&index=1&type=search
2025-07-28 23:37:16,131 - INFO - 商品项 2 提取到item_id: 690841537159
2025-07-28 23:37:16,140 - INFO - 商品项 2 商品名称: 保税直供 凡士林Vaseline烟酰胺身体乳400ml 725ml无压泵款200ml
2025-07-28 23:37:16,151 - INFO - 找到商品项 3，检查元素信息...
2025-07-28 23:37:16,159 - INFO - 商品项 3 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:37:16,162 - INFO - 商品项 3 直接获取到data-autolog: item_id=789447097664&path=search_goods_card&index=2&type=search
2025-07-28 23:37:16,162 - INFO - 商品项 3 data-autolog: item_id=789447097664&path=search_goods_card&index=2&type=search
2025-07-28 23:37:16,162 - INFO - 商品项 3 提取到item_id: 789447097664
2025-07-28 23:37:16,170 - INFO - 商品项 3 商品名称: 【甄选】珂润Curel泡沫洗面奶150ml
2025-07-28 23:37:16,180 - INFO - 找到商品项 4，检查元素信息...
2025-07-28 23:37:16,253 - INFO - 商品项 4 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:37:16,264 - INFO - 商品项 4 直接获取到data-autolog: item_id=694682843417&path=search_goods_card&index=3&type=search
2025-07-28 23:37:16,264 - INFO - 商品项 4 data-autolog: item_id=694682843417&path=search_goods_card&index=3&type=search
2025-07-28 23:37:16,265 - INFO - 商品项 4 提取到item_id: 694682843417
2025-07-28 23:37:16,284 - INFO - 商品项 4 商品名称: 【可开授权】日台混发Fino发膜美容液护发素 发膜230g
2025-07-28 23:37:16,297 - INFO - 找到商品项 5，检查元素信息...
2025-07-28 23:37:16,308 - INFO - 商品项 5 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:37:16,312 - INFO - 商品项 5 直接获取到data-autolog: item_id=681082809892&path=search_goods_card&index=4&type=search
2025-07-28 23:37:16,312 - INFO - 商品项 5 data-autolog: item_id=681082809892&path=search_goods_card&index=4&type=search
2025-07-28 23:37:16,312 - INFO - 商品项 5 提取到item_id: 681082809892
2025-07-28 23:37:16,321 - INFO - 商品项 5 商品名称: 保税直供 Dove多芬大白碗润肤身体乳300ml 【1//2//3罐】
2025-07-28 23:37:16,333 - INFO - 找到商品项 6，检查元素信息...
2025-07-28 23:37:16,342 - INFO - 商品项 6 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:37:16,346 - INFO - 商品项 6 直接获取到data-autolog: item_id=694675776881&path=search_goods_card&index=5&type=search
2025-07-28 23:37:16,346 - INFO - 商品项 6 data-autolog: item_id=694675776881&path=search_goods_card&index=5&type=search
2025-07-28 23:37:16,347 - INFO - 商品项 6 提取到item_id: 694675776881
2025-07-28 23:37:16,356 - INFO - 商品项 6 商品名称: 保税直供 Pantene潘婷护发素三分钟奇迹多效修护发膜级护发素
2025-07-28 23:37:16,366 - INFO - 找到商品项 7，检查元素信息...
2025-07-28 23:37:16,376 - INFO - 商品项 7 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:37:16,379 - INFO - 商品项 7 直接获取到data-autolog: item_id=680729304287&path=search_goods_card&index=6&type=search
2025-07-28 23:37:16,379 - INFO - 商品项 7 data-autolog: item_id=680729304287&path=search_goods_card&index=6&type=search
2025-07-28 23:37:16,379 - INFO - 商品项 7 提取到item_id: 680729304287
2025-07-28 23:37:16,389 - INFO - 商品项 7 商品名称: 保税直供 多芬dove空气感无硅油 洗发水480g 护发素480g护发临期
2025-07-28 23:37:16,400 - INFO - 找到商品项 8，检查元素信息...
2025-07-28 23:37:16,409 - INFO - 商品项 8 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:37:16,411 - INFO - 商品项 8 直接获取到data-autolog: item_id=710355107054&path=search_goods_card&index=7&type=search
2025-07-28 23:37:16,411 - INFO - 商品项 8 data-autolog: item_id=710355107054&path=search_goods_card&index=7&type=search
2025-07-28 23:37:16,411 - INFO - 商品项 8 提取到item_id: 710355107054
2025-07-28 23:37:16,421 - INFO - 商品项 8 商品名称: 保税直供 Dove多芬洗发水护发素空气感洗发水480g 【护发素临期
2025-07-28 23:37:16,432 - INFO - 找到商品项 9，检查元素信息...
2025-07-28 23:37:16,440 - INFO - 商品项 9 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:37:16,442 - INFO - 商品项 9 直接获取到data-autolog: item_id=740463658412&path=search_goods_card&index=8&type=search
2025-07-28 23:37:16,442 - INFO - 商品项 9 data-autolog: item_id=740463658412&path=search_goods_card&index=8&type=search
2025-07-28 23:37:16,442 - INFO - 商品项 9 提取到item_id: 740463658412
2025-07-28 23:37:16,452 - INFO - 商品项 9 商品名称: 【主推链接】花王cape空气感定型喷雾50g/180g 编码：3305300000
2025-07-28 23:37:16,463 - INFO - 找到商品项 10，检查元素信息...
2025-07-28 23:37:16,471 - INFO - 商品项 10 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:37:16,474 - INFO - 商品项 10 直接获取到data-autolog: item_id=821067902127&path=search_goods_card&index=9&type=search
2025-07-28 23:37:16,474 - INFO - 商品项 10 data-autolog: item_id=821067902127&path=search_goods_card&index=9&type=search
2025-07-28 23:37:16,474 - INFO - 商品项 10 提取到item_id: 821067902127
2025-07-28 23:37:16,483 - INFO - 商品项 10 商品名称: 丹碧丝TAMPAX卫生棉条长导管式内置式纯棉月经棉条棉棒游泳卫生巾
2025-07-28 23:37:16,495 - INFO - 找到商品项 11，检查元素信息...
2025-07-28 23:37:16,503 - INFO - 商品项 11 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:37:16,506 - INFO - 商品项 11 直接获取到data-autolog: item_id=789835300215&path=search_goods_card&index=10&type=search
2025-07-28 23:37:16,506 - INFO - 商品项 11 data-autolog: item_id=789835300215&path=search_goods_card&index=10&type=search
2025-07-28 23:37:16,506 - INFO - 商品项 11 提取到item_id: 789835300215
2025-07-28 23:37:16,515 - INFO - 商品项 11 商品名称: 【保税】雅漾喷雾爽肤水300ml
2025-07-28 23:37:16,525 - INFO - 找到商品项 12，检查元素信息...
2025-07-28 23:37:16,533 - INFO - 商品项 12 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:37:16,536 - INFO - 商品项 12 直接获取到data-autolog: item_id=843747605382&path=search_goods_card&index=11&type=search
2025-07-28 23:37:16,537 - INFO - 商品项 12 data-autolog: item_id=843747605382&path=search_goods_card&index=11&type=search
2025-07-28 23:37:16,537 - INFO - 商品项 12 提取到item_id: 843747605382
2025-07-28 23:37:16,545 - INFO - 商品项 12 商品名称: 倩碧紫胖子卸妆膏125ml/瓶
2025-07-28 23:37:16,581 - INFO - 找到商品项 13，检查元素信息...
2025-07-28 23:37:16,591 - INFO - 商品项 13 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:37:16,594 - INFO - 商品项 13 直接获取到data-autolog: item_id=675816539149&path=search_goods_card&index=12&type=search
2025-07-28 23:37:16,594 - INFO - 商品项 13 data-autolog: item_id=675816539149&path=search_goods_card&index=12&type=search
2025-07-28 23:37:16,594 - INFO - 商品项 13 提取到item_id: 675816539149
2025-07-28 23:37:16,603 - INFO - 商品项 13 商品名称: 新加坡Vicopyl维可保益生菌(Pylopass/拒绝幽门菌/抑口臭/调肠胃)
2025-07-28 23:37:16,614 - INFO - 找到商品项 14，检查元素信息...
2025-07-28 23:37:16,623 - INFO - 商品项 14 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:37:16,626 - INFO - 商品项 14 直接获取到data-autolog: item_id=846634805775&path=search_goods_card&index=13&type=search
2025-07-28 23:37:16,626 - INFO - 商品项 14 data-autolog: item_id=846634805775&path=search_goods_card&index=13&type=search
2025-07-28 23:37:16,627 - INFO - 商品项 14 提取到item_id: 846634805775
2025-07-28 23:37:16,636 - INFO - 商品项 14 商品名称: 【甄选】cow牛乳石碱原味/玫瑰花香 瓶装480ml
2025-07-28 23:37:16,647 - INFO - 找到商品项 15，检查元素信息...
2025-07-28 23:37:16,657 - INFO - 商品项 15 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:37:16,659 - INFO - 商品项 15 直接获取到data-autolog: item_id=852510575114&path=search_goods_card&index=14&type=search
2025-07-28 23:37:16,660 - INFO - 商品项 15 data-autolog: item_id=852510575114&path=search_goods_card&index=14&type=search
2025-07-28 23:37:16,660 - INFO - 商品项 15 提取到item_id: 852510575114
2025-07-28 23:37:16,684 - INFO - 商品项 15 商品名称: Nars 纳斯 红壳蜜粉饼16g（含粉扑）、Nars纳斯裸光蜜粉饼10g
2025-07-28 23:37:16,695 - INFO - 找到商品项 16，检查元素信息...
2025-07-28 23:37:16,704 - INFO - 商品项 16 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:37:16,707 - INFO - 商品项 16 直接获取到data-autolog: item_id=743442966826&path=search_goods_card&index=15&type=search
2025-07-28 23:37:16,707 - INFO - 商品项 16 data-autolog: item_id=743442966826&path=search_goods_card&index=15&type=search
2025-07-28 23:37:16,707 - INFO - 商品项 16 提取到item_id: 743442966826
2025-07-28 23:37:16,717 - INFO - 商品项 16 商品名称: ESI卡路里拜拜片白芸豆阻断片大餐救星膳食纤维身材热控管理
2025-07-28 23:37:16,727 - INFO - 找到商品项 17，检查元素信息...
2025-07-28 23:37:16,737 - INFO - 商品项 17 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:37:16,740 - INFO - 商品项 17 直接获取到data-autolog: item_id=848934676698&path=search_goods_card&index=16&type=search
2025-07-28 23:37:16,740 - INFO - 商品项 17 data-autolog: item_id=848934676698&path=search_goods_card&index=16&type=search
2025-07-28 23:37:16,740 - INFO - 商品项 17 提取到item_id: 848934676698
2025-07-28 23:37:16,748 - INFO - 商品项 17 商品名称: Estee Lauder 雅诗兰黛持妆粉底液30ml 2C0 /1w1/1C1
2025-07-28 23:37:16,761 - INFO - 找到商品项 18，检查元素信息...
2025-07-28 23:37:16,768 - INFO - 商品项 18 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:37:16,770 - INFO - 商品项 18 直接获取到data-autolog: item_id=693758717931&path=search_goods_card&index=17&type=search
2025-07-28 23:37:16,771 - INFO - 商品项 18 data-autolog: item_id=693758717931&path=search_goods_card&index=17&type=search
2025-07-28 23:37:16,771 - INFO - 商品项 18 提取到item_id: 693758717931
2025-07-28 23:37:16,781 - INFO - 商品项 18 商品名称: 保税直供 资生堂fino高效渗透发膜 230g （1/2/3罐）日版
2025-07-28 23:37:16,793 - INFO - 找到商品项 19，检查元素信息...
2025-07-28 23:37:16,801 - INFO - 商品项 19 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:37:16,804 - INFO - 商品项 19 直接获取到data-autolog: item_id=691142128229&path=search_goods_card&index=18&type=search
2025-07-28 23:37:16,805 - INFO - 商品项 19 data-autolog: item_id=691142128229&path=search_goods_card&index=18&type=search
2025-07-28 23:37:16,805 - INFO - 商品项 19 提取到item_id: 691142128229
2025-07-28 23:37:16,829 - INFO - 商品项 19 商品名称: 保税 日版Dove多芬洁面洗面奶新版 正装瓶150ml 补充袋125ml 甄选
2025-07-28 23:37:16,840 - INFO - 找到商品项 20，检查元素信息...
2025-07-28 23:37:16,847 - INFO - 商品项 20 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:37:16,850 - INFO - 商品项 20 直接获取到data-autolog: item_id=690739721578&path=search_goods_card&index=19&type=search
2025-07-28 23:37:16,850 - INFO - 商品项 20 data-autolog: item_id=690739721578&path=search_goods_card&index=19&type=search
2025-07-28 23:37:16,850 - INFO - 商品项 20 提取到item_id: 690739721578
2025-07-28 23:37:16,858 - INFO - 商品项 20 商品名称: 保税直供 SUNSILK夏士莲椰子洗发水//护发素450ml 泰产
2025-07-28 23:37:16,859 - INFO - 已爬取到 20 个商品，停止当前页面爬取
2025-07-28 23:37:16,859 - INFO - 当前页面爬取完成，找到 20 个商品
2025-07-28 23:37:16,859 - INFO - 第 1 页找到 20 个商品，总计: 20
2025-07-28 23:37:16,859 - INFO - 已达到最大商品数量 20，停止爬取
2025-07-28 23:37:16,859 - INFO - 爬取完成，共找到 20 个商品
2025-07-28 23:37:16,859 - INFO - 成功读取到 20 个商品（目标20个），开始铺货任务
2025-07-28 23:37:16,859 - INFO - 第四步：开始处理商品...
2025-07-28 23:37:16,871 - INFO - 已读取 20 个商品，目标成功铺货 3 个商品
2025-07-28 23:37:16,871 - INFO - 当前批次大小: 15
2025-07-28 23:37:16,872 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=681778175573
2025-07-28 23:37:20,528 - INFO - 页面加载完成
2025-07-28 23:37:20,529 - INFO - 模拟阅读行为，停顿 1.6 秒
2025-07-28 23:37:22,161 - INFO - 添加额外延迟: 1.0秒
2025-07-28 23:37:24,672 - INFO - 开始第 1 次铺货尝试...
2025-07-28 23:37:24,672 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:37:24,672 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:37:24,683 - INFO - span innerHTML: 立即铺货
2025-07-28 23:37:24,714 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-28 23:37:24,714 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-28 23:37:24,726 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-28 23:37:25,835 - INFO - 尝试ActionChains点击...
2025-07-28 23:37:26,120 - INFO - ActionChains点击成功
2025-07-28 23:37:26,361 - INFO - 铺货按钮点击成功
2025-07-28 23:37:28,376 - INFO - 使用配置的XPath找到确认按钮
2025-07-28 23:37:28,402 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-28 23:37:29,701 - INFO - 尝试ActionChains点击...
2025-07-28 23:37:29,967 - INFO - ActionChains点击成功
2025-07-28 23:37:30,253 - INFO - 确认按钮点击成功
2025-07-28 23:37:32,255 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-28 23:37:32,262 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-28 23:37:32,262 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:37:33,291 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-28 23:37:33,292 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:37:34,300 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-28 23:37:34,300 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:37:35,309 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-28 23:37:35,309 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:37:36,316 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-07-28 23:37:36,316 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:37:37,326 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-07-28 23:37:37,326 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:37:38,335 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-07-28 23:37:38,335 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:37:39,342 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-07-28 23:37:39,342 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:37:40,350 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-07-28 23:37:40,350 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:37:41,357 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-07-28 23:37:41,357 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:37:42,358 - INFO - 等待超时，进行最后一次检查...
2025-07-28 23:37:42,366 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-07-28 23:37:42,366 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-07-28 23:37:42,366 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-07-28 23:37:42,367 - INFO - 检测是否出现滑块验证...
2025-07-28 23:37:42,375 - INFO - 在主文档中未找到滑块，检查iframe...
2025-07-28 23:37:42,382 - INFO - 找到 1 个iframe
2025-07-28 23:37:42,382 - INFO - 检查第 1 个iframe...
2025-07-28 23:37:42,405 - INFO - 在第 1 个iframe中检测到滑块验证，开始处理...
2025-07-28 23:37:42,405 - INFO - 开始处理滑块验证...
2025-07-28 23:37:42,419 - INFO - 滑块容器宽度: 300px
2025-07-28 23:37:42,419 - INFO - 计算拖拽距离: 274px (比例: 0.92)
2025-07-28 23:37:42,420 - INFO - 开始人工化拖拽...
2025-07-28 23:37:51,153 - INFO - 人工化拖拽完成
2025-07-28 23:37:51,157 - INFO - 滑块验证成功，滑块已消失
2025-07-28 23:37:51,159 - INFO - 滑块验证处理成功，准备重试铺货
2025-07-28 23:37:53,160 - INFO - 开始第 2 次铺货尝试...
2025-07-28 23:37:53,160 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:37:53,160 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:37:53,170 - INFO - span innerHTML: 立即铺货
2025-07-28 23:38:03,395 - WARNING - 等待元素可点击超时: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:38:03,395 - WARNING - 使用配置的XPath未找到添加商品按钮
2025-07-28 23:38:03,396 - ERROR - 未找到铺货按钮
2025-07-28 23:38:03,396 - WARNING - 第 1 个商品处理失败（不计入成功数量）
2025-07-28 23:38:03,396 - INFO - 商品处理失败，立即处理下一个商品
2025-07-28 23:38:03,409 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=690841537159
2025-07-28 23:38:07,046 - INFO - 页面加载完成
2025-07-28 23:38:07,046 - INFO - 模拟阅读行为，停顿 3.0 秒
2025-07-28 23:38:11,044 - INFO - 开始第 1 次铺货尝试...
2025-07-28 23:38:11,044 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:38:11,044 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:38:11,051 - INFO - span innerHTML: 立即铺货
2025-07-28 23:38:11,071 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-28 23:38:11,071 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-28 23:38:11,085 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-28 23:38:12,270 - INFO - 尝试ActionChains点击...
2025-07-28 23:38:12,540 - INFO - ActionChains点击成功
2025-07-28 23:38:12,822 - INFO - 铺货按钮点击成功
2025-07-28 23:38:14,837 - INFO - 使用配置的XPath找到确认按钮
2025-07-28 23:38:14,850 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-28 23:38:15,952 - INFO - 尝试ActionChains点击...
2025-07-28 23:38:16,221 - INFO - ActionChains点击成功
2025-07-28 23:38:16,534 - INFO - 确认按钮点击成功
2025-07-28 23:38:18,534 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-28 23:38:18,546 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-28 23:38:18,546 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:38:19,555 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-28 23:38:19,555 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:38:20,565 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-28 23:38:20,565 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:38:21,572 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-28 23:38:21,572 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:38:22,581 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-07-28 23:38:22,581 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:38:23,588 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-07-28 23:38:23,589 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:38:24,596 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-07-28 23:38:24,597 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:38:25,604 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-07-28 23:38:25,604 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:38:26,613 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-07-28 23:38:26,613 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:38:27,623 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-07-28 23:38:27,623 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:38:28,624 - INFO - 等待超时，进行最后一次检查...
2025-07-28 23:38:28,631 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-07-28 23:38:28,631 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-07-28 23:38:28,631 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-07-28 23:38:28,631 - INFO - 检测是否出现滑块验证...
2025-07-28 23:38:28,638 - INFO - 在主文档中未找到滑块，检查iframe...
2025-07-28 23:38:28,647 - INFO - 找到 1 个iframe
2025-07-28 23:38:28,647 - INFO - 检查第 1 个iframe...
2025-07-28 23:38:28,667 - INFO - 在第 1 个iframe中检测到滑块验证，开始处理...
2025-07-28 23:38:28,667 - INFO - 开始处理滑块验证...
2025-07-28 23:38:28,678 - INFO - 滑块容器宽度: 300px
2025-07-28 23:38:28,679 - INFO - 计算拖拽距离: 269px (比例: 0.90)
2025-07-28 23:38:28,679 - INFO - 开始人工化拖拽...
2025-07-28 23:38:37,874 - INFO - 人工化拖拽完成
2025-07-28 23:38:37,879 - INFO - 滑块验证成功，滑块已消失
2025-07-28 23:38:37,880 - INFO - 滑块验证处理成功，准备重试铺货
2025-07-28 23:38:39,881 - INFO - 开始第 2 次铺货尝试...
2025-07-28 23:38:39,881 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:38:39,881 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:38:39,892 - INFO - span innerHTML: 立即铺货
2025-07-28 23:38:50,104 - WARNING - 等待元素可点击超时: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:38:50,105 - WARNING - 使用配置的XPath未找到添加商品按钮
2025-07-28 23:38:50,105 - ERROR - 未找到铺货按钮
2025-07-28 23:38:50,105 - WARNING - 第 2 个商品处理失败（不计入成功数量）
2025-07-28 23:38:50,105 - INFO - 商品处理失败，立即处理下一个商品
2025-07-28 23:38:50,117 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=789447097664
2025-07-28 23:38:53,816 - INFO - 页面加载完成
2025-07-28 23:38:53,817 - INFO - 模拟阅读行为，停顿 1.2 秒
2025-07-28 23:38:56,098 - INFO - 开始第 1 次铺货尝试...
2025-07-28 23:38:56,099 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:38:56,099 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:38:56,105 - INFO - span innerHTML: 立即铺货
2025-07-28 23:38:56,130 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-28 23:38:56,130 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-28 23:38:56,144 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-28 23:38:57,388 - INFO - 尝试ActionChains点击...
2025-07-28 23:38:57,674 - INFO - ActionChains点击成功
2025-07-28 23:38:58,111 - INFO - 铺货按钮点击成功
2025-07-28 23:39:00,129 - INFO - 使用配置的XPath找到确认按钮
2025-07-28 23:39:00,142 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-28 23:39:01,463 - INFO - 尝试ActionChains点击...
2025-07-28 23:39:01,730 - INFO - ActionChains点击成功
2025-07-28 23:39:02,119 - INFO - 确认按钮点击成功
2025-07-28 23:39:04,120 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-28 23:39:04,128 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-28 23:39:04,128 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:39:05,138 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-28 23:39:05,138 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:39:06,146 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-28 23:39:06,146 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:39:07,155 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-28 23:39:07,155 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:39:08,162 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-07-28 23:39:08,162 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:39:09,171 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-07-28 23:39:09,172 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:39:10,182 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-07-28 23:39:10,182 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:39:11,192 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-07-28 23:39:11,192 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:39:12,201 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-07-28 23:39:12,201 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:39:13,210 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-07-28 23:39:13,211 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:39:14,211 - INFO - 等待超时，进行最后一次检查...
2025-07-28 23:39:14,218 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-07-28 23:39:14,218 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-07-28 23:39:14,219 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-07-28 23:39:14,219 - INFO - 检测是否出现滑块验证...
2025-07-28 23:39:14,227 - INFO - 在主文档中未找到滑块，检查iframe...
2025-07-28 23:39:14,234 - INFO - 找到 1 个iframe
2025-07-28 23:39:14,234 - INFO - 检查第 1 个iframe...
2025-07-28 23:39:14,255 - INFO - 在第 1 个iframe中检测到滑块验证，开始处理...
2025-07-28 23:39:14,255 - INFO - 开始处理滑块验证...
2025-07-28 23:39:14,278 - INFO - 滑块容器宽度: 300px
2025-07-28 23:39:14,278 - INFO - 计算拖拽距离: 278px (比例: 0.93)
2025-07-28 23:39:14,278 - INFO - 开始人工化拖拽...
2025-07-28 23:39:25,246 - INFO - 人工化拖拽完成
2025-07-28 23:39:25,250 - INFO - 滑块验证成功，滑块已消失
2025-07-28 23:39:25,252 - INFO - 滑块验证处理成功，准备重试铺货
2025-07-28 23:39:27,253 - INFO - 开始第 2 次铺货尝试...
2025-07-28 23:39:27,253 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:39:27,253 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:39:27,264 - INFO - span innerHTML: 立即铺货
2025-07-28 23:39:37,532 - WARNING - 等待元素可点击超时: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:39:37,532 - WARNING - 使用配置的XPath未找到添加商品按钮
2025-07-28 23:39:37,532 - ERROR - 未找到铺货按钮
2025-07-28 23:39:37,532 - WARNING - 第 3 个商品处理失败（不计入成功数量）
2025-07-28 23:39:37,532 - INFO - 商品处理失败，立即处理下一个商品
2025-07-28 23:39:37,533 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=694682843417
2025-07-28 23:39:41,115 - INFO - 页面加载完成
2025-07-28 23:39:41,115 - INFO - 模拟阅读行为，停顿 2.8 秒
2025-07-28 23:39:45,362 - INFO - 开始第 1 次铺货尝试...
2025-07-28 23:39:45,362 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:39:45,362 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:39:45,371 - INFO - span innerHTML: 立即铺货
2025-07-28 23:39:45,394 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-28 23:39:45,395 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-28 23:39:45,411 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-28 23:39:46,478 - INFO - 尝试ActionChains点击...
2025-07-28 23:39:46,770 - INFO - ActionChains点击成功
2025-07-28 23:39:47,140 - INFO - 铺货按钮点击成功
2025-07-28 23:39:49,164 - INFO - 使用配置的XPath找到确认按钮
2025-07-28 23:39:49,177 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-28 23:39:50,579 - INFO - 尝试ActionChains点击...
2025-07-28 23:39:50,846 - INFO - ActionChains点击成功
2025-07-28 23:39:51,194 - INFO - 确认按钮点击成功
2025-07-28 23:39:53,194 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-28 23:39:53,202 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-28 23:39:53,203 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:39:54,212 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-28 23:39:54,212 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:39:55,221 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-28 23:39:55,221 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:39:56,229 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-28 23:39:56,229 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:39:57,238 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-07-28 23:39:57,238 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:39:58,246 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-07-28 23:39:58,246 - INFO - span仍为'立即铺货'，继续等待...
