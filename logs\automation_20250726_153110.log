2025-07-26 15:31:15,980 - INFO - 使用固定桌面版User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-26 15:31:17,419 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9cda]
	(No symbol) [0x0x7ff7a71f1679]
	(No symbol) [0x0x7ff7a71f4a61]
	(No symbol) [0x0x7ff7a7291e4b]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:31:17,430 - INFO - 防检测机制设置完成
2025-07-26 15:31:17,466 - INFO - 浏览器窗口已最大化
2025-07-26 15:31:17,542 - INFO - 已尝试进入全屏模式（F11）
2025-07-26 15:31:17,581 - INFO - 已通过JavaScript请求全屏
2025-07-26 15:31:17,593 - INFO - 浏览器窗口最终尺寸: 2560x1440
2025-07-26 15:31:17,602 - INFO - 浏览器驱动初始化成功
2025-07-26 15:31:17,618 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:31:17,635 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:31:17,653 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:31:17,666 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:31:17,684 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:31:17,699 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:31:17,713 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:31:17,724 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:31:17,737 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:31:17,749 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:31:17,760 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:31:17,771 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:31:17,783 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:31:17,794 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:31:17,804 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:31:17,815 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:31:17,826 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:31:17,835 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:31:17,843 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:31:17,852 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:31:17,862 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:31:17,869 - INFO - Cookies已加载
2025-07-26 15:31:17,882 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?channel=global_zx&spm=a21ug5.29159167.6452766900.2ndview_zxmore_click
2025-07-26 15:31:17,892 - INFO - 添加额外延迟: 1.7秒
2025-07-26 15:31:24,279 - INFO - 当前User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-26 15:31:24,292 - INFO - 确认：使用桌面版User-Agent
2025-07-26 15:31:24,311 - INFO - 桌面版User-Agent设置完成: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-26 15:31:24,359 - INFO - 浏览器窗口已最大化
2025-07-26 15:31:24,436 - INFO - 已尝试进入全屏模式（F11）
2025-07-26 15:31:24,475 - INFO - 已通过JavaScript请求全屏
2025-07-26 15:31:24,496 - INFO - 浏览器窗口最终尺寸: 2560x1440
2025-07-26 15:31:25,160 - WARNING - 随机鼠标移动失败: Message: move target out of bounds
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a729be53]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:31:25,171 - WARNING - 检测到需要登录，请手动登录后继续
2025-07-26 15:31:25,186 - INFO - 请在打开的浏览器中完成登录，登录完成后点击'确认登录完成'按钮
2025-07-26 15:31:36,290 - INFO - Cookies已保存
2025-07-26 15:31:36,293 - INFO - 登录成功！现在可以开始铺货任务
2025-07-26 15:31:40,985 - INFO - 开始自动化铺货流程
2025-07-26 15:31:40,994 - INFO - 正在返回主页面...
2025-07-26 15:31:44,049 - INFO - 已返回主页面
2025-07-26 15:31:44,078 - INFO - 找到 40 个直接子div
2025-07-26 15:31:44,106 - INFO - 商品 1: 位置={'x': 678, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=681778175573&path=search_goods_card&type=s
2025-07-26 15:31:44,133 - INFO - 商品 2: 位置={'x': 915, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=690841537159&path=search_goods_card&index=
2025-07-26 15:31:44,188 - INFO - 商品 3: 位置={'x': 1152, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=789447097664&path=search_goods_card&index=
2025-07-26 15:31:44,222 - INFO - 商品 4: 位置={'x': 1389, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=694682843417&path=search_goods_card&index=
2025-07-26 15:31:44,257 - INFO - 商品 5: 位置={'x': 1626, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=681082809892&path=search_goods_card&index=
2025-07-26 15:31:44,293 - INFO - 商品 6: 位置={'x': 678, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=694675776881&path=search_goods_card&index=
2025-07-26 15:31:44,343 - INFO - 商品 7: 位置={'x': 915, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=680729304287&path=search_goods_card&index=
2025-07-26 15:31:44,384 - INFO - 商品 8: 位置={'x': 1152, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=710355107054&path=search_goods_card&index=
2025-07-26 15:31:44,422 - INFO - 商品 9: 位置={'x': 1389, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=740463658412&path=search_goods_card&index=
2025-07-26 15:31:44,459 - INFO - 商品 10: 位置={'x': 1626, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=821067902127&path=search_goods_card&index=
2025-07-26 15:31:44,498 - INFO - 商品 11: 位置={'x': 678, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=789835300215&path=search_goods_card&index=
2025-07-26 15:31:44,538 - INFO - 商品 12: 位置={'x': 915, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=843747605382&path=search_goods_card&index=
2025-07-26 15:31:44,581 - INFO - 商品 13: 位置={'x': 1152, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=675816539149&path=search_goods_card&index=
2025-07-26 15:31:44,619 - INFO - 商品 14: 位置={'x': 1389, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=846634805775&path=search_goods_card&index=
2025-07-26 15:31:44,659 - INFO - 商品 15: 位置={'x': 1626, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=852510575114&path=search_goods_card&index=
2025-07-26 15:31:44,697 - INFO - 商品 16: 位置={'x': 678, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=743442966826&path=search_goods_card&index=
2025-07-26 15:31:44,738 - INFO - 商品 17: 位置={'x': 915, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=848934676698&path=search_goods_card&index=
2025-07-26 15:31:44,776 - INFO - 商品 18: 位置={'x': 1152, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=693758717931&path=search_goods_card&index=
2025-07-26 15:31:44,817 - INFO - 商品 19: 位置={'x': 1389, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=691142128229&path=search_goods_card&index=
2025-07-26 15:31:44,856 - INFO - 商品 20: 位置={'x': 1626, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=690739721578&path=search_goods_card&index=
2025-07-26 15:31:44,894 - INFO - 商品 21: 位置={'x': 678, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=691918824108&path=search_goods_card&index=
2025-07-26 15:31:44,934 - INFO - 商品 22: 位置={'x': 915, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=875045022715&path=search_goods_card&index=
2025-07-26 15:31:44,973 - INFO - 商品 23: 位置={'x': 1152, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=873911671570&path=search_goods_card&index=
2025-07-26 15:31:45,012 - INFO - 商品 24: 位置={'x': 1389, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=722937163980&path=search_goods_card&index=
2025-07-26 15:31:45,053 - INFO - 商品 25: 位置={'x': 1626, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=739090443937&path=search_goods_card&index=
2025-07-26 15:31:45,094 - INFO - 商品 26: 位置={'x': 678, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=777862601010&path=search_goods_card&index=
2025-07-26 15:31:45,147 - INFO - 商品 27: 位置={'x': 915, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=686870174450&path=search_goods_card&index=
2025-07-26 15:31:45,187 - INFO - 商品 28: 位置={'x': 1152, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=814542665480&path=search_goods_card&index=
2025-07-26 15:31:45,227 - INFO - 商品 29: 位置={'x': 1389, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=852625718015&path=search_goods_card&index=
2025-07-26 15:31:45,286 - INFO - 商品 30: 位置={'x': 1626, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=681472049289&path=search_goods_card&index=
2025-07-26 15:31:45,326 - INFO - 商品 31: 位置={'x': 678, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=683142617961&path=search_goods_card&index=
2025-07-26 15:31:45,362 - INFO - 商品 32: 位置={'x': 915, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=763659951211&path=search_goods_card&index=
2025-07-26 15:31:45,403 - INFO - 商品 33: 位置={'x': 1152, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=675478966678&path=search_goods_card&index=
2025-07-26 15:31:45,443 - INFO - 商品 34: 位置={'x': 1389, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=800365611234&path=search_goods_card&index=
2025-07-26 15:31:45,482 - INFO - 商品 35: 位置={'x': 1626, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=896721954982&path=search_goods_card&index=
2025-07-26 15:31:45,521 - INFO - 商品 36: 位置={'x': 678, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=795389643023&path=search_goods_card&index=
2025-07-26 15:31:45,575 - INFO - 商品 37: 位置={'x': 915, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=689584396900&path=search_goods_card&index=
2025-07-26 15:31:45,614 - INFO - 商品 38: 位置={'x': 1152, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=788516102627&path=search_goods_card&index=
2025-07-26 15:31:45,675 - INFO - 商品 39: 位置={'x': 1389, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=825025224622&path=search_goods_card&index=
2025-07-26 15:31:45,714 - INFO - 商品 40: 位置={'x': 1626, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=842638336638&path=search_goods_card&index=
2025-07-26 15:31:45,748 - INFO - 找到 40 个有效的商品容器
2025-07-26 15:31:45,775 - INFO - 找到 40 个商品，将处理 20 个
2025-07-26 15:31:45,803 - INFO - 当前批次大小: 13
2025-07-26 15:31:45,827 - INFO - 开始处理第 1/40 个商品
2025-07-26 15:31:45,870 - INFO - 找到 40 个直接子div
2025-07-26 15:31:45,905 - INFO - 商品 1: 位置={'x': 678, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=681778175573&path=search_goods_card&type=s
2025-07-26 15:31:45,941 - INFO - 商品 2: 位置={'x': 915, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=690841537159&path=search_goods_card&index=
2025-07-26 15:31:45,979 - INFO - 商品 3: 位置={'x': 1152, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=789447097664&path=search_goods_card&index=
2025-07-26 15:31:46,015 - INFO - 商品 4: 位置={'x': 1389, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=694682843417&path=search_goods_card&index=
2025-07-26 15:31:46,054 - INFO - 商品 5: 位置={'x': 1626, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=681082809892&path=search_goods_card&index=
2025-07-26 15:31:46,096 - INFO - 商品 6: 位置={'x': 678, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=694675776881&path=search_goods_card&index=
2025-07-26 15:31:46,138 - INFO - 商品 7: 位置={'x': 915, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=680729304287&path=search_goods_card&index=
2025-07-26 15:31:46,176 - INFO - 商品 8: 位置={'x': 1152, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=710355107054&path=search_goods_card&index=
2025-07-26 15:31:46,217 - INFO - 商品 9: 位置={'x': 1389, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=740463658412&path=search_goods_card&index=
2025-07-26 15:31:46,256 - INFO - 商品 10: 位置={'x': 1626, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=821067902127&path=search_goods_card&index=
2025-07-26 15:31:46,299 - INFO - 商品 11: 位置={'x': 678, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=789835300215&path=search_goods_card&index=
2025-07-26 15:31:46,401 - INFO - 商品 12: 位置={'x': 915, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=843747605382&path=search_goods_card&index=
2025-07-26 15:31:46,446 - INFO - 商品 13: 位置={'x': 1152, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=675816539149&path=search_goods_card&index=
2025-07-26 15:31:46,486 - INFO - 商品 14: 位置={'x': 1389, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=846634805775&path=search_goods_card&index=
2025-07-26 15:31:46,529 - INFO - 商品 15: 位置={'x': 1626, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=852510575114&path=search_goods_card&index=
2025-07-26 15:31:46,567 - INFO - 商品 16: 位置={'x': 678, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=743442966826&path=search_goods_card&index=
2025-07-26 15:31:46,607 - INFO - 商品 17: 位置={'x': 915, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=848934676698&path=search_goods_card&index=
2025-07-26 15:31:46,645 - INFO - 商品 18: 位置={'x': 1152, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=693758717931&path=search_goods_card&index=
2025-07-26 15:31:46,685 - INFO - 商品 19: 位置={'x': 1389, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=691142128229&path=search_goods_card&index=
2025-07-26 15:31:46,726 - INFO - 商品 20: 位置={'x': 1626, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=690739721578&path=search_goods_card&index=
2025-07-26 15:31:46,765 - INFO - 商品 21: 位置={'x': 678, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=691918824108&path=search_goods_card&index=
2025-07-26 15:31:46,807 - INFO - 商品 22: 位置={'x': 915, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=875045022715&path=search_goods_card&index=
2025-07-26 15:31:46,845 - INFO - 商品 23: 位置={'x': 1152, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=873911671570&path=search_goods_card&index=
2025-07-26 15:31:46,883 - INFO - 商品 24: 位置={'x': 1389, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=722937163980&path=search_goods_card&index=
2025-07-26 15:31:46,921 - INFO - 商品 25: 位置={'x': 1626, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=739090443937&path=search_goods_card&index=
2025-07-26 15:31:46,967 - INFO - 商品 26: 位置={'x': 678, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=777862601010&path=search_goods_card&index=
2025-07-26 15:31:47,012 - INFO - 商品 27: 位置={'x': 915, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=686870174450&path=search_goods_card&index=
2025-07-26 15:31:47,079 - INFO - 商品 28: 位置={'x': 1152, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=814542665480&path=search_goods_card&index=
2025-07-26 15:31:47,117 - INFO - 商品 29: 位置={'x': 1389, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=852625718015&path=search_goods_card&index=
2025-07-26 15:31:47,158 - INFO - 商品 30: 位置={'x': 1626, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=681472049289&path=search_goods_card&index=
2025-07-26 15:31:47,199 - INFO - 商品 31: 位置={'x': 678, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=683142617961&path=search_goods_card&index=
2025-07-26 15:31:47,239 - INFO - 商品 32: 位置={'x': 915, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=763659951211&path=search_goods_card&index=
2025-07-26 15:31:47,280 - INFO - 商品 33: 位置={'x': 1152, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=675478966678&path=search_goods_card&index=
2025-07-26 15:31:47,317 - INFO - 商品 34: 位置={'x': 1389, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=800365611234&path=search_goods_card&index=
2025-07-26 15:31:47,359 - INFO - 商品 35: 位置={'x': 1626, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=896721954982&path=search_goods_card&index=
2025-07-26 15:31:47,398 - INFO - 商品 36: 位置={'x': 678, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=795389643023&path=search_goods_card&index=
2025-07-26 15:31:47,439 - INFO - 商品 37: 位置={'x': 915, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=689584396900&path=search_goods_card&index=
2025-07-26 15:31:47,480 - INFO - 商品 38: 位置={'x': 1152, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=788516102627&path=search_goods_card&index=
2025-07-26 15:31:47,520 - INFO - 商品 39: 位置={'x': 1389, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=825025224622&path=search_goods_card&index=
2025-07-26 15:31:47,561 - INFO - 商品 40: 位置={'x': 1626, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=842638336638&path=search_goods_card&index=
2025-07-26 15:31:47,595 - INFO - 找到 40 个有效的商品容器
2025-07-26 15:31:47,621 - WARNING - 警告：第 1 个元素可能不是商品项，class=''
2025-07-26 15:31:47,667 - INFO - 商品 1 信息: 文本='保税直供 Dove多芬磨砂膏石榴籽乳木果风味身体磨砂膏298g
采购价¥35.00销量11.2万+件', 位置={'x': 678, 'y': 737}
2025-07-26 15:31:49,206 - INFO - 商品 1 位置: {'x': 678, 'y': 737}, 尺寸: {'height': 347, 'width': 226}
2025-07-26 15:31:49,227 - INFO - 模拟阅读行为，停顿 2.7 秒
2025-07-26 15:31:52,832 - INFO - 添加额外延迟: 2.4秒
2025-07-26 15:31:56,406 - INFO - 点击前标签页数量: 1
2025-07-26 15:31:56,426 - INFO - 尝试策略: ActionChains点击
2025-07-26 15:31:58,393 - INFO - 点击后标签页数量: 2
2025-07-26 15:31:58,411 - INFO - ActionChains点击成功，新标签页已打开
2025-07-26 15:31:58,425 - INFO - 已点击第 1 个商品，等待页面响应...
2025-07-26 15:32:05,451 - WARNING - 等待新标签页超时，未检测到新标签页
2025-07-26 15:32:05,461 - WARNING - 未检测到新标签页，可能是页面内跳转
2025-07-26 15:32:08,477 - INFO - 找到 0 个iframe
2025-07-26 15:32:08,486 - INFO - 未找到包含目标元素的iframe，保持在主文档
2025-07-26 15:32:08,492 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-26 15:32:18,638 - WARNING - 等待元素可点击超时: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-26 15:32:18,648 - INFO - 尝试立即铺货选择器: //button[contains(text(), '立即铺货')]
2025-07-26 15:32:18,664 - INFO - 尝试立即铺货选择器: //button[.//span[contains(text(), '立即铺货')]]
2025-07-26 15:32:18,685 - INFO - 尝试立即铺货选择器: //button[@class='next-btn next-large next-btn-primary'][contains(., '立即铺货')]
2025-07-26 15:32:18,704 - INFO - 尝试立即铺货选择器: //*[@id='root']//button[contains(text(), '立即铺货')]
2025-07-26 15:32:18,719 - INFO - 尝试立即铺货选择器: //*[@id='root']//div[3]//button[contains(text(), '立即铺货')]
2025-07-26 15:32:18,746 - INFO - 尝试立即铺货选择器: //*[@id='root']//div[contains(@class, 'action')]//button[contains(text(), '立即铺货')]
2025-07-26 15:32:18,767 - INFO - 尝试查找页面中包含'立即铺货'的按钮...
2025-07-26 15:32:18,784 - INFO - 找到 10 个按钮元素
2025-07-26 15:32:18,810 - INFO - 按钮 1: 文本='筛选', class='next-btn next-medium next-btn-primary', id=''
2025-07-26 15:32:18,831 - INFO - 按钮 2: 文本='重置', class='next-btn next-medium next-btn-normal', id=''
2025-07-26 15:32:18,852 - INFO - 按钮 3: 文本='上一页', class='next-btn next-medium next-btn-normal next-paginati', id=''
2025-07-26 15:32:18,873 - INFO - 按钮 4: 文本='1', class='next-btn next-medium next-btn-normal next-paginati', id=''
2025-07-26 15:32:18,912 - INFO - 按钮 5: 文本='2', class='next-btn next-medium next-btn-normal next-paginati', id=''
2025-07-26 15:32:18,930 - INFO - 按钮 6: 文本='3', class='next-btn next-medium next-btn-normal next-paginati', id=''
2025-07-26 15:32:18,951 - INFO - 按钮 7: 文本='4', class='next-btn next-medium next-btn-normal next-paginati', id=''
2025-07-26 15:32:18,968 - INFO - 按钮 8: 文本='44', class='next-btn next-medium next-btn-normal next-paginati', id=''
2025-07-26 15:32:18,985 - INFO - 按钮 9: 文本='下一页', class='next-btn next-medium next-btn-normal next-paginati', id=''
2025-07-26 15:32:19,002 - INFO - 按钮 10: 文本='确定', class='next-btn next-medium next-btn-normal next-paginati', id=''
2025-07-26 15:32:19,011 - ERROR - 未能找到任何合适的添加商品按钮
2025-07-26 15:32:19,020 - ERROR - 未找到添加商品按钮
2025-07-26 15:32:22,423 - WARNING - 第 1 个商品处理失败
2025-07-26 15:32:22,429 - INFO - 等待 7.9 秒后处理下一个商品
2025-07-26 15:32:30,347 - INFO - 开始处理第 2/40 个商品
2025-07-26 15:32:30,368 - INFO - 找到 40 个直接子div
2025-07-26 15:32:30,396 - INFO - 商品 1: 位置={'x': 678, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=681778175573&path=search_goods_card&type=s
2025-07-26 15:32:30,422 - INFO - 商品 2: 位置={'x': 915, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=690841537159&path=search_goods_card&index=
2025-07-26 15:32:30,449 - INFO - 商品 3: 位置={'x': 1152, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=789447097664&path=search_goods_card&index=
2025-07-26 15:32:30,481 - INFO - 商品 4: 位置={'x': 1389, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=694682843417&path=search_goods_card&index=
2025-07-26 15:32:30,514 - INFO - 商品 5: 位置={'x': 1626, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=681082809892&path=search_goods_card&index=
2025-07-26 15:32:30,549 - INFO - 商品 6: 位置={'x': 678, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=694675776881&path=search_goods_card&index=
2025-07-26 15:32:30,584 - INFO - 商品 7: 位置={'x': 915, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=680729304287&path=search_goods_card&index=
2025-07-26 15:32:30,623 - INFO - 商品 8: 位置={'x': 1152, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=710355107054&path=search_goods_card&index=
2025-07-26 15:32:30,660 - INFO - 商品 9: 位置={'x': 1389, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=740463658412&path=search_goods_card&index=
2025-07-26 15:32:30,701 - INFO - 商品 10: 位置={'x': 1626, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=821067902127&path=search_goods_card&index=
2025-07-26 15:32:30,739 - INFO - 商品 11: 位置={'x': 678, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=789835300215&path=search_goods_card&index=
2025-07-26 15:32:30,780 - INFO - 商品 12: 位置={'x': 915, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=843747605382&path=search_goods_card&index=
2025-07-26 15:32:30,816 - INFO - 商品 13: 位置={'x': 1152, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=675816539149&path=search_goods_card&index=
2025-07-26 15:32:30,857 - INFO - 商品 14: 位置={'x': 1389, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=846634805775&path=search_goods_card&index=
2025-07-26 15:32:30,895 - INFO - 商品 15: 位置={'x': 1626, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=852510575114&path=search_goods_card&index=
2025-07-26 15:32:30,943 - INFO - 商品 16: 位置={'x': 678, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=743442966826&path=search_goods_card&index=
2025-07-26 15:32:30,983 - INFO - 商品 17: 位置={'x': 915, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=848934676698&path=search_goods_card&index=
2025-07-26 15:32:31,022 - INFO - 商品 18: 位置={'x': 1152, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=693758717931&path=search_goods_card&index=
2025-07-26 15:32:31,062 - INFO - 商品 19: 位置={'x': 1389, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=691142128229&path=search_goods_card&index=
2025-07-26 15:32:31,102 - INFO - 商品 20: 位置={'x': 1626, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=690739721578&path=search_goods_card&index=
2025-07-26 15:32:31,141 - INFO - 商品 21: 位置={'x': 678, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=691918824108&path=search_goods_card&index=
2025-07-26 15:32:31,181 - INFO - 商品 22: 位置={'x': 915, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=875045022715&path=search_goods_card&index=
2025-07-26 15:32:31,221 - INFO - 商品 23: 位置={'x': 1152, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=873911671570&path=search_goods_card&index=
2025-07-26 15:32:31,260 - INFO - 商品 24: 位置={'x': 1389, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=722937163980&path=search_goods_card&index=
2025-07-26 15:32:31,299 - INFO - 商品 25: 位置={'x': 1626, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=739090443937&path=search_goods_card&index=
2025-07-26 15:32:31,344 - INFO - 商品 26: 位置={'x': 678, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=777862601010&path=search_goods_card&index=
2025-07-26 15:32:31,385 - INFO - 商品 27: 位置={'x': 915, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=686870174450&path=search_goods_card&index=
2025-07-26 15:32:31,427 - INFO - 商品 28: 位置={'x': 1152, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=814542665480&path=search_goods_card&index=
2025-07-26 15:32:31,469 - INFO - 商品 29: 位置={'x': 1389, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=852625718015&path=search_goods_card&index=
2025-07-26 15:32:31,507 - INFO - 商品 30: 位置={'x': 1626, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=681472049289&path=search_goods_card&index=
2025-07-26 15:32:31,551 - INFO - 商品 31: 位置={'x': 678, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=683142617961&path=search_goods_card&index=
2025-07-26 15:32:31,592 - INFO - 商品 32: 位置={'x': 915, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=763659951211&path=search_goods_card&index=
2025-07-26 15:32:31,630 - INFO - 商品 33: 位置={'x': 1152, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=675478966678&path=search_goods_card&index=
2025-07-26 15:32:31,671 - INFO - 商品 34: 位置={'x': 1389, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=800365611234&path=search_goods_card&index=
2025-07-26 15:32:31,711 - INFO - 商品 35: 位置={'x': 1626, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=896721954982&path=search_goods_card&index=
2025-07-26 15:32:31,752 - INFO - 商品 36: 位置={'x': 678, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=795389643023&path=search_goods_card&index=
2025-07-26 15:32:31,790 - INFO - 商品 37: 位置={'x': 915, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=689584396900&path=search_goods_card&index=
2025-07-26 15:32:31,832 - INFO - 商品 38: 位置={'x': 1152, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=788516102627&path=search_goods_card&index=
2025-07-26 15:32:31,872 - INFO - 商品 39: 位置={'x': 1389, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=825025224622&path=search_goods_card&index=
2025-07-26 15:32:31,910 - INFO - 商品 40: 位置={'x': 1626, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=842638336638&path=search_goods_card&index=
2025-07-26 15:32:31,944 - INFO - 找到 40 个有效的商品容器
2025-07-26 15:32:31,977 - WARNING - 警告：第 2 个元素可能不是商品项，class=''
2025-07-26 15:32:32,040 - INFO - 商品 2 信息: 文本='保税直供 凡士林Vaseline烟酰胺身体乳400ml 725ml无压泵款200ml
采购价¥19.', 位置={'x': 915, 'y': 737}
2025-07-26 15:32:33,585 - INFO - 商品 2 位置: {'x': 914, 'y': 737}, 尺寸: {'height': 347, 'width': 226}
2025-07-26 15:32:33,610 - INFO - 模拟阅读行为，停顿 2.0 秒
2025-07-26 15:32:36,808 - INFO - 点击前标签页数量: 2
2025-07-26 15:32:36,833 - INFO - 尝试策略: ActionChains点击
2025-07-26 15:32:43,884 - INFO - 点击后标签页数量: 3
2025-07-26 15:32:43,902 - INFO - ActionChains点击成功，新标签页已打开
2025-07-26 15:32:43,918 - INFO - 已点击第 2 个商品，等待页面响应...
2025-07-26 15:32:50,949 - WARNING - 等待新标签页超时，未检测到新标签页
2025-07-26 15:32:50,964 - WARNING - 未检测到新标签页，可能是页面内跳转
2025-07-26 15:32:53,977 - INFO - 找到 0 个iframe
2025-07-26 15:32:53,992 - INFO - 未找到包含目标元素的iframe，保持在主文档
2025-07-26 15:32:54,004 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-26 15:33:04,249 - WARNING - 等待元素可点击超时: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-26 15:33:04,260 - INFO - 尝试立即铺货选择器: //button[contains(text(), '立即铺货')]
2025-07-26 15:33:04,271 - INFO - 尝试立即铺货选择器: //button[.//span[contains(text(), '立即铺货')]]
2025-07-26 15:33:04,309 - INFO - 尝试立即铺货选择器: //button[@class='next-btn next-large next-btn-primary'][contains(., '立即铺货')]
2025-07-26 15:33:04,323 - INFO - 尝试立即铺货选择器: //*[@id='root']//button[contains(text(), '立即铺货')]
2025-07-26 15:33:04,356 - INFO - 尝试立即铺货选择器: //*[@id='root']//div[3]//button[contains(text(), '立即铺货')]
2025-07-26 15:33:04,374 - INFO - 尝试立即铺货选择器: //*[@id='root']//div[contains(@class, 'action')]//button[contains(text(), '立即铺货')]
2025-07-26 15:33:04,392 - INFO - 尝试查找页面中包含'立即铺货'的按钮...
2025-07-26 15:33:04,411 - INFO - 找到 10 个按钮元素
2025-07-26 15:33:04,433 - INFO - 按钮 1: 文本='筛选', class='next-btn next-medium next-btn-primary', id=''
2025-07-26 15:33:04,454 - INFO - 按钮 2: 文本='重置', class='next-btn next-medium next-btn-normal', id=''
2025-07-26 15:33:04,477 - INFO - 按钮 3: 文本='上一页', class='next-btn next-medium next-btn-normal next-paginati', id=''
2025-07-26 15:33:04,497 - INFO - 按钮 4: 文本='1', class='next-btn next-medium next-btn-normal next-paginati', id=''
2025-07-26 15:33:04,522 - INFO - 按钮 5: 文本='2', class='next-btn next-medium next-btn-normal next-paginati', id=''
2025-07-26 15:33:04,545 - INFO - 按钮 6: 文本='3', class='next-btn next-medium next-btn-normal next-paginati', id=''
2025-07-26 15:33:04,566 - INFO - 按钮 7: 文本='4', class='next-btn next-medium next-btn-normal next-paginati', id=''
2025-07-26 15:33:04,585 - INFO - 按钮 8: 文本='44', class='next-btn next-medium next-btn-normal next-paginati', id=''
2025-07-26 15:33:04,603 - INFO - 按钮 9: 文本='下一页', class='next-btn next-medium next-btn-normal next-paginati', id=''
2025-07-26 15:33:04,621 - INFO - 按钮 10: 文本='确定', class='next-btn next-medium next-btn-normal next-paginati', id=''
2025-07-26 15:33:04,631 - ERROR - 未能找到任何合适的添加商品按钮
2025-07-26 15:33:04,639 - ERROR - 未找到添加商品按钮
2025-07-26 15:33:06,664 - WARNING - 第 2 个商品处理失败
2025-07-26 15:33:06,670 - INFO - 等待 6.9 秒后处理下一个商品
2025-07-26 15:33:13,584 - INFO - 开始处理第 3/40 个商品
2025-07-26 15:33:13,597 - INFO - 找到 0 个直接子div
2025-07-26 15:33:13,603 - INFO - 找到 0 个有效的商品容器
2025-07-26 15:33:13,610 - ERROR - 无法获取商品列表
2025-07-26 15:33:13,619 - WARNING - 第 3 个商品处理失败
2025-07-26 15:33:13,628 - INFO - 等待 4.4 秒后处理下一个商品
2025-07-26 15:33:17,992 - INFO - 开始处理第 4/40 个商品
2025-07-26 15:33:18,005 - INFO - 找到 0 个直接子div
2025-07-26 15:33:18,011 - INFO - 找到 0 个有效的商品容器
2025-07-26 15:33:18,015 - ERROR - 无法获取商品列表
2025-07-26 15:33:18,016 - WARNING - 第 4 个商品处理失败
2025-07-26 15:33:18,022 - INFO - 等待 5.7 秒后处理下一个商品
2025-07-26 15:33:23,744 - INFO - 开始处理第 5/40 个商品
2025-07-26 15:33:23,758 - INFO - 找到 0 个直接子div
2025-07-26 15:33:23,763 - INFO - 找到 0 个有效的商品容器
2025-07-26 15:33:23,768 - ERROR - 无法获取商品列表
2025-07-26 15:33:23,776 - WARNING - 第 5 个商品处理失败
2025-07-26 15:33:23,781 - INFO - 等待 5.2 秒后处理下一个商品
2025-07-26 15:33:28,880 - INFO - 用户请求停止自动化流程
2025-07-26 15:33:29,020 - INFO - 用户停止了流程
2025-07-26 15:33:29,026 - INFO - 自动化流程已停止
2025-07-26 15:33:29,592 - INFO - Cookies已保存
2025-07-26 15:33:31,880 - INFO - 浏览器已关闭
