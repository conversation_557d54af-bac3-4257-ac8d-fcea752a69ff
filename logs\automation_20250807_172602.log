2025-08-07 17:26:08,617 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff6f911e925+77845]
	GetHandleVerifier [0x0x7ff6f911e980+77936]
	(No symbol) [0x0x7ff6f8ed9cda]
	(No symbol) [0x0x7ff6f8ee1679]
	(No symbol) [0x0x7ff6f8ee4a61]
	(No symbol) [0x0x7ff6f8f81e4b]
	(No symbol) [0x0x7ff6f8f588ca]
	(No symbol) [0x0x7ff6f8f80b07]
	(No symbol) [0x0x7ff6f8f586a3]
	(No symbol) [0x0x7ff6f8f21791]
	(No symbol) [0x0x7ff6f8f22523]
	GetHandleVerifier [0x0x7ff6f93f683d+3059501]
	GetHandleVerifier [0x0x7ff6f93f0bfd+3035885]
	GetHandleVerifier [0x0x7ff6f94103f0+3164896]
	GetHandleVerifier [0x0x7ff6f9138c2e+185118]
	GetHandleVerifier [0x0x7ff6f914053f+216111]
	GetHandleVerifier [0x0x7ff6f91272d4+113092]
	GetHandleVerifier [0x0x7ff6f9127489+113529]
	GetHandleVerifier [0x0x7ff6f910e288+10616]
	BaseThreadInitThunk [0x0x7ffecc2ae8d7+23]
	RtlUserThreadStart [0x0x7ffecd81c34c+44]

2025-08-07 17:26:08,618 - INFO - 防检测机制设置完成
2025-08-07 17:26:08,618 - INFO - 浏览器驱动初始化成功
2025-08-07 17:26:08,624 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff6f911e925+77845]
	GetHandleVerifier [0x0x7ff6f911e980+77936]
	(No symbol) [0x0x7ff6f8ed9b0c]
	(No symbol) [0x0x7ff6f8f95b46]
	(No symbol) [0x0x7ff6f8f588ca]
	(No symbol) [0x0x7ff6f8f80b07]
	(No symbol) [0x0x7ff6f8f586a3]
	(No symbol) [0x0x7ff6f8f21791]
	(No symbol) [0x0x7ff6f8f22523]
	GetHandleVerifier [0x0x7ff6f93f683d+3059501]
	GetHandleVerifier [0x0x7ff6f93f0bfd+3035885]
	GetHandleVerifier [0x0x7ff6f94103f0+3164896]
	GetHandleVerifier [0x0x7ff6f9138c2e+185118]
	GetHandleVerifier [0x0x7ff6f914053f+216111]
	GetHandleVerifier [0x0x7ff6f91272d4+113092]
	GetHandleVerifier [0x0x7ff6f9127489+113529]
	GetHandleVerifier [0x0x7ff6f910e288+10616]
	BaseThreadInitThunk [0x0x7ffecc2ae8d7+23]
	RtlUserThreadStart [0x0x7ffecd81c34c+44]

2025-08-07 17:26:08,628 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff6f911e925+77845]
	GetHandleVerifier [0x0x7ff6f911e980+77936]
	(No symbol) [0x0x7ff6f8ed9b0c]
	(No symbol) [0x0x7ff6f8f95b46]
	(No symbol) [0x0x7ff6f8f588ca]
	(No symbol) [0x0x7ff6f8f80b07]
	(No symbol) [0x0x7ff6f8f586a3]
	(No symbol) [0x0x7ff6f8f21791]
	(No symbol) [0x0x7ff6f8f22523]
	GetHandleVerifier [0x0x7ff6f93f683d+3059501]
	GetHandleVerifier [0x0x7ff6f93f0bfd+3035885]
	GetHandleVerifier [0x0x7ff6f94103f0+3164896]
	GetHandleVerifier [0x0x7ff6f9138c2e+185118]
	GetHandleVerifier [0x0x7ff6f914053f+216111]
	GetHandleVerifier [0x0x7ff6f91272d4+113092]
	GetHandleVerifier [0x0x7ff6f9127489+113529]
	GetHandleVerifier [0x0x7ff6f910e288+10616]
	BaseThreadInitThunk [0x0x7ffecc2ae8d7+23]
	RtlUserThreadStart [0x0x7ffecd81c34c+44]

2025-08-07 17:26:08,632 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff6f911e925+77845]
	GetHandleVerifier [0x0x7ff6f911e980+77936]
	(No symbol) [0x0x7ff6f8ed9b0c]
	(No symbol) [0x0x7ff6f8f95b46]
	(No symbol) [0x0x7ff6f8f588ca]
	(No symbol) [0x0x7ff6f8f80b07]
	(No symbol) [0x0x7ff6f8f586a3]
	(No symbol) [0x0x7ff6f8f21791]
	(No symbol) [0x0x7ff6f8f22523]
	GetHandleVerifier [0x0x7ff6f93f683d+3059501]
	GetHandleVerifier [0x0x7ff6f93f0bfd+3035885]
	GetHandleVerifier [0x0x7ff6f94103f0+3164896]
	GetHandleVerifier [0x0x7ff6f9138c2e+185118]
	GetHandleVerifier [0x0x7ff6f914053f+216111]
	GetHandleVerifier [0x0x7ff6f91272d4+113092]
	GetHandleVerifier [0x0x7ff6f9127489+113529]
	GetHandleVerifier [0x0x7ff6f910e288+10616]
	BaseThreadInitThunk [0x0x7ffecc2ae8d7+23]
	RtlUserThreadStart [0x0x7ffecd81c34c+44]

2025-08-07 17:26:08,636 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff6f911e925+77845]
	GetHandleVerifier [0x0x7ff6f911e980+77936]
	(No symbol) [0x0x7ff6f8ed9b0c]
	(No symbol) [0x0x7ff6f8f95b46]
	(No symbol) [0x0x7ff6f8f588ca]
	(No symbol) [0x0x7ff6f8f80b07]
	(No symbol) [0x0x7ff6f8f586a3]
	(No symbol) [0x0x7ff6f8f21791]
	(No symbol) [0x0x7ff6f8f22523]
	GetHandleVerifier [0x0x7ff6f93f683d+3059501]
	GetHandleVerifier [0x0x7ff6f93f0bfd+3035885]
	GetHandleVerifier [0x0x7ff6f94103f0+3164896]
	GetHandleVerifier [0x0x7ff6f9138c2e+185118]
	GetHandleVerifier [0x0x7ff6f914053f+216111]
	GetHandleVerifier [0x0x7ff6f91272d4+113092]
	GetHandleVerifier [0x0x7ff6f9127489+113529]
	GetHandleVerifier [0x0x7ff6f910e288+10616]
	BaseThreadInitThunk [0x0x7ffecc2ae8d7+23]
	RtlUserThreadStart [0x0x7ffecd81c34c+44]

2025-08-07 17:26:08,640 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff6f911e925+77845]
	GetHandleVerifier [0x0x7ff6f911e980+77936]
	(No symbol) [0x0x7ff6f8ed9b0c]
	(No symbol) [0x0x7ff6f8f95b46]
	(No symbol) [0x0x7ff6f8f588ca]
	(No symbol) [0x0x7ff6f8f80b07]
	(No symbol) [0x0x7ff6f8f586a3]
	(No symbol) [0x0x7ff6f8f21791]
	(No symbol) [0x0x7ff6f8f22523]
	GetHandleVerifier [0x0x7ff6f93f683d+3059501]
	GetHandleVerifier [0x0x7ff6f93f0bfd+3035885]
	GetHandleVerifier [0x0x7ff6f94103f0+3164896]
	GetHandleVerifier [0x0x7ff6f9138c2e+185118]
	GetHandleVerifier [0x0x7ff6f914053f+216111]
	GetHandleVerifier [0x0x7ff6f91272d4+113092]
	GetHandleVerifier [0x0x7ff6f9127489+113529]
	GetHandleVerifier [0x0x7ff6f910e288+10616]
	BaseThreadInitThunk [0x0x7ffecc2ae8d7+23]
	RtlUserThreadStart [0x0x7ffecd81c34c+44]

2025-08-07 17:26:08,644 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff6f911e925+77845]
	GetHandleVerifier [0x0x7ff6f911e980+77936]
	(No symbol) [0x0x7ff6f8ed9b0c]
	(No symbol) [0x0x7ff6f8f95b46]
	(No symbol) [0x0x7ff6f8f588ca]
	(No symbol) [0x0x7ff6f8f80b07]
	(No symbol) [0x0x7ff6f8f586a3]
	(No symbol) [0x0x7ff6f8f21791]
	(No symbol) [0x0x7ff6f8f22523]
	GetHandleVerifier [0x0x7ff6f93f683d+3059501]
	GetHandleVerifier [0x0x7ff6f93f0bfd+3035885]
	GetHandleVerifier [0x0x7ff6f94103f0+3164896]
	GetHandleVerifier [0x0x7ff6f9138c2e+185118]
	GetHandleVerifier [0x0x7ff6f914053f+216111]
	GetHandleVerifier [0x0x7ff6f91272d4+113092]
	GetHandleVerifier [0x0x7ff6f9127489+113529]
	GetHandleVerifier [0x0x7ff6f910e288+10616]
	BaseThreadInitThunk [0x0x7ffecc2ae8d7+23]
	RtlUserThreadStart [0x0x7ffecd81c34c+44]

2025-08-07 17:26:08,648 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff6f911e925+77845]
	GetHandleVerifier [0x0x7ff6f911e980+77936]
	(No symbol) [0x0x7ff6f8ed9b0c]
	(No symbol) [0x0x7ff6f8f95b46]
	(No symbol) [0x0x7ff6f8f588ca]
	(No symbol) [0x0x7ff6f8f80b07]
	(No symbol) [0x0x7ff6f8f586a3]
	(No symbol) [0x0x7ff6f8f21791]
	(No symbol) [0x0x7ff6f8f22523]
	GetHandleVerifier [0x0x7ff6f93f683d+3059501]
	GetHandleVerifier [0x0x7ff6f93f0bfd+3035885]
	GetHandleVerifier [0x0x7ff6f94103f0+3164896]
	GetHandleVerifier [0x0x7ff6f9138c2e+185118]
	GetHandleVerifier [0x0x7ff6f914053f+216111]
	GetHandleVerifier [0x0x7ff6f91272d4+113092]
	GetHandleVerifier [0x0x7ff6f9127489+113529]
	GetHandleVerifier [0x0x7ff6f910e288+10616]
	BaseThreadInitThunk [0x0x7ffecc2ae8d7+23]
	RtlUserThreadStart [0x0x7ffecd81c34c+44]

2025-08-07 17:26:08,652 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff6f911e925+77845]
	GetHandleVerifier [0x0x7ff6f911e980+77936]
	(No symbol) [0x0x7ff6f8ed9b0c]
	(No symbol) [0x0x7ff6f8f95b46]
	(No symbol) [0x0x7ff6f8f588ca]
	(No symbol) [0x0x7ff6f8f80b07]
	(No symbol) [0x0x7ff6f8f586a3]
	(No symbol) [0x0x7ff6f8f21791]
	(No symbol) [0x0x7ff6f8f22523]
	GetHandleVerifier [0x0x7ff6f93f683d+3059501]
	GetHandleVerifier [0x0x7ff6f93f0bfd+3035885]
	GetHandleVerifier [0x0x7ff6f94103f0+3164896]
	GetHandleVerifier [0x0x7ff6f9138c2e+185118]
	GetHandleVerifier [0x0x7ff6f914053f+216111]
	GetHandleVerifier [0x0x7ff6f91272d4+113092]
	GetHandleVerifier [0x0x7ff6f9127489+113529]
	GetHandleVerifier [0x0x7ff6f910e288+10616]
	BaseThreadInitThunk [0x0x7ffecc2ae8d7+23]
	RtlUserThreadStart [0x0x7ffecd81c34c+44]

2025-08-07 17:26:08,656 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff6f911e925+77845]
	GetHandleVerifier [0x0x7ff6f911e980+77936]
	(No symbol) [0x0x7ff6f8ed9b0c]
	(No symbol) [0x0x7ff6f8f95b46]
	(No symbol) [0x0x7ff6f8f588ca]
	(No symbol) [0x0x7ff6f8f80b07]
	(No symbol) [0x0x7ff6f8f586a3]
	(No symbol) [0x0x7ff6f8f21791]
	(No symbol) [0x0x7ff6f8f22523]
	GetHandleVerifier [0x0x7ff6f93f683d+3059501]
	GetHandleVerifier [0x0x7ff6f93f0bfd+3035885]
	GetHandleVerifier [0x0x7ff6f94103f0+3164896]
	GetHandleVerifier [0x0x7ff6f9138c2e+185118]
	GetHandleVerifier [0x0x7ff6f914053f+216111]
	GetHandleVerifier [0x0x7ff6f91272d4+113092]
	GetHandleVerifier [0x0x7ff6f9127489+113529]
	GetHandleVerifier [0x0x7ff6f910e288+10616]
	BaseThreadInitThunk [0x0x7ffecc2ae8d7+23]
	RtlUserThreadStart [0x0x7ffecd81c34c+44]

2025-08-07 17:26:08,659 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff6f911e925+77845]
	GetHandleVerifier [0x0x7ff6f911e980+77936]
	(No symbol) [0x0x7ff6f8ed9b0c]
	(No symbol) [0x0x7ff6f8f95b46]
	(No symbol) [0x0x7ff6f8f588ca]
	(No symbol) [0x0x7ff6f8f80b07]
	(No symbol) [0x0x7ff6f8f586a3]
	(No symbol) [0x0x7ff6f8f21791]
	(No symbol) [0x0x7ff6f8f22523]
	GetHandleVerifier [0x0x7ff6f93f683d+3059501]
	GetHandleVerifier [0x0x7ff6f93f0bfd+3035885]
	GetHandleVerifier [0x0x7ff6f94103f0+3164896]
	GetHandleVerifier [0x0x7ff6f9138c2e+185118]
	GetHandleVerifier [0x0x7ff6f914053f+216111]
	GetHandleVerifier [0x0x7ff6f91272d4+113092]
	GetHandleVerifier [0x0x7ff6f9127489+113529]
	GetHandleVerifier [0x0x7ff6f910e288+10616]
	BaseThreadInitThunk [0x0x7ffecc2ae8d7+23]
	RtlUserThreadStart [0x0x7ffecd81c34c+44]

2025-08-07 17:26:08,662 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff6f911e925+77845]
	GetHandleVerifier [0x0x7ff6f911e980+77936]
	(No symbol) [0x0x7ff6f8ed9b0c]
	(No symbol) [0x0x7ff6f8f95b46]
	(No symbol) [0x0x7ff6f8f588ca]
	(No symbol) [0x0x7ff6f8f80b07]
	(No symbol) [0x0x7ff6f8f586a3]
	(No symbol) [0x0x7ff6f8f21791]
	(No symbol) [0x0x7ff6f8f22523]
	GetHandleVerifier [0x0x7ff6f93f683d+3059501]
	GetHandleVerifier [0x0x7ff6f93f0bfd+3035885]
	GetHandleVerifier [0x0x7ff6f94103f0+3164896]
	GetHandleVerifier [0x0x7ff6f9138c2e+185118]
	GetHandleVerifier [0x0x7ff6f914053f+216111]
	GetHandleVerifier [0x0x7ff6f91272d4+113092]
	GetHandleVerifier [0x0x7ff6f9127489+113529]
	GetHandleVerifier [0x0x7ff6f910e288+10616]
	BaseThreadInitThunk [0x0x7ffecc2ae8d7+23]
	RtlUserThreadStart [0x0x7ffecd81c34c+44]

2025-08-07 17:26:08,666 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff6f911e925+77845]
	GetHandleVerifier [0x0x7ff6f911e980+77936]
	(No symbol) [0x0x7ff6f8ed9b0c]
	(No symbol) [0x0x7ff6f8f95b46]
	(No symbol) [0x0x7ff6f8f588ca]
	(No symbol) [0x0x7ff6f8f80b07]
	(No symbol) [0x0x7ff6f8f586a3]
	(No symbol) [0x0x7ff6f8f21791]
	(No symbol) [0x0x7ff6f8f22523]
	GetHandleVerifier [0x0x7ff6f93f683d+3059501]
	GetHandleVerifier [0x0x7ff6f93f0bfd+3035885]
	GetHandleVerifier [0x0x7ff6f94103f0+3164896]
	GetHandleVerifier [0x0x7ff6f9138c2e+185118]
	GetHandleVerifier [0x0x7ff6f914053f+216111]
	GetHandleVerifier [0x0x7ff6f91272d4+113092]
	GetHandleVerifier [0x0x7ff6f9127489+113529]
	GetHandleVerifier [0x0x7ff6f910e288+10616]
	BaseThreadInitThunk [0x0x7ffecc2ae8d7+23]
	RtlUserThreadStart [0x0x7ffecd81c34c+44]

2025-08-07 17:26:08,668 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff6f911e925+77845]
	GetHandleVerifier [0x0x7ff6f911e980+77936]
	(No symbol) [0x0x7ff6f8ed9b0c]
	(No symbol) [0x0x7ff6f8f95b46]
	(No symbol) [0x0x7ff6f8f588ca]
	(No symbol) [0x0x7ff6f8f80b07]
	(No symbol) [0x0x7ff6f8f586a3]
	(No symbol) [0x0x7ff6f8f21791]
	(No symbol) [0x0x7ff6f8f22523]
	GetHandleVerifier [0x0x7ff6f93f683d+3059501]
	GetHandleVerifier [0x0x7ff6f93f0bfd+3035885]
	GetHandleVerifier [0x0x7ff6f94103f0+3164896]
	GetHandleVerifier [0x0x7ff6f9138c2e+185118]
	GetHandleVerifier [0x0x7ff6f914053f+216111]
	GetHandleVerifier [0x0x7ff6f91272d4+113092]
	GetHandleVerifier [0x0x7ff6f9127489+113529]
	GetHandleVerifier [0x0x7ff6f910e288+10616]
	BaseThreadInitThunk [0x0x7ffecc2ae8d7+23]
	RtlUserThreadStart [0x0x7ffecd81c34c+44]

2025-08-07 17:26:08,673 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff6f911e925+77845]
	GetHandleVerifier [0x0x7ff6f911e980+77936]
	(No symbol) [0x0x7ff6f8ed9b0c]
	(No symbol) [0x0x7ff6f8f95b46]
	(No symbol) [0x0x7ff6f8f588ca]
	(No symbol) [0x0x7ff6f8f80b07]
	(No symbol) [0x0x7ff6f8f586a3]
	(No symbol) [0x0x7ff6f8f21791]
	(No symbol) [0x0x7ff6f8f22523]
	GetHandleVerifier [0x0x7ff6f93f683d+3059501]
	GetHandleVerifier [0x0x7ff6f93f0bfd+3035885]
	GetHandleVerifier [0x0x7ff6f94103f0+3164896]
	GetHandleVerifier [0x0x7ff6f9138c2e+185118]
	GetHandleVerifier [0x0x7ff6f914053f+216111]
	GetHandleVerifier [0x0x7ff6f91272d4+113092]
	GetHandleVerifier [0x0x7ff6f9127489+113529]
	GetHandleVerifier [0x0x7ff6f910e288+10616]
	BaseThreadInitThunk [0x0x7ffecc2ae8d7+23]
	RtlUserThreadStart [0x0x7ffecd81c34c+44]

2025-08-07 17:26:08,676 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff6f911e925+77845]
	GetHandleVerifier [0x0x7ff6f911e980+77936]
	(No symbol) [0x0x7ff6f8ed9b0c]
	(No symbol) [0x0x7ff6f8f95b46]
	(No symbol) [0x0x7ff6f8f588ca]
	(No symbol) [0x0x7ff6f8f80b07]
	(No symbol) [0x0x7ff6f8f586a3]
	(No symbol) [0x0x7ff6f8f21791]
	(No symbol) [0x0x7ff6f8f22523]
	GetHandleVerifier [0x0x7ff6f93f683d+3059501]
	GetHandleVerifier [0x0x7ff6f93f0bfd+3035885]
	GetHandleVerifier [0x0x7ff6f94103f0+3164896]
	GetHandleVerifier [0x0x7ff6f9138c2e+185118]
	GetHandleVerifier [0x0x7ff6f914053f+216111]
	GetHandleVerifier [0x0x7ff6f91272d4+113092]
	GetHandleVerifier [0x0x7ff6f9127489+113529]
	GetHandleVerifier [0x0x7ff6f910e288+10616]
	BaseThreadInitThunk [0x0x7ffecc2ae8d7+23]
	RtlUserThreadStart [0x0x7ffecd81c34c+44]

2025-08-07 17:26:08,681 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff6f911e925+77845]
	GetHandleVerifier [0x0x7ff6f911e980+77936]
	(No symbol) [0x0x7ff6f8ed9b0c]
	(No symbol) [0x0x7ff6f8f95b46]
	(No symbol) [0x0x7ff6f8f588ca]
	(No symbol) [0x0x7ff6f8f80b07]
	(No symbol) [0x0x7ff6f8f586a3]
	(No symbol) [0x0x7ff6f8f21791]
	(No symbol) [0x0x7ff6f8f22523]
	GetHandleVerifier [0x0x7ff6f93f683d+3059501]
	GetHandleVerifier [0x0x7ff6f93f0bfd+3035885]
	GetHandleVerifier [0x0x7ff6f94103f0+3164896]
	GetHandleVerifier [0x0x7ff6f9138c2e+185118]
	GetHandleVerifier [0x0x7ff6f914053f+216111]
	GetHandleVerifier [0x0x7ff6f91272d4+113092]
	GetHandleVerifier [0x0x7ff6f9127489+113529]
	GetHandleVerifier [0x0x7ff6f910e288+10616]
	BaseThreadInitThunk [0x0x7ffecc2ae8d7+23]
	RtlUserThreadStart [0x0x7ffecd81c34c+44]

2025-08-07 17:26:08,684 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff6f911e925+77845]
	GetHandleVerifier [0x0x7ff6f911e980+77936]
	(No symbol) [0x0x7ff6f8ed9b0c]
	(No symbol) [0x0x7ff6f8f95b46]
	(No symbol) [0x0x7ff6f8f588ca]
	(No symbol) [0x0x7ff6f8f80b07]
	(No symbol) [0x0x7ff6f8f586a3]
	(No symbol) [0x0x7ff6f8f21791]
	(No symbol) [0x0x7ff6f8f22523]
	GetHandleVerifier [0x0x7ff6f93f683d+3059501]
	GetHandleVerifier [0x0x7ff6f93f0bfd+3035885]
	GetHandleVerifier [0x0x7ff6f94103f0+3164896]
	GetHandleVerifier [0x0x7ff6f9138c2e+185118]
	GetHandleVerifier [0x0x7ff6f914053f+216111]
	GetHandleVerifier [0x0x7ff6f91272d4+113092]
	GetHandleVerifier [0x0x7ff6f9127489+113529]
	GetHandleVerifier [0x0x7ff6f910e288+10616]
	BaseThreadInitThunk [0x0x7ffecc2ae8d7+23]
	RtlUserThreadStart [0x0x7ffecd81c34c+44]

2025-08-07 17:26:08,688 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff6f911e925+77845]
	GetHandleVerifier [0x0x7ff6f911e980+77936]
	(No symbol) [0x0x7ff6f8ed9b0c]
	(No symbol) [0x0x7ff6f8f95b46]
	(No symbol) [0x0x7ff6f8f588ca]
	(No symbol) [0x0x7ff6f8f80b07]
	(No symbol) [0x0x7ff6f8f586a3]
	(No symbol) [0x0x7ff6f8f21791]
	(No symbol) [0x0x7ff6f8f22523]
	GetHandleVerifier [0x0x7ff6f93f683d+3059501]
	GetHandleVerifier [0x0x7ff6f93f0bfd+3035885]
	GetHandleVerifier [0x0x7ff6f94103f0+3164896]
	GetHandleVerifier [0x0x7ff6f9138c2e+185118]
	GetHandleVerifier [0x0x7ff6f914053f+216111]
	GetHandleVerifier [0x0x7ff6f91272d4+113092]
	GetHandleVerifier [0x0x7ff6f9127489+113529]
	GetHandleVerifier [0x0x7ff6f910e288+10616]
	BaseThreadInitThunk [0x0x7ffecc2ae8d7+23]
	RtlUserThreadStart [0x0x7ffecd81c34c+44]

2025-08-07 17:26:08,690 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff6f911e925+77845]
	GetHandleVerifier [0x0x7ff6f911e980+77936]
	(No symbol) [0x0x7ff6f8ed9b0c]
	(No symbol) [0x0x7ff6f8f95b46]
	(No symbol) [0x0x7ff6f8f588ca]
	(No symbol) [0x0x7ff6f8f80b07]
	(No symbol) [0x0x7ff6f8f586a3]
	(No symbol) [0x0x7ff6f8f21791]
	(No symbol) [0x0x7ff6f8f22523]
	GetHandleVerifier [0x0x7ff6f93f683d+3059501]
	GetHandleVerifier [0x0x7ff6f93f0bfd+3035885]
	GetHandleVerifier [0x0x7ff6f94103f0+3164896]
	GetHandleVerifier [0x0x7ff6f9138c2e+185118]
	GetHandleVerifier [0x0x7ff6f914053f+216111]
	GetHandleVerifier [0x0x7ff6f91272d4+113092]
	GetHandleVerifier [0x0x7ff6f9127489+113529]
	GetHandleVerifier [0x0x7ff6f910e288+10616]
	BaseThreadInitThunk [0x0x7ffecc2ae8d7+23]
	RtlUserThreadStart [0x0x7ffecd81c34c+44]

2025-08-07 17:26:08,694 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff6f911e925+77845]
	GetHandleVerifier [0x0x7ff6f911e980+77936]
	(No symbol) [0x0x7ff6f8ed9b0c]
	(No symbol) [0x0x7ff6f8f95b46]
	(No symbol) [0x0x7ff6f8f588ca]
	(No symbol) [0x0x7ff6f8f80b07]
	(No symbol) [0x0x7ff6f8f586a3]
	(No symbol) [0x0x7ff6f8f21791]
	(No symbol) [0x0x7ff6f8f22523]
	GetHandleVerifier [0x0x7ff6f93f683d+3059501]
	GetHandleVerifier [0x0x7ff6f93f0bfd+3035885]
	GetHandleVerifier [0x0x7ff6f94103f0+3164896]
	GetHandleVerifier [0x0x7ff6f9138c2e+185118]
	GetHandleVerifier [0x0x7ff6f914053f+216111]
	GetHandleVerifier [0x0x7ff6f91272d4+113092]
	GetHandleVerifier [0x0x7ff6f9127489+113529]
	GetHandleVerifier [0x0x7ff6f910e288+10616]
	BaseThreadInitThunk [0x0x7ffecc2ae8d7+23]
	RtlUserThreadStart [0x0x7ffecd81c34c+44]

2025-08-07 17:26:08,695 - INFO - Cookies已加载
2025-08-07 17:26:08,695 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?channel=global_zx&spm=a21ug5.29159167.6452766900.2ndview_zxmore_click
2025-08-07 17:26:13,201 - INFO - 当前User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-07 17:26:13,202 - INFO - 确认：使用桌面版User-Agent
2025-08-07 17:26:13,205 - INFO - 桌面版User-Agent设置完成: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-07 17:26:13,207 - INFO - 成功导航到目标页面
2025-08-07 17:26:13,208 - INFO - 登录成功！现在可以开始铺货任务
2025-08-07 17:26:30,782 - INFO - 开始测试仓库移除功能...
2025-08-07 17:26:30,783 - INFO - 开始执行批量管理操作...
2025-08-07 17:26:30,783 - INFO - 正在导航到管理页面: https://myseller.taobao.com/home.htm/SellManage/in_stock?current=1&pageSize=20
2025-08-07 17:26:32,380 - INFO - 页面加载完成
2025-08-07 17:26:34,099 - INFO - 准备点击元素: tag=span, text='', class='next-checkbox-inner', id=''
2025-08-07 17:26:35,152 - INFO - 尝试ActionChains点击...
2025-08-07 17:26:35,503 - INFO - ActionChains点击成功
2025-08-07 17:26:35,958 - INFO - 已点击全选复选框
2025-08-07 17:26:35,958 - INFO - 等待 1.7 秒
2025-08-07 17:26:37,686 - INFO - 准备点击元素: tag=button, text='批量删除', class='next-btn next-medium next-btn-', id=''
2025-08-07 17:26:38,969 - INFO - 尝试ActionChains点击...
2025-08-07 17:26:39,249 - INFO - ActionChains点击成功
2025-08-07 17:26:39,494 - INFO - 已点击批量编辑按钮
2025-08-07 17:26:39,494 - INFO - 等待 2.8 秒
2025-08-07 17:26:42,385 - INFO - 准备点击元素: tag=div, text='批量删除
是否确认删除？删除宝贝后可到‘回收站’查看并恢复被删除的宝贝，但删除后需要24小时后才能操作恢复，非必要请不要删除
确定', class='next-dialog next-closeable nex', id=''
2025-08-07 17:26:43,718 - INFO - 尝试ActionChains点击...
2025-08-07 17:26:43,990 - INFO - ActionChains点击成功
2025-08-07 17:26:44,440 - INFO - 已关闭弹窗: //div[contains(@class, 'close')]
2025-08-07 17:26:44,477 - INFO - 准备点击元素: tag=span, text='确定', class='next-btn-helper', id=''
2025-08-07 17:26:45,767 - INFO - 尝试ActionChains点击...
2025-08-07 17:26:46,041 - INFO - ActionChains点击成功
2025-08-07 17:26:46,459 - INFO - 批量管理操作完成
2025-08-07 17:26:49,459 - INFO - 仓库移除测试成功完成！
2025-08-07 17:27:09,309 - ERROR - 保存cookies失败: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff6f911e925+77845]
	GetHandleVerifier [0x0x7ff6f911e980+77936]
	(No symbol) [0x0x7ff6f8ed9cda]
	(No symbol) [0x0x7ff6f8ec5f35]
	(No symbol) [0x0x7ff6f8eeaabe]
	(No symbol) [0x0x7ff6f8f5feb5]
	(No symbol) [0x0x7ff6f8f80432]
	(No symbol) [0x0x7ff6f8f586a3]
	(No symbol) [0x0x7ff6f8f21791]
	(No symbol) [0x0x7ff6f8f22523]
	GetHandleVerifier [0x0x7ff6f93f683d+3059501]
	GetHandleVerifier [0x0x7ff6f93f0bfd+3035885]
	GetHandleVerifier [0x0x7ff6f94103f0+3164896]
	GetHandleVerifier [0x0x7ff6f9138c2e+185118]
	GetHandleVerifier [0x0x7ff6f914053f+216111]
	GetHandleVerifier [0x0x7ff6f91272d4+113092]
	GetHandleVerifier [0x0x7ff6f9127489+113529]
	GetHandleVerifier [0x0x7ff6f910e288+10616]
	BaseThreadInitThunk [0x0x7ffecc2ae8d7+23]
	RtlUserThreadStart [0x0x7ffecd81c34c+44]

2025-08-07 17:27:11,339 - INFO - 浏览器已关闭
