2025-07-29 23:44:57,660 - INFO - 配置已保存
2025-07-29 23:45:01,195 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649cda]
	(No symbol) [0x0x7ff742651679]
	(No symbol) [0x0x7ff742654a61]
	(No symbol) [0x0x7ff7426f1e4b]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:45:01,195 - INFO - 防检测机制设置完成
2025-07-29 23:45:01,195 - INFO - 浏览器驱动初始化成功
2025-07-29 23:45:01,200 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:45:01,204 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:45:01,209 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:45:01,211 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:45:01,215 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:45:01,218 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:45:01,222 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:45:01,225 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:45:01,227 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:45:01,230 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:45:01,233 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:45:01,237 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:45:01,239 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:45:01,242 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:45:01,244 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:45:01,246 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:45:01,249 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:45:01,252 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:45:01,255 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:45:01,258 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:45:01,258 - INFO - Cookies已加载
2025-07-29 23:45:01,258 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?channel=global_zx&spm=a21ug5.29159167.6452766900.2ndview_zxmore_click
2025-07-29 23:45:01,258 - INFO - 添加额外延迟: 1.0秒
2025-07-29 23:45:07,865 - INFO - 当前User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-29 23:45:07,865 - INFO - 确认：使用桌面版User-Agent
2025-07-29 23:45:07,868 - INFO - 桌面版User-Agent设置完成: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-29 23:45:07,875 - INFO - 成功导航到目标页面
2025-07-29 23:45:07,876 - INFO - 登录成功！现在可以开始铺货任务
2025-07-29 23:45:13,080 - INFO - Cookies已保存
2025-07-29 23:45:15,358 - INFO - 浏览器已关闭
2025-07-29 23:45:15,359 - INFO - 以无界面(headless)模式启动Chrome，仅添加headless、no-sandbox、disable-dev-shm-usage参数
2025-07-29 23:45:16,683 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649cda]
	(No symbol) [0x0x7ff742651679]
	(No symbol) [0x0x7ff742654a61]
	(No symbol) [0x0x7ff7426f1e4b]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:45:16,683 - INFO - 防检测机制设置完成
2025-07-29 23:45:16,684 - INFO - 浏览器驱动初始化成功
2025-07-29 23:45:16,684 - INFO - 开始自动化铺货流程（无头模式）
2025-07-29 23:45:16,684 - INFO - 第一步：随机选择了【服饰箱包】分类页面
2025-07-29 23:45:16,684 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?cateName=%25E6%259C%258D%25E9%25A5%25B0%25E7%25AE%25B1%25E5%258C%2585&categoryList=50011740%2C16%2C50006843%2C1625%2C30%2C50006842%2C50010404%2C50011397%2C28%2C50013864%2C50468001&keyword=&spm=a21ug5.29159167.9840561350.showIndustry
2025-07-29 23:45:21,210 - INFO - 第一步：点击登录按钮
2025-07-29 23:45:21,240 - INFO - 找到登录按钮，使用选择器: //*[@id='login-form']/div[6]/button
2025-07-29 23:45:22,262 - INFO - 准备点击登录按钮: 标签=button, 文本='登录', 选择器=//*[@id='login-form']/div[6]/button
2025-07-29 23:45:22,278 - INFO - 准备点击元素: tag=button, text='登录', class='fm-button fm-submit password-l', id=''
2025-07-29 23:45:23,557 - INFO - 尝试ActionChains点击...
2025-07-29 23:45:23,966 - INFO - ActionChains点击成功
2025-07-29 23:45:24,176 - INFO - 登录按钮点击成功
2025-07-29 23:45:24,176 - INFO - 登录按钮点击成功
2025-07-29 23:45:27,177 - INFO - 第二步：获取账号信息...
2025-07-29 23:45:27,177 - INFO - 正在跳转到个人中心页面: https://jingya.taobao.com/?v=2
2025-07-29 23:45:53,282 - INFO - 尝试获取账号名称，使用XPath: //*[@id='workbench-user-tool-btn']/div/div[2]
2025-07-29 23:45:53,299 - INFO - 成功获取账号信息: 熙阳13店:茉茉
2025-07-29 23:45:53,299 - INFO - 获取到账号名称: 熙阳13店:茉茉
2025-07-29 23:45:53,300 - INFO - 第三步：开始读取20个商品信息...
2025-07-29 23:45:53,302 - INFO - GUI界面已更新账号显示: 熙阳13店:茉茉
2025-07-29 23:45:53,303 - INFO - 开始爬取商品信息，最大数量: 20
2025-07-29 23:45:57,418 - INFO - 正在爬取第 1 页商品信息...
2025-07-29 23:45:59,426 - INFO - 找到商品容器，开始爬取商品，最大数量: 20
2025-07-29 23:45:59,433 - INFO - 处理商品项 1 时出错: Message: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@id='root']/div[2]/div/div/div/div[5]/div[1]/div[1]"}
  (Session info: chrome=138.0.7204.169); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649cda]
	(No symbol) [0x0x7ff7426a06aa]
	(No symbol) [0x0x7ff7426a095c]
	(No symbol) [0x0x7ff7426f3d07]
	(No symbol) [0x0x7ff7426c890f]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:45:59,440 - INFO - 处理商品项 2 时出错: Message: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@id='root']/div[2]/div/div/div/div[5]/div[1]/div[2]"}
  (Session info: chrome=138.0.7204.169); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649cda]
	(No symbol) [0x0x7ff7426a06aa]
	(No symbol) [0x0x7ff7426a095c]
	(No symbol) [0x0x7ff7426f3d07]
	(No symbol) [0x0x7ff7426c890f]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:45:59,449 - INFO - 处理商品项 3 时出错: Message: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@id='root']/div[2]/div/div/div/div[5]/div[1]/div[3]"}
  (Session info: chrome=138.0.7204.169); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649cda]
	(No symbol) [0x0x7ff7426a06aa]
	(No symbol) [0x0x7ff7426a095c]
	(No symbol) [0x0x7ff7426f3d07]
	(No symbol) [0x0x7ff7426c890f]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:45:59,455 - INFO - 处理商品项 4 时出错: Message: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@id='root']/div[2]/div/div/div/div[5]/div[1]/div[4]"}
  (Session info: chrome=138.0.7204.169); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649cda]
	(No symbol) [0x0x7ff7426a06aa]
	(No symbol) [0x0x7ff7426a095c]
	(No symbol) [0x0x7ff7426f3d07]
	(No symbol) [0x0x7ff7426c890f]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:45:59,462 - INFO - 处理商品项 5 时出错: Message: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@id='root']/div[2]/div/div/div/div[5]/div[1]/div[5]"}
  (Session info: chrome=138.0.7204.169); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649cda]
	(No symbol) [0x0x7ff7426a06aa]
	(No symbol) [0x0x7ff7426a095c]
	(No symbol) [0x0x7ff7426f3d07]
	(No symbol) [0x0x7ff7426c890f]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:45:59,471 - INFO - 处理商品项 6 时出错: Message: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@id='root']/div[2]/div/div/div/div[5]/div[1]/div[6]"}
  (Session info: chrome=138.0.7204.169); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649cda]
	(No symbol) [0x0x7ff7426a06aa]
	(No symbol) [0x0x7ff7426a095c]
	(No symbol) [0x0x7ff7426f3d07]
	(No symbol) [0x0x7ff7426c890f]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:45:59,471 - INFO - 当前页面爬取完成，找到 0 个商品
2025-07-29 23:45:59,471 - INFO - 第 1 页未找到商品，停止爬取
2025-07-29 23:45:59,471 - INFO - 爬取完成，共找到 0 个商品
2025-07-29 23:45:59,471 - ERROR - 未能获取到商品信息，停止流程
