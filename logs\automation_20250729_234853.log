2025-07-29 23:48:57,142 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649cda]
	(No symbol) [0x0x7ff742651679]
	(No symbol) [0x0x7ff742654a61]
	(No symbol) [0x0x7ff7426f1e4b]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:48:57,143 - INFO - 防检测机制设置完成
2025-07-29 23:48:57,143 - INFO - 浏览器驱动初始化成功
2025-07-29 23:48:57,147 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:48:57,158 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:48:57,162 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:48:57,166 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:48:57,170 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:48:57,173 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:48:57,176 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:48:57,178 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:48:57,184 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:48:57,187 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:48:57,190 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:48:57,192 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:48:57,195 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:48:57,198 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:48:57,202 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:48:57,204 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:48:57,206 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:48:57,208 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:48:57,210 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:48:57,214 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:48:57,217 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:48:57,217 - INFO - Cookies已加载
2025-07-29 23:48:57,217 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?channel=global_zx&spm=a21ug5.29159167.6452766900.2ndview_zxmore_click
2025-07-29 23:49:02,986 - INFO - 当前User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-29 23:49:02,986 - INFO - 确认：使用桌面版User-Agent
2025-07-29 23:49:03,025 - INFO - 桌面版User-Agent设置完成: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-29 23:49:03,924 - WARNING - 随机鼠标移动失败: Message: move target out of bounds
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff7426fbe53]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:49:04,977 - INFO - 成功导航到目标页面
2025-07-29 23:49:04,978 - INFO - 登录成功！现在可以开始铺货任务
2025-07-29 23:49:07,416 - INFO - Cookies已保存
2025-07-29 23:49:09,686 - INFO - 浏览器已关闭
2025-07-29 23:49:11,019 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649cda]
	(No symbol) [0x0x7ff742651679]
	(No symbol) [0x0x7ff742654a61]
	(No symbol) [0x0x7ff7426f1e4b]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:49:11,019 - INFO - 防检测机制设置完成
2025-07-29 23:49:11,019 - INFO - 浏览器驱动初始化成功
2025-07-29 23:49:11,020 - INFO - 开始自动化铺货流程（有界面模式）
2025-07-29 23:49:11,020 - INFO - 第一步：随机选择了【宠物园艺】分类页面
2025-07-29 23:49:11,020 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?cateName=%25E5%25AE%25A0%25E7%2589%25A9%25E5%259B%25AD%25E8%2589%25BA&categoryList=50007216%2C29%2C124466001&keyword=&spm=a21ug5.29159167.9840561350.showIndustry
2025-07-29 23:49:14,788 - INFO - 第一步：点击登录按钮
2025-07-29 23:49:18,871 - INFO - 找到登录按钮，使用选择器: //*[@id='login-form']/div[6]/button
2025-07-29 23:49:19,897 - INFO - 准备点击登录按钮: 标签=button, 文本='登录', 选择器=//*[@id='login-form']/div[6]/button
2025-07-29 23:49:19,911 - INFO - 准备点击元素: tag=button, text='登录', class='fm-button fm-submit password-l', id=''
2025-07-29 23:49:21,200 - INFO - 尝试ActionChains点击...
2025-07-29 23:49:21,598 - INFO - ActionChains点击成功
2025-07-29 23:49:21,896 - INFO - 登录按钮点击成功
2025-07-29 23:49:21,896 - INFO - 登录按钮点击成功
2025-07-29 23:49:24,897 - INFO - 第二步：获取账号信息...
2025-07-29 23:49:24,898 - INFO - 正在跳转到个人中心页面: https://jingya.taobao.com/?v=2
2025-07-29 23:49:50,629 - INFO - 尝试获取账号名称，使用XPath: //*[@id='workbench-user-tool-btn']/div/div[2]
2025-07-29 23:49:50,642 - INFO - 成功获取账号信息: 熙阳13店:茉茉
2025-07-29 23:49:50,642 - INFO - 获取到账号名称: 熙阳13店:茉茉
2025-07-29 23:49:50,643 - INFO - GUI界面已更新账号显示: 熙阳13店:茉茉
2025-07-29 23:49:50,643 - INFO - 第三步：开始读取20个商品信息...
2025-07-29 23:49:50,646 - INFO - 开始爬取商品信息，最大数量: 20
2025-07-29 23:49:54,707 - INFO - 正在爬取第 1 页商品信息...
2025-07-29 23:49:56,717 - INFO - 找到商品容器，开始爬取商品，最大数量: 20
2025-07-29 23:49:56,724 - INFO - 处理商品项 1 时出错: Message: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@id='root']/div[2]/div/div/div/div[5]/div[1]/div[1]"}
  (Session info: chrome=138.0.7204.169); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649cda]
	(No symbol) [0x0x7ff7426a06aa]
	(No symbol) [0x0x7ff7426a095c]
	(No symbol) [0x0x7ff7426f3d07]
	(No symbol) [0x0x7ff7426c890f]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:49:56,732 - INFO - 处理商品项 2 时出错: Message: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@id='root']/div[2]/div/div/div/div[5]/div[1]/div[2]"}
  (Session info: chrome=138.0.7204.169); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649cda]
	(No symbol) [0x0x7ff7426a06aa]
	(No symbol) [0x0x7ff7426a095c]
	(No symbol) [0x0x7ff7426f3d07]
	(No symbol) [0x0x7ff7426c890f]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:49:56,741 - INFO - 处理商品项 3 时出错: Message: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@id='root']/div[2]/div/div/div/div[5]/div[1]/div[3]"}
  (Session info: chrome=138.0.7204.169); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649cda]
	(No symbol) [0x0x7ff7426a06aa]
	(No symbol) [0x0x7ff7426a095c]
	(No symbol) [0x0x7ff7426f3d07]
	(No symbol) [0x0x7ff7426c890f]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:49:56,748 - INFO - 处理商品项 4 时出错: Message: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@id='root']/div[2]/div/div/div/div[5]/div[1]/div[4]"}
  (Session info: chrome=138.0.7204.169); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649cda]
	(No symbol) [0x0x7ff7426a06aa]
	(No symbol) [0x0x7ff7426a095c]
	(No symbol) [0x0x7ff7426f3d07]
	(No symbol) [0x0x7ff7426c890f]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:49:56,756 - INFO - 处理商品项 5 时出错: Message: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@id='root']/div[2]/div/div/div/div[5]/div[1]/div[5]"}
  (Session info: chrome=138.0.7204.169); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649cda]
	(No symbol) [0x0x7ff7426a06aa]
	(No symbol) [0x0x7ff7426a095c]
	(No symbol) [0x0x7ff7426f3d07]
	(No symbol) [0x0x7ff7426c890f]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:49:56,762 - INFO - 处理商品项 6 时出错: Message: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@id='root']/div[2]/div/div/div/div[5]/div[1]/div[6]"}
  (Session info: chrome=138.0.7204.169); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649cda]
	(No symbol) [0x0x7ff7426a06aa]
	(No symbol) [0x0x7ff7426a095c]
	(No symbol) [0x0x7ff7426f3d07]
	(No symbol) [0x0x7ff7426c890f]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:49:56,762 - INFO - 当前页面爬取完成，找到 0 个商品
2025-07-29 23:49:56,762 - INFO - 第 1 页未找到商品，停止爬取
2025-07-29 23:49:56,763 - INFO - 爬取完成，共找到 0 个商品
2025-07-29 23:49:56,763 - ERROR - 未能获取到商品信息，停止流程
