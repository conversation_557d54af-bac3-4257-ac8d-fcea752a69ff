2025-08-04 17:24:29,815 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff7440fe925+77845]
	GetHandleVerifier [0x0x7ff7440fe980+77936]
	(No symbol) [0x0x7ff743eb9cda]
	(No symbol) [0x0x7ff743ec1679]
	(No symbol) [0x0x7ff743ec4a61]
	(No symbol) [0x0x7ff743f61e4b]
	(No symbol) [0x0x7ff743f388ca]
	(No symbol) [0x0x7ff743f60b07]
	(No symbol) [0x0x7ff743f386a3]
	(No symbol) [0x0x7ff743f01791]
	(No symbol) [0x0x7ff743f02523]
	GetHandleVerifier [0x0x7ff7443d683d+3059501]
	GetHandleVerifier [0x0x7ff7443d0bfd+3035885]
	GetHandleVerifier [0x0x7ff7443f03f0+3164896]
	GetHandleVerifier [0x0x7ff744118c2e+185118]
	GetHandleVerifier [0x0x7ff74412053f+216111]
	GetHandleVerifier [0x0x7ff7441072d4+113092]
	GetHandleVerifier [0x0x7ff744107489+113529]
	GetHandleVerifier [0x0x7ff7440ee288+10616]
	BaseThreadInitThunk [0x0x7ffc7ac9e8d7+23]
	RtlUserThreadStart [0x0x7ffc7bdbc34c+44]

2025-08-04 17:24:29,815 - INFO - 防检测机制设置完成
2025-08-04 17:24:29,815 - INFO - 浏览器驱动初始化成功
2025-08-04 17:24:29,821 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff7440fe925+77845]
	GetHandleVerifier [0x0x7ff7440fe980+77936]
	(No symbol) [0x0x7ff743eb9b0c]
	(No symbol) [0x0x7ff743f75b46]
	(No symbol) [0x0x7ff743f388ca]
	(No symbol) [0x0x7ff743f60b07]
	(No symbol) [0x0x7ff743f386a3]
	(No symbol) [0x0x7ff743f01791]
	(No symbol) [0x0x7ff743f02523]
	GetHandleVerifier [0x0x7ff7443d683d+3059501]
	GetHandleVerifier [0x0x7ff7443d0bfd+3035885]
	GetHandleVerifier [0x0x7ff7443f03f0+3164896]
	GetHandleVerifier [0x0x7ff744118c2e+185118]
	GetHandleVerifier [0x0x7ff74412053f+216111]
	GetHandleVerifier [0x0x7ff7441072d4+113092]
	GetHandleVerifier [0x0x7ff744107489+113529]
	GetHandleVerifier [0x0x7ff7440ee288+10616]
	BaseThreadInitThunk [0x0x7ffc7ac9e8d7+23]
	RtlUserThreadStart [0x0x7ffc7bdbc34c+44]

2025-08-04 17:24:29,826 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff7440fe925+77845]
	GetHandleVerifier [0x0x7ff7440fe980+77936]
	(No symbol) [0x0x7ff743eb9b0c]
	(No symbol) [0x0x7ff743f75b46]
	(No symbol) [0x0x7ff743f388ca]
	(No symbol) [0x0x7ff743f60b07]
	(No symbol) [0x0x7ff743f386a3]
	(No symbol) [0x0x7ff743f01791]
	(No symbol) [0x0x7ff743f02523]
	GetHandleVerifier [0x0x7ff7443d683d+3059501]
	GetHandleVerifier [0x0x7ff7443d0bfd+3035885]
	GetHandleVerifier [0x0x7ff7443f03f0+3164896]
	GetHandleVerifier [0x0x7ff744118c2e+185118]
	GetHandleVerifier [0x0x7ff74412053f+216111]
	GetHandleVerifier [0x0x7ff7441072d4+113092]
	GetHandleVerifier [0x0x7ff744107489+113529]
	GetHandleVerifier [0x0x7ff7440ee288+10616]
	BaseThreadInitThunk [0x0x7ffc7ac9e8d7+23]
	RtlUserThreadStart [0x0x7ffc7bdbc34c+44]

2025-08-04 17:24:29,828 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff7440fe925+77845]
	GetHandleVerifier [0x0x7ff7440fe980+77936]
	(No symbol) [0x0x7ff743eb9b0c]
	(No symbol) [0x0x7ff743f75b46]
	(No symbol) [0x0x7ff743f388ca]
	(No symbol) [0x0x7ff743f60b07]
	(No symbol) [0x0x7ff743f386a3]
	(No symbol) [0x0x7ff743f01791]
	(No symbol) [0x0x7ff743f02523]
	GetHandleVerifier [0x0x7ff7443d683d+3059501]
	GetHandleVerifier [0x0x7ff7443d0bfd+3035885]
	GetHandleVerifier [0x0x7ff7443f03f0+3164896]
	GetHandleVerifier [0x0x7ff744118c2e+185118]
	GetHandleVerifier [0x0x7ff74412053f+216111]
	GetHandleVerifier [0x0x7ff7441072d4+113092]
	GetHandleVerifier [0x0x7ff744107489+113529]
	GetHandleVerifier [0x0x7ff7440ee288+10616]
	BaseThreadInitThunk [0x0x7ffc7ac9e8d7+23]
	RtlUserThreadStart [0x0x7ffc7bdbc34c+44]

2025-08-04 17:24:29,831 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff7440fe925+77845]
	GetHandleVerifier [0x0x7ff7440fe980+77936]
	(No symbol) [0x0x7ff743eb9b0c]
	(No symbol) [0x0x7ff743f75b46]
	(No symbol) [0x0x7ff743f388ca]
	(No symbol) [0x0x7ff743f60b07]
	(No symbol) [0x0x7ff743f386a3]
	(No symbol) [0x0x7ff743f01791]
	(No symbol) [0x0x7ff743f02523]
	GetHandleVerifier [0x0x7ff7443d683d+3059501]
	GetHandleVerifier [0x0x7ff7443d0bfd+3035885]
	GetHandleVerifier [0x0x7ff7443f03f0+3164896]
	GetHandleVerifier [0x0x7ff744118c2e+185118]
	GetHandleVerifier [0x0x7ff74412053f+216111]
	GetHandleVerifier [0x0x7ff7441072d4+113092]
	GetHandleVerifier [0x0x7ff744107489+113529]
	GetHandleVerifier [0x0x7ff7440ee288+10616]
	BaseThreadInitThunk [0x0x7ffc7ac9e8d7+23]
	RtlUserThreadStart [0x0x7ffc7bdbc34c+44]

2025-08-04 17:24:29,835 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff7440fe925+77845]
	GetHandleVerifier [0x0x7ff7440fe980+77936]
	(No symbol) [0x0x7ff743eb9b0c]
	(No symbol) [0x0x7ff743f75b46]
	(No symbol) [0x0x7ff743f388ca]
	(No symbol) [0x0x7ff743f60b07]
	(No symbol) [0x0x7ff743f386a3]
	(No symbol) [0x0x7ff743f01791]
	(No symbol) [0x0x7ff743f02523]
	GetHandleVerifier [0x0x7ff7443d683d+3059501]
	GetHandleVerifier [0x0x7ff7443d0bfd+3035885]
	GetHandleVerifier [0x0x7ff7443f03f0+3164896]
	GetHandleVerifier [0x0x7ff744118c2e+185118]
	GetHandleVerifier [0x0x7ff74412053f+216111]
	GetHandleVerifier [0x0x7ff7441072d4+113092]
	GetHandleVerifier [0x0x7ff744107489+113529]
	GetHandleVerifier [0x0x7ff7440ee288+10616]
	BaseThreadInitThunk [0x0x7ffc7ac9e8d7+23]
	RtlUserThreadStart [0x0x7ffc7bdbc34c+44]

2025-08-04 17:24:29,839 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff7440fe925+77845]
	GetHandleVerifier [0x0x7ff7440fe980+77936]
	(No symbol) [0x0x7ff743eb9b0c]
	(No symbol) [0x0x7ff743f75b46]
	(No symbol) [0x0x7ff743f388ca]
	(No symbol) [0x0x7ff743f60b07]
	(No symbol) [0x0x7ff743f386a3]
	(No symbol) [0x0x7ff743f01791]
	(No symbol) [0x0x7ff743f02523]
	GetHandleVerifier [0x0x7ff7443d683d+3059501]
	GetHandleVerifier [0x0x7ff7443d0bfd+3035885]
	GetHandleVerifier [0x0x7ff7443f03f0+3164896]
	GetHandleVerifier [0x0x7ff744118c2e+185118]
	GetHandleVerifier [0x0x7ff74412053f+216111]
	GetHandleVerifier [0x0x7ff7441072d4+113092]
	GetHandleVerifier [0x0x7ff744107489+113529]
	GetHandleVerifier [0x0x7ff7440ee288+10616]
	BaseThreadInitThunk [0x0x7ffc7ac9e8d7+23]
	RtlUserThreadStart [0x0x7ffc7bdbc34c+44]

2025-08-04 17:24:29,842 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff7440fe925+77845]
	GetHandleVerifier [0x0x7ff7440fe980+77936]
	(No symbol) [0x0x7ff743eb9b0c]
	(No symbol) [0x0x7ff743f75b46]
	(No symbol) [0x0x7ff743f388ca]
	(No symbol) [0x0x7ff743f60b07]
	(No symbol) [0x0x7ff743f386a3]
	(No symbol) [0x0x7ff743f01791]
	(No symbol) [0x0x7ff743f02523]
	GetHandleVerifier [0x0x7ff7443d683d+3059501]
	GetHandleVerifier [0x0x7ff7443d0bfd+3035885]
	GetHandleVerifier [0x0x7ff7443f03f0+3164896]
	GetHandleVerifier [0x0x7ff744118c2e+185118]
	GetHandleVerifier [0x0x7ff74412053f+216111]
	GetHandleVerifier [0x0x7ff7441072d4+113092]
	GetHandleVerifier [0x0x7ff744107489+113529]
	GetHandleVerifier [0x0x7ff7440ee288+10616]
	BaseThreadInitThunk [0x0x7ffc7ac9e8d7+23]
	RtlUserThreadStart [0x0x7ffc7bdbc34c+44]

2025-08-04 17:24:29,845 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff7440fe925+77845]
	GetHandleVerifier [0x0x7ff7440fe980+77936]
	(No symbol) [0x0x7ff743eb9b0c]
	(No symbol) [0x0x7ff743f75b46]
	(No symbol) [0x0x7ff743f388ca]
	(No symbol) [0x0x7ff743f60b07]
	(No symbol) [0x0x7ff743f386a3]
	(No symbol) [0x0x7ff743f01791]
	(No symbol) [0x0x7ff743f02523]
	GetHandleVerifier [0x0x7ff7443d683d+3059501]
	GetHandleVerifier [0x0x7ff7443d0bfd+3035885]
	GetHandleVerifier [0x0x7ff7443f03f0+3164896]
	GetHandleVerifier [0x0x7ff744118c2e+185118]
	GetHandleVerifier [0x0x7ff74412053f+216111]
	GetHandleVerifier [0x0x7ff7441072d4+113092]
	GetHandleVerifier [0x0x7ff744107489+113529]
	GetHandleVerifier [0x0x7ff7440ee288+10616]
	BaseThreadInitThunk [0x0x7ffc7ac9e8d7+23]
	RtlUserThreadStart [0x0x7ffc7bdbc34c+44]

2025-08-04 17:24:29,851 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff7440fe925+77845]
	GetHandleVerifier [0x0x7ff7440fe980+77936]
	(No symbol) [0x0x7ff743eb9b0c]
	(No symbol) [0x0x7ff743f75b46]
	(No symbol) [0x0x7ff743f388ca]
	(No symbol) [0x0x7ff743f60b07]
	(No symbol) [0x0x7ff743f386a3]
	(No symbol) [0x0x7ff743f01791]
	(No symbol) [0x0x7ff743f02523]
	GetHandleVerifier [0x0x7ff7443d683d+3059501]
	GetHandleVerifier [0x0x7ff7443d0bfd+3035885]
	GetHandleVerifier [0x0x7ff7443f03f0+3164896]
	GetHandleVerifier [0x0x7ff744118c2e+185118]
	GetHandleVerifier [0x0x7ff74412053f+216111]
	GetHandleVerifier [0x0x7ff7441072d4+113092]
	GetHandleVerifier [0x0x7ff744107489+113529]
	GetHandleVerifier [0x0x7ff7440ee288+10616]
	BaseThreadInitThunk [0x0x7ffc7ac9e8d7+23]
	RtlUserThreadStart [0x0x7ffc7bdbc34c+44]

2025-08-04 17:24:29,855 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff7440fe925+77845]
	GetHandleVerifier [0x0x7ff7440fe980+77936]
	(No symbol) [0x0x7ff743eb9b0c]
	(No symbol) [0x0x7ff743f75b46]
	(No symbol) [0x0x7ff743f388ca]
	(No symbol) [0x0x7ff743f60b07]
	(No symbol) [0x0x7ff743f386a3]
	(No symbol) [0x0x7ff743f01791]
	(No symbol) [0x0x7ff743f02523]
	GetHandleVerifier [0x0x7ff7443d683d+3059501]
	GetHandleVerifier [0x0x7ff7443d0bfd+3035885]
	GetHandleVerifier [0x0x7ff7443f03f0+3164896]
	GetHandleVerifier [0x0x7ff744118c2e+185118]
	GetHandleVerifier [0x0x7ff74412053f+216111]
	GetHandleVerifier [0x0x7ff7441072d4+113092]
	GetHandleVerifier [0x0x7ff744107489+113529]
	GetHandleVerifier [0x0x7ff7440ee288+10616]
	BaseThreadInitThunk [0x0x7ffc7ac9e8d7+23]
	RtlUserThreadStart [0x0x7ffc7bdbc34c+44]

2025-08-04 17:24:29,857 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff7440fe925+77845]
	GetHandleVerifier [0x0x7ff7440fe980+77936]
	(No symbol) [0x0x7ff743eb9b0c]
	(No symbol) [0x0x7ff743f75b46]
	(No symbol) [0x0x7ff743f388ca]
	(No symbol) [0x0x7ff743f60b07]
	(No symbol) [0x0x7ff743f386a3]
	(No symbol) [0x0x7ff743f01791]
	(No symbol) [0x0x7ff743f02523]
	GetHandleVerifier [0x0x7ff7443d683d+3059501]
	GetHandleVerifier [0x0x7ff7443d0bfd+3035885]
	GetHandleVerifier [0x0x7ff7443f03f0+3164896]
	GetHandleVerifier [0x0x7ff744118c2e+185118]
	GetHandleVerifier [0x0x7ff74412053f+216111]
	GetHandleVerifier [0x0x7ff7441072d4+113092]
	GetHandleVerifier [0x0x7ff744107489+113529]
	GetHandleVerifier [0x0x7ff7440ee288+10616]
	BaseThreadInitThunk [0x0x7ffc7ac9e8d7+23]
	RtlUserThreadStart [0x0x7ffc7bdbc34c+44]

2025-08-04 17:24:29,861 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff7440fe925+77845]
	GetHandleVerifier [0x0x7ff7440fe980+77936]
	(No symbol) [0x0x7ff743eb9b0c]
	(No symbol) [0x0x7ff743f75b46]
	(No symbol) [0x0x7ff743f388ca]
	(No symbol) [0x0x7ff743f60b07]
	(No symbol) [0x0x7ff743f386a3]
	(No symbol) [0x0x7ff743f01791]
	(No symbol) [0x0x7ff743f02523]
	GetHandleVerifier [0x0x7ff7443d683d+3059501]
	GetHandleVerifier [0x0x7ff7443d0bfd+3035885]
	GetHandleVerifier [0x0x7ff7443f03f0+3164896]
	GetHandleVerifier [0x0x7ff744118c2e+185118]
	GetHandleVerifier [0x0x7ff74412053f+216111]
	GetHandleVerifier [0x0x7ff7441072d4+113092]
	GetHandleVerifier [0x0x7ff744107489+113529]
	GetHandleVerifier [0x0x7ff7440ee288+10616]
	BaseThreadInitThunk [0x0x7ffc7ac9e8d7+23]
	RtlUserThreadStart [0x0x7ffc7bdbc34c+44]

2025-08-04 17:24:29,867 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff7440fe925+77845]
	GetHandleVerifier [0x0x7ff7440fe980+77936]
	(No symbol) [0x0x7ff743eb9b0c]
	(No symbol) [0x0x7ff743f75b46]
	(No symbol) [0x0x7ff743f388ca]
	(No symbol) [0x0x7ff743f60b07]
	(No symbol) [0x0x7ff743f386a3]
	(No symbol) [0x0x7ff743f01791]
	(No symbol) [0x0x7ff743f02523]
	GetHandleVerifier [0x0x7ff7443d683d+3059501]
	GetHandleVerifier [0x0x7ff7443d0bfd+3035885]
	GetHandleVerifier [0x0x7ff7443f03f0+3164896]
	GetHandleVerifier [0x0x7ff744118c2e+185118]
	GetHandleVerifier [0x0x7ff74412053f+216111]
	GetHandleVerifier [0x0x7ff7441072d4+113092]
	GetHandleVerifier [0x0x7ff744107489+113529]
	GetHandleVerifier [0x0x7ff7440ee288+10616]
	BaseThreadInitThunk [0x0x7ffc7ac9e8d7+23]
	RtlUserThreadStart [0x0x7ffc7bdbc34c+44]

2025-08-04 17:24:29,869 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff7440fe925+77845]
	GetHandleVerifier [0x0x7ff7440fe980+77936]
	(No symbol) [0x0x7ff743eb9b0c]
	(No symbol) [0x0x7ff743f75b46]
	(No symbol) [0x0x7ff743f388ca]
	(No symbol) [0x0x7ff743f60b07]
	(No symbol) [0x0x7ff743f386a3]
	(No symbol) [0x0x7ff743f01791]
	(No symbol) [0x0x7ff743f02523]
	GetHandleVerifier [0x0x7ff7443d683d+3059501]
	GetHandleVerifier [0x0x7ff7443d0bfd+3035885]
	GetHandleVerifier [0x0x7ff7443f03f0+3164896]
	GetHandleVerifier [0x0x7ff744118c2e+185118]
	GetHandleVerifier [0x0x7ff74412053f+216111]
	GetHandleVerifier [0x0x7ff7441072d4+113092]
	GetHandleVerifier [0x0x7ff744107489+113529]
	GetHandleVerifier [0x0x7ff7440ee288+10616]
	BaseThreadInitThunk [0x0x7ffc7ac9e8d7+23]
	RtlUserThreadStart [0x0x7ffc7bdbc34c+44]

2025-08-04 17:24:29,873 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff7440fe925+77845]
	GetHandleVerifier [0x0x7ff7440fe980+77936]
	(No symbol) [0x0x7ff743eb9b0c]
	(No symbol) [0x0x7ff743f75b46]
	(No symbol) [0x0x7ff743f388ca]
	(No symbol) [0x0x7ff743f60b07]
	(No symbol) [0x0x7ff743f386a3]
	(No symbol) [0x0x7ff743f01791]
	(No symbol) [0x0x7ff743f02523]
	GetHandleVerifier [0x0x7ff7443d683d+3059501]
	GetHandleVerifier [0x0x7ff7443d0bfd+3035885]
	GetHandleVerifier [0x0x7ff7443f03f0+3164896]
	GetHandleVerifier [0x0x7ff744118c2e+185118]
	GetHandleVerifier [0x0x7ff74412053f+216111]
	GetHandleVerifier [0x0x7ff7441072d4+113092]
	GetHandleVerifier [0x0x7ff744107489+113529]
	GetHandleVerifier [0x0x7ff7440ee288+10616]
	BaseThreadInitThunk [0x0x7ffc7ac9e8d7+23]
	RtlUserThreadStart [0x0x7ffc7bdbc34c+44]

2025-08-04 17:24:29,876 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff7440fe925+77845]
	GetHandleVerifier [0x0x7ff7440fe980+77936]
	(No symbol) [0x0x7ff743eb9b0c]
	(No symbol) [0x0x7ff743f75b46]
	(No symbol) [0x0x7ff743f388ca]
	(No symbol) [0x0x7ff743f60b07]
	(No symbol) [0x0x7ff743f386a3]
	(No symbol) [0x0x7ff743f01791]
	(No symbol) [0x0x7ff743f02523]
	GetHandleVerifier [0x0x7ff7443d683d+3059501]
	GetHandleVerifier [0x0x7ff7443d0bfd+3035885]
	GetHandleVerifier [0x0x7ff7443f03f0+3164896]
	GetHandleVerifier [0x0x7ff744118c2e+185118]
	GetHandleVerifier [0x0x7ff74412053f+216111]
	GetHandleVerifier [0x0x7ff7441072d4+113092]
	GetHandleVerifier [0x0x7ff744107489+113529]
	GetHandleVerifier [0x0x7ff7440ee288+10616]
	BaseThreadInitThunk [0x0x7ffc7ac9e8d7+23]
	RtlUserThreadStart [0x0x7ffc7bdbc34c+44]

2025-08-04 17:24:29,879 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff7440fe925+77845]
	GetHandleVerifier [0x0x7ff7440fe980+77936]
	(No symbol) [0x0x7ff743eb9b0c]
	(No symbol) [0x0x7ff743f75b46]
	(No symbol) [0x0x7ff743f388ca]
	(No symbol) [0x0x7ff743f60b07]
	(No symbol) [0x0x7ff743f386a3]
	(No symbol) [0x0x7ff743f01791]
	(No symbol) [0x0x7ff743f02523]
	GetHandleVerifier [0x0x7ff7443d683d+3059501]
	GetHandleVerifier [0x0x7ff7443d0bfd+3035885]
	GetHandleVerifier [0x0x7ff7443f03f0+3164896]
	GetHandleVerifier [0x0x7ff744118c2e+185118]
	GetHandleVerifier [0x0x7ff74412053f+216111]
	GetHandleVerifier [0x0x7ff7441072d4+113092]
	GetHandleVerifier [0x0x7ff744107489+113529]
	GetHandleVerifier [0x0x7ff7440ee288+10616]
	BaseThreadInitThunk [0x0x7ffc7ac9e8d7+23]
	RtlUserThreadStart [0x0x7ffc7bdbc34c+44]

2025-08-04 17:24:29,883 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff7440fe925+77845]
	GetHandleVerifier [0x0x7ff7440fe980+77936]
	(No symbol) [0x0x7ff743eb9b0c]
	(No symbol) [0x0x7ff743f75b46]
	(No symbol) [0x0x7ff743f388ca]
	(No symbol) [0x0x7ff743f60b07]
	(No symbol) [0x0x7ff743f386a3]
	(No symbol) [0x0x7ff743f01791]
	(No symbol) [0x0x7ff743f02523]
	GetHandleVerifier [0x0x7ff7443d683d+3059501]
	GetHandleVerifier [0x0x7ff7443d0bfd+3035885]
	GetHandleVerifier [0x0x7ff7443f03f0+3164896]
	GetHandleVerifier [0x0x7ff744118c2e+185118]
	GetHandleVerifier [0x0x7ff74412053f+216111]
	GetHandleVerifier [0x0x7ff7441072d4+113092]
	GetHandleVerifier [0x0x7ff744107489+113529]
	GetHandleVerifier [0x0x7ff7440ee288+10616]
	BaseThreadInitThunk [0x0x7ffc7ac9e8d7+23]
	RtlUserThreadStart [0x0x7ffc7bdbc34c+44]

2025-08-04 17:24:29,888 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff7440fe925+77845]
	GetHandleVerifier [0x0x7ff7440fe980+77936]
	(No symbol) [0x0x7ff743eb9b0c]
	(No symbol) [0x0x7ff743f75b46]
	(No symbol) [0x0x7ff743f388ca]
	(No symbol) [0x0x7ff743f60b07]
	(No symbol) [0x0x7ff743f386a3]
	(No symbol) [0x0x7ff743f01791]
	(No symbol) [0x0x7ff743f02523]
	GetHandleVerifier [0x0x7ff7443d683d+3059501]
	GetHandleVerifier [0x0x7ff7443d0bfd+3035885]
	GetHandleVerifier [0x0x7ff7443f03f0+3164896]
	GetHandleVerifier [0x0x7ff744118c2e+185118]
	GetHandleVerifier [0x0x7ff74412053f+216111]
	GetHandleVerifier [0x0x7ff7441072d4+113092]
	GetHandleVerifier [0x0x7ff744107489+113529]
	GetHandleVerifier [0x0x7ff7440ee288+10616]
	BaseThreadInitThunk [0x0x7ffc7ac9e8d7+23]
	RtlUserThreadStart [0x0x7ffc7bdbc34c+44]

2025-08-04 17:24:29,892 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff7440fe925+77845]
	GetHandleVerifier [0x0x7ff7440fe980+77936]
	(No symbol) [0x0x7ff743eb9b0c]
	(No symbol) [0x0x7ff743f75b46]
	(No symbol) [0x0x7ff743f388ca]
	(No symbol) [0x0x7ff743f60b07]
	(No symbol) [0x0x7ff743f386a3]
	(No symbol) [0x0x7ff743f01791]
	(No symbol) [0x0x7ff743f02523]
	GetHandleVerifier [0x0x7ff7443d683d+3059501]
	GetHandleVerifier [0x0x7ff7443d0bfd+3035885]
	GetHandleVerifier [0x0x7ff7443f03f0+3164896]
	GetHandleVerifier [0x0x7ff744118c2e+185118]
	GetHandleVerifier [0x0x7ff74412053f+216111]
	GetHandleVerifier [0x0x7ff7441072d4+113092]
	GetHandleVerifier [0x0x7ff744107489+113529]
	GetHandleVerifier [0x0x7ff7440ee288+10616]
	BaseThreadInitThunk [0x0x7ffc7ac9e8d7+23]
	RtlUserThreadStart [0x0x7ffc7bdbc34c+44]

2025-08-04 17:24:29,896 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff7440fe925+77845]
	GetHandleVerifier [0x0x7ff7440fe980+77936]
	(No symbol) [0x0x7ff743eb9b0c]
	(No symbol) [0x0x7ff743f75b46]
	(No symbol) [0x0x7ff743f388ca]
	(No symbol) [0x0x7ff743f60b07]
	(No symbol) [0x0x7ff743f386a3]
	(No symbol) [0x0x7ff743f01791]
	(No symbol) [0x0x7ff743f02523]
	GetHandleVerifier [0x0x7ff7443d683d+3059501]
	GetHandleVerifier [0x0x7ff7443d0bfd+3035885]
	GetHandleVerifier [0x0x7ff7443f03f0+3164896]
	GetHandleVerifier [0x0x7ff744118c2e+185118]
	GetHandleVerifier [0x0x7ff74412053f+216111]
	GetHandleVerifier [0x0x7ff7441072d4+113092]
	GetHandleVerifier [0x0x7ff744107489+113529]
	GetHandleVerifier [0x0x7ff7440ee288+10616]
	BaseThreadInitThunk [0x0x7ffc7ac9e8d7+23]
	RtlUserThreadStart [0x0x7ffc7bdbc34c+44]

2025-08-04 17:24:29,897 - INFO - Cookies已加载
2025-08-04 17:24:29,897 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?channel=global_zx&spm=a21ug5.29159167.6452766900.2ndview_zxmore_click
2025-08-04 17:24:36,781 - INFO - 当前User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-04 17:24:36,781 - INFO - 确认：使用桌面版User-Agent
2025-08-04 17:24:36,784 - INFO - 桌面版User-Agent设置完成: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-04 17:24:37,789 - WARNING - 检测到需要登录，请手动登录后继续
2025-08-04 17:24:37,888 - INFO - 调试截图已保存: debug_logs/login_required_screenshot_20250804_172437.png
2025-08-04 17:24:37,890 - INFO - 浏览器日志已保存: debug_logs/login_required_browser_log_20250804_172437.txt
2025-08-04 17:24:37,903 - INFO - 页面源码已保存: debug_logs/login_required_page_source_20250804_172437.html
2025-08-04 17:24:37,906 - INFO - 调试信息 - 当前URL: https://login.taobao.com/havanaone/login/login.htm?bizName=taobao&ttid=&redirectURL=https%3A%2F%2Fjingya-x.taobao.com%2Fwow%2Fz%2Ftfx%2Fstatic%2FdXCnMcpEntsE375tfHGp%3Fchannel%3Dglobal_zx%26spm%3Da21ug5.29159167.6452766900.2ndview_zxmore_click&sub=true
2025-08-04 17:24:37,907 - INFO - 调试信息 - 错误: 检测到需要登录
2025-08-04 17:24:37,907 - INFO - 请在打开的浏览器中完成登录，登录完成后点击'确认登录完成'按钮
2025-08-04 17:27:48,039 - INFO - Cookies已保存
2025-08-04 17:27:48,040 - INFO - 登录成功！现在可以开始铺货任务
2025-08-04 17:27:53,225 - INFO - Cookies已保存
2025-08-04 17:27:55,591 - INFO - 浏览器已关闭
2025-08-04 17:27:55,593 - INFO - 以无界面(headless)模式启动Chrome，仅添加headless、no-sandbox、disable-dev-shm-usage参数
2025-08-04 17:27:57,678 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff7440fe925+77845]
	GetHandleVerifier [0x0x7ff7440fe980+77936]
	(No symbol) [0x0x7ff743eb9cda]
	(No symbol) [0x0x7ff743ec1679]
	(No symbol) [0x0x7ff743ec4a61]
	(No symbol) [0x0x7ff743f61e4b]
	(No symbol) [0x0x7ff743f388ca]
	(No symbol) [0x0x7ff743f60b07]
	(No symbol) [0x0x7ff743f386a3]
	(No symbol) [0x0x7ff743f01791]
	(No symbol) [0x0x7ff743f02523]
	GetHandleVerifier [0x0x7ff7443d683d+3059501]
	GetHandleVerifier [0x0x7ff7443d0bfd+3035885]
	GetHandleVerifier [0x0x7ff7443f03f0+3164896]
	GetHandleVerifier [0x0x7ff744118c2e+185118]
	GetHandleVerifier [0x0x7ff74412053f+216111]
	GetHandleVerifier [0x0x7ff7441072d4+113092]
	GetHandleVerifier [0x0x7ff744107489+113529]
	GetHandleVerifier [0x0x7ff7440ee288+10616]
	BaseThreadInitThunk [0x0x7ffc7ac9e8d7+23]
	RtlUserThreadStart [0x0x7ffc7bdbc34c+44]

2025-08-04 17:27:57,678 - INFO - 防检测机制设置完成
2025-08-04 17:27:57,678 - INFO - 浏览器驱动初始化成功
2025-08-04 17:27:57,678 - INFO - 开始自动化铺货流程（无头模式）
2025-08-04 17:27:57,678 - INFO - 第一步：随机选择了【家居百货】分类页面
2025-08-04 17:27:57,678 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?cateName=%25E5%25AE%25B6%25E5%25B1%2585%25E7%2599%25BE%25E8%25B4%25A7&categoryList=50016349%2C50016348%2C50008163%2C21%2C50020808%2C122928002%2C122950001%2C122952001%2C50008164%2C50025705&keyword=&spm=a21ug5.29159167.9840561350.showIndustry
2025-08-04 17:28:00,893 - INFO - 第一步：点击登录按钮
2025-08-04 17:28:18,882 - INFO - 找到登录按钮，使用选择器: //*[@id='login-form']/div[6]/button
2025-08-04 17:28:19,906 - INFO - 准备点击登录按钮: 标签=button, 文本='登录', 选择器=//*[@id='login-form']/div[6]/button
2025-08-04 17:28:19,919 - INFO - 准备点击元素: tag=button, text='登录', class='fm-button fm-submit password-l', id=''
2025-08-04 17:28:21,153 - INFO - 尝试ActionChains点击...
2025-08-04 17:28:21,672 - INFO - ActionChains点击成功
2025-08-04 17:28:21,957 - INFO - 登录按钮点击成功
2025-08-04 17:28:21,957 - INFO - 登录按钮点击成功
2025-08-04 17:28:24,958 - INFO - 第二步：获取账号信息...
2025-08-04 17:28:24,958 - INFO - 正在跳转到个人中心页面: https://jingya.taobao.com/?v=2
2025-08-04 17:28:51,649 - INFO - 尝试获取账号名称，使用XPath: //*[@id='workbench-user-tool-btn']/div/div[2]
2025-08-04 17:28:51,669 - INFO - 成功获取账号信息: 亚辉11店:时橙
2025-08-04 17:28:51,669 - INFO - 获取到账号名称: 亚辉11店:时橙
2025-08-04 17:28:51,672 - INFO - GUI界面已更新账号显示: 亚辉11店:时橙
2025-08-04 17:28:51,672 - INFO - 第三步：开始读取60个商品信息...
2025-08-04 17:28:51,674 - INFO - 开始爬取商品信息，最大数量: 60
2025-08-04 17:28:51,675 - INFO - 使用目标URL: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?cateName=%25E5%25AE%25B6%25E5%25B1%2585%25E7%2599%25BE%25E8%25B4%25A7&categoryList=50016349%2C50016348%2C50008163%2C21%2C50020808%2C122928002%2C122950001%2C122952001%2C50008164%2C50025705&keyword=&spm=a21ug5.29159167.9840561350.showIndustry
2025-08-04 17:28:54,756 - INFO - 正在爬取第 1 页商品信息...
2025-08-04 17:28:56,765 - INFO - 找到商品容器，开始爬取商品，最大数量: 60
2025-08-04 17:28:56,778 - INFO - 找到商品项 1，检查元素信息...
2025-08-04 17:28:56,795 - INFO - 商品项 1 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:28:56,795 - INFO - 商品项 1 当前元素无data-autolog，查找子元素...
2025-08-04 17:28:56,807 - INFO - 商品项 1 找到子元素，class: tfx-item
2025-08-04 17:28:56,811 - INFO - 商品项 1 直接获取到data-autolog: item_id=630604349715&path=search_goods_card&type=category
2025-08-04 17:28:56,811 - INFO - 商品项 1 data-autolog: item_id=630604349715&path=search_goods_card&type=category
2025-08-04 17:28:56,811 - INFO - 商品项 1 提取到item_id: 630604349715
2025-08-04 17:28:56,823 - INFO - 商品项 1 商品名称: 英国莱氏姆深层洁净洗衣液留香香味持久家用香水型（蓝色）
2025-08-04 17:28:56,837 - INFO - 找到商品项 2，检查元素信息...
2025-08-04 17:28:56,846 - INFO - 商品项 2 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:28:56,846 - INFO - 商品项 2 当前元素无data-autolog，查找子元素...
2025-08-04 17:28:56,854 - INFO - 商品项 2 找到子元素，class: tfx-item
2025-08-04 17:28:56,857 - INFO - 商品项 2 直接获取到data-autolog: item_id=681787375440&path=search_goods_card&index=1&type=category
2025-08-04 17:28:56,857 - INFO - 商品项 2 data-autolog: item_id=681787375440&path=search_goods_card&index=1&type=category
2025-08-04 17:28:56,857 - INFO - 商品项 2 提取到item_id: 681787375440
2025-08-04 17:28:56,868 - INFO - 商品项 2 商品名称: 保税直供 拜耳Canesten凯妮汀衣物除菌液1L【 蓝色澳版，橙色港版
2025-08-04 17:28:56,882 - INFO - 找到商品项 3，检查元素信息...
2025-08-04 17:28:56,891 - INFO - 商品项 3 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:28:56,891 - INFO - 商品项 3 当前元素无data-autolog，查找子元素...
2025-08-04 17:28:56,901 - INFO - 商品项 3 找到子元素，class: tfx-item
2025-08-04 17:28:56,904 - INFO - 商品项 3 直接获取到data-autolog: item_id=821067902127&path=search_goods_card&index=2&type=category
2025-08-04 17:28:56,905 - INFO - 商品项 3 data-autolog: item_id=821067902127&path=search_goods_card&index=2&type=category
2025-08-04 17:28:56,905 - INFO - 商品项 3 提取到item_id: 821067902127
2025-08-04 17:28:56,913 - INFO - 商品项 3 商品名称: 丹碧丝TAMPAX卫生棉条长导管式内置式纯棉月经棉条棉棒游泳卫生巾
2025-08-04 17:28:56,925 - INFO - 找到商品项 4，检查元素信息...
2025-08-04 17:28:56,936 - INFO - 商品项 4 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:28:56,936 - INFO - 商品项 4 当前元素无data-autolog，查找子元素...
2025-08-04 17:28:56,951 - INFO - 商品项 4 找到子元素，class: tfx-item
2025-08-04 17:28:56,958 - INFO - 商品项 4 直接获取到data-autolog: item_id=846634805775&path=search_goods_card&index=3&type=category
2025-08-04 17:28:56,958 - INFO - 商品项 4 data-autolog: item_id=846634805775&path=search_goods_card&index=3&type=category
2025-08-04 17:28:56,959 - INFO - 商品项 4 提取到item_id: 846634805775
2025-08-04 17:28:56,973 - INFO - 商品项 4 商品名称: 【甄选】cow牛乳石碱原味/玫瑰花香 瓶装480ml
2025-08-04 17:28:56,985 - INFO - 找到商品项 5，检查元素信息...
2025-08-04 17:28:56,996 - INFO - 商品项 5 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:28:56,996 - INFO - 商品项 5 当前元素无data-autolog，查找子元素...
2025-08-04 17:28:57,003 - INFO - 商品项 5 找到子元素，class: tfx-item
2025-08-04 17:28:57,008 - INFO - 商品项 5 直接获取到data-autolog: item_id=694650705326&path=search_goods_card&index=4&type=category
2025-08-04 17:28:57,008 - INFO - 商品项 5 data-autolog: item_id=694650705326&path=search_goods_card&index=4&type=category
2025-08-04 17:28:57,008 - INFO - 商品项 5 提取到item_id: 694650705326
2025-08-04 17:28:57,016 - INFO - 商品项 5 商品名称: 日本直采EBISU惠百施软毛宽头牙刷6列48孔舒适 颜色随机 P10
2025-08-04 17:28:57,029 - INFO - 找到商品项 6，检查元素信息...
2025-08-04 17:28:57,039 - INFO - 商品项 6 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:28:57,039 - INFO - 商品项 6 当前元素无data-autolog，查找子元素...
2025-08-04 17:28:57,049 - INFO - 商品项 6 找到子元素，class: tfx-item
2025-08-04 17:28:57,052 - INFO - 商品项 6 直接获取到data-autolog: item_id=927913724907&path=search_goods_card&index=5&type=category
2025-08-04 17:28:57,053 - INFO - 商品项 6 data-autolog: item_id=927913724907&path=search_goods_card&index=5&type=category
2025-08-04 17:28:57,053 - INFO - 商品项 6 提取到item_id: 927913724907
2025-08-04 17:28:57,068 - INFO - 商品项 6 商品名称: 超威驱蚊蚊香10圈
2025-08-04 17:28:57,149 - INFO - 找到商品项 7，检查元素信息...
2025-08-04 17:28:57,161 - INFO - 商品项 7 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:28:57,161 - INFO - 商品项 7 当前元素无data-autolog，查找子元素...
2025-08-04 17:28:57,174 - INFO - 商品项 7 找到子元素，class: tfx-item
2025-08-04 17:28:57,177 - INFO - 商品项 7 直接获取到data-autolog: item_id=644179310984&path=search_goods_card&index=6&type=category
2025-08-04 17:28:57,177 - INFO - 商品项 7 data-autolog: item_id=644179310984&path=search_goods_card&index=6&type=category
2025-08-04 17:28:57,177 - INFO - 商品项 7 提取到item_id: 644179310984
2025-08-04 17:28:57,187 - INFO - 商品项 7 商品名称: 库梅克斯皂液洗衣液除菌型香味持久留香家庭装
2025-08-04 17:28:57,204 - INFO - 找到商品项 8，检查元素信息...
2025-08-04 17:28:57,215 - INFO - 商品项 8 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:28:57,215 - INFO - 商品项 8 当前元素无data-autolog，查找子元素...
2025-08-04 17:28:57,227 - INFO - 商品项 8 找到子元素，class: tfx-item
2025-08-04 17:28:57,231 - INFO - 商品项 8 直接获取到data-autolog: item_id=784715750417&path=search_goods_card&index=7&type=category
2025-08-04 17:28:57,231 - INFO - 商品项 8 data-autolog: item_id=784715750417&path=search_goods_card&index=7&type=category
2025-08-04 17:28:57,232 - INFO - 商品项 8 提取到item_id: 784715750417
2025-08-04 17:28:57,241 - INFO - 商品项 8 商品名称: 【渠道授权】日本EBISU惠百施软毛宽头牙刷6列高效舒适
2025-08-04 17:28:57,251 - INFO - 找到商品项 9，检查元素信息...
2025-08-04 17:28:57,259 - INFO - 商品项 9 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:28:57,260 - INFO - 商品项 9 当前元素无data-autolog，查找子元素...
2025-08-04 17:28:57,266 - INFO - 商品项 9 找到子元素，class: tfx-item
2025-08-04 17:28:57,268 - INFO - 商品项 9 直接获取到data-autolog: item_id=778440875151&path=search_goods_card&index=8&type=category
2025-08-04 17:28:57,269 - INFO - 商品项 9 data-autolog: item_id=778440875151&path=search_goods_card&index=8&type=category
2025-08-04 17:28:57,269 - INFO - 商品项 9 提取到item_id: 778440875151
2025-08-04 17:28:57,279 - INFO - 商品项 9 商品名称: 勿动1
2025-08-04 17:28:57,289 - INFO - 找到商品项 10，检查元素信息...
2025-08-04 17:28:57,296 - INFO - 商品项 10 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:28:57,296 - INFO - 商品项 10 当前元素无data-autolog，查找子元素...
2025-08-04 17:28:57,303 - INFO - 商品项 10 找到子元素，class: tfx-item
2025-08-04 17:28:57,306 - INFO - 商品项 10 直接获取到data-autolog: item_id=775343553518&path=search_goods_card&index=9&type=category
2025-08-04 17:28:57,306 - INFO - 商品项 10 data-autolog: item_id=775343553518&path=search_goods_card&index=9&type=category
2025-08-04 17:28:57,306 - INFO - 商品项 10 提取到item_id: 775343553518
2025-08-04 17:28:57,313 - INFO - 商品项 10 商品名称: 德国小红牙膏
2025-08-04 17:28:57,324 - INFO - 找到商品项 11，检查元素信息...
2025-08-04 17:28:57,333 - INFO - 商品项 11 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:28:57,333 - INFO - 商品项 11 当前元素无data-autolog，查找子元素...
2025-08-04 17:28:57,340 - INFO - 商品项 11 找到子元素，class: tfx-item
2025-08-04 17:28:57,343 - INFO - 商品项 11 直接获取到data-autolog: item_id=773704801582&path=search_goods_card&index=10&type=category
2025-08-04 17:28:57,343 - INFO - 商品项 11 data-autolog: item_id=773704801582&path=search_goods_card&index=10&type=category
2025-08-04 17:28:57,343 - INFO - 商品项 11 提取到item_id: 773704801582
2025-08-04 17:28:57,352 - INFO - 商品项 11 商品名称: 【保税直发】泰国喜净Hygiene柔顺剂衣物防静电持久留香柔软剂
2025-08-04 17:28:57,363 - INFO - 找到商品项 12，检查元素信息...
2025-08-04 17:28:57,375 - INFO - 商品项 12 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:28:57,375 - INFO - 商品项 12 当前元素无data-autolog，查找子元素...
2025-08-04 17:28:57,384 - INFO - 商品项 12 找到子元素，class: tfx-item
2025-08-04 17:28:57,387 - INFO - 商品项 12 直接获取到data-autolog: item_id=690522809098&path=search_goods_card&index=11&type=category
2025-08-04 17:28:57,387 - INFO - 商品项 12 data-autolog: item_id=690522809098&path=search_goods_card&index=11&type=category
2025-08-04 17:28:57,388 - INFO - 商品项 12 提取到item_id: 690522809098
2025-08-04 17:28:57,399 - INFO - 商品项 12 商品名称: 24年制 日本本土采 KAO/花王乐而雅超薄卫生巾生理期姨妈巾卫生棉
2025-08-04 17:28:57,409 - INFO - 找到商品项 13，检查元素信息...
2025-08-04 17:28:57,418 - INFO - 商品项 13 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:28:57,419 - INFO - 商品项 13 当前元素无data-autolog，查找子元素...
2025-08-04 17:28:57,426 - INFO - 商品项 13 找到子元素，class: tfx-item
2025-08-04 17:28:57,429 - INFO - 商品项 13 直接获取到data-autolog: item_id=698669180031&path=search_goods_card&index=12&type=category
2025-08-04 17:28:57,430 - INFO - 商品项 13 data-autolog: item_id=698669180031&path=search_goods_card&index=12&type=category
2025-08-04 17:28:57,430 - INFO - 商品项 13 提取到item_id: 698669180031
2025-08-04 17:28:57,438 - INFO - 商品项 13 商品名称: 分销商 加V加拿大TAMPAX丹碧丝卫生棉条长导管式内置月经棉棒96支
2025-08-04 17:28:57,450 - INFO - 找到商品项 14，检查元素信息...
2025-08-04 17:28:57,458 - INFO - 商品项 14 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:28:57,458 - INFO - 商品项 14 当前元素无data-autolog，查找子元素...
2025-08-04 17:28:57,466 - INFO - 商品项 14 找到子元素，class: tfx-item
2025-08-04 17:28:57,468 - INFO - 商品项 14 直接获取到data-autolog: item_id=710114820531&path=search_goods_card&index=13&type=category
2025-08-04 17:28:57,469 - INFO - 商品项 14 data-autolog: item_id=710114820531&path=search_goods_card&index=13&type=category
2025-08-04 17:28:57,469 - INFO - 商品项 14 提取到item_id: 710114820531
2025-08-04 17:28:57,478 - INFO - 商品项 14 商品名称: 【保税直发】泰国香蕉膏 防裂膏手足干裂脚后跟修复膏 20g*6瓶/盒
2025-08-04 17:28:57,491 - INFO - 找到商品项 15，检查元素信息...
2025-08-04 17:28:57,497 - INFO - 商品项 15 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:28:57,497 - INFO - 商品项 15 当前元素无data-autolog，查找子元素...
2025-08-04 17:28:57,506 - INFO - 商品项 15 找到子元素，class: tfx-item
2025-08-04 17:28:57,509 - INFO - 商品项 15 直接获取到data-autolog: item_id=642497736652&path=search_goods_card&index=14&type=category
2025-08-04 17:28:57,509 - INFO - 商品项 15 data-autolog: item_id=642497736652&path=search_goods_card&index=14&type=category
2025-08-04 17:28:57,510 - INFO - 商品项 15 提取到item_id: 642497736652
2025-08-04 17:28:57,520 - INFO - 商品项 15 商品名称: 德国班奈特香水洗衣液3KG留香留香香味持久家用香水型
2025-08-04 17:28:57,531 - INFO - 找到商品项 16，检查元素信息...
2025-08-04 17:28:57,542 - INFO - 商品项 16 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:28:57,542 - INFO - 商品项 16 当前元素无data-autolog，查找子元素...
2025-08-04 17:28:57,549 - INFO - 商品项 16 找到子元素，class: tfx-item
2025-08-04 17:28:57,555 - INFO - 商品项 16 直接获取到data-autolog: item_id=743986957239&path=search_goods_card&index=15&type=category
2025-08-04 17:28:57,555 - INFO - 商品项 16 data-autolog: item_id=743986957239&path=search_goods_card&index=15&type=category
2025-08-04 17:28:57,555 - INFO - 商品项 16 提取到item_id: 743986957239
2025-08-04 17:28:57,565 - INFO - 商品项 16 商品名称: 保税直供 拜耳出品Canesten凯妮汀衣物除菌液1L【港版】
2025-08-04 17:28:57,576 - INFO - 找到商品项 17，检查元素信息...
2025-08-04 17:28:57,586 - INFO - 商品项 17 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:28:57,586 - INFO - 商品项 17 当前元素无data-autolog，查找子元素...
2025-08-04 17:28:57,594 - INFO - 商品项 17 找到子元素，class: tfx-item
2025-08-04 17:28:57,597 - INFO - 商品项 17 直接获取到data-autolog: item_id=703823332399&path=search_goods_card&index=16&type=category
2025-08-04 17:28:57,597 - INFO - 商品项 17 data-autolog: item_id=703823332399&path=search_goods_card&index=16&type=category
2025-08-04 17:28:57,597 - INFO - 商品项 17 提取到item_id: 703823332399
2025-08-04 17:28:57,608 - INFO - 商品项 17 商品名称: 意大利Marvis玛尔斯牙膏银色白色蓝色85ml [注意纸盒易折痕】
2025-08-04 17:28:57,618 - INFO - 找到商品项 18，检查元素信息...
2025-08-04 17:28:57,626 - INFO - 商品项 18 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:28:57,626 - INFO - 商品项 18 当前元素无data-autolog，查找子元素...
2025-08-04 17:28:57,632 - INFO - 商品项 18 找到子元素，class: tfx-item
2025-08-04 17:28:57,636 - INFO - 商品项 18 直接获取到data-autolog: item_id=668810723647&path=search_goods_card&index=17&type=category
2025-08-04 17:28:57,636 - INFO - 商品项 18 data-autolog: item_id=668810723647&path=search_goods_card&index=17&type=category
2025-08-04 17:28:57,636 - INFO - 商品项 18 提取到item_id: 668810723647
2025-08-04 17:28:57,644 - INFO - 商品项 18 商品名称: 日本white conc沐浴露全身沐浴乳进口持久留香林允同款变白女
2025-08-04 17:28:57,655 - INFO - 找到商品项 19，检查元素信息...
2025-08-04 17:28:57,664 - INFO - 商品项 19 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:28:57,664 - INFO - 商品项 19 当前元素无data-autolog，查找子元素...
2025-08-04 17:28:57,672 - INFO - 商品项 19 找到子元素，class: tfx-item
2025-08-04 17:28:57,676 - INFO - 商品项 19 直接获取到data-autolog: item_id=742357678329&path=search_goods_card&index=18&type=category
2025-08-04 17:28:57,676 - INFO - 商品项 19 data-autolog: item_id=742357678329&path=search_goods_card&index=18&type=category
2025-08-04 17:28:57,677 - INFO - 商品项 19 提取到item_id: 742357678329
2025-08-04 17:28:57,685 - INFO - 商品项 19 商品名称: 【保税直发】德国芭乐雅Balea沐浴露300ml 25.10
2025-08-04 17:28:57,695 - INFO - 找到商品项 20，检查元素信息...
2025-08-04 17:28:57,703 - INFO - 商品项 20 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:28:57,704 - INFO - 商品项 20 当前元素无data-autolog，查找子元素...
2025-08-04 17:28:57,710 - INFO - 商品项 20 找到子元素，class: tfx-item
2025-08-04 17:28:57,714 - INFO - 商品项 20 直接获取到data-autolog: item_id=679559792422&path=search_goods_card&index=19&type=category
2025-08-04 17:28:57,714 - INFO - 商品项 20 data-autolog: item_id=679559792422&path=search_goods_card&index=19&type=category
2025-08-04 17:28:57,715 - INFO - 商品项 20 提取到item_id: 679559792422
2025-08-04 17:28:57,726 - INFO - 商品项 20 商品名称: Proraso 檀香泡沫300ML（泡沫类目）-2.0
2025-08-04 17:28:57,737 - INFO - 找到商品项 21，检查元素信息...
2025-08-04 17:28:57,746 - INFO - 商品项 21 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:28:57,746 - INFO - 商品项 21 当前元素无data-autolog，查找子元素...
2025-08-04 17:28:57,751 - INFO - 商品项 21 找到子元素，class: tfx-item
2025-08-04 17:28:57,758 - INFO - 商品项 21 直接获取到data-autolog: item_id=755157258216&path=search_goods_card&index=20&type=category
2025-08-04 17:28:57,758 - INFO - 商品项 21 data-autolog: item_id=755157258216&path=search_goods_card&index=20&type=category
2025-08-04 17:28:57,758 - INFO - 商品项 21 提取到item_id: 755157258216
2025-08-04 17:28:57,767 - INFO - 商品项 21 商品名称: 【羽阳商贸淘宝仓】泰国hygiene衣物柔顺剂三色 大容量3402509000
2025-08-04 17:28:57,777 - INFO - 找到商品项 22，检查元素信息...
2025-08-04 17:28:57,787 - INFO - 商品项 22 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:28:57,787 - INFO - 商品项 22 当前元素无data-autolog，查找子元素...
2025-08-04 17:28:57,795 - INFO - 商品项 22 找到子元素，class: tfx-item
2025-08-04 17:28:57,798 - INFO - 商品项 22 直接获取到data-autolog: item_id=762133318080&path=search_goods_card&index=21&type=category
2025-08-04 17:28:57,798 - INFO - 商品项 22 data-autolog: item_id=762133318080&path=search_goods_card&index=21&type=category
2025-08-04 17:28:57,798 - INFO - 商品项 22 提取到item_id: 762133318080
2025-08-04 17:28:57,807 - INFO - 商品项 22 商品名称: 美国Listerine李施德林口气清新片薄荷味(3*24片)/盒 2106909090
2025-08-04 17:28:57,818 - INFO - 找到商品项 23，检查元素信息...
2025-08-04 17:28:57,827 - INFO - 商品项 23 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:28:57,827 - INFO - 商品项 23 当前元素无data-autolog，查找子元素...
2025-08-04 17:28:57,835 - INFO - 商品项 23 找到子元素，class: tfx-item
2025-08-04 17:28:57,838 - INFO - 商品项 23 直接获取到data-autolog: item_id=759842528968&path=search_goods_card&index=22&type=category
2025-08-04 17:28:57,838 - INFO - 商品项 23 data-autolog: item_id=759842528968&path=search_goods_card&index=22&type=category
2025-08-04 17:28:57,838 - INFO - 商品项 23 提取到item_id: 759842528968
2025-08-04 17:28:57,846 - INFO - 商品项 23 商品名称: 【保税现货】泰国DENTISTE丹师特牙膏薄荷持久清新口气去黄异味
2025-08-04 17:28:57,857 - INFO - 找到商品项 24，检查元素信息...
2025-08-04 17:28:57,866 - INFO - 商品项 24 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:28:57,866 - INFO - 商品项 24 当前元素无data-autolog，查找子元素...
2025-08-04 17:28:57,872 - INFO - 商品项 24 找到子元素，class: tfx-item
2025-08-04 17:28:57,875 - INFO - 商品项 24 直接获取到data-autolog: item_id=740410737413&path=search_goods_card&index=23&type=category
2025-08-04 17:28:57,875 - INFO - 商品项 24 data-autolog: item_id=740410737413&path=search_goods_card&index=23&type=category
2025-08-04 17:28:57,876 - INFO - 商品项 24 提取到item_id: 740410737413
2025-08-04 17:28:57,884 - INFO - 商品项 24 商品名称: 第一三共CLEANDENTAL牙周护理牙膏50g 90g 100g 漱口水
2025-08-04 17:28:57,898 - INFO - 找到商品项 25，检查元素信息...
2025-08-04 17:28:57,907 - INFO - 商品项 25 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:28:57,907 - INFO - 商品项 25 当前元素无data-autolog，查找子元素...
2025-08-04 17:28:57,914 - INFO - 商品项 25 找到子元素，class: tfx-item
2025-08-04 17:28:57,917 - INFO - 商品项 25 直接获取到data-autolog: item_id=778744140129&path=search_goods_card&index=24&type=category
2025-08-04 17:28:57,917 - INFO - 商品项 25 data-autolog: item_id=778744140129&path=search_goods_card&index=24&type=category
2025-08-04 17:28:57,917 - INFO - 商品项 25 提取到item_id: 778744140129
2025-08-04 17:28:57,925 - INFO - 商品项 25 商品名称: 花瓶1
2025-08-04 17:28:57,936 - INFO - 找到商品项 26，检查元素信息...
2025-08-04 17:28:57,945 - INFO - 商品项 26 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:28:57,945 - INFO - 商品项 26 当前元素无data-autolog，查找子元素...
2025-08-04 17:28:57,951 - INFO - 商品项 26 找到子元素，class: tfx-item
2025-08-04 17:28:57,955 - INFO - 商品项 26 直接获取到data-autolog: item_id=680534455265&path=search_goods_card&index=25&type=category
2025-08-04 17:28:57,956 - INFO - 商品项 26 data-autolog: item_id=680534455265&path=search_goods_card&index=25&type=category
2025-08-04 17:28:57,956 - INFO - 商品项 26 提取到item_id: 680534455265
2025-08-04 17:28:57,963 - INFO - 商品项 26 商品名称: 美国Listerine李施德林口气清新片冰爽薄荷味肉桂味(3*24片)/盒
2025-08-04 17:28:57,975 - INFO - 找到商品项 27，检查元素信息...
2025-08-04 17:28:57,983 - INFO - 商品项 27 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:28:57,983 - INFO - 商品项 27 当前元素无data-autolog，查找子元素...
2025-08-04 17:28:57,989 - INFO - 商品项 27 找到子元素，class: tfx-item
2025-08-04 17:28:57,991 - INFO - 商品项 27 直接获取到data-autolog: item_id=796223606931&path=search_goods_card&index=26&type=category
2025-08-04 17:28:57,991 - INFO - 商品项 27 data-autolog: item_id=796223606931&path=search_goods_card&index=26&type=category
2025-08-04 17:28:57,993 - INFO - 商品项 27 提取到item_id: 796223606931
2025-08-04 17:28:58,001 - INFO - 商品项 27 商品名称: 勿动3
2025-08-04 17:28:58,013 - INFO - 找到商品项 28，检查元素信息...
2025-08-04 17:28:58,021 - INFO - 商品项 28 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:28:58,021 - INFO - 商品项 28 当前元素无data-autolog，查找子元素...
2025-08-04 17:28:58,028 - INFO - 商品项 28 找到子元素，class: tfx-item
2025-08-04 17:28:58,032 - INFO - 商品项 28 直接获取到data-autolog: item_id=805523184209&path=search_goods_card&index=27&type=category
2025-08-04 17:28:58,032 - INFO - 商品项 28 data-autolog: item_id=805523184209&path=search_goods_card&index=27&type=category
2025-08-04 17:28:58,033 - INFO - 商品项 28 提取到item_id: 805523184209
2025-08-04 17:28:58,043 - INFO - 商品项 28 商品名称: 【渠道授权】4支装 日本EBISU惠百施软毛宽头牙刷6列
2025-08-04 17:28:58,054 - INFO - 找到商品项 29，检查元素信息...
2025-08-04 17:28:58,062 - INFO - 商品项 29 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:28:58,062 - INFO - 商品项 29 当前元素无data-autolog，查找子元素...
2025-08-04 17:28:58,070 - INFO - 商品项 29 找到子元素，class: tfx-item
2025-08-04 17:28:58,073 - INFO - 商品项 29 直接获取到data-autolog: item_id=679995147317&path=search_goods_card&index=28&type=category
2025-08-04 17:28:58,073 - INFO - 商品项 29 data-autolog: item_id=679995147317&path=search_goods_card&index=28&type=category
2025-08-04 17:28:58,073 - INFO - 商品项 29 提取到item_id: 679995147317
2025-08-04 17:28:58,081 - INFO - 商品项 29 商品名称: 日本white conc沐浴露全身沐浴乳进口持久留香林允同款变白女
2025-08-04 17:28:58,092 - INFO - 找到商品项 30，检查元素信息...
2025-08-04 17:28:58,099 - INFO - 商品项 30 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:28:58,100 - INFO - 商品项 30 当前元素无data-autolog，查找子元素...
2025-08-04 17:28:58,107 - INFO - 商品项 30 找到子元素，class: tfx-item
2025-08-04 17:28:58,109 - INFO - 商品项 30 直接获取到data-autolog: item_id=751408783831&path=search_goods_card&index=29&type=category
2025-08-04 17:28:58,109 - INFO - 商品项 30 data-autolog: item_id=751408783831&path=search_goods_card&index=29&type=category
2025-08-04 17:28:58,109 - INFO - 商品项 30 提取到item_id: 751408783831
2025-08-04 17:28:58,120 - INFO - 商品项 30 商品名称: 日本white conc沐浴露全身沐浴乳进口持久留香林允同款
2025-08-04 17:28:58,130 - INFO - 找到商品项 31，检查元素信息...
2025-08-04 17:28:58,137 - INFO - 商品项 31 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:28:58,137 - INFO - 商品项 31 当前元素无data-autolog，查找子元素...
2025-08-04 17:28:58,143 - INFO - 商品项 31 找到子元素，class: tfx-item
2025-08-04 17:28:58,146 - INFO - 商品项 31 直接获取到data-autolog: item_id=651574474225&path=search_goods_card&index=30&type=category
2025-08-04 17:28:58,146 - INFO - 商品项 31 data-autolog: item_id=651574474225&path=search_goods_card&index=30&type=category
2025-08-04 17:28:58,146 - INFO - 商品项 31 提取到item_id: 651574474225
2025-08-04 17:28:58,155 - INFO - 商品项 31 商品名称: 英国LAISHEMUM莱氏姆洗衣液3.1KG留香香味持久家用香水型品牌正品
2025-08-04 17:28:58,165 - INFO - 找到商品项 32，检查元素信息...
2025-08-04 17:28:58,173 - INFO - 商品项 32 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:28:58,173 - INFO - 商品项 32 当前元素无data-autolog，查找子元素...
2025-08-04 17:28:58,181 - INFO - 商品项 32 找到子元素，class: tfx-item
2025-08-04 17:28:58,184 - INFO - 商品项 32 直接获取到data-autolog: item_id=706488285938&path=search_goods_card&index=31&type=category
2025-08-04 17:28:58,184 - INFO - 商品项 32 data-autolog: item_id=706488285938&path=search_goods_card&index=31&type=category
2025-08-04 17:28:58,184 - INFO - 商品项 32 提取到item_id: 706488285938
2025-08-04 17:28:58,192 - INFO - 商品项 32 商品名称: 强生婴儿沐浴露儿童沐浴乳滋润牛奶沐浴液洗浴露全家可用1000ml
2025-08-04 17:28:58,203 - INFO - 找到商品项 33，检查元素信息...
2025-08-04 17:28:58,212 - INFO - 商品项 33 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:28:58,212 - INFO - 商品项 33 当前元素无data-autolog，查找子元素...
2025-08-04 17:28:58,220 - INFO - 商品项 33 找到子元素，class: tfx-item
2025-08-04 17:28:58,223 - INFO - 商品项 33 直接获取到data-autolog: item_id=739782656534&path=search_goods_card&index=32&type=category
2025-08-04 17:28:58,223 - INFO - 商品项 33 data-autolog: item_id=739782656534&path=search_goods_card&index=32&type=category
2025-08-04 17:28:58,223 - INFO - 商品项 33 提取到item_id: 739782656534
2025-08-04 17:28:58,231 - INFO - 商品项 33 商品名称: 德国益周适缓解牙龈出血牙肉红肿防蛀抗敏感去黄美白牙膏75ML
2025-08-04 17:28:58,243 - INFO - 找到商品项 34，检查元素信息...
2025-08-04 17:28:58,253 - INFO - 商品项 34 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:28:58,253 - INFO - 商品项 34 当前元素无data-autolog，查找子元素...
2025-08-04 17:28:58,261 - INFO - 商品项 34 找到子元素，class: tfx-item
2025-08-04 17:28:58,264 - INFO - 商品项 34 直接获取到data-autolog: item_id=686904390887&path=search_goods_card&index=33&type=category
2025-08-04 17:28:58,264 - INFO - 商品项 34 data-autolog: item_id=686904390887&path=search_goods_card&index=33&type=category
2025-08-04 17:28:58,264 - INFO - 商品项 34 提取到item_id: 686904390887
2025-08-04 17:28:58,272 - INFO - 商品项 34 商品名称: 新西兰国民品牌Glow Lab沐浴露900ml原装进口 多规格效期不一致
2025-08-04 17:28:58,283 - INFO - 找到商品项 35，检查元素信息...
2025-08-04 17:28:58,292 - INFO - 商品项 35 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:28:58,292 - INFO - 商品项 35 当前元素无data-autolog，查找子元素...
2025-08-04 17:28:58,298 - INFO - 商品项 35 找到子元素，class: tfx-item
2025-08-04 17:28:58,303 - INFO - 商品项 35 直接获取到data-autolog: item_id=822594991780&path=search_goods_card&index=34&type=category
2025-08-04 17:28:58,303 - INFO - 商品项 35 data-autolog: item_id=822594991780&path=search_goods_card&index=34&type=category
2025-08-04 17:28:58,303 - INFO - 商品项 35 提取到item_id: 822594991780
2025-08-04 17:28:58,311 - INFO - 商品项 35 商品名称: 勿动9
2025-08-04 17:28:58,321 - INFO - 找到商品项 36，检查元素信息...
2025-08-04 17:28:58,331 - INFO - 商品项 36 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:28:58,332 - INFO - 商品项 36 当前元素无data-autolog，查找子元素...
2025-08-04 17:28:58,339 - INFO - 商品项 36 找到子元素，class: tfx-item
2025-08-04 17:28:58,343 - INFO - 商品项 36 直接获取到data-autolog: item_id=831506717611&path=search_goods_card&index=35&type=category
2025-08-04 17:28:58,343 - INFO - 商品项 36 data-autolog: item_id=831506717611&path=search_goods_card&index=35&type=category
2025-08-04 17:28:58,343 - INFO - 商品项 36 提取到item_id: 831506717611
2025-08-04 17:28:58,352 - INFO - 商品项 36 商品名称: 【保税】第一三共cleandental牙膏牙垢牙周护理100g/50g
2025-08-04 17:28:58,363 - INFO - 找到商品项 37，检查元素信息...
2025-08-04 17:28:58,372 - INFO - 商品项 37 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:28:58,372 - INFO - 商品项 37 当前元素无data-autolog，查找子元素...
2025-08-04 17:28:58,379 - INFO - 商品项 37 找到子元素，class: tfx-item
2025-08-04 17:28:58,383 - INFO - 商品项 37 直接获取到data-autolog: item_id=679319445210&path=search_goods_card&index=36&type=category
2025-08-04 17:28:58,383 - INFO - 商品项 37 data-autolog: item_id=679319445210&path=search_goods_card&index=36&type=category
2025-08-04 17:28:58,383 - INFO - 商品项 37 提取到item_id: 679319445210
2025-08-04 17:28:58,390 - INFO - 商品项 37 商品名称: 美国Tampax丹碧丝导管式卫生棉条超值装
2025-08-04 17:28:58,402 - INFO - 找到商品项 38，检查元素信息...
2025-08-04 17:28:58,411 - INFO - 商品项 38 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:28:58,411 - INFO - 商品项 38 当前元素无data-autolog，查找子元素...
2025-08-04 17:28:58,419 - INFO - 商品项 38 找到子元素，class: tfx-item
2025-08-04 17:28:58,422 - INFO - 商品项 38 直接获取到data-autolog: item_id=738635978220&path=search_goods_card&index=37&type=category
2025-08-04 17:28:58,423 - INFO - 商品项 38 data-autolog: item_id=738635978220&path=search_goods_card&index=37&type=category
2025-08-04 17:28:58,423 - INFO - 商品项 38 提取到item_id: 738635978220
2025-08-04 17:28:58,431 - INFO - 商品项 38 商品名称: 【品牌直供】韩国爱茉莉麦迪安93牙膏 清洁清新口气亮白去黄3支30
2025-08-04 17:28:58,442 - INFO - 找到商品项 39，检查元素信息...
2025-08-04 17:28:58,451 - INFO - 商品项 39 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:28:58,451 - INFO - 商品项 39 当前元素无data-autolog，查找子元素...
2025-08-04 17:28:58,458 - INFO - 商品项 39 找到子元素，class: tfx-item
2025-08-04 17:28:58,462 - INFO - 商品项 39 直接获取到data-autolog: item_id=763435676681&path=search_goods_card&index=38&type=category
2025-08-04 17:28:58,462 - INFO - 商品项 39 data-autolog: item_id=763435676681&path=search_goods_card&index=38&type=category
2025-08-04 17:28:58,462 - INFO - 商品项 39 提取到item_id: 763435676681
2025-08-04 17:28:58,469 - INFO - 商品项 39 商品名称: 美国Tide to Go汰渍便携式衣物清洁剂去渍笔10ml 3402509000
2025-08-04 17:28:58,481 - INFO - 找到商品项 40，检查元素信息...
2025-08-04 17:28:58,489 - INFO - 商品项 40 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:28:58,489 - INFO - 商品项 40 当前元素无data-autolog，查找子元素...
2025-08-04 17:28:58,496 - INFO - 商品项 40 找到子元素，class: tfx-item
2025-08-04 17:28:58,499 - INFO - 商品项 40 直接获取到data-autolog: item_id=679916637964&path=search_goods_card&index=39&type=category
2025-08-04 17:28:58,499 - INFO - 商品项 40 data-autolog: item_id=679916637964&path=search_goods_card&index=39&type=category
2025-08-04 17:28:58,500 - INFO - 商品项 40 提取到item_id: 679916637964
2025-08-04 17:28:58,510 - INFO - 商品项 40 商品名称: Proraso 燕麦泡沫300ML（泡沫类目）-2.0
2025-08-04 17:28:58,510 - INFO - 当前页面爬取完成，找到 40 个商品
2025-08-04 17:28:58,510 - INFO - 第 1 页找到 40 个商品，总计: 40
2025-08-04 17:28:58,542 - INFO - 找到下一页按钮: text='下一页', class='next-btn next-medium next-btn-normal next-pagination-item next-next'
2025-08-04 17:28:58,542 - INFO - 下一页按钮可点击，准备点击
2025-08-04 17:28:58,563 - INFO - 准备点击元素: tag=button, text='下一页', class='next-btn next-medium next-btn-', id=''
2025-08-04 17:28:59,728 - INFO - 尝试ActionChains点击...
2025-08-04 17:29:00,049 - INFO - ActionChains点击成功
2025-08-04 17:29:00,520 - INFO - 成功点击下一页按钮
2025-08-04 17:29:03,520 - INFO - 正在爬取第 2 页商品信息...
2025-08-04 17:29:05,531 - INFO - 找到商品容器，开始爬取商品，最大数量: 20
2025-08-04 17:29:05,540 - INFO - 找到商品项 1，检查元素信息...
2025-08-04 17:29:05,548 - INFO - 商品项 1 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:29:05,549 - INFO - 商品项 1 当前元素无data-autolog，查找子元素...
2025-08-04 17:29:05,556 - INFO - 商品项 1 找到子元素，class: tfx-item
2025-08-04 17:29:05,559 - INFO - 商品项 1 直接获取到data-autolog: item_id=756909858622&path=search_goods_card&type=category
2025-08-04 17:29:05,559 - INFO - 商品项 1 data-autolog: item_id=756909858622&path=search_goods_card&type=category
2025-08-04 17:29:05,559 - INFO - 商品项 1 提取到item_id: 756909858622
2025-08-04 17:29:05,571 - INFO - 商品项 1 商品名称: 吉列Gillette锋速3剃须刀3层手动剃须刀头 链路齐全
2025-08-04 17:29:05,595 - INFO - 找到商品项 2，检查元素信息...
2025-08-04 17:29:05,603 - INFO - 商品项 2 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:29:05,603 - INFO - 商品项 2 当前元素无data-autolog，查找子元素...
2025-08-04 17:29:05,612 - INFO - 商品项 2 找到子元素，class: tfx-item
2025-08-04 17:29:05,615 - INFO - 商品项 2 直接获取到data-autolog: item_id=679916877746&path=search_goods_card&index=1&type=category
2025-08-04 17:29:05,615 - INFO - 商品项 2 data-autolog: item_id=679916877746&path=search_goods_card&index=1&type=category
2025-08-04 17:29:05,616 - INFO - 商品项 2 提取到item_id: 679916877746
2025-08-04 17:29:05,625 - INFO - 商品项 2 商品名称: Proraso 芦荟剃须泡沫 300ML-2.0
2025-08-04 17:29:05,634 - INFO - 找到商品项 3，检查元素信息...
2025-08-04 17:29:05,643 - INFO - 商品项 3 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:29:05,643 - INFO - 商品项 3 当前元素无data-autolog，查找子元素...
2025-08-04 17:29:05,651 - INFO - 商品项 3 找到子元素，class: tfx-item
2025-08-04 17:29:05,654 - INFO - 商品项 3 直接获取到data-autolog: item_id=679560394795&path=search_goods_card&index=2&type=category
2025-08-04 17:29:05,654 - INFO - 商品项 3 data-autolog: item_id=679560394795&path=search_goods_card&index=2&type=category
2025-08-04 17:29:05,654 - INFO - 商品项 3 提取到item_id: 679560394795
2025-08-04 17:29:05,664 - INFO - 商品项 3 商品名称: 意大利Marvis玛尔斯牙膏85ml
2025-08-04 17:29:05,672 - INFO - 找到商品项 4，检查元素信息...
2025-08-04 17:29:05,681 - INFO - 商品项 4 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:29:05,681 - INFO - 商品项 4 当前元素无data-autolog，查找子元素...
2025-08-04 17:29:05,688 - INFO - 商品项 4 找到子元素，class: tfx-item
2025-08-04 17:29:05,691 - INFO - 商品项 4 直接获取到data-autolog: item_id=693959618019&path=search_goods_card&index=3&type=category
2025-08-04 17:29:05,691 - INFO - 商品项 4 data-autolog: item_id=693959618019&path=search_goods_card&index=3&type=category
2025-08-04 17:29:05,691 - INFO - 商品项 4 提取到item_id: 693959618019
2025-08-04 17:29:05,701 - INFO - 商品项 4 商品名称: 【保税直发】fancl药盒
2025-08-04 17:29:05,708 - INFO - 找到商品项 5，检查元素信息...
2025-08-04 17:29:05,716 - INFO - 商品项 5 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:29:05,716 - INFO - 商品项 5 当前元素无data-autolog，查找子元素...
2025-08-04 17:29:05,723 - INFO - 商品项 5 找到子元素，class: tfx-item
2025-08-04 17:29:05,725 - INFO - 商品项 5 直接获取到data-autolog: item_id=816763140646&path=search_goods_card&index=4&type=category
2025-08-04 17:29:05,725 - INFO - 商品项 5 data-autolog: item_id=816763140646&path=search_goods_card&index=4&type=category
2025-08-04 17:29:05,726 - INFO - 商品项 5 提取到item_id: 816763140646
2025-08-04 17:29:05,734 - INFO - 商品项 5 商品名称: 进口Euthymol复古粉色牙膏持久清新口气清洁牙齿护龈去牙垢75ml
2025-08-04 17:29:05,743 - INFO - 找到商品项 6，检查元素信息...
2025-08-04 17:29:05,750 - INFO - 商品项 6 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:29:05,750 - INFO - 商品项 6 当前元素无data-autolog，查找子元素...
2025-08-04 17:29:05,758 - INFO - 商品项 6 找到子元素，class: tfx-item
2025-08-04 17:29:05,762 - INFO - 商品项 6 直接获取到data-autolog: item_id=680234193283&path=search_goods_card&index=5&type=category
2025-08-04 17:29:05,762 - INFO - 商品项 6 data-autolog: item_id=680234193283&path=search_goods_card&index=5&type=category
2025-08-04 17:29:05,762 - INFO - 商品项 6 提取到item_id: 680234193283
2025-08-04 17:29:05,773 - INFO - 商品项 6 商品名称: 美国Playtex倍得适内置长导管卫生棉条运动款48支 可选
2025-08-04 17:29:05,781 - INFO - 找到商品项 7，检查元素信息...
2025-08-04 17:29:05,805 - INFO - 商品项 7 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:29:05,805 - INFO - 商品项 7 当前元素无data-autolog，查找子元素...
2025-08-04 17:29:05,813 - INFO - 商品项 7 找到子元素，class: tfx-item
2025-08-04 17:29:05,816 - INFO - 商品项 7 直接获取到data-autolog: item_id=822251094523&path=search_goods_card&index=6&type=category
2025-08-04 17:29:05,816 - INFO - 商品项 7 data-autolog: item_id=822251094523&path=search_goods_card&index=6&type=category
2025-08-04 17:29:05,816 - INFO - 商品项 7 提取到item_id: 822251094523
2025-08-04 17:29:05,826 - INFO - 商品项 7 商品名称: 日本原装进口巴斯克林美肌泡澡浴盐系列600g/罐
2025-08-04 17:29:05,833 - INFO - 找到商品项 8，检查元素信息...
2025-08-04 17:29:05,841 - INFO - 商品项 8 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:29:05,841 - INFO - 商品项 8 当前元素无data-autolog，查找子元素...
2025-08-04 17:29:05,851 - INFO - 商品项 8 找到子元素，class: tfx-item
2025-08-04 17:29:05,854 - INFO - 商品项 8 直接获取到data-autolog: item_id=727595139035&path=search_goods_card&index=7&type=category
2025-08-04 17:29:05,854 - INFO - 商品项 8 data-autolog: item_id=727595139035&path=search_goods_card&index=7&type=category
2025-08-04 17:29:05,854 - INFO - 商品项 8 提取到item_id: 727595139035
2025-08-04 17:29:05,863 - INFO - 商品项 8 商品名称: 【保税】花王牙膏美白护齿165g
2025-08-04 17:29:05,870 - INFO - 找到商品项 9，检查元素信息...
2025-08-04 17:29:05,879 - INFO - 商品项 9 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:29:05,879 - INFO - 商品项 9 当前元素无data-autolog，查找子元素...
2025-08-04 17:29:05,885 - INFO - 商品项 9 找到子元素，class: tfx-item
2025-08-04 17:29:05,889 - INFO - 商品项 9 直接获取到data-autolog: item_id=937604879458&path=search_goods_card&index=8&type=category
2025-08-04 17:29:05,889 - INFO - 商品项 9 data-autolog: item_id=937604879458&path=search_goods_card&index=8&type=category
2025-08-04 17:29:05,889 - INFO - 商品项 9 提取到item_id: 937604879458
2025-08-04 17:29:05,898 - INFO - 商品项 9 商品名称: 夏季大帽檐防紫外线空顶防晒太阳帽一个 米白色
2025-08-04 17:29:05,906 - INFO - 找到商品项 10，检查元素信息...
2025-08-04 17:29:05,914 - INFO - 商品项 10 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:29:05,914 - INFO - 商品项 10 当前元素无data-autolog，查找子元素...
2025-08-04 17:29:05,921 - INFO - 商品项 10 找到子元素，class: tfx-item
2025-08-04 17:29:05,925 - INFO - 商品项 10 直接获取到data-autolog: item_id=824593169092&path=search_goods_card&index=9&type=category
2025-08-04 17:29:05,925 - INFO - 商品项 10 data-autolog: item_id=824593169092&path=search_goods_card&index=9&type=category
2025-08-04 17:29:05,925 - INFO - 商品项 10 提取到item_id: 824593169092
2025-08-04 17:29:05,934 - INFO - 商品项 10 商品名称: INSTANT SMILE白月光美白牙膜INSMILE牙齿美白亮白牙膏去黄牙凝胶
2025-08-04 17:29:05,944 - INFO - 找到商品项 11，检查元素信息...
2025-08-04 17:29:05,951 - INFO - 商品项 11 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:29:05,951 - INFO - 商品项 11 当前元素无data-autolog，查找子元素...
2025-08-04 17:29:05,958 - INFO - 商品项 11 找到子元素，class: tfx-item
2025-08-04 17:29:05,961 - INFO - 商品项 11 直接获取到data-autolog: item_id=680475089390&path=search_goods_card&index=10&type=category
2025-08-04 17:29:05,962 - INFO - 商品项 11 data-autolog: item_id=680475089390&path=search_goods_card&index=10&type=category
2025-08-04 17:29:05,962 - INFO - 商品项 11 提取到item_id: 680475089390
2025-08-04 17:29:05,971 - INFO - 商品项 11 商品名称: 特价 日落琥珀26.05 欧舒丹香皂4*50G 3401110090
2025-08-04 17:29:05,977 - INFO - 找到商品项 12，检查元素信息...
2025-08-04 17:29:05,986 - INFO - 商品项 12 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:29:05,986 - INFO - 商品项 12 当前元素无data-autolog，查找子元素...
2025-08-04 17:29:05,993 - INFO - 商品项 12 找到子元素，class: tfx-item
2025-08-04 17:29:05,996 - INFO - 商品项 12 直接获取到data-autolog: item_id=678624292168&path=search_goods_card&index=11&type=category
2025-08-04 17:29:05,996 - INFO - 商品项 12 data-autolog: item_id=678624292168&path=search_goods_card&index=11&type=category
2025-08-04 17:29:05,996 - INFO - 商品项 12 提取到item_id: 678624292168
2025-08-04 17:29:06,004 - INFO - 商品项 12 商品名称: 加拿大Tampax丹碧丝卫生棉条导管式内置96支
2025-08-04 17:29:06,013 - INFO - 找到商品项 13，检查元素信息...
2025-08-04 17:29:06,022 - INFO - 商品项 13 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:29:06,022 - INFO - 商品项 13 当前元素无data-autolog，查找子元素...
2025-08-04 17:29:06,029 - INFO - 商品项 13 找到子元素，class: tfx-item
2025-08-04 17:29:06,031 - INFO - 商品项 13 直接获取到data-autolog: item_id=678615972761&path=search_goods_card&index=12&type=category
2025-08-04 17:29:06,033 - INFO - 商品项 13 data-autolog: item_id=678615972761&path=search_goods_card&index=12&type=category
2025-08-04 17:29:06,033 - INFO - 商品项 13 提取到item_id: 678615972761
2025-08-04 17:29:06,042 - INFO - 商品项 13 商品名称: 美国BadAir Sponge甲醛装修汽车异味空气净化剂400g 3307490000
2025-08-04 17:29:06,051 - INFO - 找到商品项 14，检查元素信息...
2025-08-04 17:29:06,059 - INFO - 商品项 14 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:29:06,059 - INFO - 商品项 14 当前元素无data-autolog，查找子元素...
2025-08-04 17:29:06,067 - INFO - 商品项 14 找到子元素，class: tfx-item
2025-08-04 17:29:06,070 - INFO - 商品项 14 直接获取到data-autolog: item_id=775364477598&path=search_goods_card&index=13&type=category
2025-08-04 17:29:06,070 - INFO - 商品项 14 data-autolog: item_id=775364477598&path=search_goods_card&index=13&type=category
2025-08-04 17:29:06,070 - INFO - 商品项 14 提取到item_id: 775364477598
2025-08-04 17:29:06,079 - INFO - 商品项 14 商品名称: 1-2-3-4支装德国小红牙膏
2025-08-04 17:29:06,088 - INFO - 找到商品项 15，检查元素信息...
2025-08-04 17:29:06,095 - INFO - 商品项 15 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:29:06,095 - INFO - 商品项 15 当前元素无data-autolog，查找子元素...
2025-08-04 17:29:06,103 - INFO - 商品项 15 找到子元素，class: tfx-item
2025-08-04 17:29:06,106 - INFO - 商品项 15 直接获取到data-autolog: item_id=667871296860&path=search_goods_card&index=14&type=category
2025-08-04 17:29:06,106 - INFO - 商品项 15 data-autolog: item_id=667871296860&path=search_goods_card&index=14&type=category
2025-08-04 17:29:06,106 - INFO - 商品项 15 提取到item_id: 667871296860
2025-08-04 17:29:06,116 - INFO - 商品项 15 商品名称: 日本white conc沐浴露全身沐浴乳进口持久留香林允同款变白新
2025-08-04 17:29:06,123 - INFO - 找到商品项 16，检查元素信息...
2025-08-04 17:29:06,131 - INFO - 商品项 16 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:29:06,131 - INFO - 商品项 16 当前元素无data-autolog，查找子元素...
2025-08-04 17:29:06,138 - INFO - 商品项 16 找到子元素，class: tfx-item
2025-08-04 17:29:06,141 - INFO - 商品项 16 直接获取到data-autolog: item_id=721466765064&path=search_goods_card&index=15&type=category
2025-08-04 17:29:06,141 - INFO - 商品项 16 data-autolog: item_id=721466765064&path=search_goods_card&index=15&type=category
2025-08-04 17:29:06,141 - INFO - 商品项 16 提取到item_id: 721466765064
2025-08-04 17:29:06,149 - INFO - 商品项 16 商品名称: 日本未来VAPE防蚊水喷雾宝宝液防叮防虫水婴儿防叮咬神器户外便携
2025-08-04 17:29:06,157 - INFO - 找到商品项 17，检查元素信息...
2025-08-04 17:29:06,165 - INFO - 商品项 17 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:29:06,166 - INFO - 商品项 17 当前元素无data-autolog，查找子元素...
2025-08-04 17:29:06,173 - INFO - 商品项 17 找到子元素，class: tfx-item
2025-08-04 17:29:06,176 - INFO - 商品项 17 直接获取到data-autolog: item_id=832246402264&path=search_goods_card&index=16&type=category
2025-08-04 17:29:06,176 - INFO - 商品项 17 data-autolog: item_id=832246402264&path=search_goods_card&index=16&type=category
2025-08-04 17:29:06,176 - INFO - 商品项 17 提取到item_id: 832246402264
2025-08-04 17:29:06,185 - INFO - 商品项 17 商品名称: 【保税直发】喜净Hygiene衣物清新剂衣物护理祛皱柔顺喷雾220ml
2025-08-04 17:29:06,192 - INFO - 找到商品项 18，检查元素信息...
2025-08-04 17:29:06,201 - INFO - 商品项 18 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:29:06,201 - INFO - 商品项 18 当前元素无data-autolog，查找子元素...
2025-08-04 17:29:06,208 - INFO - 商品项 18 找到子元素，class: tfx-item
2025-08-04 17:29:06,212 - INFO - 商品项 18 直接获取到data-autolog: item_id=646360607664&path=search_goods_card&index=17&type=category
2025-08-04 17:29:06,212 - INFO - 商品项 18 data-autolog: item_id=646360607664&path=search_goods_card&index=17&type=category
2025-08-04 17:29:06,212 - INFO - 商品项 18 提取到item_id: 646360607664
2025-08-04 17:29:06,221 - INFO - 商品项 18 商品名称: 莱氏姆柠檬洗洁精1KG去油家庭装家用按压瓶水果蔬菜英国品牌
2025-08-04 17:29:06,229 - INFO - 找到商品项 19，检查元素信息...
2025-08-04 17:29:06,237 - INFO - 商品项 19 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:29:06,238 - INFO - 商品项 19 当前元素无data-autolog，查找子元素...
2025-08-04 17:29:06,246 - INFO - 商品项 19 找到子元素，class: tfx-item
2025-08-04 17:29:06,250 - INFO - 商品项 19 直接获取到data-autolog: item_id=738499396463&path=search_goods_card&index=18&type=category
2025-08-04 17:29:06,250 - INFO - 商品项 19 data-autolog: item_id=738499396463&path=search_goods_card&index=18&type=category
2025-08-04 17:29:06,250 - INFO - 商品项 19 提取到item_id: 738499396463
2025-08-04 17:29:06,257 - INFO - 商品项 19 商品名称: 德国Dr Beckmann贝克曼博士混洗衣吸色纸防染色串色布
2025-08-04 17:29:06,277 - INFO - 找到商品项 20，检查元素信息...
2025-08-04 17:29:06,287 - INFO - 商品项 20 - 元素标签: div, class: , 有data-autolog: False
2025-08-04 17:29:06,287 - INFO - 商品项 20 当前元素无data-autolog，查找子元素...
2025-08-04 17:29:06,294 - INFO - 商品项 20 找到子元素，class: tfx-item
2025-08-04 17:29:06,296 - INFO - 商品项 20 直接获取到data-autolog: item_id=702821195703&path=search_goods_card&index=19&type=category
2025-08-04 17:29:06,296 - INFO - 商品项 20 data-autolog: item_id=702821195703&path=search_goods_card&index=19&type=category
2025-08-04 17:29:06,296 - INFO - 商品项 20 提取到item_id: 702821195703
2025-08-04 17:29:06,308 - INFO - 商品项 20 商品名称: 韩国TOOSTY牙膏80g，联系vx或旺旺客服领取价格和上架资料
2025-08-04 17:29:06,308 - INFO - 已爬取到 20 个商品，停止当前页面爬取
2025-08-04 17:29:06,308 - INFO - 当前页面爬取完成，找到 20 个商品
2025-08-04 17:29:06,308 - INFO - 第 2 页找到 20 个商品，总计: 60
2025-08-04 17:29:06,308 - INFO - 已达到最大商品数量 60，停止爬取
2025-08-04 17:29:06,308 - INFO - 爬取完成，共找到 60 个商品
2025-08-04 17:29:06,309 - INFO - 成功读取到 60 个商品（目标60个），开始铺货任务
2025-08-04 17:29:06,310 - INFO - 进度状态已保存
2025-08-04 17:29:06,310 - INFO - 第四步：开始处理商品...
2025-08-04 17:29:06,310 - INFO - 已读取 60 个商品，目标成功铺货 3 个商品
2025-08-04 17:29:06,310 - INFO - 将按顺序处理商品
2025-08-04 17:29:06,310 - INFO - 当前批次大小: 12
2025-08-04 17:29:06,311 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=630604349715
2025-08-04 17:29:10,078 - INFO - 页面加载完成
2025-08-04 17:29:10,079 - INFO - 模拟阅读行为，停顿 1.2 秒
2025-08-04 17:29:13,058 - INFO - 开始第 1 次铺货尝试...
2025-08-04 17:29:13,059 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-08-04 17:29:13,060 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-08-04 17:29:13,078 - INFO - span innerHTML: 已铺货
2025-08-04 17:29:13,078 - INFO - 检测到'已铺货'，跳过此商品
2025-08-04 17:29:13,078 - INFO - 第 1 个商品已铺货，跳过（不计入成功数量）
2025-08-04 17:29:13,078 - INFO - 商品已铺货，立即处理下一个商品
2025-08-04 17:29:13,079 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=681787375440
2025-08-04 17:29:16,880 - INFO - 页面加载完成
2025-08-04 17:29:16,880 - INFO - 模拟阅读行为，停顿 1.7 秒
2025-08-04 17:29:19,209 - INFO - 开始第 1 次铺货尝试...
2025-08-04 17:29:19,210 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-08-04 17:29:19,210 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-08-04 17:29:19,216 - INFO - span innerHTML: 已铺货
2025-08-04 17:29:19,216 - INFO - 检测到'已铺货'，跳过此商品
2025-08-04 17:29:19,217 - INFO - 第 2 个商品已铺货，跳过（不计入成功数量）
2025-08-04 17:29:19,217 - INFO - 商品已铺货，立即处理下一个商品
2025-08-04 17:29:19,217 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=821067902127
2025-08-04 17:29:22,320 - INFO - 页面加载完成
2025-08-04 17:29:22,320 - INFO - 模拟阅读行为，停顿 1.8 秒
2025-08-04 17:29:24,105 - INFO - 添加额外延迟: 2.4秒
2025-08-04 17:29:27,992 - INFO - 开始第 1 次铺货尝试...
2025-08-04 17:29:27,992 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-08-04 17:29:27,992 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-08-04 17:29:27,999 - INFO - span innerHTML: 已铺货
2025-08-04 17:29:27,999 - INFO - 检测到'已铺货'，跳过此商品
2025-08-04 17:29:27,999 - INFO - 第 3 个商品已铺货，跳过（不计入成功数量）
2025-08-04 17:29:28,000 - INFO - 商品已铺货，立即处理下一个商品
2025-08-04 17:29:28,000 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=846634805775
2025-08-04 17:29:31,628 - INFO - 页面加载完成
2025-08-04 17:29:31,628 - INFO - 模拟阅读行为，停顿 2.0 秒
2025-08-04 17:29:35,566 - INFO - 开始第 1 次铺货尝试...
2025-08-04 17:29:35,566 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-08-04 17:29:35,566 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-08-04 17:29:35,573 - INFO - span innerHTML: 已铺货
2025-08-04 17:29:35,573 - INFO - 检测到'已铺货'，跳过此商品
2025-08-04 17:29:35,573 - INFO - 第 4 个商品已铺货，跳过（不计入成功数量）
2025-08-04 17:29:35,574 - INFO - 商品已铺货，立即处理下一个商品
2025-08-04 17:29:35,574 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=694650705326
2025-08-04 17:29:39,225 - INFO - 页面加载完成
2025-08-04 17:29:39,226 - INFO - 模拟阅读行为，停顿 2.4 秒
2025-08-04 17:29:42,915 - INFO - 开始第 1 次铺货尝试...
2025-08-04 17:29:42,916 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-08-04 17:29:42,916 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-08-04 17:29:42,922 - INFO - span innerHTML: 已铺货
2025-08-04 17:29:42,923 - INFO - 检测到'已铺货'，跳过此商品
2025-08-04 17:29:42,923 - INFO - 第 5 个商品已铺货，跳过（不计入成功数量）
2025-08-04 17:29:42,923 - INFO - 商品已铺货，立即处理下一个商品
2025-08-04 17:29:42,924 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=927913724907
2025-08-04 17:29:46,611 - INFO - 页面加载完成
2025-08-04 17:29:46,611 - INFO - 模拟阅读行为，停顿 1.5 秒
2025-08-04 17:29:48,156 - INFO - 添加额外延迟: 1.5秒
2025-08-04 17:29:51,149 - INFO - 开始第 1 次铺货尝试...
2025-08-04 17:29:51,149 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-08-04 17:29:51,149 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-08-04 17:29:51,153 - INFO - 检查span元素失败: Message: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span"}
  (Session info: chrome=138.0.7204.184); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff7440fe925+77845]
	GetHandleVerifier [0x0x7ff7440fe980+77936]
	(No symbol) [0x0x7ff743eb9cda]
	(No symbol) [0x0x7ff743f106aa]
	(No symbol) [0x0x7ff743f1095c]
	(No symbol) [0x0x7ff743f63d07]
	(No symbol) [0x0x7ff743f3890f]
	(No symbol) [0x0x7ff743f60b07]
	(No symbol) [0x0x7ff743f386a3]
	(No symbol) [0x0x7ff743f01791]
	(No symbol) [0x0x7ff743f02523]
	GetHandleVerifier [0x0x7ff7443d683d+3059501]
	GetHandleVerifier [0x0x7ff7443d0bfd+3035885]
	GetHandleVerifier [0x0x7ff7443f03f0+3164896]
	GetHandleVerifier [0x0x7ff744118c2e+185118]
	GetHandleVerifier [0x0x7ff74412053f+216111]
	GetHandleVerifier [0x0x7ff7441072d4+113092]
	GetHandleVerifier [0x0x7ff744107489+113529]
	GetHandleVerifier [0x0x7ff7440ee288+10616]
	BaseThreadInitThunk [0x0x7ffc7ac9e8d7+23]
	RtlUserThreadStart [0x0x7ffc7bdbc34c+44]

2025-08-04 17:30:01,337 - WARNING - 等待元素可点击超时: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-08-04 17:30:01,337 - WARNING - 使用配置的XPath未找到添加商品按钮
2025-08-04 17:30:01,337 - ERROR - 未找到铺货按钮
2025-08-04 17:30:01,337 - WARNING - 第 6 个商品处理失败（不计入成功数量）
2025-08-04 17:30:01,338 - INFO - 商品处理失败，立即处理下一个商品
2025-08-04 17:30:01,338 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=644179310984
2025-08-04 17:30:04,428 - INFO - 页面加载完成
2025-08-04 17:30:04,429 - INFO - 模拟阅读行为，停顿 1.8 秒
2025-08-04 17:30:07,536 - INFO - 开始第 1 次铺货尝试...
2025-08-04 17:30:07,536 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-08-04 17:30:07,536 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-08-04 17:30:07,543 - INFO - span innerHTML: 已铺货
2025-08-04 17:30:07,543 - INFO - 检测到'已铺货'，跳过此商品
2025-08-04 17:30:07,543 - INFO - 第 7 个商品已铺货，跳过（不计入成功数量）
2025-08-04 17:30:07,544 - INFO - 商品已铺货，立即处理下一个商品
2025-08-04 17:30:07,544 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=784715750417
2025-08-04 17:30:11,559 - INFO - 页面加载完成
2025-08-04 17:30:11,559 - INFO - 模拟阅读行为，停顿 1.3 秒
2025-08-04 17:30:13,753 - INFO - 开始第 1 次铺货尝试...
2025-08-04 17:30:13,753 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-08-04 17:30:13,753 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-08-04 17:30:13,768 - INFO - span innerHTML: 已铺货
2025-08-04 17:30:13,768 - INFO - 检测到'已铺货'，跳过此商品
2025-08-04 17:30:13,769 - INFO - 第 8 个商品已铺货，跳过（不计入成功数量）
2025-08-04 17:30:13,769 - INFO - 商品已铺货，立即处理下一个商品
2025-08-04 17:30:13,786 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=778440875151
2025-08-04 17:30:16,865 - INFO - 页面加载完成
2025-08-04 17:30:16,865 - INFO - 模拟阅读行为，停顿 2.3 秒
2025-08-04 17:30:20,493 - INFO - 开始第 1 次铺货尝试...
2025-08-04 17:30:20,494 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-08-04 17:30:20,494 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-08-04 17:30:20,498 - INFO - 检查span元素失败: Message: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span"}
  (Session info: chrome=138.0.7204.184); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff7440fe925+77845]
	GetHandleVerifier [0x0x7ff7440fe980+77936]
	(No symbol) [0x0x7ff743eb9cda]
	(No symbol) [0x0x7ff743f106aa]
	(No symbol) [0x0x7ff743f1095c]
	(No symbol) [0x0x7ff743f63d07]
	(No symbol) [0x0x7ff743f3890f]
	(No symbol) [0x0x7ff743f60b07]
	(No symbol) [0x0x7ff743f386a3]
	(No symbol) [0x0x7ff743f01791]
	(No symbol) [0x0x7ff743f02523]
	GetHandleVerifier [0x0x7ff7443d683d+3059501]
	GetHandleVerifier [0x0x7ff7443d0bfd+3035885]
	GetHandleVerifier [0x0x7ff7443f03f0+3164896]
	GetHandleVerifier [0x0x7ff744118c2e+185118]
	GetHandleVerifier [0x0x7ff74412053f+216111]
	GetHandleVerifier [0x0x7ff7441072d4+113092]
	GetHandleVerifier [0x0x7ff744107489+113529]
	GetHandleVerifier [0x0x7ff7440ee288+10616]
	BaseThreadInitThunk [0x0x7ffc7ac9e8d7+23]
	RtlUserThreadStart [0x0x7ffc7bdbc34c+44]

2025-08-04 17:30:30,656 - WARNING - 等待元素可点击超时: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-08-04 17:30:30,656 - WARNING - 使用配置的XPath未找到添加商品按钮
2025-08-04 17:30:30,656 - ERROR - 未找到铺货按钮
2025-08-04 17:30:30,657 - WARNING - 第 9 个商品处理失败（不计入成功数量）
2025-08-04 17:30:30,657 - INFO - 商品处理失败，立即处理下一个商品
2025-08-04 17:30:30,675 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=775343553518
2025-08-04 17:30:34,525 - INFO - 页面加载完成
2025-08-04 17:30:34,525 - INFO - 模拟阅读行为，停顿 2.0 秒
2025-08-04 17:30:37,250 - INFO - 开始第 1 次铺货尝试...
2025-08-04 17:30:37,251 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-08-04 17:30:37,251 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-08-04 17:30:37,259 - INFO - span innerHTML: 立即铺货
2025-08-04 17:30:37,282 - INFO - 找到按钮，文本内容: '立即铺货'
2025-08-04 17:30:37,282 - INFO - 使用配置的XPath找到添加商品按钮
2025-08-04 17:30:37,302 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-08-04 17:30:38,421 - INFO - 尝试ActionChains点击...
2025-08-04 17:30:38,724 - INFO - ActionChains点击成功
2025-08-04 17:30:38,941 - INFO - 铺货按钮点击成功
2025-08-04 17:30:40,966 - INFO - 使用配置的XPath找到确认按钮
2025-08-04 17:30:40,981 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-08-04 17:30:42,223 - INFO - 尝试ActionChains点击...
2025-08-04 17:30:42,510 - INFO - ActionChains点击成功
2025-08-04 17:30:42,830 - INFO - 确认按钮点击成功
2025-08-04 17:30:44,830 - INFO - 验证铺货是否成功，检查span元素状态...
2025-08-04 17:30:44,849 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-08-04 17:30:44,849 - INFO - span仍为'立即铺货'，继续等待...
2025-08-04 17:30:45,860 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-08-04 17:30:45,860 - INFO - span仍为'立即铺货'，继续等待...
2025-08-04 17:30:46,871 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-08-04 17:30:46,871 - INFO - span仍为'立即铺货'，继续等待...
2025-08-04 17:30:47,882 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-08-04 17:30:47,882 - INFO - span仍为'立即铺货'，继续等待...
2025-08-04 17:30:48,898 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-08-04 17:30:48,899 - INFO - span仍为'立即铺货'，继续等待...
2025-08-04 17:30:49,920 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-08-04 17:30:49,921 - INFO - span仍为'立即铺货'，继续等待...
2025-08-04 17:30:50,928 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-08-04 17:30:50,928 - INFO - span仍为'立即铺货'，继续等待...
2025-08-04 17:30:51,951 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-08-04 17:30:51,951 - INFO - span仍为'立即铺货'，继续等待...
2025-08-04 17:30:52,963 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-08-04 17:30:52,963 - INFO - span仍为'立即铺货'，继续等待...
2025-08-04 17:30:53,982 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-08-04 17:30:53,982 - INFO - span仍为'立即铺货'，继续等待...
2025-08-04 17:30:54,982 - INFO - 等待超时，进行最后一次检查...
2025-08-04 17:30:54,998 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-08-04 17:30:54,998 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-08-04 17:30:54,999 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-08-04 17:30:54,999 - INFO - 检测是否出现滑块验证...
2025-08-04 17:30:55,010 - INFO - 在主文档中未找到滑块，检查iframe...
2025-08-04 17:30:55,018 - INFO - 找到 0 个iframe
2025-08-04 17:30:55,018 - INFO - 在所有iframe中都未检测到滑块验证
2025-08-04 17:30:55,018 - INFO - 未检测到滑块验证或处理失败
2025-08-04 17:30:55,019 - WARNING - 第 10 个商品处理失败（不计入成功数量）
2025-08-04 17:30:55,019 - INFO - 商品处理失败，立即处理下一个商品
2025-08-04 17:30:55,019 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=773704801582
2025-08-04 17:30:59,077 - INFO - 页面加载完成
2025-08-04 17:30:59,077 - INFO - 模拟阅读行为，停顿 2.2 秒
2025-08-04 17:31:02,791 - INFO - 开始第 1 次铺货尝试...
2025-08-04 17:31:02,793 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-08-04 17:31:02,793 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-08-04 17:31:02,800 - INFO - span innerHTML: 已铺货
2025-08-04 17:31:02,801 - INFO - 检测到'已铺货'，跳过此商品
2025-08-04 17:31:02,801 - INFO - 第 11 个商品已铺货，跳过（不计入成功数量）
2025-08-04 17:31:02,801 - INFO - 商品已铺货，立即处理下一个商品
2025-08-04 17:31:02,814 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=690522809098
2025-08-04 17:31:06,630 - INFO - 页面加载完成
2025-08-04 17:31:06,631 - INFO - 模拟阅读行为，停顿 1.5 秒
2025-08-04 17:31:09,700 - INFO - 开始第 1 次铺货尝试...
2025-08-04 17:31:09,700 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-08-04 17:31:09,700 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-08-04 17:31:09,707 - INFO - span innerHTML: 立即铺货
2025-08-04 17:31:09,734 - INFO - 找到按钮，文本内容: '立即铺货'
2025-08-04 17:31:09,734 - INFO - 使用配置的XPath找到添加商品按钮
2025-08-04 17:31:09,751 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-08-04 17:31:11,036 - INFO - 尝试ActionChains点击...
2025-08-04 17:31:11,345 - INFO - ActionChains点击成功
2025-08-04 17:31:11,683 - INFO - 铺货按钮点击成功
2025-08-04 17:31:13,705 - INFO - 使用配置的XPath找到确认按钮
2025-08-04 17:31:13,717 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-08-04 17:31:15,069 - INFO - 尝试ActionChains点击...
2025-08-04 17:31:15,341 - INFO - ActionChains点击成功
2025-08-04 17:31:15,817 - INFO - 确认按钮点击成功
2025-08-04 17:31:17,818 - INFO - 验证铺货是否成功，检查span元素状态...
2025-08-04 17:31:17,833 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-08-04 17:31:17,834 - INFO - span仍为'立即铺货'，继续等待...
2025-08-04 17:31:18,845 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-08-04 17:31:18,846 - INFO - span仍为'立即铺货'，继续等待...
2025-08-04 17:31:19,861 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-08-04 17:31:19,861 - INFO - span仍为'立即铺货'，继续等待...
2025-08-04 17:31:20,878 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-08-04 17:31:20,878 - INFO - span仍为'立即铺货'，继续等待...
2025-08-04 17:31:21,893 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-08-04 17:31:21,893 - INFO - span仍为'立即铺货'，继续等待...
2025-08-04 17:31:22,911 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-08-04 17:31:22,911 - INFO - span仍为'立即铺货'，继续等待...
2025-08-04 17:31:23,921 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-08-04 17:31:23,921 - INFO - span仍为'立即铺货'，继续等待...
2025-08-04 17:31:24,940 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-08-04 17:31:24,941 - INFO - span仍为'立即铺货'，继续等待...
2025-08-04 17:31:25,950 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-08-04 17:31:25,950 - INFO - span仍为'立即铺货'，继续等待...
2025-08-04 17:31:26,959 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-08-04 17:31:26,959 - INFO - span仍为'立即铺货'，继续等待...
2025-08-04 17:31:27,959 - INFO - 等待超时，进行最后一次检查...
2025-08-04 17:31:27,978 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-08-04 17:31:27,979 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-08-04 17:31:27,979 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-08-04 17:31:27,979 - INFO - 检测是否出现滑块验证...
2025-08-04 17:31:27,991 - INFO - 在主文档中未找到滑块，检查iframe...
2025-08-04 17:31:27,996 - INFO - 找到 0 个iframe
2025-08-04 17:31:27,996 - INFO - 在所有iframe中都未检测到滑块验证
2025-08-04 17:31:27,996 - INFO - 未检测到滑块验证或处理失败
2025-08-04 17:31:27,997 - WARNING - 第 12 个商品处理失败（不计入成功数量）
2025-08-04 17:31:27,997 - INFO - 商品处理失败，立即处理下一个商品
2025-08-04 17:31:27,998 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=698669180031
2025-08-04 17:31:31,764 - INFO - 页面加载完成
2025-08-04 17:31:31,764 - INFO - 模拟阅读行为，停顿 2.0 秒
2025-08-04 17:31:34,463 - INFO - 添加额外延迟: 2.4秒
2025-08-04 17:31:37,850 - INFO - 开始第 1 次铺货尝试...
2025-08-04 17:31:37,851 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-08-04 17:31:37,851 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-08-04 17:31:37,857 - INFO - span innerHTML: 已铺货
2025-08-04 17:31:37,858 - INFO - 检测到'已铺货'，跳过此商品
2025-08-04 17:31:37,858 - INFO - 第 13 个商品已铺货，跳过（不计入成功数量）
2025-08-04 17:31:37,858 - INFO - 商品已铺货，立即处理下一个商品
2025-08-04 17:31:37,870 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=710114820531
2025-08-04 17:31:41,482 - INFO - 页面加载完成
2025-08-04 17:31:41,482 - INFO - 模拟阅读行为，停顿 2.5 秒
2025-08-04 17:31:46,448 - INFO - 开始第 1 次铺货尝试...
2025-08-04 17:31:46,448 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-08-04 17:31:46,449 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-08-04 17:31:46,452 - INFO - 检查span元素失败: Message: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span"}
  (Session info: chrome=138.0.7204.184); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff7440fe925+77845]
	GetHandleVerifier [0x0x7ff7440fe980+77936]
	(No symbol) [0x0x7ff743eb9cda]
	(No symbol) [0x0x7ff743f106aa]
	(No symbol) [0x0x7ff743f1095c]
	(No symbol) [0x0x7ff743f63d07]
	(No symbol) [0x0x7ff743f3890f]
	(No symbol) [0x0x7ff743f60b07]
	(No symbol) [0x0x7ff743f386a3]
	(No symbol) [0x0x7ff743f01791]
	(No symbol) [0x0x7ff743f02523]
	GetHandleVerifier [0x0x7ff7443d683d+3059501]
	GetHandleVerifier [0x0x7ff7443d0bfd+3035885]
	GetHandleVerifier [0x0x7ff7443f03f0+3164896]
	GetHandleVerifier [0x0x7ff744118c2e+185118]
	GetHandleVerifier [0x0x7ff74412053f+216111]
	GetHandleVerifier [0x0x7ff7441072d4+113092]
	GetHandleVerifier [0x0x7ff744107489+113529]
	GetHandleVerifier [0x0x7ff7440ee288+10616]
	BaseThreadInitThunk [0x0x7ffc7ac9e8d7+23]
	RtlUserThreadStart [0x0x7ffc7bdbc34c+44]

2025-08-04 17:31:56,626 - WARNING - 等待元素可点击超时: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-08-04 17:31:56,626 - WARNING - 使用配置的XPath未找到添加商品按钮
2025-08-04 17:31:56,627 - ERROR - 未找到铺货按钮
2025-08-04 17:31:56,627 - WARNING - 第 14 个商品处理失败（不计入成功数量）
2025-08-04 17:31:56,627 - INFO - 商品处理失败，立即处理下一个商品
2025-08-04 17:31:56,649 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=642497736652
2025-08-04 17:32:00,623 - INFO - 页面加载完成
2025-08-04 17:32:00,624 - INFO - 模拟阅读行为，停顿 2.4 秒
2025-08-04 17:32:04,190 - INFO - 开始第 1 次铺货尝试...
2025-08-04 17:32:04,190 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-08-04 17:32:04,190 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-08-04 17:32:04,197 - INFO - span innerHTML: 已铺货
2025-08-04 17:32:04,197 - INFO - 检测到'已铺货'，跳过此商品
2025-08-04 17:32:04,197 - INFO - 第 15 个商品已铺货，跳过（不计入成功数量）
2025-08-04 17:32:04,198 - INFO - 商品已铺货，立即处理下一个商品
2025-08-04 17:32:04,209 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=743986957239
2025-08-04 17:32:07,794 - INFO - 页面加载完成
2025-08-04 17:32:07,794 - INFO - 模拟阅读行为，停顿 2.5 秒
2025-08-04 17:32:10,336 - INFO - 添加额外延迟: 2.2秒
2025-08-04 17:32:13,566 - INFO - 开始第 1 次铺货尝试...
2025-08-04 17:32:13,566 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-08-04 17:32:13,566 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-08-04 17:32:13,572 - INFO - span innerHTML: 已铺货
2025-08-04 17:32:13,573 - INFO - 检测到'已铺货'，跳过此商品
2025-08-04 17:32:13,573 - INFO - 第 16 个商品已铺货，跳过（不计入成功数量）
2025-08-04 17:32:13,573 - INFO - 商品已铺货，立即处理下一个商品
2025-08-04 17:32:13,573 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=703823332399
2025-08-04 17:32:17,491 - INFO - 页面加载完成
2025-08-04 17:32:17,491 - INFO - 模拟阅读行为，停顿 2.3 秒
2025-08-04 17:32:20,407 - INFO - 开始第 1 次铺货尝试...
2025-08-04 17:32:20,407 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-08-04 17:32:20,407 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-08-04 17:32:20,414 - INFO - span innerHTML: 已铺货
2025-08-04 17:32:20,415 - INFO - 检测到'已铺货'，跳过此商品
2025-08-04 17:32:20,415 - INFO - 第 17 个商品已铺货，跳过（不计入成功数量）
2025-08-04 17:32:20,415 - INFO - 商品已铺货，立即处理下一个商品
2025-08-04 17:32:20,416 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=668810723647
2025-08-04 17:32:24,246 - INFO - 页面加载完成
2025-08-04 17:32:24,247 - INFO - 模拟阅读行为，停顿 2.4 秒
2025-08-04 17:32:27,642 - INFO - 添加额外延迟: 2.6秒
2025-08-04 17:32:31,296 - INFO - 开始第 1 次铺货尝试...
2025-08-04 17:32:31,297 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-08-04 17:32:31,297 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-08-04 17:32:31,304 - INFO - span innerHTML: 立即铺货
2025-08-04 17:32:31,328 - INFO - 找到按钮，文本内容: '立即铺货'
2025-08-04 17:32:31,328 - INFO - 使用配置的XPath找到添加商品按钮
2025-08-04 17:32:31,343 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-08-04 17:32:32,487 - INFO - 尝试ActionChains点击...
2025-08-04 17:32:32,787 - INFO - ActionChains点击成功
2025-08-04 17:32:33,255 - INFO - 铺货按钮点击成功
2025-08-04 17:32:35,277 - INFO - 使用配置的XPath找到确认按钮
2025-08-04 17:32:35,290 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-08-04 17:32:36,575 - INFO - 尝试ActionChains点击...
2025-08-04 17:32:36,854 - INFO - ActionChains点击成功
2025-08-04 17:32:37,272 - INFO - 确认按钮点击成功
2025-08-04 17:32:39,272 - INFO - 验证铺货是否成功，检查span元素状态...
2025-08-04 17:32:39,280 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-08-04 17:32:39,280 - INFO - span仍为'立即铺货'，继续等待...
2025-08-04 17:32:40,293 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-08-04 17:32:40,293 - INFO - span仍为'立即铺货'，继续等待...
2025-08-04 17:32:41,304 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-08-04 17:32:41,304 - INFO - span仍为'立即铺货'，继续等待...
2025-08-04 17:32:42,317 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-08-04 17:32:42,317 - INFO - span仍为'立即铺货'，继续等待...
2025-08-04 17:32:43,335 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-08-04 17:32:43,335 - INFO - span仍为'立即铺货'，继续等待...
2025-08-04 17:32:44,349 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-08-04 17:32:44,350 - INFO - span仍为'立即铺货'，继续等待...
2025-08-04 17:32:45,360 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-08-04 17:32:45,361 - INFO - span仍为'立即铺货'，继续等待...
2025-08-04 17:32:46,368 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-08-04 17:32:46,369 - INFO - span仍为'立即铺货'，继续等待...
2025-08-04 17:32:47,383 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-08-04 17:32:47,383 - INFO - span仍为'立即铺货'，继续等待...
2025-08-04 17:32:48,405 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-08-04 17:32:48,405 - INFO - span仍为'立即铺货'，继续等待...
2025-08-04 17:32:49,406 - INFO - 等待超时，进行最后一次检查...
2025-08-04 17:32:49,423 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-08-04 17:32:49,424 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-08-04 17:32:49,424 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-08-04 17:32:49,424 - INFO - 检测是否出现滑块验证...
2025-08-04 17:32:49,433 - INFO - 在主文档中未找到滑块，检查iframe...
2025-08-04 17:32:49,438 - INFO - 找到 0 个iframe
2025-08-04 17:32:49,439 - INFO - 在所有iframe中都未检测到滑块验证
2025-08-04 17:32:49,439 - INFO - 未检测到滑块验证或处理失败
2025-08-04 17:32:49,439 - WARNING - 第 18 个商品处理失败（不计入成功数量）
2025-08-04 17:32:49,439 - INFO - 商品处理失败，立即处理下一个商品
2025-08-04 17:32:49,453 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=742357678329
2025-08-04 17:32:53,169 - INFO - 页面加载完成
2025-08-04 17:32:53,169 - INFO - 模拟阅读行为，停顿 2.8 秒
2025-08-04 17:32:57,153 - INFO - 添加额外延迟: 1.6秒
2025-08-04 17:32:59,867 - INFO - 开始第 1 次铺货尝试...
2025-08-04 17:32:59,867 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-08-04 17:32:59,867 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-08-04 17:32:59,874 - INFO - span innerHTML: 已铺货
2025-08-04 17:32:59,874 - INFO - 检测到'已铺货'，跳过此商品
2025-08-04 17:32:59,875 - INFO - 第 19 个商品已铺货，跳过（不计入成功数量）
2025-08-04 17:32:59,875 - INFO - 商品已铺货，立即处理下一个商品
2025-08-04 17:32:59,888 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=679559792422
2025-08-04 17:33:03,556 - INFO - 页面加载完成
2025-08-04 17:33:03,556 - INFO - 模拟阅读行为，停顿 1.7 秒
2025-08-04 17:33:05,943 - INFO - 开始第 1 次铺货尝试...
2025-08-04 17:33:05,943 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-08-04 17:33:05,944 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-08-04 17:33:05,947 - INFO - 检查span元素失败: Message: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span"}
  (Session info: chrome=138.0.7204.184); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff7440fe925+77845]
	GetHandleVerifier [0x0x7ff7440fe980+77936]
	(No symbol) [0x0x7ff743eb9cda]
	(No symbol) [0x0x7ff743f106aa]
	(No symbol) [0x0x7ff743f1095c]
	(No symbol) [0x0x7ff743f63d07]
	(No symbol) [0x0x7ff743f3890f]
	(No symbol) [0x0x7ff743f60b07]
	(No symbol) [0x0x7ff743f386a3]
	(No symbol) [0x0x7ff743f01791]
	(No symbol) [0x0x7ff743f02523]
	GetHandleVerifier [0x0x7ff7443d683d+3059501]
	GetHandleVerifier [0x0x7ff7443d0bfd+3035885]
	GetHandleVerifier [0x0x7ff7443f03f0+3164896]
	GetHandleVerifier [0x0x7ff744118c2e+185118]
	GetHandleVerifier [0x0x7ff74412053f+216111]
	GetHandleVerifier [0x0x7ff7441072d4+113092]
	GetHandleVerifier [0x0x7ff744107489+113529]
	GetHandleVerifier [0x0x7ff7440ee288+10616]
	BaseThreadInitThunk [0x0x7ffc7ac9e8d7+23]
	RtlUserThreadStart [0x0x7ffc7bdbc34c+44]

2025-08-04 17:33:16,120 - WARNING - 等待元素可点击超时: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-08-04 17:33:16,120 - WARNING - 使用配置的XPath未找到添加商品按钮
2025-08-04 17:33:16,121 - ERROR - 未找到铺货按钮
2025-08-04 17:33:16,121 - WARNING - 第 20 个商品处理失败（不计入成功数量）
2025-08-04 17:33:16,122 - INFO - 商品处理失败，立即处理下一个商品
2025-08-04 17:33:16,137 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=755157258216
2025-08-04 17:33:19,971 - INFO - 页面加载完成
2025-08-04 17:33:19,972 - INFO - 模拟阅读行为，停顿 2.9 秒
2025-08-04 17:33:23,963 - INFO - 开始第 1 次铺货尝试...
2025-08-04 17:33:23,963 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-08-04 17:33:23,963 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-08-04 17:33:23,971 - INFO - span innerHTML: 已铺货
2025-08-04 17:33:23,971 - INFO - 检测到'已铺货'，跳过此商品
2025-08-04 17:33:23,971 - INFO - 第 21 个商品已铺货，跳过（不计入成功数量）
2025-08-04 17:33:23,971 - INFO - 商品已铺货，立即处理下一个商品
2025-08-04 17:33:23,986 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=762133318080
2025-08-04 17:33:28,111 - INFO - 页面加载完成
2025-08-04 17:33:28,112 - INFO - 模拟阅读行为，停顿 2.0 秒
2025-08-04 17:33:32,883 - INFO - 开始第 1 次铺货尝试...
2025-08-04 17:33:32,883 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-08-04 17:33:32,883 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-08-04 17:33:32,891 - INFO - span innerHTML: 已铺货
2025-08-04 17:33:32,891 - INFO - 检测到'已铺货'，跳过此商品
2025-08-04 17:33:32,891 - INFO - 第 22 个商品已铺货，跳过（不计入成功数量）
2025-08-04 17:33:32,893 - INFO - 商品已铺货，立即处理下一个商品
2025-08-04 17:33:32,905 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=759842528968
2025-08-04 17:33:36,478 - INFO - 页面加载完成
2025-08-04 17:33:36,478 - INFO - 模拟阅读行为，停顿 2.2 秒
2025-08-04 17:33:40,141 - INFO - 开始第 1 次铺货尝试...
2025-08-04 17:33:40,141 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-08-04 17:33:40,142 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-08-04 17:33:40,147 - INFO - span innerHTML: 已铺货
2025-08-04 17:33:40,147 - INFO - 检测到'已铺货'，跳过此商品
2025-08-04 17:33:40,147 - INFO - 第 23 个商品已铺货，跳过（不计入成功数量）
2025-08-04 17:33:40,148 - INFO - 商品已铺货，立即处理下一个商品
2025-08-04 17:33:40,160 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=740410737413
2025-08-04 17:33:44,096 - INFO - 页面加载完成
2025-08-04 17:33:44,096 - INFO - 模拟阅读行为，停顿 1.2 秒
2025-08-04 17:33:46,432 - INFO - 开始第 1 次铺货尝试...
2025-08-04 17:33:46,432 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-08-04 17:33:46,432 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-08-04 17:33:46,439 - INFO - span innerHTML: 已铺货
2025-08-04 17:33:46,439 - INFO - 检测到'已铺货'，跳过此商品
2025-08-04 17:33:46,439 - INFO - 第 24 个商品已铺货，跳过（不计入成功数量）
2025-08-04 17:33:46,439 - INFO - 商品已铺货，立即处理下一个商品
2025-08-04 17:33:46,454 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=778744140129
2025-08-04 17:33:50,121 - INFO - 页面加载完成
2025-08-04 17:33:50,121 - INFO - 模拟阅读行为，停顿 2.4 秒
2025-08-04 17:33:54,471 - INFO - 开始第 1 次铺货尝试...
2025-08-04 17:33:54,471 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-08-04 17:33:54,471 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-08-04 17:33:54,475 - INFO - 检查span元素失败: Message: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span"}
  (Session info: chrome=138.0.7204.184); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff7440fe925+77845]
	GetHandleVerifier [0x0x7ff7440fe980+77936]
	(No symbol) [0x0x7ff743eb9cda]
	(No symbol) [0x0x7ff743f106aa]
	(No symbol) [0x0x7ff743f1095c]
	(No symbol) [0x0x7ff743f63d07]
	(No symbol) [0x0x7ff743f3890f]
	(No symbol) [0x0x7ff743f60b07]
	(No symbol) [0x0x7ff743f386a3]
	(No symbol) [0x0x7ff743f01791]
	(No symbol) [0x0x7ff743f02523]
	GetHandleVerifier [0x0x7ff7443d683d+3059501]
	GetHandleVerifier [0x0x7ff7443d0bfd+3035885]
	GetHandleVerifier [0x0x7ff7443f03f0+3164896]
	GetHandleVerifier [0x0x7ff744118c2e+185118]
	GetHandleVerifier [0x0x7ff74412053f+216111]
	GetHandleVerifier [0x0x7ff7441072d4+113092]
	GetHandleVerifier [0x0x7ff744107489+113529]
	GetHandleVerifier [0x0x7ff7440ee288+10616]
	BaseThreadInitThunk [0x0x7ffc7ac9e8d7+23]
	RtlUserThreadStart [0x0x7ffc7bdbc34c+44]

2025-08-04 17:34:04,645 - WARNING - 等待元素可点击超时: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-08-04 17:34:04,646 - WARNING - 使用配置的XPath未找到添加商品按钮
2025-08-04 17:34:04,646 - ERROR - 未找到铺货按钮
2025-08-04 17:34:04,647 - WARNING - 第 25 个商品处理失败（不计入成功数量）
2025-08-04 17:34:04,648 - INFO - 商品处理失败，立即处理下一个商品
2025-08-04 17:34:04,664 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=680534455265
2025-08-04 17:34:08,417 - INFO - 页面加载完成
2025-08-04 17:34:08,417 - INFO - 模拟阅读行为，停顿 2.4 秒
2025-08-04 17:34:13,059 - INFO - 开始第 1 次铺货尝试...
2025-08-04 17:34:13,059 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-08-04 17:34:13,059 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-08-04 17:34:13,069 - INFO - span innerHTML: 已铺货
2025-08-04 17:34:13,069 - INFO - 检测到'已铺货'，跳过此商品
2025-08-04 17:34:13,069 - INFO - 第 26 个商品已铺货，跳过（不计入成功数量）
2025-08-04 17:34:13,070 - INFO - 商品已铺货，立即处理下一个商品
2025-08-04 17:34:13,084 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=796223606931
2025-08-04 17:34:16,675 - INFO - 页面加载完成
2025-08-04 17:34:16,675 - INFO - 模拟阅读行为，停顿 2.8 秒
2025-08-04 17:34:20,303 - INFO - 开始第 1 次铺货尝试...
2025-08-04 17:34:20,304 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-08-04 17:34:20,304 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-08-04 17:34:20,309 - INFO - span innerHTML: 暂未授权
2025-08-04 17:34:30,770 - WARNING - 等待元素可点击超时: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-08-04 17:34:30,770 - WARNING - 使用配置的XPath未找到添加商品按钮
2025-08-04 17:34:30,770 - ERROR - 未找到铺货按钮
2025-08-04 17:34:30,770 - WARNING - 第 27 个商品处理失败（不计入成功数量）
2025-08-04 17:34:30,771 - INFO - 商品处理失败，立即处理下一个商品
2025-08-04 17:34:30,771 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=805523184209
2025-08-04 17:34:34,491 - INFO - 页面加载完成
2025-08-04 17:34:34,491 - INFO - 模拟阅读行为，停顿 2.0 秒
2025-08-04 17:34:36,457 - INFO - 添加额外延迟: 1.6秒
2025-08-04 17:34:39,522 - INFO - 开始第 1 次铺货尝试...
2025-08-04 17:34:39,522 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-08-04 17:34:39,522 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-08-04 17:34:39,529 - INFO - span innerHTML: 已铺货
2025-08-04 17:34:39,529 - INFO - 检测到'已铺货'，跳过此商品
2025-08-04 17:34:39,529 - INFO - 第 28 个商品已铺货，跳过（不计入成功数量）
2025-08-04 17:34:39,530 - INFO - 商品已铺货，立即处理下一个商品
2025-08-04 17:34:39,542 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=679995147317
2025-08-04 17:34:43,261 - INFO - 页面加载完成
2025-08-04 17:34:43,261 - INFO - 模拟阅读行为，停顿 2.4 秒
2025-08-04 17:34:47,799 - INFO - 开始第 1 次铺货尝试...
2025-08-04 17:34:47,799 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-08-04 17:34:47,799 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-08-04 17:34:47,806 - INFO - span innerHTML: 已铺货
2025-08-04 17:34:47,806 - INFO - 检测到'已铺货'，跳过此商品
2025-08-04 17:34:47,807 - INFO - 第 29 个商品已铺货，跳过（不计入成功数量）
2025-08-04 17:34:47,807 - INFO - 商品已铺货，立即处理下一个商品
2025-08-04 17:34:47,807 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=751408783831
2025-08-04 17:34:51,526 - INFO - 页面加载完成
2025-08-04 17:34:51,527 - INFO - 模拟阅读行为，停顿 2.8 秒
2025-08-04 17:34:54,982 - INFO - 开始第 1 次铺货尝试...
2025-08-04 17:34:54,982 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-08-04 17:34:54,982 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-08-04 17:34:54,987 - INFO - span innerHTML: 已铺货
2025-08-04 17:34:54,989 - INFO - 检测到'已铺货'，跳过此商品
2025-08-04 17:34:54,989 - INFO - 第 30 个商品已铺货，跳过（不计入成功数量）
2025-08-04 17:34:54,989 - INFO - 商品已铺货，立即处理下一个商品
2025-08-04 17:34:55,001 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=651574474225
2025-08-04 17:34:58,794 - INFO - 页面加载完成
2025-08-04 17:34:58,794 - INFO - 模拟阅读行为，停顿 2.9 秒
2025-08-04 17:35:02,943 - INFO - 开始第 1 次铺货尝试...
2025-08-04 17:35:02,943 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-08-04 17:35:02,943 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-08-04 17:35:02,949 - INFO - span innerHTML: 已铺货
2025-08-04 17:35:02,949 - INFO - 检测到'已铺货'，跳过此商品
2025-08-04 17:35:02,949 - INFO - 第 31 个商品已铺货，跳过（不计入成功数量）
2025-08-04 17:35:02,950 - INFO - 商品已铺货，立即处理下一个商品
2025-08-04 17:35:02,961 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=706488285938
2025-08-04 17:35:06,676 - INFO - 页面加载完成
2025-08-04 17:35:06,677 - INFO - 模拟阅读行为，停顿 1.7 秒
2025-08-04 17:35:09,491 - INFO - 添加额外延迟: 1.6秒
2025-08-04 17:35:12,069 - INFO - 开始第 1 次铺货尝试...
2025-08-04 17:35:12,070 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-08-04 17:35:12,070 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-08-04 17:35:12,077 - INFO - span innerHTML: 已铺货
2025-08-04 17:35:12,077 - INFO - 检测到'已铺货'，跳过此商品
2025-08-04 17:35:12,077 - INFO - 第 32 个商品已铺货，跳过（不计入成功数量）
2025-08-04 17:35:12,077 - INFO - 商品已铺货，立即处理下一个商品
2025-08-04 17:35:12,091 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=739782656534
2025-08-04 17:35:15,790 - INFO - 页面加载完成
2025-08-04 17:35:15,790 - INFO - 模拟阅读行为，停顿 2.2 秒
2025-08-04 17:35:20,819 - INFO - 开始第 1 次铺货尝试...
2025-08-04 17:35:20,820 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-08-04 17:35:20,820 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-08-04 17:35:20,825 - INFO - span innerHTML: 已铺货
2025-08-04 17:35:20,825 - INFO - 检测到'已铺货'，跳过此商品
2025-08-04 17:35:20,826 - INFO - 第 33 个商品已铺货，跳过（不计入成功数量）
2025-08-04 17:35:20,826 - INFO - 商品已铺货，立即处理下一个商品
2025-08-04 17:35:20,839 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=686904390887
2025-08-04 17:35:24,938 - INFO - 页面加载完成
2025-08-04 17:35:24,939 - INFO - 模拟阅读行为，停顿 1.7 秒
2025-08-04 17:35:27,334 - INFO - 开始第 1 次铺货尝试...
2025-08-04 17:35:27,334 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-08-04 17:35:27,334 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-08-04 17:35:27,341 - INFO - span innerHTML: 已铺货
2025-08-04 17:35:27,341 - INFO - 检测到'已铺货'，跳过此商品
2025-08-04 17:35:27,343 - INFO - 第 34 个商品已铺货，跳过（不计入成功数量）
2025-08-04 17:35:27,343 - INFO - 商品已铺货，立即处理下一个商品
2025-08-04 17:35:27,343 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=822594991780
2025-08-04 17:35:30,914 - INFO - 页面加载完成
2025-08-04 17:35:30,914 - INFO - 模拟阅读行为，停顿 2.8 秒
2025-08-04 17:35:35,298 - INFO - 开始第 1 次铺货尝试...
2025-08-04 17:35:35,298 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-08-04 17:35:35,298 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-08-04 17:35:35,302 - INFO - 检查span元素失败: Message: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span"}
  (Session info: chrome=138.0.7204.184); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff7440fe925+77845]
	GetHandleVerifier [0x0x7ff7440fe980+77936]
	(No symbol) [0x0x7ff743eb9cda]
	(No symbol) [0x0x7ff743f106aa]
	(No symbol) [0x0x7ff743f1095c]
	(No symbol) [0x0x7ff743f63d07]
	(No symbol) [0x0x7ff743f3890f]
	(No symbol) [0x0x7ff743f60b07]
	(No symbol) [0x0x7ff743f386a3]
	(No symbol) [0x0x7ff743f01791]
	(No symbol) [0x0x7ff743f02523]
	GetHandleVerifier [0x0x7ff7443d683d+3059501]
	GetHandleVerifier [0x0x7ff7443d0bfd+3035885]
	GetHandleVerifier [0x0x7ff7443f03f0+3164896]
	GetHandleVerifier [0x0x7ff744118c2e+185118]
	GetHandleVerifier [0x0x7ff74412053f+216111]
	GetHandleVerifier [0x0x7ff7441072d4+113092]
	GetHandleVerifier [0x0x7ff744107489+113529]
	GetHandleVerifier [0x0x7ff7440ee288+10616]
	BaseThreadInitThunk [0x0x7ffc7ac9e8d7+23]
	RtlUserThreadStart [0x0x7ffc7bdbc34c+44]

2025-08-04 17:35:44,826 - INFO - Cookies已保存
2025-08-04 17:35:44,945 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))': /session/75f37ce3b515a2f35df9664aeef79617
