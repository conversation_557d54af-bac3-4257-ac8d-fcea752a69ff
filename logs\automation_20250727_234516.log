2025-07-27 23:45:20,057 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169cda]
	(No symbol) [0x0x7ff7cd171679]
	(No symbol) [0x0x7ff7cd174a61]
	(No symbol) [0x0x7ff7cd211e4b]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:45:20,057 - INFO - 防检测机制设置完成
2025-07-27 23:45:20,057 - INFO - 浏览器驱动初始化成功
2025-07-27 23:45:20,063 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:45:20,066 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:45:20,069 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:45:20,075 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:45:20,081 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:45:20,083 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:45:20,091 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:45:20,095 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:45:20,098 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:45:20,101 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:45:20,105 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:45:20,109 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:45:20,112 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:45:20,114 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:45:20,119 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:45:20,123 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:45:20,126 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:45:20,133 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:45:20,137 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:45:20,141 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:45:20,142 - INFO - Cookies已加载
2025-07-27 23:45:20,142 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?channel=global_zx&spm=a21ug5.29159167.6452766900.2ndview_zxmore_click
2025-07-27 23:45:24,757 - INFO - 当前User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-27 23:45:24,758 - INFO - 确认：使用桌面版User-Agent
2025-07-27 23:45:24,761 - INFO - 桌面版User-Agent设置完成: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-27 23:45:26,606 - WARNING - 随机鼠标移动失败: Message: move target out of bounds
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd21be53]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:45:26,609 - INFO - 成功导航到目标页面
2025-07-27 23:45:26,609 - INFO - 登录成功！现在可以开始铺货任务
2025-07-27 23:45:29,070 - INFO - Cookies已保存
2025-07-27 23:45:31,331 - INFO - 浏览器已关闭
2025-07-27 23:45:31,331 - INFO - 以无界面(headless)模式启动Chrome，仅添加headless、no-sandbox、disable-dev-shm-usage参数
2025-07-27 23:45:32,679 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169cda]
	(No symbol) [0x0x7ff7cd171679]
	(No symbol) [0x0x7ff7cd174a61]
	(No symbol) [0x0x7ff7cd211e4b]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:45:32,679 - INFO - 防检测机制设置完成
2025-07-27 23:45:32,680 - INFO - 浏览器驱动初始化成功
2025-07-27 23:45:32,680 - INFO - 开始自动化铺货流程（无头模式）
2025-07-27 23:45:32,680 - INFO - 第一步：正在导航到目标页面...
2025-07-27 23:45:36,538 - INFO - 第一步：点击登录按钮
2025-07-27 23:45:36,566 - INFO - 找到登录按钮，使用选择器: //*[@id='login-form']/div[6]/button
2025-07-27 23:45:37,589 - INFO - 准备点击登录按钮: 标签=button, 文本='登录', 选择器=//*[@id='login-form']/div[6]/button
2025-07-27 23:45:37,605 - INFO - 准备点击元素: tag=button, text='登录', class='fm-button fm-submit password-l', id=''
2025-07-27 23:45:38,815 - INFO - 尝试ActionChains点击...
2025-07-27 23:45:39,267 - INFO - ActionChains点击成功
2025-07-27 23:45:39,652 - INFO - 登录按钮点击成功
2025-07-27 23:45:39,652 - INFO - 登录按钮点击成功
2025-07-27 23:45:42,653 - INFO - 第二步：获取账号信息...
2025-07-27 23:45:42,653 - INFO - 正在跳转到个人中心页面: https://jingya.taobao.com/?v=2
2025-07-27 23:46:07,357 - INFO - 尝试获取账号名称，使用XPath: //*[@id='workbench-user-tool-btn']/div/div[2]
2025-07-27 23:46:07,397 - INFO - 成功获取账号信息: tb997603987291:书生
2025-07-27 23:46:07,398 - INFO - 获取到账号名称: tb997603987291:书生
2025-07-27 23:46:07,398 - INFO - 第三步：开始爬取商品信息...
2025-07-27 23:46:07,399 - INFO - GUI界面已更新账号显示: tb997603987291:书生
2025-07-27 23:46:07,400 - INFO - 开始爬取商品信息，最大数量: 55
2025-07-27 23:46:11,582 - INFO - 正在爬取第 1 页商品信息...
2025-07-27 23:46:13,591 - INFO - 找到商品容器，开始爬取商品，最大数量: 55
2025-07-27 23:46:13,605 - INFO - 找到商品项 1，检查元素信息...
2025-07-27 23:46:13,623 - INFO - 商品项 1 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:13,627 - INFO - 商品项 1 直接获取到data-autolog: item_id=681778175573&path=search_goods_card&type=search
2025-07-27 23:46:13,627 - INFO - 商品项 1 data-autolog: item_id=681778175573&path=search_goods_card&type=search
2025-07-27 23:46:13,628 - INFO - 商品项 1 提取到item_id: 681778175573
2025-07-27 23:46:13,640 - INFO - 商品项 1 商品名称: 保税直供 Dove多芬磨砂膏石榴籽乳木果风味身体磨砂膏298g
2025-07-27 23:46:13,655 - INFO - 找到商品项 2，检查元素信息...
2025-07-27 23:46:13,665 - INFO - 商品项 2 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:13,670 - INFO - 商品项 2 直接获取到data-autolog: item_id=690841537159&path=search_goods_card&index=1&type=search
2025-07-27 23:46:13,670 - INFO - 商品项 2 data-autolog: item_id=690841537159&path=search_goods_card&index=1&type=search
2025-07-27 23:46:13,670 - INFO - 商品项 2 提取到item_id: 690841537159
2025-07-27 23:46:13,752 - INFO - 商品项 2 商品名称: 保税直供 凡士林Vaseline烟酰胺身体乳400ml 725ml无压泵款200ml
2025-07-27 23:46:13,776 - INFO - 找到商品项 3，检查元素信息...
2025-07-27 23:46:13,787 - INFO - 商品项 3 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:13,790 - INFO - 商品项 3 直接获取到data-autolog: item_id=789447097664&path=search_goods_card&index=2&type=search
2025-07-27 23:46:13,790 - INFO - 商品项 3 data-autolog: item_id=789447097664&path=search_goods_card&index=2&type=search
2025-07-27 23:46:13,790 - INFO - 商品项 3 提取到item_id: 789447097664
2025-07-27 23:46:13,799 - INFO - 商品项 3 商品名称: 【甄选】珂润Curel泡沫洗面奶150ml
2025-07-27 23:46:13,811 - INFO - 找到商品项 4，检查元素信息...
2025-07-27 23:46:13,821 - INFO - 商品项 4 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:13,824 - INFO - 商品项 4 直接获取到data-autolog: item_id=694682843417&path=search_goods_card&index=3&type=search
2025-07-27 23:46:13,824 - INFO - 商品项 4 data-autolog: item_id=694682843417&path=search_goods_card&index=3&type=search
2025-07-27 23:46:13,824 - INFO - 商品项 4 提取到item_id: 694682843417
2025-07-27 23:46:13,836 - INFO - 商品项 4 商品名称: 【可开授权】日台混发Fino发膜美容液护发素 发膜230g
2025-07-27 23:46:13,846 - INFO - 找到商品项 5，检查元素信息...
2025-07-27 23:46:13,856 - INFO - 商品项 5 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:13,861 - INFO - 商品项 5 直接获取到data-autolog: item_id=681082809892&path=search_goods_card&index=4&type=search
2025-07-27 23:46:13,862 - INFO - 商品项 5 data-autolog: item_id=681082809892&path=search_goods_card&index=4&type=search
2025-07-27 23:46:13,862 - INFO - 商品项 5 提取到item_id: 681082809892
2025-07-27 23:46:13,879 - INFO - 商品项 5 商品名称: 保税直供 Dove多芬大白碗润肤身体乳300ml 【1//2//3罐】
2025-07-27 23:46:13,890 - INFO - 找到商品项 6，检查元素信息...
2025-07-27 23:46:13,899 - INFO - 商品项 6 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:13,902 - INFO - 商品项 6 直接获取到data-autolog: item_id=694675776881&path=search_goods_card&index=5&type=search
2025-07-27 23:46:13,903 - INFO - 商品项 6 data-autolog: item_id=694675776881&path=search_goods_card&index=5&type=search
2025-07-27 23:46:13,903 - INFO - 商品项 6 提取到item_id: 694675776881
2025-07-27 23:46:13,911 - INFO - 商品项 6 商品名称: 保税直供 Pantene潘婷护发素三分钟奇迹多效修护发膜级护发素
2025-07-27 23:46:13,923 - INFO - 找到商品项 7，检查元素信息...
2025-07-27 23:46:13,931 - INFO - 商品项 7 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:13,934 - INFO - 商品项 7 直接获取到data-autolog: item_id=680729304287&path=search_goods_card&index=6&type=search
2025-07-27 23:46:13,934 - INFO - 商品项 7 data-autolog: item_id=680729304287&path=search_goods_card&index=6&type=search
2025-07-27 23:46:13,934 - INFO - 商品项 7 提取到item_id: 680729304287
2025-07-27 23:46:13,944 - INFO - 商品项 7 商品名称: 保税直供 多芬dove空气感无硅油 洗发水480g 护发素480g护发临期
2025-07-27 23:46:13,954 - INFO - 找到商品项 8，检查元素信息...
2025-07-27 23:46:13,963 - INFO - 商品项 8 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:13,966 - INFO - 商品项 8 直接获取到data-autolog: item_id=710355107054&path=search_goods_card&index=7&type=search
2025-07-27 23:46:13,966 - INFO - 商品项 8 data-autolog: item_id=710355107054&path=search_goods_card&index=7&type=search
2025-07-27 23:46:13,966 - INFO - 商品项 8 提取到item_id: 710355107054
2025-07-27 23:46:13,989 - INFO - 商品项 8 商品名称: 保税直供 Dove多芬洗发水护发素空气感洗发水480g 【护发素临期
2025-07-27 23:46:14,000 - INFO - 找到商品项 9，检查元素信息...
2025-07-27 23:46:14,009 - INFO - 商品项 9 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:14,013 - INFO - 商品项 9 直接获取到data-autolog: item_id=740463658412&path=search_goods_card&index=8&type=search
2025-07-27 23:46:14,013 - INFO - 商品项 9 data-autolog: item_id=740463658412&path=search_goods_card&index=8&type=search
2025-07-27 23:46:14,013 - INFO - 商品项 9 提取到item_id: 740463658412
2025-07-27 23:46:14,023 - INFO - 商品项 9 商品名称: 【主推链接】花王cape空气感定型喷雾50g/180g 编码：3305300000
2025-07-27 23:46:14,035 - INFO - 找到商品项 10，检查元素信息...
2025-07-27 23:46:14,044 - INFO - 商品项 10 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:14,047 - INFO - 商品项 10 直接获取到data-autolog: item_id=821067902127&path=search_goods_card&index=9&type=search
2025-07-27 23:46:14,047 - INFO - 商品项 10 data-autolog: item_id=821067902127&path=search_goods_card&index=9&type=search
2025-07-27 23:46:14,047 - INFO - 商品项 10 提取到item_id: 821067902127
2025-07-27 23:46:14,056 - INFO - 商品项 10 商品名称: 丹碧丝TAMPAX卫生棉条长导管式内置式纯棉月经棉条棉棒游泳卫生巾
2025-07-27 23:46:14,067 - INFO - 找到商品项 11，检查元素信息...
2025-07-27 23:46:14,075 - INFO - 商品项 11 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:14,078 - INFO - 商品项 11 直接获取到data-autolog: item_id=789835300215&path=search_goods_card&index=10&type=search
2025-07-27 23:46:14,078 - INFO - 商品项 11 data-autolog: item_id=789835300215&path=search_goods_card&index=10&type=search
2025-07-27 23:46:14,079 - INFO - 商品项 11 提取到item_id: 789835300215
2025-07-27 23:46:14,088 - INFO - 商品项 11 商品名称: 【保税】雅漾喷雾爽肤水300ml
2025-07-27 23:46:14,101 - INFO - 找到商品项 12，检查元素信息...
2025-07-27 23:46:14,110 - INFO - 商品项 12 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:14,113 - INFO - 商品项 12 直接获取到data-autolog: item_id=843747605382&path=search_goods_card&index=11&type=search
2025-07-27 23:46:14,113 - INFO - 商品项 12 data-autolog: item_id=843747605382&path=search_goods_card&index=11&type=search
2025-07-27 23:46:14,113 - INFO - 商品项 12 提取到item_id: 843747605382
2025-07-27 23:46:14,122 - INFO - 商品项 12 商品名称: 倩碧紫胖子卸妆膏125ml/瓶
2025-07-27 23:46:14,134 - INFO - 找到商品项 13，检查元素信息...
2025-07-27 23:46:14,143 - INFO - 商品项 13 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:14,146 - INFO - 商品项 13 直接获取到data-autolog: item_id=675816539149&path=search_goods_card&index=12&type=search
2025-07-27 23:46:14,146 - INFO - 商品项 13 data-autolog: item_id=675816539149&path=search_goods_card&index=12&type=search
2025-07-27 23:46:14,147 - INFO - 商品项 13 提取到item_id: 675816539149
2025-07-27 23:46:14,175 - INFO - 商品项 13 商品名称: 新加坡Vicopyl维可保益生菌(Pylopass/拒绝幽门菌/抑口臭/调肠胃)
2025-07-27 23:46:14,186 - INFO - 找到商品项 14，检查元素信息...
2025-07-27 23:46:14,195 - INFO - 商品项 14 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:14,197 - INFO - 商品项 14 直接获取到data-autolog: item_id=846634805775&path=search_goods_card&index=13&type=search
2025-07-27 23:46:14,197 - INFO - 商品项 14 data-autolog: item_id=846634805775&path=search_goods_card&index=13&type=search
2025-07-27 23:46:14,197 - INFO - 商品项 14 提取到item_id: 846634805775
2025-07-27 23:46:14,209 - INFO - 商品项 14 商品名称: 【甄选】cow牛乳石碱原味/玫瑰花香 瓶装480ml
2025-07-27 23:46:14,220 - INFO - 找到商品项 15，检查元素信息...
2025-07-27 23:46:14,230 - INFO - 商品项 15 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:14,233 - INFO - 商品项 15 直接获取到data-autolog: item_id=852510575114&path=search_goods_card&index=14&type=search
2025-07-27 23:46:14,233 - INFO - 商品项 15 data-autolog: item_id=852510575114&path=search_goods_card&index=14&type=search
2025-07-27 23:46:14,233 - INFO - 商品项 15 提取到item_id: 852510575114
2025-07-27 23:46:14,244 - INFO - 商品项 15 商品名称: Nars 纳斯 红壳蜜粉饼16g（含粉扑）、Nars纳斯裸光蜜粉饼10g
2025-07-27 23:46:14,255 - INFO - 找到商品项 16，检查元素信息...
2025-07-27 23:46:14,263 - INFO - 商品项 16 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:14,265 - INFO - 商品项 16 直接获取到data-autolog: item_id=743442966826&path=search_goods_card&index=15&type=search
2025-07-27 23:46:14,265 - INFO - 商品项 16 data-autolog: item_id=743442966826&path=search_goods_card&index=15&type=search
2025-07-27 23:46:14,266 - INFO - 商品项 16 提取到item_id: 743442966826
2025-07-27 23:46:14,275 - INFO - 商品项 16 商品名称: ESI卡路里拜拜片白芸豆阻断片大餐救星膳食纤维身材热控管理
2025-07-27 23:46:14,288 - INFO - 找到商品项 17，检查元素信息...
2025-07-27 23:46:14,295 - INFO - 商品项 17 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:14,298 - INFO - 商品项 17 直接获取到data-autolog: item_id=848934676698&path=search_goods_card&index=16&type=search
2025-07-27 23:46:14,298 - INFO - 商品项 17 data-autolog: item_id=848934676698&path=search_goods_card&index=16&type=search
2025-07-27 23:46:14,298 - INFO - 商品项 17 提取到item_id: 848934676698
2025-07-27 23:46:14,305 - INFO - 商品项 17 商品名称: Estee Lauder 雅诗兰黛持妆粉底液30ml 2C0 /1w1/1C1
2025-07-27 23:46:14,316 - INFO - 找到商品项 18，检查元素信息...
2025-07-27 23:46:14,324 - INFO - 商品项 18 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:14,327 - INFO - 商品项 18 直接获取到data-autolog: item_id=693758717931&path=search_goods_card&index=17&type=search
2025-07-27 23:46:14,327 - INFO - 商品项 18 data-autolog: item_id=693758717931&path=search_goods_card&index=17&type=search
2025-07-27 23:46:14,327 - INFO - 商品项 18 提取到item_id: 693758717931
2025-07-27 23:46:14,336 - INFO - 商品项 18 商品名称: 保税直供 资生堂fino高效渗透发膜 230g （1/2/3罐）日版
2025-07-27 23:46:14,347 - INFO - 找到商品项 19，检查元素信息...
2025-07-27 23:46:14,355 - INFO - 商品项 19 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:14,358 - INFO - 商品项 19 直接获取到data-autolog: item_id=691142128229&path=search_goods_card&index=18&type=search
2025-07-27 23:46:14,358 - INFO - 商品项 19 data-autolog: item_id=691142128229&path=search_goods_card&index=18&type=search
2025-07-27 23:46:14,358 - INFO - 商品项 19 提取到item_id: 691142128229
2025-07-27 23:46:14,370 - INFO - 商品项 19 商品名称: 保税 日版Dove多芬洁面洗面奶新版 正装瓶150ml 补充袋125ml 甄选
2025-07-27 23:46:14,382 - INFO - 找到商品项 20，检查元素信息...
2025-07-27 23:46:14,390 - INFO - 商品项 20 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:14,393 - INFO - 商品项 20 直接获取到data-autolog: item_id=690739721578&path=search_goods_card&index=19&type=search
2025-07-27 23:46:14,393 - INFO - 商品项 20 data-autolog: item_id=690739721578&path=search_goods_card&index=19&type=search
2025-07-27 23:46:14,393 - INFO - 商品项 20 提取到item_id: 690739721578
2025-07-27 23:46:14,402 - INFO - 商品项 20 商品名称: 保税直供 SUNSILK夏士莲椰子洗发水//护发素450ml 泰产
2025-07-27 23:46:14,414 - INFO - 找到商品项 21，检查元素信息...
2025-07-27 23:46:14,421 - INFO - 商品项 21 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:14,424 - INFO - 商品项 21 直接获取到data-autolog: item_id=691918824108&path=search_goods_card&index=20&type=search
2025-07-27 23:46:14,424 - INFO - 商品项 21 data-autolog: item_id=691918824108&path=search_goods_card&index=20&type=search
2025-07-27 23:46:14,424 - INFO - 商品项 21 提取到item_id: 691918824108
2025-07-27 23:46:14,434 - INFO - 商品项 21 商品名称: 保税直供 日本资生堂fino芬浓洗发水护发素滋润型550ml
2025-07-27 23:46:14,445 - INFO - 找到商品项 22，检查元素信息...
2025-07-27 23:46:14,453 - INFO - 商品项 22 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:14,456 - INFO - 商品项 22 直接获取到data-autolog: item_id=875045022715&path=search_goods_card&index=21&type=search
2025-07-27 23:46:14,456 - INFO - 商品项 22 data-autolog: item_id=875045022715&path=search_goods_card&index=21&type=search
2025-07-27 23:46:14,456 - INFO - 商品项 22 提取到item_id: 875045022715
2025-07-27 23:46:14,464 - INFO - 商品项 22 商品名称: 欧舒丹乳木果护手霜150ml---20%乳木果 个护
2025-07-27 23:46:14,474 - INFO - 找到商品项 23，检查元素信息...
2025-07-27 23:46:14,482 - INFO - 商品项 23 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:14,486 - INFO - 商品项 23 直接获取到data-autolog: item_id=873911671570&path=search_goods_card&index=22&type=search
2025-07-27 23:46:14,486 - INFO - 商品项 23 data-autolog: item_id=873911671570&path=search_goods_card&index=22&type=search
2025-07-27 23:46:14,486 - INFO - 商品项 23 提取到item_id: 873911671570
2025-07-27 23:46:14,494 - INFO - 商品项 23 商品名称: CPB肌肤之钥光凝妆前霜37ml、37ml*2
2025-07-27 23:46:14,506 - INFO - 找到商品项 24，检查元素信息...
2025-07-27 23:46:14,515 - INFO - 商品项 24 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:14,518 - INFO - 商品项 24 直接获取到data-autolog: item_id=722937163980&path=search_goods_card&index=23&type=search
2025-07-27 23:46:14,518 - INFO - 商品项 24 data-autolog: item_id=722937163980&path=search_goods_card&index=23&type=search
2025-07-27 23:46:14,518 - INFO - 商品项 24 提取到item_id: 722937163980
2025-07-27 23:46:14,527 - INFO - 商品项 24 商品名称: 保税直供LUBRIDERM露比黎登身体乳润肤乳473ml无香款 淡香款
2025-07-27 23:46:14,539 - INFO - 找到商品项 25，检查元素信息...
2025-07-27 23:46:14,549 - INFO - 商品项 25 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:14,552 - INFO - 商品项 25 直接获取到data-autolog: item_id=739090443937&path=search_goods_card&index=24&type=search
2025-07-27 23:46:14,552 - INFO - 商品项 25 data-autolog: item_id=739090443937&path=search_goods_card&index=24&type=search
2025-07-27 23:46:14,552 - INFO - 商品项 25 提取到item_id: 739090443937
2025-07-27 23:46:14,561 - INFO - 商品项 25 商品名称: 【保税仓】花王cape空气感定型喷雾50g/180g 紫/蓝/绿/黑
2025-07-27 23:46:14,574 - INFO - 找到商品项 26，检查元素信息...
2025-07-27 23:46:14,583 - INFO - 商品项 26 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:14,586 - INFO - 商品项 26 直接获取到data-autolog: item_id=777862601010&path=search_goods_card&index=25&type=search
2025-07-27 23:46:14,586 - INFO - 商品项 26 data-autolog: item_id=777862601010&path=search_goods_card&index=25&type=search
2025-07-27 23:46:14,586 - INFO - 商品项 26 提取到item_id: 777862601010
2025-07-27 23:46:14,594 - INFO - 商品项 26 商品名称: 保税直供 Vaseline凡士林身体乳润肤乳 200ml 400ml 725ml 晶冻
2025-07-27 23:46:14,606 - INFO - 找到商品项 27，检查元素信息...
2025-07-27 23:46:14,614 - INFO - 商品项 27 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:14,617 - INFO - 商品项 27 直接获取到data-autolog: item_id=686870174450&path=search_goods_card&index=26&type=search
2025-07-27 23:46:14,617 - INFO - 商品项 27 data-autolog: item_id=686870174450&path=search_goods_card&index=26&type=search
2025-07-27 23:46:14,617 - INFO - 商品项 27 提取到item_id: 686870174450
2025-07-27 23:46:14,624 - INFO - 商品项 27 商品名称: 保税直供 Dove多芬磨砂膏石榴籽乳木果风味身体磨砂膏298g
2025-07-27 23:46:14,637 - INFO - 找到商品项 28，检查元素信息...
2025-07-27 23:46:14,646 - INFO - 商品项 28 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:14,649 - INFO - 商品项 28 直接获取到data-autolog: item_id=814542665480&path=search_goods_card&index=27&type=search
2025-07-27 23:46:14,649 - INFO - 商品项 28 data-autolog: item_id=814542665480&path=search_goods_card&index=27&type=search
2025-07-27 23:46:14,649 - INFO - 商品项 28 提取到item_id: 814542665480
2025-07-27 23:46:14,662 - INFO - 商品项 28 商品名称: 【保税】花王cape空气感定型喷雾50g/180g 专属链接
2025-07-27 23:46:14,675 - INFO - 找到商品项 29，检查元素信息...
2025-07-27 23:46:14,683 - INFO - 商品项 29 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:14,685 - INFO - 商品项 29 直接获取到data-autolog: item_id=852625718015&path=search_goods_card&index=28&type=search
2025-07-27 23:46:14,685 - INFO - 商品项 29 data-autolog: item_id=852625718015&path=search_goods_card&index=28&type=search
2025-07-27 23:46:14,685 - INFO - 商品项 29 提取到item_id: 852625718015
2025-07-27 23:46:14,693 - INFO - 商品项 29 商品名称: 保税仓 sabon身体沐浴油白茶100ml
2025-07-27 23:46:14,705 - INFO - 找到商品项 30，检查元素信息...
2025-07-27 23:46:14,713 - INFO - 商品项 30 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:14,716 - INFO - 商品项 30 直接获取到data-autolog: item_id=681472049289&path=search_goods_card&index=29&type=search
2025-07-27 23:46:14,716 - INFO - 商品项 30 data-autolog: item_id=681472049289&path=search_goods_card&index=29&type=search
2025-07-27 23:46:14,716 - INFO - 商品项 30 提取到item_id: 681472049289
2025-07-27 23:46:14,726 - INFO - 商品项 30 商品名称: 保税直供 资生堂fino芬浓高效渗透发膜 230g (1/2/3罐) 日版
2025-07-27 23:46:14,737 - INFO - 找到商品项 31，检查元素信息...
2025-07-27 23:46:14,744 - INFO - 商品项 31 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:14,747 - INFO - 商品项 31 直接获取到data-autolog: item_id=683142617961&path=search_goods_card&index=30&type=search
2025-07-27 23:46:14,748 - INFO - 商品项 31 data-autolog: item_id=683142617961&path=search_goods_card&index=30&type=search
2025-07-27 23:46:14,748 - INFO - 商品项 31 提取到item_id: 683142617961
2025-07-27 23:46:14,756 - INFO - 商品项 31 商品名称: Lancome/兰蔻 滋润/清爽防晒50ml
2025-07-27 23:46:14,768 - INFO - 找到商品项 32，检查元素信息...
2025-07-27 23:46:14,776 - INFO - 商品项 32 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:14,779 - INFO - 商品项 32 直接获取到data-autolog: item_id=763659951211&path=search_goods_card&index=31&type=search
2025-07-27 23:46:14,779 - INFO - 商品项 32 data-autolog: item_id=763659951211&path=search_goods_card&index=31&type=search
2025-07-27 23:46:14,779 - INFO - 商品项 32 提取到item_id: 763659951211
2025-07-27 23:46:14,803 - INFO - 商品项 32 商品名称: MaisonMargiela马丁马吉拉慵懒周末淡香水100ml
2025-07-27 23:46:14,814 - INFO - 找到商品项 33，检查元素信息...
2025-07-27 23:46:14,822 - INFO - 商品项 33 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:14,825 - INFO - 商品项 33 直接获取到data-autolog: item_id=675478966678&path=search_goods_card&index=32&type=search
2025-07-27 23:46:14,825 - INFO - 商品项 33 data-autolog: item_id=675478966678&path=search_goods_card&index=32&type=search
2025-07-27 23:46:14,825 - INFO - 商品项 33 提取到item_id: 675478966678
2025-07-27 23:46:14,835 - INFO - 商品项 33 商品名称: 新加坡Vicosa维可飒舒敏益生菌(改善鼻敏感/花粉症/鼻干痒)
2025-07-27 23:46:14,847 - INFO - 找到商品项 34，检查元素信息...
2025-07-27 23:46:14,854 - INFO - 商品项 34 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:14,856 - INFO - 商品项 34 直接获取到data-autolog: item_id=800365611234&path=search_goods_card&index=33&type=search
2025-07-27 23:46:14,856 - INFO - 商品项 34 data-autolog: item_id=800365611234&path=search_goods_card&index=33&type=search
2025-07-27 23:46:14,857 - INFO - 商品项 34 提取到item_id: 800365611234
2025-07-27 23:46:14,865 - INFO - 商品项 34 商品名称: 资生堂润唇膏唇炎专用维生素滋润保湿补水修复口角女男通用正品
2025-07-27 23:46:14,877 - INFO - 找到商品项 35，检查元素信息...
2025-07-27 23:46:14,886 - INFO - 商品项 35 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:14,889 - INFO - 商品项 35 直接获取到data-autolog: item_id=896721954982&path=search_goods_card&index=34&type=search
2025-07-27 23:46:14,889 - INFO - 商品项 35 data-autolog: item_id=896721954982&path=search_goods_card&index=34&type=search
2025-07-27 23:46:14,889 - INFO - 商品项 35 提取到item_id: 896721954982
2025-07-27 23:46:14,900 - INFO - 商品项 35 商品名称: Cosme Decorte 黛珂 调光师散粉系列
2025-07-27 23:46:14,914 - INFO - 找到商品项 36，检查元素信息...
2025-07-27 23:46:14,925 - INFO - 商品项 36 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:14,928 - INFO - 商品项 36 直接获取到data-autolog: item_id=795389643023&path=search_goods_card&index=35&type=search
2025-07-27 23:46:14,928 - INFO - 商品项 36 data-autolog: item_id=795389643023&path=search_goods_card&index=35&type=search
2025-07-27 23:46:14,928 - INFO - 商品项 36 提取到item_id: 795389643023
2025-07-27 23:46:14,939 - INFO - 商品项 36 商品名称: 保税直供 日本资生堂fino芬浓洗发水护发素滋润型550ml
2025-07-27 23:46:14,954 - INFO - 找到商品项 37，检查元素信息...
2025-07-27 23:46:14,965 - INFO - 商品项 37 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:14,968 - INFO - 商品项 37 直接获取到data-autolog: item_id=689584396900&path=search_goods_card&index=36&type=search
2025-07-27 23:46:14,969 - INFO - 商品项 37 data-autolog: item_id=689584396900&path=search_goods_card&index=36&type=search
2025-07-27 23:46:14,969 - INFO - 商品项 37 提取到item_id: 689584396900
2025-07-27 23:46:14,980 - INFO - 商品项 37 商品名称: CLARINS娇韵诗「保税品牌防伪」娇韵诗第九代双萃精华50/75/50*2
2025-07-27 23:46:14,992 - INFO - 找到商品项 38，检查元素信息...
2025-07-27 23:46:15,001 - INFO - 商品项 38 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:15,004 - INFO - 商品项 38 直接获取到data-autolog: item_id=788516102627&path=search_goods_card&index=37&type=search
2025-07-27 23:46:15,005 - INFO - 商品项 38 data-autolog: item_id=788516102627&path=search_goods_card&index=37&type=search
2025-07-27 23:46:15,005 - INFO - 商品项 38 提取到item_id: 788516102627
2025-07-27 23:46:15,015 - INFO - 商品项 38 商品名称: Cosme Decorte 黛珂植物韵律泡沫洁面200ml
2025-07-27 23:46:15,027 - INFO - 找到商品项 39，检查元素信息...
2025-07-27 23:46:15,037 - INFO - 商品项 39 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:15,041 - INFO - 商品项 39 直接获取到data-autolog: item_id=842638336638&path=search_goods_card&index=38&type=search
2025-07-27 23:46:15,041 - INFO - 商品项 39 data-autolog: item_id=842638336638&path=search_goods_card&index=38&type=search
2025-07-27 23:46:15,041 - INFO - 商品项 39 提取到item_id: 842638336638
2025-07-27 23:46:15,051 - INFO - 商品项 39 商品名称: 倩碧紫胖子卸妆育125ml/瓶
2025-07-27 23:46:15,064 - INFO - 找到商品项 40，检查元素信息...
2025-07-27 23:46:15,075 - INFO - 商品项 40 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:15,078 - INFO - 商品项 40 直接获取到data-autolog: item_id=747578504653&path=search_goods_card&index=39&type=search
2025-07-27 23:46:15,079 - INFO - 商品项 40 data-autolog: item_id=747578504653&path=search_goods_card&index=39&type=search
2025-07-27 23:46:15,079 - INFO - 商品项 40 提取到item_id: 747578504653
2025-07-27 23:46:15,088 - INFO - 商品项 40 商品名称: 科颜氏白泥面膜亚马逊泥膜125ml去黑头粉刺毛孔深层清洁
2025-07-27 23:46:15,088 - INFO - 当前页面爬取完成，找到 40 个商品
2025-07-27 23:46:15,089 - INFO - 第 1 页找到 40 个商品，总计: 40
2025-07-27 23:46:15,129 - INFO - 找到下一页按钮: text='下一页', class='next-btn next-medium next-btn-normal next-pagination-item next-next'
2025-07-27 23:46:15,129 - INFO - 下一页按钮可点击，准备点击
2025-07-27 23:46:15,145 - INFO - 准备点击元素: tag=button, text='下一页', class='next-btn next-medium next-btn-', id=''
2025-07-27 23:46:16,358 - INFO - 尝试ActionChains点击...
2025-07-27 23:46:16,676 - INFO - ActionChains点击成功
2025-07-27 23:46:16,983 - INFO - 成功点击下一页按钮
2025-07-27 23:46:19,984 - INFO - 正在爬取第 2 页商品信息...
2025-07-27 23:46:21,990 - INFO - 找到商品容器，开始爬取商品，最大数量: 15
2025-07-27 23:46:21,998 - INFO - 找到商品项 1，检查元素信息...
2025-07-27 23:46:22,007 - INFO - 商品项 1 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:22,010 - INFO - 商品项 1 直接获取到data-autolog: item_id=774245462064&path=search_goods_card&type=search
2025-07-27 23:46:22,010 - INFO - 商品项 1 data-autolog: item_id=774245462064&path=search_goods_card&type=search
2025-07-27 23:46:22,010 - INFO - 商品项 1 提取到item_id: 774245462064
2025-07-27 23:46:22,037 - INFO - 商品项 1 商品名称: 【重磅上新】mesoestetic第二代美斯蒂克亮白饮2.0西班牙内调口服
2025-07-27 23:46:22,059 - INFO - 找到商品项 2，检查元素信息...
2025-07-27 23:46:22,068 - INFO - 商品项 2 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:22,070 - INFO - 商品项 2 直接获取到data-autolog: item_id=714460214063&path=search_goods_card&index=1&type=search
2025-07-27 23:46:22,071 - INFO - 商品项 2 data-autolog: item_id=714460214063&path=search_goods_card&index=1&type=search
2025-07-27 23:46:22,071 - INFO - 商品项 2 提取到item_id: 714460214063
2025-07-27 23:46:22,080 - INFO - 商品项 2 商品名称: 保税直供 LUBRIDERM露比黎登身体乳润肤乳473ml【淡香蓝、无香红
2025-07-27 23:46:22,088 - INFO - 找到商品项 3，检查元素信息...
2025-07-27 23:46:22,098 - INFO - 商品项 3 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:22,101 - INFO - 商品项 3 直接获取到data-autolog: item_id=736396160366&path=search_goods_card&index=2&type=search
2025-07-27 23:46:22,102 - INFO - 商品项 3 data-autolog: item_id=736396160366&path=search_goods_card&index=2&type=search
2025-07-27 23:46:22,102 - INFO - 商品项 3 提取到item_id: 736396160366
2025-07-27 23:46:22,114 - INFO - 商品项 3 商品名称: 金凯撒鱼油omegor深海鱼油软胶囊欧米茄鱼油omega3
2025-07-27 23:46:22,124 - INFO - 找到商品项 4，检查元素信息...
2025-07-27 23:46:22,135 - INFO - 商品项 4 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:22,139 - INFO - 商品项 4 直接获取到data-autolog: item_id=730860424215&path=search_goods_card&index=3&type=search
2025-07-27 23:46:22,139 - INFO - 商品项 4 data-autolog: item_id=730860424215&path=search_goods_card&index=3&type=search
2025-07-27 23:46:22,139 - INFO - 商品项 4 提取到item_id: 730860424215
2025-07-27 23:46:22,149 - INFO - 商品项 4 商品名称: CLARINS/娇韵诗透明隔离/粉隔离50ml
2025-07-27 23:46:22,160 - INFO - 找到商品项 5，检查元素信息...
2025-07-27 23:46:22,180 - INFO - 商品项 5 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:22,184 - INFO - 商品项 5 直接获取到data-autolog: item_id=675398658821&path=search_goods_card&index=4&type=search
2025-07-27 23:46:22,184 - INFO - 商品项 5 data-autolog: item_id=675398658821&path=search_goods_card&index=4&type=search
2025-07-27 23:46:22,184 - INFO - 商品项 5 提取到item_id: 675398658821
2025-07-27 23:46:22,195 - INFO - 商品项 5 商品名称: 德国PROCEANIS铂熙漾玻尿酸口服液-喝出水光“鲜”女肌
2025-07-27 23:46:22,206 - INFO - 找到商品项 6，检查元素信息...
2025-07-27 23:46:22,215 - INFO - 商品项 6 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:22,218 - INFO - 商品项 6 直接获取到data-autolog: item_id=803074947716&path=search_goods_card&index=5&type=search
2025-07-27 23:46:22,219 - INFO - 商品项 6 data-autolog: item_id=803074947716&path=search_goods_card&index=5&type=search
2025-07-27 23:46:22,219 - INFO - 商品项 6 提取到item_id: 803074947716
2025-07-27 23:46:22,231 - INFO - 商品项 6 商品名称: Kiehls 科颜氏 高保湿面霜125ml
2025-07-27 23:46:22,247 - INFO - 找到商品项 7，检查元素信息...
2025-07-27 23:46:22,258 - INFO - 商品项 7 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:22,261 - INFO - 商品项 7 直接获取到data-autolog: item_id=846156716664&path=search_goods_card&index=6&type=search
2025-07-27 23:46:22,261 - INFO - 商品项 7 data-autolog: item_id=846156716664&path=search_goods_card&index=6&type=search
2025-07-27 23:46:22,261 - INFO - 商品项 7 提取到item_id: 846156716664
2025-07-27 23:46:22,273 - INFO - 商品项 7 商品名称: Nars 纳斯 超方瓶粉底液YUKON L2.5/Gobi L3/L2/L1/L0
2025-07-27 23:46:22,289 - INFO - 找到商品项 8，检查元素信息...
2025-07-27 23:46:22,299 - INFO - 商品项 8 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:22,302 - INFO - 商品项 8 直接获取到data-autolog: item_id=723517954743&path=search_goods_card&index=7&type=search
2025-07-27 23:46:22,302 - INFO - 商品项 8 data-autolog: item_id=723517954743&path=search_goods_card&index=7&type=search
2025-07-27 23:46:22,302 - INFO - 商品项 8 提取到item_id: 723517954743
2025-07-27 23:46:22,312 - INFO - 商品项 8 商品名称: 保税 Pantene潘婷护发素3分钟奇迹多效发膜级护发素150ml【300ml
2025-07-27 23:46:22,322 - INFO - 找到商品项 9，检查元素信息...
2025-07-27 23:46:22,330 - INFO - 商品项 9 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:22,332 - INFO - 商品项 9 直接获取到data-autolog: item_id=797348974716&path=search_goods_card&index=8&type=search
2025-07-27 23:46:22,332 - INFO - 商品项 9 data-autolog: item_id=797348974716&path=search_goods_card&index=8&type=search
2025-07-27 23:46:22,333 - INFO - 商品项 9 提取到item_id: 797348974716
2025-07-27 23:46:22,341 - INFO - 商品项 9 商品名称: Clinique倩碧紫胖子卸妆膏125ml
2025-07-27 23:46:22,351 - INFO - 找到商品项 10，检查元素信息...
2025-07-27 23:46:22,359 - INFO - 商品项 10 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:22,362 - INFO - 商品项 10 直接获取到data-autolog: item_id=866109337581&path=search_goods_card&index=9&type=search
2025-07-27 23:46:22,362 - INFO - 商品项 10 data-autolog: item_id=866109337581&path=search_goods_card&index=9&type=search
2025-07-27 23:46:22,362 - INFO - 商品项 10 提取到item_id: 866109337581
2025-07-27 23:46:22,370 - INFO - 商品项 10 商品名称: Clarins/娇韵诗第九代双萃精华50ml*2
2025-07-27 23:46:22,394 - INFO - 找到商品项 11，检查元素信息...
2025-07-27 23:46:22,402 - INFO - 商品项 11 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:22,405 - INFO - 商品项 11 直接获取到data-autolog: item_id=702988777310&path=search_goods_card&index=10&type=search
2025-07-27 23:46:22,405 - INFO - 商品项 11 data-autolog: item_id=702988777310&path=search_goods_card&index=10&type=search
2025-07-27 23:46:22,406 - INFO - 商品项 11 提取到item_id: 702988777310
2025-07-27 23:46:22,413 - INFO - 商品项 11 商品名称: 保税直供 资生堂 美润 尿素红罐 护手霜【100g/罐】
2025-07-27 23:46:22,423 - INFO - 找到商品项 12，检查元素信息...
2025-07-27 23:46:22,431 - INFO - 商品项 12 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:22,434 - INFO - 商品项 12 直接获取到data-autolog: item_id=800306261712&path=search_goods_card&index=11&type=search
2025-07-27 23:46:22,434 - INFO - 商品项 12 data-autolog: item_id=800306261712&path=search_goods_card&index=11&type=search
2025-07-27 23:46:22,434 - INFO - 商品项 12 提取到item_id: 800306261712
2025-07-27 23:46:22,444 - INFO - 商品项 12 商品名称: 【甄选保税现货】欧舒丹甜扁桃沐浴油补充装500ml[EXP：2027.12]
2025-07-27 23:46:22,453 - INFO - 找到商品项 13，检查元素信息...
2025-07-27 23:46:22,461 - INFO - 商品项 13 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:22,464 - INFO - 商品项 13 直接获取到data-autolog: item_id=833429348167&path=search_goods_card&index=12&type=search
2025-07-27 23:46:22,464 - INFO - 商品项 13 data-autolog: item_id=833429348167&path=search_goods_card&index=12&type=search
2025-07-27 23:46:22,464 - INFO - 商品项 13 提取到item_id: 833429348167
2025-07-27 23:46:22,473 - INFO - 商品项 13 商品名称: 【保税】SABON身体磨砂膏320g
2025-07-27 23:46:22,483 - INFO - 找到商品项 14，检查元素信息...
2025-07-27 23:46:22,490 - INFO - 商品项 14 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:22,493 - INFO - 商品项 14 直接获取到data-autolog: item_id=837670570405&path=search_goods_card&index=13&type=search
2025-07-27 23:46:22,494 - INFO - 商品项 14 data-autolog: item_id=837670570405&path=search_goods_card&index=13&type=search
2025-07-27 23:46:22,494 - INFO - 商品项 14 提取到item_id: 837670570405
2025-07-27 23:46:22,514 - INFO - 商品项 14 商品名称: Shiseido/资生堂 悦薇滋润、清爽水乳组合（水150ml+乳100ml）
2025-07-27 23:46:22,523 - INFO - 找到商品项 15，检查元素信息...
2025-07-27 23:46:22,531 - INFO - 商品项 15 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-27 23:46:22,535 - INFO - 商品项 15 直接获取到data-autolog: item_id=884394598985&path=search_goods_card&index=14&type=search
2025-07-27 23:46:22,535 - INFO - 商品项 15 data-autolog: item_id=884394598985&path=search_goods_card&index=14&type=search
2025-07-27 23:46:22,536 - INFO - 商品项 15 提取到item_id: 884394598985
2025-07-27 23:46:22,544 - INFO - 商品项 15 商品名称: ShuUemura植村秀 黄金琥珀卸妆油450ml/150ml
2025-07-27 23:46:22,545 - INFO - 已爬取到 15 个商品，停止当前页面爬取
2025-07-27 23:46:22,546 - INFO - 当前页面爬取完成，找到 15 个商品
2025-07-27 23:46:22,546 - INFO - 第 2 页找到 15 个商品，总计: 55
2025-07-27 23:46:22,546 - INFO - 已达到最大商品数量 55，停止爬取
2025-07-27 23:46:22,546 - INFO - 爬取完成，共找到 55 个商品
2025-07-27 23:46:22,546 - INFO - 成功爬取到 55 个商品
2025-07-27 23:46:22,546 - INFO - 第四步：开始处理商品...
2025-07-27 23:46:22,555 - INFO - 将处理 55 个商品
2025-07-27 23:46:22,556 - INFO - 当前批次大小: 13
2025-07-27 23:46:22,557 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=681778175573
2025-07-27 23:46:26,569 - INFO - 页面加载完成
2025-07-27 23:46:26,571 - INFO - 模拟阅读行为，停顿 2.3 秒
2025-07-27 23:46:30,836 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-27 23:46:30,836 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-27 23:46:30,852 - INFO - span innerHTML: 立即铺货
2025-07-27 23:46:30,874 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-27 23:46:30,875 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-27 23:46:30,890 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-27 23:46:31,990 - INFO - 尝试ActionChains点击...
2025-07-27 23:46:32,282 - INFO - ActionChains点击成功
2025-07-27 23:46:32,637 - INFO - 铺货按钮点击成功
2025-07-27 23:46:34,667 - INFO - 使用配置的XPath找到确认按钮
2025-07-27 23:46:34,679 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-27 23:46:35,904 - INFO - 尝试ActionChains点击...
2025-07-27 23:46:36,175 - INFO - ActionChains点击成功
2025-07-27 23:46:36,457 - INFO - 确认按钮点击成功
2025-07-27 23:46:38,458 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-27 23:46:38,465 - INFO - 第1次检查 - span innerHTML: 已铺货
2025-07-27 23:46:38,465 - INFO - 验证成功：span已变为'已铺货'，商品铺货成功
2025-07-27 23:46:38,466 - INFO - 第 1 个商品处理成功 (总成功: 1, 批次进度: 1/13)
2025-07-27 23:46:38,466 - INFO - 等待 3.6 秒后处理下一个商品
2025-07-27 23:46:39,223 - INFO - Cookies已保存
2025-07-27 23:46:41,471 - INFO - 浏览器已关闭
