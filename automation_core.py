#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
淘宝铺货自动化核心模块
包含浏览器自动化的核心功能
"""

import time
import random
import json
import os
import pickle
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import (
    TimeoutException, 
    NoSuchElementException, 
    ElementClickInterceptedException,
    WebDriverException
)
from datetime import datetime

class TaobaoAutomationCore:
    def __init__(self, config, log_callback=None):
        """
        初始化自动化核心
        
        Args:
            config: 配置字典
            log_callback: 日志回调函数
        """
        self.config = config
        self.log_callback = log_callback or print
        self.driver = None
        self.wait = None
        self.processed_count = 0
        self.batch_count = 0
        
    def log(self, message, level="INFO"):
        """记录日志"""
        self.log_callback(message, level)
    
    def init_driver(self, headless=False):
        """初始化浏览器驱动
        Args:
            headless: 是否无界面运行
        """
        try:
            chrome_options = Options()
            user_data_dir = os.path.abspath(self.config.get("user_data_dir", "./chrome_user_data"))
            if not os.path.exists(user_data_dir):
                os.makedirs(user_data_dir)
            chrome_options.add_argument(f"--user-data-dir={user_data_dir}")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            desktop_user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            chrome_options.add_argument(f"--user-agent={desktop_user_agent}")
            if headless:
                chrome_options.add_argument("--headless")
                chrome_options.add_argument("--no-sandbox")
                chrome_options.add_argument("--disable-dev-shm-usage")
                # 屏蔽控制台输出
                chrome_options.add_argument("--log-level=3")
                chrome_options.add_argument("--silent")
                chrome_options.add_argument("--disable-logging")
                chrome_options.add_argument("--disable-ssl-error-background")
                chrome_options.add_argument("--disable-web-security")
                chrome_options.add_argument("--disable-features=VizDisplayCompositor")
                chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
                self.log("以无界面(headless)模式启动Chrome，仅添加headless、no-sandbox、disable-dev-shm-usage参数")
            else:
                # 移除 --start-maximized 避免浏览器自动获得焦点
                chrome_options.add_argument("--window-size=1920,1080")
                # 添加参数确保浏览器不会自动置顶
                chrome_options.add_argument("--no-first-run")
                chrome_options.add_argument("--no-default-browser-check")
                chrome_options.add_experimental_option("prefs", {
                    "intl.accept_languages": "zh-CN,zh,en-US,en",
                    "profile.default_content_setting_values.notifications": 2,
                })
            # 初始化驱动
            from selenium.webdriver.chrome.service import Service
            driver_path = self.config.get("chrome_driver_path", "./chromedriver.exe")
            if not os.path.exists(driver_path):
                self.log("ChromeDriver不存在，尝试自动下载...")
                try:
                    from webdriver_manager.chrome import ChromeDriverManager
                    driver_path = ChromeDriverManager().install()
                    self.log(f"ChromeDriver已自动下载到: {driver_path}")
                except Exception as e:
                    self.log(f"自动下载ChromeDriver失败: {e}", "ERROR")
                    raise FileNotFoundError(f"ChromeDriver not found at {driver_path} and auto-download failed")
            service = Service(executable_path=driver_path, log_path=os.devnull)
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.setup_anti_detection()
            self.wait = WebDriverWait(self.driver, 10)
            self.log("浏览器驱动初始化成功")
            return True
        except Exception as e:
            self.log(f"浏览器驱动初始化失败: {e}", "ERROR")
            return False

    def setup_anti_detection(self):
        """设置基础防检测机制"""
        try:
            # 简化的防检测脚本，只保留核心功能
            anti_detection_script = """
                // 隐藏webdriver属性
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined
                });

                // 删除webdriver相关属性
                delete navigator.__proto__.webdriver;

                // 确保User-Agent为桌面版
                const DESKTOP_UA = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
                Object.defineProperty(navigator, 'userAgent', {
                    get: () => DESKTOP_UA,
                    configurable: false
                });

                // 设置桌面版平台
                Object.defineProperty(navigator, 'platform', {
                    get: () => 'Win32'
                });

                // 设置桌面版语言
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['zh-CN', 'zh', 'en-US', 'en']
                });

                // 设置桌面版屏幕尺寸
                Object.defineProperty(screen, 'width', {
                    get: () => 1920
                });
                Object.defineProperty(screen, 'height', {
                    get: () => 1080
                });
            """

            self.driver.execute_script(anti_detection_script)

            # 设置随机的viewport
            viewport_width = random.randint(1366, 1920)
            viewport_height = random.randint(768, 1080)
            self.driver.set_window_size(viewport_width, viewport_height)

            # 设置网络请求拦截，确保请求头为桌面版
            self.setup_request_interception()

            self.log("防检测机制设置完成")

        except Exception as e:
            self.log(f"设置防检测机制失败: {e}", "WARNING")

    def setup_request_interception(self):
        """设置简化的请求拦截，确保桌面版User-Agent"""
        try:
            # 简化的请求头设置
            desktop_ua = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"

            request_script = f"""
                // 强制设置桌面版User-Agent
                const DESKTOP_USER_AGENT = '{desktop_ua}';

                // 确保navigator.userAgent始终返回桌面版
                Object.defineProperty(navigator, 'userAgent', {{
                    get: () => DESKTOP_USER_AGENT,
                    configurable: false,
                    enumerable: true
                }});

                console.log('桌面版User-Agent已设置:', navigator.userAgent);
            """

            self.driver.execute_script(request_script)
            self.log(f"桌面版User-Agent设置完成: {desktop_ua}")

        except Exception as e:
            self.log(f"设置User-Agent失败: {e}", "WARNING")



    def is_already_distributed(self, button_text):
        """检测商品是否已经铺货"""
        try:
            # 已铺货的关键词
            distributed_keywords = [
                "已铺货", "已添加", "已选择", "已加入",
                "已分销", "已上架", "已导入", "已同步",
                "distributed", "added", "selected", "imported"
            ]

            button_text_lower = button_text.lower()

            for keyword in distributed_keywords:
                if keyword.lower() in button_text_lower:
                    self.log(f"检测到已铺货关键词: '{keyword}' 在按钮文本 '{button_text}' 中")
                    return True

            return False

        except Exception as e:
            self.log(f"检测已铺货状态失败: {e}", "WARNING")
            return False

    def verify_user_agent(self):
        """验证User-Agent是否为桌面版"""
        try:
            current_ua = self.driver.execute_script("return navigator.userAgent;")
            self.log(f"当前User-Agent: {current_ua}")

            # 检查是否包含手机版标识
            mobile_indicators = ['Mobile', 'Android', 'iPhone', 'iPad', 'iPod', 'BlackBerry', 'Windows Phone']
            is_mobile = any(indicator in current_ua for indicator in mobile_indicators)

            if is_mobile:
                self.log("警告：检测到手机版User-Agent！", "ERROR")
                return False
            else:
                self.log("确认：使用桌面版User-Agent")
                return True

        except Exception as e:
            self.log(f"验证User-Agent失败: {e}", "WARNING")
            return False

    def find_next_page_button(self):
        """查找下一页按钮"""
        try:
            # 尝试多种下一页按钮选择器
            next_page_selectors = [
                # 配置中的XPath
                "//*[@id='root']/div[2]/div/div/div/div[5]/div[2]/div/div[2]/button[2]",

                # 通用的下一页选择器
                "//button[contains(@aria-label, '下一页')]",
                "//button[contains(@class, 'next-btn')]",
                "//button[contains(@class, 'next-pagination')]",
                "//button[contains(@class, 'pagination-next')]",

                # 文本匹配
                "//button[contains(text(), '下一页') or contains(text(), '下页')]",
                "//a[contains(text(), '下一页') or contains(text(), '下页')]",
                "//button[contains(text(), 'Next') or contains(text(), 'next')]",
                "//a[contains(text(), 'Next') or contains(text(), 'next')]",

                # 图标匹配
                "//button[contains(@class, 'arrow-right')]",
                "//button[.//i[contains(@class, 'arrow-right')]]",
                "//button[.//span[contains(text(), '下一页')]]",

                # 分页组件中的下一页
                "//*[contains(@class, 'pagination')]//button[last()]",
                "//*[contains(@class, 'pager')]//button[last()]"
            ]

            for selector in next_page_selectors:
                try:
                    self.log(f"尝试下一页选择器: {selector}")
                    elements = self.driver.find_elements(By.XPATH, selector)

                    for element in elements:
                        # 检查按钮是否可用（不是禁用状态）
                        disabled = element.get_attribute("disabled")
                        aria_disabled = element.get_attribute("aria-disabled")

                        if disabled == "true" or aria_disabled == "true":
                            self.log("下一页按钮已禁用，可能已到最后一页")
                            continue

                        # 检查按钮文本和属性
                        button_text = element.text.strip()
                        aria_label = element.get_attribute("aria-label") or ""
                        class_name = element.get_attribute("class") or ""

                        self.log(f"找到下一页按钮: 文本='{button_text}', aria-label='{aria_label}', class='{class_name[:50]}'")
                        return element

                except Exception as e:
                    self.log(f"选择器 {selector} 失败: {e}", "WARNING")
                    continue

            self.log("未找到可用的下一页按钮", "WARNING")
            return None

        except Exception as e:
            self.log(f"查找下一页按钮时出错: {e}", "ERROR")
            return None

    def go_to_next_page(self):
        """跳转到下一页"""
        try:
            # 查找下一页按钮
            next_button = self.find_next_page_button()
            if not next_button:
                self.log("未找到下一页按钮，可能已到最后一页", "WARNING")
                return False

            # 记录当前页面URL，用于验证是否成功跳转
            current_url = self.driver.current_url

            # 滚动到下一页按钮位置
            self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", next_button)
            time.sleep(1)

            # 高亮显示下一页按钮
            try:
                self.driver.execute_script("arguments[0].style.border='3px solid blue';", next_button)
                time.sleep(0.5)
            except:
                pass

            # 模拟用户行为
            self.simulate_reading_behavior()

            # 点击下一页按钮
            if self.safe_click(next_button):
                self.log("已点击下一页按钮，等待页面加载...")

                # 等待页面加载
                time.sleep(random.uniform(2, 4))

                # 验证是否成功跳转
                new_url = self.driver.current_url
                if new_url != current_url:
                    self.log("成功跳转到下一页")

                    # 等待新页面完全加载
                    self.wait_for_page_load()

                    # 重新设置防检测机制
                    self.setup_request_interception()

                    return True
                else:
                    self.log("页面URL未变化，可能跳转失败", "WARNING")
                    return False
            else:
                self.log("点击下一页按钮失败", "ERROR")
                return False

        except Exception as e:
            self.log(f"跳转下一页失败: {e}", "ERROR")
            return False
    
    def save_cookies(self, filename="cookies.pkl"):
        """保存cookies"""
        try:
            if self.driver:
                cookies = self.driver.get_cookies()
                with open(filename, 'wb') as f:
                    pickle.dump(cookies, f)
                self.log("Cookies已保存")
        except Exception as e:
            self.log(f"保存cookies失败: {e}", "ERROR")
    
    def load_cookies(self, filename="cookies.pkl"):
        """加载cookies"""
        try:
            if os.path.exists(filename) and self.driver:
                with open(filename, 'rb') as f:
                    cookies = pickle.load(f)
                for cookie in cookies:
                    try:
                        self.driver.add_cookie(cookie)
                    except Exception as e:
                        self.log(f"添加cookie失败: {e}", "WARNING")
                self.log("Cookies已加载")
                return True
        except Exception as e:
            self.log(f"加载cookies失败: {e}", "ERROR")
        return False
    
    def navigate_to_target_page(self, check_login=True):
        """导航到目标页面"""
        try:
            target_url = self.config.get("target_url")
            if not target_url:
                raise ValueError("目标URL未配置")

            self.log(f"正在导航到目标页面: {target_url}")

            # 添加随机延迟，模拟真实用户行为
            self.add_random_delays()

            self.driver.get(target_url)

            # 等待页面加载并模拟人类行为
            time.sleep(random.uniform(2, 4))



            # 验证User-Agent是否正确
            self.verify_user_agent()

            # 重新设置防检测机制（确保在新页面中生效）
            self.setup_request_interception()

            self.simulate_human_behavior()

            # 检查是否需要登录
            if check_login and "login" in self.driver.current_url.lower():
                self.log("检测到需要登录，请手动登录后继续", "WARNING")
                # 保存调试信息
                self.save_debug_info("检测到需要登录", "login_required")
                return "need_login"

            self.log("成功导航到目标页面")
            return True

        except Exception as e:
            self.log(f"导航到目标页面失败: {e}", "ERROR")
            # 保存调试信息
            self.save_debug_info(f"导航到目标页面失败: {e}", "navigation_error")
            return False
    
    def wait_for_element(self, xpath, timeout=10):
        """等待元素出现"""
        try:
            element = WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((By.XPATH, xpath))
            )
            return element
        except TimeoutException:
            self.log(f"等待元素超时: {xpath}", "WARNING")
            return None
    
    def wait_for_clickable_element(self, xpath, timeout=10):
        """等待元素可点击"""
        try:
            element = WebDriverWait(self.driver, timeout).until(
                EC.element_to_be_clickable((By.XPATH, xpath))
            )
            return element
        except TimeoutException:
            self.log(f"等待元素可点击超时: {xpath}", "WARNING")
            return None
    
    def safe_click(self, element_or_xpath, timeout=10):
        """安全点击元素 - 增强版"""
        try:
            if isinstance(element_or_xpath, str):
                element = self.wait_for_clickable_element(element_or_xpath, timeout)
                if not element:
                    self.log(f"无法找到元素: {element_or_xpath}", "ERROR")
                    return False
            else:
                element = element_or_xpath

            # 获取元素信息用于调试
            try:
                element_tag = element.tag_name
                element_text = element.text.strip()
                element_class = element.get_attribute("class") or ""
                element_id = element.get_attribute("id") or ""
                self.log(f"准备点击元素: tag={element_tag}, text='{element_text}', class='{element_class[:30]}', id='{element_id}'")
            except:
                pass

            # 滚动到元素位置
            self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", element)
            time.sleep(random.uniform(0.5, 1.0))

            # 高亮显示元素
            try:
                self.driver.execute_script("arguments[0].style.border='3px solid red';", element)
                time.sleep(0.5)
            except:
                pass

            # 尝试多种点击方式
            click_methods = [
                ("ActionChains点击", self._try_action_chains_click),
                ("直接点击", self._try_direct_click),
                ("JavaScript点击", self._try_js_click),
                ("坐标点击", self._try_coordinate_click)
            ]

            for method_name, method_func in click_methods:
                try:
                    self.log(f"尝试{method_name}...")
                    if method_func(element):
                        self.log(f"{method_name}成功")
                        time.sleep(random.uniform(0.2, 0.5))
                        return True
                except Exception as e:
                    self.log(f"{method_name}失败: {e}", "WARNING")
                    continue

            self.log("所有点击方式都失败", "ERROR")
            return False

        except Exception as e:
            self.log(f"点击元素失败: {e}", "ERROR")
            return False

    def _try_action_chains_click(self, element):
        """尝试ActionChains点击"""
        actions = ActionChains(self.driver)
        actions.move_to_element(element).click().perform()
        return True

    def _try_direct_click(self, element):
        """尝试直接点击"""
        element.click()
        return True

    def _try_js_click(self, element):
        """尝试JavaScript点击"""
        self.driver.execute_script("arguments[0].click();", element)
        return True

    def _try_coordinate_click(self, element):
        """尝试坐标点击"""
        location = element.location
        size = element.size
        x = location['x'] + size['width'] // 2
        y = location['y'] + size['height'] // 2

        actions = ActionChains(self.driver)
        actions.move_by_offset(x, y).click().perform()
        return True
    
    def switch_to_new_tab(self):
        """切换到新标签页 - 使用更可靠的方法"""
        try:
            # 等待新标签页打开，最多等待5秒
            max_wait = 5
            wait_interval = 0.5
            waited = 0

            initial_handles = self.driver.window_handles
            initial_count = len(initial_handles)
            self.log(f"点击前窗口句柄数: {initial_count}")

            while waited < max_wait:
                current_handles = self.driver.window_handles
                current_count = len(current_handles)

                if current_count > initial_count:
                    self.log(f"检测到新窗口，当前句柄: {current_handles}")

                    # 直接切换到最新生成的页面（最后一个句柄）
                    self.driver.switch_to.window(current_handles[-1])

                    # 获取新页面信息
                    new_url = self.driver.current_url
                    new_title = self.driver.title
                    self.log(f"已切换到新标签页，URL: {new_url}, 标题: {new_title}")

                    return True

                time.sleep(wait_interval)
                waited += wait_interval

            # 如果等待超时，尝试直接切换到最后一个窗口
            current_handles = self.driver.window_handles
            if len(current_handles) > 1:
                self.log("等待超时，但检测到多个窗口，切换到最后一个")
                self.driver.switch_to.window(current_handles[-1])

                new_url = self.driver.current_url
                self.log(f"已切换到最后一个窗口，URL: {new_url}")

                return True

            self.log("等待新标签页超时，未检测到新标签页", "WARNING")
            return False

        except Exception as e:
            self.log(f"切换标签页失败: {e}", "ERROR")
            return False
    
    def close_current_tab_and_switch_back(self):
        """关闭当前标签页并切换回主标签页"""
        try:
            handles = self.driver.window_handles
            if len(handles) > 1:
                # 关闭当前标签页
                self.driver.close()
                # 切换回主标签页
                self.driver.switch_to.window(handles[0])
                self.log("已关闭当前标签页并切换回主页面")
                return True
            else:
                self.log("只有一个标签页，无需关闭", "WARNING")
                return False
                
        except Exception as e:
            self.log(f"关闭标签页失败: {e}", "ERROR")
            return False
    
    def check_iframe_and_switch(self):
        """检查并切换到iframe"""
        try:
            # 首先确保在主文档中
            self.driver.switch_to.default_content()

            # 查找所有iframe
            iframes = self.driver.find_elements(By.TAG_NAME, "iframe")
            self.log(f"找到 {len(iframes)} 个iframe")

            for i, iframe in enumerate(iframes):
                try:
                    # 检查iframe是否可见
                    if not iframe.is_displayed():
                        continue

                    self.driver.switch_to.frame(iframe)
                    self.log(f"已切换到iframe {i}")

                    # 检查iframe中是否有我们需要的元素
                    if self.check_iframe_content():
                        return True
                    else:
                        # 如果没有找到需要的元素，切换回主文档继续查找
                        self.driver.switch_to.default_content()

                except Exception as e:
                    self.log(f"切换到iframe {i} 失败: {e}", "WARNING")
                    try:
                        self.driver.switch_to.default_content()
                    except:
                        pass
                    continue

            # 如果没有找到合适的iframe，保持在主文档
            self.driver.switch_to.default_content()
            self.log("未找到包含目标元素的iframe，保持在主文档")
            return False

        except Exception as e:
            self.log(f"处理iframe失败: {e}", "ERROR")
            try:
                self.driver.switch_to.default_content()
            except:
                pass
            return False

    def check_iframe_content(self):
        """检查当前iframe中是否包含目标元素"""
        try:
            # 检查是否有添加商品按钮
            add_button = self.find_add_product_button()
            if add_button:
                self.log("在当前iframe中找到添加商品按钮")
                return True

            # 检查是否有其他重要元素
            important_selectors = [
                "//button[contains(text(), '添加')]",
                "//button[contains(text(), '确认')]",
                "//button[contains(@class, 'primary')]"
            ]

            for selector in important_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    if elements:
                        self.log(f"在iframe中找到重要元素: {selector}")
                        return True
                except:
                    continue

            return False

        except Exception as e:
            self.log(f"检查iframe内容失败: {e}", "WARNING")
            return False
    
    def random_sleep(self, min_seconds=None, max_seconds=None):
        """随机等待"""
        min_time = min_seconds or self.config.get("min_interval", 3)
        max_time = max_seconds or self.config.get("max_interval", 8)

        sleep_time = random.uniform(min_time, max_time)
        self.log(f"等待 {sleep_time:.1f} 秒")
        time.sleep(sleep_time)

    def human_like_mouse_move(self, element):
        """人性化鼠标移动"""
        try:
            # 获取当前鼠标位置（模拟）
            current_x = random.randint(100, 800)
            current_y = random.randint(100, 600)

            # 获取目标元素位置
            target_location = element.location
            target_x = target_location['x'] + element.size['width'] // 2
            target_y = target_location['y'] + element.size['height'] // 2

            # 创建人性化的移动路径
            actions = ActionChains(self.driver)

            # 添加随机的中间点，模拟真实鼠标移动
            steps = random.randint(3, 6)
            for i in range(steps):
                progress = (i + 1) / steps
                # 添加一些随机偏移
                offset_x = random.randint(-20, 20)
                offset_y = random.randint(-20, 20)

                intermediate_x = current_x + (target_x - current_x) * progress + offset_x
                intermediate_y = current_y + (target_y - current_y) * progress + offset_y

                # 移动到中间点
                actions.move_by_offset(
                    intermediate_x - current_x,
                    intermediate_y - current_y
                )
                current_x, current_y = intermediate_x, intermediate_y

                # 随机停顿
                time.sleep(random.uniform(0.01, 0.05))

            # 最终移动到目标元素
            actions.move_to_element(element)
            actions.perform()

            # 随机停顿后点击
            time.sleep(random.uniform(0.1, 0.3))

        except Exception as e:
            self.log(f"人性化鼠标移动失败: {e}", "WARNING")

    def random_scroll(self):
        """随机滚动页面，模拟用户浏览行为"""
        try:
            # 随机滚动方向和距离
            scroll_direction = random.choice(['up', 'down'])
            scroll_distance = random.randint(100, 500)

            if scroll_direction == 'down':
                self.driver.execute_script(f"window.scrollBy(0, {scroll_distance});")
            else:
                self.driver.execute_script(f"window.scrollBy(0, -{scroll_distance});")

            # 随机停顿
            time.sleep(random.uniform(0.5, 1.5))

        except Exception as e:
            self.log(f"随机滚动失败: {e}", "WARNING")

    def simulate_reading_behavior(self):
        """模拟阅读行为"""
        try:
            # 随机停顿，模拟阅读时间
            reading_time = random.uniform(1.0, 3.0)
            self.log(f"模拟阅读行为，停顿 {reading_time:.1f} 秒")
            time.sleep(reading_time)

            # 随机滚动
            if random.random() < 0.3:  # 30%概率滚动
                self.random_scroll()

        except Exception as e:
            self.log(f"模拟阅读行为失败: {e}", "WARNING")

    def simulate_human_behavior(self):
        """模拟人类浏览行为"""
        try:
            behaviors = [
                self.random_mouse_movement,
                self.random_scroll,
                self.simulate_typing_pause,
                self.random_page_interaction
            ]

            # 随机选择1-2个行为执行
            selected_behaviors = random.sample(behaviors, random.randint(1, 2))

            for behavior in selected_behaviors:
                if random.random() < 0.7:  # 70%概率执行
                    behavior()

        except Exception as e:
            self.log(f"模拟人类行为失败: {e}", "WARNING")

    def random_mouse_movement(self):
        """随机鼠标移动"""
        try:
            # 获取当前窗口大小
            window_size = self.driver.get_window_size()
            width = window_size['width']
            height = window_size['height']

            # 随机移动鼠标
            actions = ActionChains(self.driver)
            for _ in range(random.randint(2, 5)):
                x = random.randint(100, width - 100)
                y = random.randint(100, height - 100)
                actions.move_by_offset(x - width//2, y - height//2)
                time.sleep(random.uniform(0.1, 0.3))

            actions.perform()

        except Exception as e:
            self.log(f"随机鼠标移动失败: {e}", "WARNING")

    def simulate_typing_pause(self):
        """模拟打字停顿"""
        try:
            # 模拟用户思考或查看的停顿
            pause_time = random.uniform(0.5, 2.0)
            time.sleep(pause_time)

        except Exception as e:
            self.log(f"模拟打字停顿失败: {e}", "WARNING")

    def random_page_interaction(self):
        """随机页面交互"""
        try:
            interactions = [
                lambda: self.driver.execute_script("window.scrollBy(0, 100);"),
                lambda: self.driver.execute_script("window.scrollBy(0, -50);"),
                lambda: time.sleep(random.uniform(0.5, 1.5)),
            ]

            selected_interaction = random.choice(interactions)
            selected_interaction()

        except Exception as e:
            self.log(f"随机页面交互失败: {e}", "WARNING")

    def add_random_delays(self):
        """添加随机延迟，模拟真实用户操作"""
        try:
            # 基础延迟
            base_delay = random.uniform(0.5, 1.5)

            # 随机添加额外延迟
            if random.random() < 0.3:  # 30%概率添加额外延迟
                extra_delay = random.uniform(1.0, 3.0)
                base_delay += extra_delay
                self.log(f"添加额外延迟: {extra_delay:.1f}秒")

            time.sleep(base_delay)

        except Exception as e:
            self.log(f"添加随机延迟失败: {e}", "WARNING")

    def _click_with_action_chains(self, element):
        """使用ActionChains点击"""
        try:
            actions = ActionChains(self.driver)
            actions.move_to_element(element).click().perform()
            return True
        except Exception as e:
            self.log(f"ActionChains点击失败: {e}", "WARNING")
            return False

    def _click_internal_link(self, element):
        """查找并点击内部链接"""
        try:
            # 查找元素内部的链接
            links = element.find_elements(By.TAG_NAME, "a")
            for link in links:
                href = link.get_attribute("href")
                if href and "item" in href.lower():
                    self.log(f"找到商品链接: {href[:100]}")
                    actions = ActionChains(self.driver)
                    actions.move_to_element(link).click().perform()
                    return True

            # 如果没有找到链接，尝试查找其他可点击元素
            clickable_elements = element.find_elements(By.XPATH, ".//*[@onclick or @href]")
            for clickable in clickable_elements:
                actions = ActionChains(self.driver)
                actions.move_to_element(clickable).click().perform()
                return True

            return False
        except Exception as e:
            self.log(f"内部链接点击失败: {e}", "WARNING")
            return False

    def _click_with_javascript(self, element):
        """使用JavaScript点击"""
        try:
            self.driver.execute_script("arguments[0].click();", element)
            return True
        except Exception as e:
            self.log(f"JavaScript点击失败: {e}", "WARNING")
            return False

    def _click_with_middle_button(self, element):
        """使用中键点击（新标签页）"""
        try:
            actions = ActionChains(self.driver)
            actions.move_to_element(element).click().perform()

            # 尝试中键点击
            actions = ActionChains(self.driver)
            actions.move_to_element(element).context_click().perform()
            time.sleep(0.5)

            # 查找"在新标签页中打开"选项
            try:
                new_tab_option = self.driver.find_element(By.XPATH, "//*[contains(text(), '新标签页') or contains(text(), '新窗口')]")
                if new_tab_option:
                    new_tab_option.click()
                    return True
            except:
                pass

            return False
        except Exception as e:
            self.log(f"中键点击失败: {e}", "WARNING")
            return False

    def _click_with_ctrl(self, element):
        """使用Ctrl+点击（新标签页）"""
        try:
            from selenium.webdriver.common.keys import Keys

            actions = ActionChains(self.driver)
            actions.key_down(Keys.CONTROL).move_to_element(element).click().key_up(Keys.CONTROL).perform()
            return True
        except Exception as e:
            self.log(f"Ctrl+点击失败: {e}", "WARNING")
            return False

    def _right_click_new_tab(self, element):
        """右键点击并选择新标签页"""
        try:
            # 右键点击
            actions = ActionChains(self.driver)
            actions.move_to_element(element).context_click().perform()
            time.sleep(1)

            # 尝试点击"在新标签页中打开链接"
            context_menu_options = [
                "//*[contains(text(), '新标签页')]",
                "//*[contains(text(), '新窗口')]",
                "//*[contains(text(), 'new tab')]",
                "//*[contains(text(), 'new window')]"
            ]

            for option_xpath in context_menu_options:
                try:
                    option = self.driver.find_element(By.XPATH, option_xpath)
                    if option.is_displayed():
                        option.click()
                        return True
                except:
                    continue

            # 如果没有找到上下文菜单选项，按ESC关闭菜单
            from selenium.webdriver.common.keys import Keys
            actions = ActionChains(self.driver)
            actions.send_keys(Keys.ESCAPE).perform()
            return False

        except Exception as e:
            self.log(f"右键新标签页失败: {e}", "WARNING")
            return False
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.driver:
                self.save_cookies()
                self.driver.quit()
                self.log("浏览器已关闭")
        except Exception as e:
            self.log(f"清理资源时出错: {e}", "ERROR")
    
    def save_debug_info(self, error_msg="", prefix="debug"):
        """保存调试信息（截图和日志）"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 创建debug目录
            debug_dir = "debug_logs"
            if not os.path.exists(debug_dir):
                os.makedirs(debug_dir)
            
            # 保存截图
            screenshot_path = f"{debug_dir}/{prefix}_screenshot_{timestamp}.png"
            self.driver.save_screenshot(screenshot_path)
            self.log(f"调试截图已保存: {screenshot_path}")
            
            # 获取浏览器日志
            try:
                browser_logs = self.driver.get_log('browser')
                if browser_logs:
                    log_path = f"{debug_dir}/{prefix}_browser_log_{timestamp}.txt"
                    with open(log_path, 'w', encoding='utf-8') as f:
                        for log in browser_logs:
                            f.write(f"{log['timestamp']} - {log['level']}: {log['message']}\n")
                    self.log(f"浏览器日志已保存: {log_path}")
            except Exception as e:
                self.log(f"获取浏览器日志失败: {e}", "WARNING")
            
            # 保存页面源码
            try:
                page_source_path = f"{debug_dir}/{prefix}_page_source_{timestamp}.html"
                with open(page_source_path, 'w', encoding='utf-8') as f:
                    f.write(self.driver.page_source)
                self.log(f"页面源码已保存: {page_source_path}")
            except Exception as e:
                self.log(f"保存页面源码失败: {e}", "WARNING")
            
            # 记录当前URL和错误信息
            current_url = self.driver.current_url
            self.log(f"调试信息 - 当前URL: {current_url}")
            if error_msg:
                self.log(f"调试信息 - 错误: {error_msg}")
                
        except Exception as e:
            self.log(f"保存调试信息失败: {e}", "ERROR")

    def get_product_list_items(self):
        """获取商品列表项"""
        try:
            container_xpath = self.config["xpaths"]["product_list_container"]
            container = self.wait_for_element(container_xpath, timeout=15)

            if not container:
                self.log("未找到商品列表容器", "ERROR")
                # 保存调试信息
                self.save_debug_info("未找到商品列表容器", "product_list_error")
                return []

            # 根据页面结构，获取容器下的直接子div（无class的div）
            # 这些div包含tfx-item商品
            direct_children = container.find_elements(By.XPATH, "./div")

            self.log(f"找到 {len(direct_children)} 个直接子div")

            # 过滤出包含tfx-item的有效商品容器
            valid_items = []
            for i, child_div in enumerate(direct_children):
                try:
                    # 检查这个div是否包含tfx-item
                    tfx_items = child_div.find_elements(By.XPATH, ".//div[contains(@class, 'tfx-item')]")
                    if tfx_items:
                        # 获取div的基本信息
                        size = child_div.size
                        location = child_div.location

                        # 确保div足够大
                        if size['height'] > 50 and size['width'] > 100:
                            valid_items.append(child_div)

                            # 调试信息
                            tfx_item = tfx_items[0]
                            item_id = tfx_item.get_attribute("data-autolog") or "无ID"
                            self.log(f"商品 {i+1}: 位置={location}, 尺寸={size}, ID={item_id[:50]}")

                except Exception as e:
                    self.log(f"检查第 {i+1} 个div时出错: {e}", "WARNING")
                    continue

            self.log(f"找到 {len(valid_items)} 个有效的商品容器")
            return valid_items

        except Exception as e:
            self.log(f"获取商品列表失败: {e}", "ERROR")
            # 保存调试信息
            self.save_debug_info(f"获取商品列表失败: {e}", "get_products_error")
            return []

    def process_single_product(self, item_index, total_items):
        """处理单个商品"""
        try:
            self.log(f"开始处理第 {item_index + 1}/{total_items} 个商品")

            # 重新获取商品列表（防止页面变化导致元素失效）
            items = self.get_product_list_items()
            if not items:
                self.log("无法获取商品列表", "ERROR")
                return False

            if item_index >= len(items):
                self.log(f"商品索引 {item_index} 超出范围，当前只有 {len(items)} 个商品", "ERROR")
                return False

            item = items[item_index]

            # 验证这是否是正确的商品项
            try:
                item_class = item.get_attribute("class") or ""
                if "tfx-item" not in item_class:
                    self.log(f"警告：第 {item_index + 1} 个元素可能不是商品项，class='{item_class}'", "WARNING")

                # 检查是否已经铺货
                item_html = item.get_attribute("innerHTML") or ""
                if "已铺货" in item_html:
                    self.log(f"第 {item_index + 1} 个商品已铺货，跳过")
                    return "already_distributed"  # 返回特殊值表示已铺货

            except:
                pass

            # 记录商品信息用于调试
            try:
                item_text = item.text[:50] if item.text else "无文本"
                item_location = item.location
                self.log(f"商品 {item_index + 1} 信息: 文本='{item_text}', 位置={item_location}")
            except:
                pass

            # 滚动到商品项位置
            try:
                self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", item)
                time.sleep(1)

                # 高亮显示当前要点击的元素（调试用）
                self.driver.execute_script("arguments[0].style.border='3px solid red';", item)
                time.sleep(0.5)

            except Exception as e:
                self.log(f"滚动到商品位置失败: {e}", "WARNING")

            # 尝试多种点击方式，确保能打开新标签页
            click_success = False

            # 获取元素的位置和尺寸信息
            location = item.location
            size = item.size
            self.log(f"商品 {item_index + 1} 位置: {location}, 尺寸: {size}")

            # 模拟真实用户行为序列
            self.simulate_reading_behavior()  # 模拟阅读
            self.add_random_delays()  # 随机延迟

            # 记录点击前的窗口句柄
            initial_handles = self.driver.window_handles
            initial_tabs = len(initial_handles)
            self.log(f"点击前标签页数量: {initial_tabs}")
            self.log(f"点击前窗口句柄: {initial_handles}")

            # 定义多种点击策略
            click_strategies = [
                ("ActionChains点击", self._click_with_action_chains),
                ("查找内部链接点击", self._click_internal_link),
                ("JavaScript点击", self._click_with_javascript),
                ("中键点击（新标签页）", self._click_with_middle_button),
                ("Ctrl+点击（新标签页）", self._click_with_ctrl),
                ("右键+新标签页", self._right_click_new_tab)
            ]

            for strategy_name, strategy_func in click_strategies:
                try:
                    self.log(f"尝试策略: {strategy_name}")

                    # 执行点击策略
                    if strategy_func(item):
                        # 等待可能的新标签页打开
                        time.sleep(random.uniform(1, 2))

                        # 检查是否有新标签页打开
                        current_handles = self.driver.window_handles
                        current_tabs = len(current_handles)
                        self.log(f"点击后标签页数量: {current_tabs}")
                        self.log(f"点击后窗口句柄: {current_handles}")

                        if current_tabs > initial_tabs:
                            self.log(f"{strategy_name}成功，新标签页已打开")
                            click_success = True
                            break
                        else:
                            self.log(f"{strategy_name}执行成功但未打开新标签页，尝试下一种方式")
                            # 如果页面发生了跳转但没有新标签页，也算成功
                            if self.driver.current_url != self.config.get("target_url", ""):
                                self.log("检测到页面跳转，认为点击成功")
                                click_success = True
                                break
                    else:
                        self.log(f"{strategy_name}执行失败")

                except Exception as e:
                    self.log(f"{strategy_name}出错: {e}", "WARNING")
                    continue

            if not click_success:
                self.log(f"所有点击策略都失败，无法点击第 {item_index + 1} 个商品", "ERROR")

            if not click_success:
                self.log(f"所有点击方式都失败，跳过第 {item_index + 1} 个商品", "ERROR")
                return False

            self.log(f"已点击第 {item_index + 1} 个商品，等待页面响应...")
            time.sleep(2)

            # 等待新标签页打开
            if not self.switch_to_new_tab():
                self.log("未检测到新标签页，可能是页面内跳转", "WARNING")
                # 尝试在当前页面处理
                return self.process_product_in_current_tab()

            # 在新标签页中处理商品
            result = self.process_product_in_new_tab()

            # 关闭当前标签页并返回主页面
            self.close_current_tab_and_switch_back()

            return result

        except Exception as e:
            self.log(f"处理第 {item_index + 1} 个商品时出错: {e}", "ERROR")
            return False

    def process_product_in_new_tab(self):
        """在新标签页中处理商品"""
        try:
            # 等待页面加载
            if not self.wait_for_page_load():
                self.log("页面加载超时", "WARNING")

            # 模拟用户浏览商品页面的行为
            self.simulate_reading_behavior()
            self.add_random_delays()

            # 处理可能的弹窗
            self.handle_unexpected_popup()

            # 检查是否在iframe中
            self.check_iframe_and_switch()

            # 模拟用户查看商品详情的行为
            if random.random() < 0.6:  # 60%概率模拟浏览行为
                self.simulate_human_behavior()
                self.random_scroll()
                time.sleep(random.uniform(1, 3))

            # 查找并点击添加商品按钮（带重试）
            def find_and_click_add_button():
                # 确认当前在新标签页中
                current_url = self.driver.current_url
                self.log(f"当前在新标签页中，URL: {current_url}")

                # 首先尝试在主文档中查找
                self.driver.switch_to.default_content()
                self.log("已切换到主文档，开始查找立即铺货按钮...")

                add_button = self.find_add_product_button()

                if add_button == "already_distributed":
                    self.log("商品已铺货，跳过此商品")
                    return "already_distributed"

                if not add_button:
                    # 如果主文档中没有，尝试在iframe中查找
                    self.log("主文档中未找到立即铺货按钮，尝试切换到iframe...")
                    if self.check_iframe_and_switch():
                        self.log("已切换到iframe，重新查找立即铺货按钮...")
                        add_button = self.find_add_product_button()

                        if add_button == "already_distributed":
                            self.log("商品已铺货，跳过此商品")
                            return "already_distributed"

                if add_button:
                    self.log("找到立即铺货按钮，准备点击...")
                    # 添加点击前的人性化行为
                    self.simulate_reading_behavior()
                    time.sleep(random.uniform(0.5, 1.5))

                    if self.safe_click(add_button):
                        self.log("已点击立即铺货按钮，等待确认对话框...")
                        return True
                    else:
                        self.log("点击立即铺货按钮失败", "ERROR")
                else:
                    self.log("在新标签页的所有上下文中都未找到立即铺货按钮", "ERROR")
                    # 输出页面信息用于调试
                    page_title = self.driver.title
                    self.log(f"当前页面标题: {page_title}")

                return False

            add_button_result = self.retry_operation(find_and_click_add_button, max_retries=2)

            if add_button_result == "already_distributed":
                self.log("商品已铺货，跳过处理")
                return "already_distributed"
            elif not add_button_result:
                self.log("无法找到或点击添加商品按钮", "ERROR")
                return False

            # 等待确认对话框出现并点击确认（带重试）
            wait_time = random.uniform(2, 4)
            self.log(f"等待确认对话框出现，停顿 {wait_time:.1f} 秒")
            time.sleep(wait_time)

            def find_and_click_confirm_button():
                # 处理可能的新弹窗
                self.handle_unexpected_popup()

                confirm_button = self.find_confirm_button()
                if confirm_button:
                    # 模拟用户确认前的思考时间
                    time.sleep(random.uniform(0.3, 1.0))

                    if self.safe_click(confirm_button):
                        self.log("商品添加成功")
                        return True
                return False

            if not self.retry_operation(find_and_click_confirm_button, max_retries=2):
                self.log("无法找到或点击确认按钮", "ERROR")
                return False

            # 等待操作完成，模拟用户查看结果
            completion_time = random.uniform(1.5, 3.0)
            self.log(f"等待操作完成，停顿 {completion_time:.1f} 秒")
            time.sleep(completion_time)

            return True

        except Exception as e:
            self.log(f"在新标签页处理商品时出错: {e}", "ERROR")
            return False

    def process_product_in_current_tab(self):
        """在当前标签页中处理商品（如果没有新标签页打开）"""
        try:
            # 等待页面加载
            time.sleep(3)

            # 检查是否在iframe中
            self.check_iframe_and_switch()

            # 查找并点击添加商品按钮
            add_button = self.find_add_product_button()

            if not add_button:
                self.log("未找到添加商品按钮", "ERROR")
                # 尝试返回主页面
                self.driver.back()
                time.sleep(2)
                return False

            if not self.safe_click(add_button):
                self.log("点击添加商品按钮失败", "ERROR")
                self.driver.back()
                time.sleep(2)
                return False

            self.log("已点击添加商品按钮，等待确认对话框...")

            # 等待确认对话框出现并点击确认
            time.sleep(3)
            confirm_button = self.find_confirm_button()

            if not confirm_button:
                self.log("未找到确认按钮", "ERROR")
                self.driver.back()
                time.sleep(2)
                return False

            if not self.safe_click(confirm_button):
                self.log("点击确认按钮失败", "ERROR")
                self.driver.back()
                time.sleep(2)
                return False

            self.log("商品添加成功，返回主页面")

            # 返回主页面
            self.driver.back()
            time.sleep(2)

            return True

        except Exception as e:
            self.log(f"在当前标签页处理商品时出错: {e}", "ERROR")
            # 尝试返回主页面
            try:
                self.driver.back()
                time.sleep(2)
            except:
                pass
            return False

    def perform_batch_management(self):
        """执行批量管理操作"""
        try:
            self.log("开始执行批量管理操作...")

            # 导航到管理页面
            manage_url = self.config.get("manage_url")
            if not manage_url:
                self.log("管理页面URL未配置", "ERROR")
                return False

            self.log(f"正在导航到管理页面: {manage_url}")
            if not self.safe_navigate(manage_url):
                self.log("导航到管理页面失败", "ERROR")
                return False

            # 处理可能的弹窗
            self.handle_unexpected_popup()

            # 查找并点击全选复选框（带重试）
            def click_select_all():
                select_all_xpath = self.config["xpaths"]["select_all_checkbox"]
                select_all_checkbox = self.wait_for_clickable_element(select_all_xpath, timeout=15)

                if select_all_checkbox and self.safe_click(select_all_checkbox):
                    self.log("已点击全选复选框")
                    return True
                return False

            if not self.retry_operation(click_select_all, max_retries=2):
                self.log("无法点击全选复选框", "ERROR")
                return False

            # 等待1-3秒
            wait_time = random.uniform(1, 3)
            self.log(f"等待 {wait_time:.1f} 秒")
            time.sleep(wait_time)

            # 查找并点击批量编辑按钮（带重试）
            def click_batch_edit():
                batch_edit_xpath = self.config["xpaths"]["batch_edit_button"]
                batch_edit_button = self.wait_for_clickable_element(batch_edit_xpath, timeout=10)

                if batch_edit_button and self.safe_click(batch_edit_button):
                    self.log("已点击批量编辑按钮")
                    return True
                return False

            if not self.retry_operation(click_batch_edit, max_retries=2):
                self.log("无法点击批量编辑按钮", "ERROR")
                return False

            # 等待1-3秒
            wait_time = random.uniform(1, 3)
            self.log(f"等待 {wait_time:.1f} 秒")
            time.sleep(wait_time)

            # 处理可能的弹窗
            self.handle_unexpected_popup()

            # 查找并点击最终确认按钮（带重试）
            def click_final_confirm():
                final_confirm_xpath = self.config["xpaths"]["final_confirm_button"]
                final_confirm_button = self.wait_for_clickable_element(final_confirm_xpath, timeout=10)

                if final_confirm_button and self.safe_click(final_confirm_button):
                    self.log("批量管理操作完成")
                    return True
                return False

            if not self.retry_operation(click_final_confirm, max_retries=2):
                self.log("无法点击最终确认按钮", "ERROR")
                return False

            # 等待操作完成
            time.sleep(3)

            return True

        except Exception as e:
            self.log(f"执行批量管理操作时出错: {e}", "ERROR")
            return False

    def return_to_main_page(self, target_url=None):
        """返回到主页面"""
        try:
            if not target_url:
                target_url = self.config.get("target_url")
            if not target_url:
                self.log("目标URL未配置", "ERROR")
                return False

            # 检查当前页面状态
            current_url = self.driver.current_url
            self.log(f"当前页面URL: {current_url}")

            # 如果当前在登录页面，先尝试点击登录按钮
            if 'login' in current_url.lower():
                self.log("检测到当前在登录页面，尝试自动点击登录按钮")
                if self.auto_click_login_button():
                    self.log("登录按钮点击成功，等待页面跳转...")
                    time.sleep(3)
                    # 检查是否跳转成功
                    new_url = self.driver.current_url
                    if 'login' not in new_url.lower():
                        self.log("已成功跳转离开登录页面")
                    else:
                        self.log("仍在登录页面，可能需要手动处理", "WARNING")
                else:
                    self.log("自动点击登录按钮失败", "WARNING")

            # 导航到目标页面
            self.log("正在导航到主页面...")
            self.driver.get(target_url)

            # 等待页面加载
            time.sleep(3)

            self.log("已返回主页面")
            return True

        except Exception as e:
            self.log(f"返回主页面时出错: {e}", "ERROR")
            return False

    def auto_click_login_button(self):
        """自动点击登录按钮"""
        try:
            # 尝试多种登录按钮选择器
            login_selectors = [
                "//*[@id='login-form']/div[6]/button",  # 原始选择器
                "//button[contains(text(), '登录') or contains(text(), '登陆')]",
                "//button[@type='submit']",
                "//input[@type='submit']",
                "//*[contains(@class, 'login-btn') or contains(@class, 'submit-btn')]//button",
                "//form//button[last()]",  # 表单中的最后一个按钮
                "//button[contains(@class, 'btn-primary')]",
                "//a[contains(text(), '登录') or contains(text(), '登陆')]"
            ]

            login_btn = None
            used_selector = None

            for selector in login_selectors:
                try:
                    login_btn = self.driver.find_element(By.XPATH, selector)
                    if login_btn.is_displayed() and login_btn.is_enabled():
                        used_selector = selector
                        self.log(f"找到登录按钮，使用选择器: {selector}")
                        break
                except:
                    continue

            if login_btn and login_btn.is_displayed() and login_btn.is_enabled():
                # 滚动到按钮位置
                self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", login_btn)
                time.sleep(1)

                # 获取按钮信息用于调试
                try:
                    button_text = login_btn.text.strip()
                    button_tag = login_btn.tag_name
                    self.log(f"准备点击登录按钮: 标签={button_tag}, 文本='{button_text}', 选择器={used_selector}")
                except:
                    pass

                # 使用safe_click方法点击登录按钮
                if self.safe_click(login_btn):
                    self.log("登录按钮点击成功")
                    return True
                else:
                    self.log("safe_click方法点击登录按钮失败", "WARNING")
                    return False
            else:
                self.log("未找到可用的登录按钮", "WARNING")
                return False

        except Exception as e:
            self.log(f"自动点击登录按钮时出错: {e}", "ERROR")
            return False

    def get_account_name(self):
        """获取账号名称"""
        try:
            # 跳转到个人中心页面
            personal_center_url = "https://jingya.taobao.com/?v=2"
            self.log(f"正在跳转到个人中心页面: {personal_center_url}")
            self.driver.get(personal_center_url)

            # 等待页面加载
            time.sleep(3)

            # 获取账号名称
            account_xpath = "//*[@id='workbench-user-tool-btn']/div/div[2]"
            self.log(f"尝试获取账号名称，使用XPath: {account_xpath}")

            # 等待元素出现
            account_element = self.wait_for_element(account_xpath, timeout=10)
            if account_element:
                account_text = account_element.text.strip()
                self.log(f"成功获取账号信息: {account_text}")
                return account_text
            else:
                self.log("未找到账号信息元素", "WARNING")
                return None

        except Exception as e:
            self.log(f"获取账号名称时出错: {e}", "ERROR")
            return None

    def crawl_all_products(self, max_products=1000, gui_callback=None, target_url=None):
        """爬取所有页面的商品信息"""
        try:
            all_products = []
            page_num = 1

            # 限制最大爬取数量
            # 使用传入的URL或配置文件中的URL
            if not target_url:
                target_url = self.config.get("target_url")
            if not target_url:
                self.log("目标URL未配置", "ERROR")
                return []

            self.log(f"开始爬取商品信息，最大数量: {max_products}")
            self.log(f"使用目标URL: {target_url}")
            self.driver.get(target_url)
            time.sleep(3)

            while len(all_products) < max_products:
                self.log(f"正在爬取第 {page_num} 页商品信息...")

                # 等待页面加载
                time.sleep(2)

                # 爬取当前页面的商品
                page_products = self.crawl_current_page_products(
                    max_products - len(all_products),  # 剩余需要的商品数量
                    gui_callback  # GUI回调函数
                )
                if not page_products:
                    self.log(f"第 {page_num} 页未找到商品，停止爬取")
                    break

                all_products.extend(page_products)
                self.log(f"第 {page_num} 页找到 {len(page_products)} 个商品，总计: {len(all_products)}")

                # 如果已达到最大数量，停止爬取
                if len(all_products) >= max_products:
                    self.log(f"已达到最大商品数量 {max_products}，停止爬取")
                    break

                # 尝试翻页
                if not self.go_to_next_page():
                    self.log("已到达最后一页或翻页失败")
                    break

                page_num += 1

                # 防止无限循环，最多爬取10页
                if page_num > 50:
                    self.log("已爬取50页，停止爬取")
                    break

            # 限制返回的商品数量
            all_products = all_products[:max_products]
            self.log(f"爬取完成，共找到 {len(all_products)} 个商品")
            return all_products

        except Exception as e:
            self.log(f"爬取商品信息时出错: {e}", "ERROR")
            return []

    def crawl_current_page_products(self, max_count=40, gui_callback=None):
        """爬取当前页面的商品信息"""
        try:
            products = []

            # 查找商品容器 - 使用更新后的XPath
            container_xpath = "//*[@id='root']/div[2]/div/div/div/div[5]/div[1]"
            container = self.wait_for_element(container_xpath, timeout=10)
            if not container:
                self.log("未找到商品容器")
                return []

            self.log(f"找到商品容器，开始爬取商品，最大数量: {max_count}")

            # 查找所有商品项 - 使用更新后的XPath格式
            for i in range(1, min(41, max_count + 1)):  # 限制爬取数量
                # 使用更新后的XPath格式定位商品：第i个商品位于div[5]/div[1]/div[i]
                item_xpath = f"//*[@id='root']/div[2]/div/div/div/div[5]/div[1]/div[{i}]"
                try:
                    # 直接定位到商品元素，这个元素本身就应该是tfx-item
                    tfx_item = self.driver.find_element(By.XPATH, item_xpath)
                    if not tfx_item.is_displayed():
                        self.log(f"商品项 {i} 不可见，跳过")
                        continue

                    self.log(f"找到商品项 {i}，检查元素信息...")

                    # 输出元素的基本信息
                    element_tag = tfx_item.tag_name
                    element_class = tfx_item.get_attribute("class")
                    has_data_autolog = tfx_item.get_attribute("data-autolog") is not None
                    self.log(f"商品项 {i} - 元素标签: {element_tag}, class: {element_class}, 有data-autolog: {has_data_autolog}")

                    # 如果这个元素没有data-autolog，可能需要查找子元素
                    if not has_data_autolog:
                        self.log(f"商品项 {i} 当前元素无data-autolog，查找子元素...")
                        try:
                            # 查找包含data-autolog的子元素
                            tfx_item = tfx_item.find_element(By.XPATH, ".//*[@data-autolog]")
                            element_class = tfx_item.get_attribute("class")
                            self.log(f"商品项 {i} 找到子元素，class: {element_class}")
                        except:
                            self.log(f"商品项 {i} 未找到包含data-autolog的子元素，跳过")
                            continue

                    # 提取item_id - 尝试多种方式获取data-autolog
                    data_autolog = None

                    # 方式1: 直接获取data-autolog属性
                    data_autolog = tfx_item.get_attribute("data-autolog")

                    # 方式2: 如果没有，尝试获取outerHTML然后解析
                    if not data_autolog:
                        try:
                            outer_html = tfx_item.get_attribute("outerHTML")
                            self.log(f"商品项 {i} outerHTML长度: {len(outer_html) if outer_html else 0}")

                            # 输出outerHTML的前200个字符用于调试
                            if outer_html:
                                preview = outer_html[:200] + "..." if len(outer_html) > 200 else outer_html
                                self.log(f"商品项 {i} outerHTML预览: {preview}")

                            if outer_html and 'data-autolog=' in outer_html:
                                import re
                                match = re.search(r'data-autolog="([^"]*)"', outer_html)
                                if match:
                                    data_autolog = match.group(1)
                                    # 处理HTML转义字符
                                    data_autolog = data_autolog.replace('&amp;', '&')
                                    self.log(f"商品项 {i} 从outerHTML获取到data-autolog: {data_autolog}")
                                else:
                                    self.log(f"商品项 {i} outerHTML中未找到data-autolog匹配")
                                    # 输出更多调试信息
                                    if 'data-autolog' in outer_html:
                                        self.log(f"商品项 {i} outerHTML包含'data-autolog'但正则匹配失败")
                            else:
                                self.log(f"商品项 {i} outerHTML中不包含data-autolog")
                        except Exception as e:
                            self.log(f"从outerHTML获取data-autolog失败: {e}")
                    else:
                        self.log(f"商品项 {i} 直接获取到data-autolog: {data_autolog}")

                    if not data_autolog:
                        self.log(f"商品项 {i} 未找到data-autolog属性，跳过")
                        continue

                    self.log(f"商品项 {i} data-autolog: {data_autolog}")

                    # 从data-autolog中提取item_id
                    import re
                    match = re.search(r'item_id=(\d+)', data_autolog)
                    if not match:
                        self.log(f"商品项 {i} 未能从data-autolog中提取item_id，跳过")
                        continue

                    item_id = match.group(1)
                    self.log(f"商品项 {i} 提取到item_id: {item_id}")

                    # 提取商品名称
                    try:
                        name_element = tfx_item.find_element(By.XPATH, ".//span[@style='vertical-align: middle;']")
                        product_name = name_element.text.strip() if name_element else f"商品{item_id}"
                    except:
                        product_name = f"商品{item_id}"

                    self.log(f"商品项 {i} 商品名称: {product_name}")

                    product_info = {
                        'item_id': item_id,
                        'name': product_name
                    }
                    products.append(product_info)

                    # 立即调用GUI回调，实时显示商品
                    if gui_callback:
                        try:
                            gui_callback(item_id, product_name)
                        except Exception as e:
                            self.log(f"GUI回调失败: {e}")

                    # 如果已达到最大数量，停止爬取
                    if len(products) >= max_count:
                        self.log(f"已爬取到 {max_count} 个商品，停止当前页面爬取")
                        break

                except Exception as e:
                    self.log(f"处理商品项 {i} 时出错: {e}")
                    # 如果连续几个商品都找不到，可能已经到了最后
                    if i > 5:  # 至少尝试前5个位置
                        break
                    continue

            self.log(f"当前页面爬取完成，找到 {len(products)} 个商品")
            return products

        except Exception as e:
            self.log(f"爬取当前页面商品时出错: {e}", "ERROR")
            return []

    def process_product_by_id(self, item_id):
        """通过商品ID直接处理商品"""
        try:
            # 构建商品详情页面URL
            detail_url = f"https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId={item_id}"

            self.log(f"正在访问商品详情页面: {detail_url}")
            self.driver.get(detail_url)

            # 等待页面加载
            time.sleep(3)

            # 等待页面完全加载
            if not self.wait_for_page_load():
                self.log("商品详情页面加载超时", "WARNING")

            # 模拟用户浏览商品页面的行为
            self.simulate_reading_behavior()
            self.add_random_delays()

            # 处理可能的弹窗
            self.handle_unexpected_popup()

            # 尝试铺货流程（最多重试一次）
            max_retries = 2
            for attempt in range(max_retries):
                self.log(f"开始第 {attempt + 1} 次铺货尝试...")

                # 查找并点击铺货按钮
                add_button = self.find_add_product_button()
                if add_button == "already_distributed":
                    return "already_distributed"
                elif add_button:
                    if self.safe_click(add_button):
                        self.log("铺货按钮点击成功")
                        time.sleep(2)

                        # 查找并点击确认按钮
                        confirm_button = self.find_confirm_button()
                        if confirm_button and self.safe_click(confirm_button):
                            self.log("确认按钮点击成功")
                            time.sleep(2)

                            # 检查铺货是否真正成功：检查span是否变为"已铺货"
                            result = self.verify_product_distributed()
                            if result:
                                return True
                            else:
                                self.log("铺货验证失败，检查是否出现滑块验证")
                                # 检测并处理滑块验证
                                verification_result = self.detect_and_handle_slider_verification()
                                if verification_result == "manual_verification_required":
                                    self.log("需要手动完成滑块验证")
                                    return "manual_verification_required"
                                elif verification_result:
                                    self.log("滑块验证处理成功，准备重试铺货")
                                    if attempt < max_retries - 1:  # 不是最后一次尝试
                                        time.sleep(2)
                                        continue  # 重试
                                    else:
                                        self.log("已达到最大重试次数")
                                        return False
                                else:
                                    self.log("未检测到滑块验证或处理失败")
                                    return False
                        else:
                            self.log("确认按钮点击失败", "WARNING")
                            if attempt < max_retries - 1:
                                # 检测滑块验证
                                verification_result = self.detect_and_handle_slider_verification()
                                if verification_result == "manual_verification_required":
                                    return "manual_verification_required"
                                elif verification_result:
                                    self.log("滑块验证处理成功，准备重试")
                                    time.sleep(2)
                                    continue
                            return False
                    else:
                        self.log("铺货按钮点击失败", "ERROR")
                        if attempt < max_retries - 1:
                            # 检测滑块验证
                            verification_result = self.detect_and_handle_slider_verification()
                            if verification_result == "manual_verification_required":
                                return "manual_verification_required"
                            elif verification_result:
                                self.log("滑块验证处理成功，准备重试")
                                time.sleep(2)
                                continue
                        return False
                else:
                    self.log("未找到铺货按钮", "ERROR")
                    return False

            # 所有重试都失败
            self.log("所有铺货尝试都失败，跳过此商品")
            return False

        except Exception as e:
            self.log(f"处理商品 {item_id} 时出错: {e}", "ERROR")
            return False

    def go_to_next_page(self):
        """翻到下一页"""
        try:
            # 使用固定的XPath定位下一页按钮
            next_button_xpath = "//*[@id='root']/div[2]/div/div/div/div[5]/div[2]/div/div[2]/button[2]"

            try:
                next_button = self.driver.find_element(By.XPATH, next_button_xpath)
                if next_button.is_displayed() and next_button.is_enabled():
                    # 检查按钮是否可点击（不是禁用状态）
                    button_class = next_button.get_attribute("class") or ""
                    button_text = next_button.text.strip()

                    self.log(f"找到下一页按钮: text='{button_text}', class='{button_class}'")

                    if "disabled" not in button_class.lower():
                        self.log("下一页按钮可点击，准备点击")
                        if self.safe_click(next_button):
                            self.log("成功点击下一页按钮")
                            time.sleep(3)  # 等待页面加载
                            return True
                        else:
                            self.log("点击下一页按钮失败")
                            return False
                    else:
                        self.log("下一页按钮已禁用，已到最后一页")
                        return False
                else:
                    self.log("下一页按钮不可见或不可用")
                    return False

            except Exception as e:
                self.log(f"未找到下一页按钮: {e}")
                return False

        except Exception as e:
            self.log(f"翻页时出错: {e}", "ERROR")
            return False

    def find_confirm_button(self):
        """查找确认按钮 - 仅使用配置的XPath"""
        try:
            confirm_button_xpath = self.config["xpaths"]["confirm_button"]
            confirm_button = self.wait_for_clickable_element(confirm_button_xpath, timeout=5)
            if confirm_button:
                self.log("使用配置的XPath找到确认按钮")
                return confirm_button
            else:
                self.log("使用配置的XPath未找到确认按钮", "WARNING")
                return None
        except Exception as e:
            self.log(f"查找确认按钮时出错: {e}", "ERROR")
            return None

    def find_add_product_button(self):
        """查找添加商品按钮 - 使用配置的XPath并检查span元素"""
        try:
            add_button_xpath = self.config["xpaths"]["add_product_button"]
            span_xpath = add_button_xpath + "/span"

            self.log(f"尝试使用配置的XPath: {add_button_xpath}")

            # 先检查span元素的innerHTML
            self.log(f"尝试点击按钮之前检查span: {span_xpath}")
            try:
                span_element = self.driver.find_element(By.XPATH, span_xpath)
                if span_element:
                    span_text = span_element.get_attribute("innerHTML").strip()
                    self.log(f"span innerHTML: {span_text}")

                    if span_text == "已铺货":
                        self.log("检测到'已铺货'，跳过此商品")
                        return "already_distributed"
            except Exception as e:
                self.log(f"检查span元素失败: {e}")

            # 查找按钮元素
            add_button = self.wait_for_clickable_element(add_button_xpath, timeout=10)
            if add_button:
                button_text = add_button.text.strip()
                self.log(f"找到按钮，文本内容: '{button_text}'")

                # 双重检查：如果按钮文本也包含"已铺货"
                if self.is_already_distributed(button_text):
                    self.log("检测到商品已铺货（按钮文本），跳过此商品")
                    return "already_distributed"

                self.log("使用配置的XPath找到添加商品按钮")
                return add_button
            else:
                self.log("使用配置的XPath未找到添加商品按钮", "WARNING")
                return None

        except Exception as e:
            self.log(f"查找添加商品按钮时出错: {e}", "ERROR")
            return None

    def verify_product_distributed(self):
        """验证商品是否成功铺货：检查span是否变为'已铺货'"""
        try:
            add_button_xpath = self.config["xpaths"]["add_product_button"]
            span_xpath = add_button_xpath + "/span"

            self.log("验证铺货是否成功，检查span元素状态...")

            # 等待一段时间让页面更新
            max_wait_time = 10  # 最多等待10秒
            check_interval = 1   # 每1秒检查一次

            for i in range(max_wait_time):
                try:
                    span_element = self.driver.find_element(By.XPATH, span_xpath)
                    if span_element:
                        span_text = span_element.get_attribute("innerHTML").strip()
                        self.log(f"第{i+1}次检查 - span innerHTML: {span_text}")

                        if span_text == "已铺货":
                            self.log("验证成功：span已变为'已铺货'，商品铺货成功")
                            return True
                        elif span_text == "立即铺货" or span_text == "铺货":
                            self.log(f"span仍为'{span_text}'，继续等待...")
                            time.sleep(check_interval)
                            continue
                        else:
                            self.log(f"span显示未知状态: '{span_text}'，继续等待...")
                            time.sleep(check_interval)
                            continue
                    else:
                        self.log(f"第{i+1}次检查 - 未找到span元素")
                        time.sleep(check_interval)
                        continue

                except Exception as e:
                    self.log(f"第{i+1}次检查span时出错: {e}")
                    time.sleep(check_interval)
                    continue

            # 超时后最后一次检查
            self.log("等待超时，进行最后一次检查...")
            try:
                span_element = self.driver.find_element(By.XPATH, span_xpath)
                if span_element:
                    final_span_text = span_element.get_attribute("innerHTML").strip()
                    self.log(f"最终检查 - span innerHTML: {final_span_text}")

                    if final_span_text == "已铺货":
                        self.log("最终验证成功：商品铺货成功")
                        return True
                    else:
                        self.log(f"最终验证失败：span未变为'已铺货'，当前为'{final_span_text}'")
                        return False
                else:
                    self.log("最终检查未找到span元素")
                    return False
            except Exception as e:
                self.log(f"最终检查时出错: {e}")
                return False

        except Exception as e:
            self.log(f"验证商品铺货状态时出错: {e}", "ERROR")
            return False

    def detect_and_handle_slider_verification(self):
        """检测并处理淘宝滑块验证（考虑iframe）"""
        try:
            slider_xpath = "//*[@id='nc_1_n1z']"
            self.log("检测是否出现滑块验证...")

            # 首先在主文档中检测
            try:
                self.driver.switch_to.default_content()
                slider_element = self.driver.find_element(By.XPATH, slider_xpath)
                if slider_element.is_displayed():
                    self.log("在主文档中检测到滑块验证，开始处理...")
                    return self.handle_slider_verification(slider_element)
            except:
                pass

            # 在iframe中检测
            try:
                self.log("在主文档中未找到滑块，检查iframe...")
                iframes = self.driver.find_elements(By.TAG_NAME, "iframe")
                self.log(f"找到 {len(iframes)} 个iframe")

                for i, iframe in enumerate(iframes):
                    try:
                        self.log(f"检查第 {i+1} 个iframe...")
                        self.driver.switch_to.frame(iframe)

                        slider_element = self.driver.find_element(By.XPATH, slider_xpath)
                        if slider_element.is_displayed():
                            self.log(f"在第 {i+1} 个iframe中检测到滑块验证，开始处理...")
                            result = self.handle_slider_verification(slider_element)
                            # 处理完成后切回主文档
                            self.driver.switch_to.default_content()
                            return result
                    except:
                        # 切回主文档，继续检查下一个iframe
                        self.driver.switch_to.default_content()
                        continue

                self.log("在所有iframe中都未检测到滑块验证")
                return False

            except Exception as e:
                self.log(f"检查iframe时出错: {e}")
                # 确保切回主文档
                self.driver.switch_to.default_content()
                return False

        except Exception as e:
            self.log(f"检测滑块验证时出错: {e}", "ERROR")
            # 确保切回主文档
            try:
                self.driver.switch_to.default_content()
            except:
                pass
            return False

    def handle_slider_verification(self, slider_element):
        """处理滑块验证 - 手动处理模式"""
        try:
            self.log("检测到滑块验证，切换为手动处理模式...")

            # 新增：通知GUI界面前置
            if self.log_callback and hasattr(self.log_callback, '__self__'):
                gui_instance = self.log_callback.__self__
                if hasattr(gui_instance, 'bring_to_front'):
                    gui_instance.bring_to_front()

            # 如果当前是无头模式，需要重新启动有界面模式的浏览器
            try:
                # 保存当前页面URL和cookies
                current_url = self.driver.current_url
                cookies = self.driver.get_cookies()
                self.log(f"保存当前页面状态: {current_url}")

                # 关闭无头模式浏览器
                self.driver.quit()
                self.log("关闭无头模式浏览器")

                # 重新启动有界面模式浏览器
                self.init_driver(headless=False)
                self.log("启动有界面模式浏览器")

                # 恢复页面状态
                self.driver.get(current_url)
                for cookie in cookies:
                    try:
                        self.driver.add_cookie(cookie)
                    except:
                        pass

                # 刷新页面以应用cookies
                self.driver.refresh()
                time.sleep(2)

                self.log("浏览器已切换为有界面模式，页面状态已恢复")

                # 确保浏览器窗口置于前台并最大化
                self.driver.switch_to.window(self.driver.current_window_handle)
                self.driver.maximize_window()

            except Exception as e:
                self.log(f"切换浏览器模式失败: {e}")
                # 如果切换失败，至少尝试置前台
                try:
                    self.driver.switch_to.window(self.driver.current_window_handle)
                    self.driver.maximize_window()
                except:
                    pass

            # 返回特殊标识，表示需要手动处理
            return "manual_verification_required"

        except Exception as e:
            self.log(f"处理滑块验证时出错: {e}", "ERROR")
            return False



    def wait_for_page_load(self, timeout=30):
        """等待页面完全加载"""
        try:
            # 等待页面加载完成
            WebDriverWait(self.driver, timeout).until(
                lambda driver: driver.execute_script("return document.readyState") == "complete"
            )
            self.log("页面加载完成")
            return True
        except TimeoutException:
            self.log("页面加载超时", "WARNING")
            return False
        except Exception as e:
            self.log(f"等待页面加载失败: {e}", "ERROR")
            return False

    def check_page_error(self):
        """检查页面是否有错误"""
        try:
            # 检查页面标题是否包含错误信息
            page_title = self.driver.title.lower()
            if any(error in page_title for error in ["404", "500", "错误", "error"]):
                self.log(f"页面标题包含错误信息: {page_title}", "ERROR")
                return True

            # 检查URL是否包含错误路径
            current_url = self.driver.current_url.lower()
            if any(error in current_url for error in ["/404", "/error", "/500"]):
                self.log(f"URL包含错误路径: {current_url}", "ERROR")
                return True

            # 检查是否有明显的错误页面元素
            try:
                error_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), '页面不存在') or contains(text(), '404') or contains(text(), '服务器错误')]")
                if error_elements and len(error_elements) > 0:
                    for element in error_elements:
                        if element.is_displayed():
                            self.log(f"发现错误页面元素: {element.text[:50]}", "ERROR")
                            return True
            except:
                pass

            return False

        except Exception as e:
            self.log(f"检查页面错误失败: {e}", "WARNING")
            return False

    def retry_operation(self, operation, max_retries=3, delay=2):
        """重试操作"""
        for attempt in range(max_retries):
            try:
                result = operation()
                if result:
                    return result
                else:
                    if attempt < max_retries - 1:
                        self.log(f"操作失败，{delay}秒后重试 (尝试 {attempt + 1}/{max_retries})")
                        time.sleep(delay)
                    else:
                        self.log(f"操作失败，已达到最大重试次数 ({max_retries})", "ERROR")
            except Exception as e:
                if attempt < max_retries - 1:
                    self.log(f"操作出错: {e}，{delay}秒后重试 (尝试 {attempt + 1}/{max_retries})", "WARNING")
                    time.sleep(delay)
                else:
                    self.log(f"操作出错: {e}，已达到最大重试次数 ({max_retries})", "ERROR")

        return False

    def safe_navigate(self, url, max_retries=3):
        """安全导航到URL"""
        def navigate():
            try:
                self.driver.get(url)
                if self.wait_for_page_load():
                    # 简单检查页面是否加载成功
                    if self.driver.current_url and len(self.driver.page_source) > 1000:
                        return True
                return False
            except Exception as e:
                self.log(f"导航失败: {e}", "ERROR")
                return False

        return self.retry_operation(navigate, max_retries)

    def handle_unexpected_popup(self):
        """处理意外弹窗"""
        try:
            # 检查是否有alert
            try:
                alert = self.driver.switch_to.alert
                alert_text = alert.text
                self.log(f"检测到alert弹窗: {alert_text}")
                alert.accept()
                self.log("已关闭alert弹窗")
                return True
            except:
                pass

            # 检查常见的弹窗关闭按钮
            close_selectors = [
                "//button[contains(@class, 'close')]",
                "//span[contains(@class, 'close')]",
                "//div[contains(@class, 'close')]",
                "//*[text()='×']",
                "//*[text()='关闭']",
                "//*[text()='取消']"
            ]

            for selector in close_selectors:
                try:
                    close_button = self.driver.find_element(By.XPATH, selector)
                    if close_button.is_displayed():
                        self.safe_click(close_button)
                        self.log(f"已关闭弹窗: {selector}")
                        return True
                except:
                    continue

            return False

        except Exception as e:
            self.log(f"处理弹窗失败: {e}", "WARNING")
            return False

    def process_products_with_pagination(self, target_count):
        """支持翻页的批量商品处理"""
        try:
            total_success = 0
            current_page = 1
            max_pages = 50  # 最大翻页数，防止无限循环

            self.log(f"开始批量处理商品，目标数量: {target_count}")

            while total_success < target_count and current_page <= max_pages:
                self.log(f"=== 处理第 {current_page} 页 ===")

                # 等待页面加载
                self.wait_for_page_load()
                time.sleep(2)

                # 获取当前页面的商品列表
                items = self.get_product_list_items()
                if not items:
                    self.log("当前页面没有商品，可能已到最后一页", "WARNING")
                    break

                self.log(f"当前页面找到 {len(items)} 个商品")

                # 处理当前页面的商品
                page_success = 0
                for item_index in range(len(items)):
                    if total_success >= target_count:
                        self.log(f"已达到目标数量 {target_count}，停止处理")
                        break

                    try:
                        result = self.process_single_product(item_index, len(items))

                        if result == "already_distributed":
                            self.log(f"第 {item_index + 1} 个商品已铺货，跳过")
                        elif result:
                            page_success += 1
                            total_success += 1
                            self.log(f"成功处理第 {item_index + 1} 个商品，总成功数: {total_success}")
                        else:
                            self.log(f"处理第 {item_index + 1} 个商品失败")

                        # 添加随机延迟
                        self.add_random_delays()

                    except Exception as e:
                        self.log(f"处理商品 {item_index + 1} 时出错: {e}", "ERROR")
                        continue

                self.log(f"第 {current_page} 页处理完成，成功: {page_success}，总成功: {total_success}")

                # 如果已达到目标数量，停止处理
                if total_success >= target_count:
                    self.log(f"已达到目标数量 {target_count}，处理完成")
                    break

                # 如果当前页面没有成功处理任何商品，可能需要翻页
                if page_success == 0 and len(items) > 0:
                    self.log("当前页面所有商品都已铺货或处理失败，尝试翻页")

                # 尝试翻到下一页
                self.log("尝试翻到下一页...")
                if self.go_to_next_page():
                    current_page += 1
                    self.log(f"成功翻到第 {current_page} 页")

                    # 等待新页面加载
                    time.sleep(random.uniform(2, 4))
                else:
                    self.log("无法翻到下一页，可能已到最后一页")
                    break

            # 处理完成总结
            if current_page > max_pages:
                self.log(f"已达到最大翻页数 {max_pages}，停止处理")

            self.log(f"批量处理完成！总共处理了 {current_page} 页，成功铺货 {total_success} 个商品")
            return total_success

        except Exception as e:
            self.log(f"批量处理商品时出错: {e}", "ERROR")
            return 0
