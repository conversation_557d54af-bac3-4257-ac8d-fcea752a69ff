2025-08-10 00:53:00,097 - INFO - 淘宝铺货自动化工具已启动
2025-08-10 00:53:00,097 - INFO - 请先登录淘宝账号，然后开始铺货任务
2025-08-10 00:53:03,644 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff7765de925+77845]
	GetHandleVerifier [0x0x7ff7765de980+77936]
	(No symbol) [0x0x7ff776399cda]
	(No symbol) [0x0x7ff7763a1679]
	(No symbol) [0x0x7ff7763a4a61]
	(No symbol) [0x0x7ff776441e4b]
	(No symbol) [0x0x7ff7764188ca]
	(No symbol) [0x0x7ff776440b07]
	(No symbol) [0x0x7ff7764186a3]
	(No symbol) [0x0x7ff7763e1791]
	(No symbol) [0x0x7ff7763e2523]
	GetHandleVerifier [0x0x7ff7768b683d+3059501]
	GetHandleVerifier [0x0x7ff7768b0bfd+3035885]
	GetHandleVerifier [0x0x7ff7768d03f0+3164896]
	GetHandleVerifier [0x0x7ff7765f8c2e+185118]
	GetHandleVerifier [0x0x7ff77660053f+216111]
	GetHandleVerifier [0x0x7ff7765e72d4+113092]
	GetHandleVerifier [0x0x7ff7765e7489+113529]
	GetHandleVerifier [0x0x7ff7765ce288+10616]
	BaseThreadInitThunk [0x0x7ffd037fe8d7+23]
	RtlUserThreadStart [0x0x7ffd04d1c34c+44]

2025-08-10 00:53:03,646 - INFO - 防检测机制设置完成
2025-08-10 00:53:03,647 - INFO - 浏览器驱动初始化成功
2025-08-10 00:53:03,655 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff7765de925+77845]
	GetHandleVerifier [0x0x7ff7765de980+77936]
	(No symbol) [0x0x7ff776399b0c]
	(No symbol) [0x0x7ff776455b46]
	(No symbol) [0x0x7ff7764188ca]
	(No symbol) [0x0x7ff776440b07]
	(No symbol) [0x0x7ff7764186a3]
	(No symbol) [0x0x7ff7763e1791]
	(No symbol) [0x0x7ff7763e2523]
	GetHandleVerifier [0x0x7ff7768b683d+3059501]
	GetHandleVerifier [0x0x7ff7768b0bfd+3035885]
	GetHandleVerifier [0x0x7ff7768d03f0+3164896]
	GetHandleVerifier [0x0x7ff7765f8c2e+185118]
	GetHandleVerifier [0x0x7ff77660053f+216111]
	GetHandleVerifier [0x0x7ff7765e72d4+113092]
	GetHandleVerifier [0x0x7ff7765e7489+113529]
	GetHandleVerifier [0x0x7ff7765ce288+10616]
	BaseThreadInitThunk [0x0x7ffd037fe8d7+23]
	RtlUserThreadStart [0x0x7ffd04d1c34c+44]

2025-08-10 00:53:03,661 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff7765de925+77845]
	GetHandleVerifier [0x0x7ff7765de980+77936]
	(No symbol) [0x0x7ff776399b0c]
	(No symbol) [0x0x7ff776455b46]
	(No symbol) [0x0x7ff7764188ca]
	(No symbol) [0x0x7ff776440b07]
	(No symbol) [0x0x7ff7764186a3]
	(No symbol) [0x0x7ff7763e1791]
	(No symbol) [0x0x7ff7763e2523]
	GetHandleVerifier [0x0x7ff7768b683d+3059501]
	GetHandleVerifier [0x0x7ff7768b0bfd+3035885]
	GetHandleVerifier [0x0x7ff7768d03f0+3164896]
	GetHandleVerifier [0x0x7ff7765f8c2e+185118]
	GetHandleVerifier [0x0x7ff77660053f+216111]
	GetHandleVerifier [0x0x7ff7765e72d4+113092]
	GetHandleVerifier [0x0x7ff7765e7489+113529]
	GetHandleVerifier [0x0x7ff7765ce288+10616]
	BaseThreadInitThunk [0x0x7ffd037fe8d7+23]
	RtlUserThreadStart [0x0x7ffd04d1c34c+44]

2025-08-10 00:53:03,669 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff7765de925+77845]
	GetHandleVerifier [0x0x7ff7765de980+77936]
	(No symbol) [0x0x7ff776399b0c]
	(No symbol) [0x0x7ff776455b46]
	(No symbol) [0x0x7ff7764188ca]
	(No symbol) [0x0x7ff776440b07]
	(No symbol) [0x0x7ff7764186a3]
	(No symbol) [0x0x7ff7763e1791]
	(No symbol) [0x0x7ff7763e2523]
	GetHandleVerifier [0x0x7ff7768b683d+3059501]
	GetHandleVerifier [0x0x7ff7768b0bfd+3035885]
	GetHandleVerifier [0x0x7ff7768d03f0+3164896]
	GetHandleVerifier [0x0x7ff7765f8c2e+185118]
	GetHandleVerifier [0x0x7ff77660053f+216111]
	GetHandleVerifier [0x0x7ff7765e72d4+113092]
	GetHandleVerifier [0x0x7ff7765e7489+113529]
	GetHandleVerifier [0x0x7ff7765ce288+10616]
	BaseThreadInitThunk [0x0x7ffd037fe8d7+23]
	RtlUserThreadStart [0x0x7ffd04d1c34c+44]

2025-08-10 00:53:03,680 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff7765de925+77845]
	GetHandleVerifier [0x0x7ff7765de980+77936]
	(No symbol) [0x0x7ff776399b0c]
	(No symbol) [0x0x7ff776455b46]
	(No symbol) [0x0x7ff7764188ca]
	(No symbol) [0x0x7ff776440b07]
	(No symbol) [0x0x7ff7764186a3]
	(No symbol) [0x0x7ff7763e1791]
	(No symbol) [0x0x7ff7763e2523]
	GetHandleVerifier [0x0x7ff7768b683d+3059501]
	GetHandleVerifier [0x0x7ff7768b0bfd+3035885]
	GetHandleVerifier [0x0x7ff7768d03f0+3164896]
	GetHandleVerifier [0x0x7ff7765f8c2e+185118]
	GetHandleVerifier [0x0x7ff77660053f+216111]
	GetHandleVerifier [0x0x7ff7765e72d4+113092]
	GetHandleVerifier [0x0x7ff7765e7489+113529]
	GetHandleVerifier [0x0x7ff7765ce288+10616]
	BaseThreadInitThunk [0x0x7ffd037fe8d7+23]
	RtlUserThreadStart [0x0x7ffd04d1c34c+44]

2025-08-10 00:53:03,686 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff7765de925+77845]
	GetHandleVerifier [0x0x7ff7765de980+77936]
	(No symbol) [0x0x7ff776399b0c]
	(No symbol) [0x0x7ff776455b46]
	(No symbol) [0x0x7ff7764188ca]
	(No symbol) [0x0x7ff776440b07]
	(No symbol) [0x0x7ff7764186a3]
	(No symbol) [0x0x7ff7763e1791]
	(No symbol) [0x0x7ff7763e2523]
	GetHandleVerifier [0x0x7ff7768b683d+3059501]
	GetHandleVerifier [0x0x7ff7768b0bfd+3035885]
	GetHandleVerifier [0x0x7ff7768d03f0+3164896]
	GetHandleVerifier [0x0x7ff7765f8c2e+185118]
	GetHandleVerifier [0x0x7ff77660053f+216111]
	GetHandleVerifier [0x0x7ff7765e72d4+113092]
	GetHandleVerifier [0x0x7ff7765e7489+113529]
	GetHandleVerifier [0x0x7ff7765ce288+10616]
	BaseThreadInitThunk [0x0x7ffd037fe8d7+23]
	RtlUserThreadStart [0x0x7ffd04d1c34c+44]

2025-08-10 00:53:03,692 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff7765de925+77845]
	GetHandleVerifier [0x0x7ff7765de980+77936]
	(No symbol) [0x0x7ff776399b0c]
	(No symbol) [0x0x7ff776455b46]
	(No symbol) [0x0x7ff7764188ca]
	(No symbol) [0x0x7ff776440b07]
	(No symbol) [0x0x7ff7764186a3]
	(No symbol) [0x0x7ff7763e1791]
	(No symbol) [0x0x7ff7763e2523]
	GetHandleVerifier [0x0x7ff7768b683d+3059501]
	GetHandleVerifier [0x0x7ff7768b0bfd+3035885]
	GetHandleVerifier [0x0x7ff7768d03f0+3164896]
	GetHandleVerifier [0x0x7ff7765f8c2e+185118]
	GetHandleVerifier [0x0x7ff77660053f+216111]
	GetHandleVerifier [0x0x7ff7765e72d4+113092]
	GetHandleVerifier [0x0x7ff7765e7489+113529]
	GetHandleVerifier [0x0x7ff7765ce288+10616]
	BaseThreadInitThunk [0x0x7ffd037fe8d7+23]
	RtlUserThreadStart [0x0x7ffd04d1c34c+44]

2025-08-10 00:53:03,697 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff7765de925+77845]
	GetHandleVerifier [0x0x7ff7765de980+77936]
	(No symbol) [0x0x7ff776399b0c]
	(No symbol) [0x0x7ff776455b46]
	(No symbol) [0x0x7ff7764188ca]
	(No symbol) [0x0x7ff776440b07]
	(No symbol) [0x0x7ff7764186a3]
	(No symbol) [0x0x7ff7763e1791]
	(No symbol) [0x0x7ff7763e2523]
	GetHandleVerifier [0x0x7ff7768b683d+3059501]
	GetHandleVerifier [0x0x7ff7768b0bfd+3035885]
	GetHandleVerifier [0x0x7ff7768d03f0+3164896]
	GetHandleVerifier [0x0x7ff7765f8c2e+185118]
	GetHandleVerifier [0x0x7ff77660053f+216111]
	GetHandleVerifier [0x0x7ff7765e72d4+113092]
	GetHandleVerifier [0x0x7ff7765e7489+113529]
	GetHandleVerifier [0x0x7ff7765ce288+10616]
	BaseThreadInitThunk [0x0x7ffd037fe8d7+23]
	RtlUserThreadStart [0x0x7ffd04d1c34c+44]

2025-08-10 00:53:03,705 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff7765de925+77845]
	GetHandleVerifier [0x0x7ff7765de980+77936]
	(No symbol) [0x0x7ff776399b0c]
	(No symbol) [0x0x7ff776455b46]
	(No symbol) [0x0x7ff7764188ca]
	(No symbol) [0x0x7ff776440b07]
	(No symbol) [0x0x7ff7764186a3]
	(No symbol) [0x0x7ff7763e1791]
	(No symbol) [0x0x7ff7763e2523]
	GetHandleVerifier [0x0x7ff7768b683d+3059501]
	GetHandleVerifier [0x0x7ff7768b0bfd+3035885]
	GetHandleVerifier [0x0x7ff7768d03f0+3164896]
	GetHandleVerifier [0x0x7ff7765f8c2e+185118]
	GetHandleVerifier [0x0x7ff77660053f+216111]
	GetHandleVerifier [0x0x7ff7765e72d4+113092]
	GetHandleVerifier [0x0x7ff7765e7489+113529]
	GetHandleVerifier [0x0x7ff7765ce288+10616]
	BaseThreadInitThunk [0x0x7ffd037fe8d7+23]
	RtlUserThreadStart [0x0x7ffd04d1c34c+44]

2025-08-10 00:53:03,711 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff7765de925+77845]
	GetHandleVerifier [0x0x7ff7765de980+77936]
	(No symbol) [0x0x7ff776399b0c]
	(No symbol) [0x0x7ff776455b46]
	(No symbol) [0x0x7ff7764188ca]
	(No symbol) [0x0x7ff776440b07]
	(No symbol) [0x0x7ff7764186a3]
	(No symbol) [0x0x7ff7763e1791]
	(No symbol) [0x0x7ff7763e2523]
	GetHandleVerifier [0x0x7ff7768b683d+3059501]
	GetHandleVerifier [0x0x7ff7768b0bfd+3035885]
	GetHandleVerifier [0x0x7ff7768d03f0+3164896]
	GetHandleVerifier [0x0x7ff7765f8c2e+185118]
	GetHandleVerifier [0x0x7ff77660053f+216111]
	GetHandleVerifier [0x0x7ff7765e72d4+113092]
	GetHandleVerifier [0x0x7ff7765e7489+113529]
	GetHandleVerifier [0x0x7ff7765ce288+10616]
	BaseThreadInitThunk [0x0x7ffd037fe8d7+23]
	RtlUserThreadStart [0x0x7ffd04d1c34c+44]

2025-08-10 00:53:03,719 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff7765de925+77845]
	GetHandleVerifier [0x0x7ff7765de980+77936]
	(No symbol) [0x0x7ff776399b0c]
	(No symbol) [0x0x7ff776455b46]
	(No symbol) [0x0x7ff7764188ca]
	(No symbol) [0x0x7ff776440b07]
	(No symbol) [0x0x7ff7764186a3]
	(No symbol) [0x0x7ff7763e1791]
	(No symbol) [0x0x7ff7763e2523]
	GetHandleVerifier [0x0x7ff7768b683d+3059501]
	GetHandleVerifier [0x0x7ff7768b0bfd+3035885]
	GetHandleVerifier [0x0x7ff7768d03f0+3164896]
	GetHandleVerifier [0x0x7ff7765f8c2e+185118]
	GetHandleVerifier [0x0x7ff77660053f+216111]
	GetHandleVerifier [0x0x7ff7765e72d4+113092]
	GetHandleVerifier [0x0x7ff7765e7489+113529]
	GetHandleVerifier [0x0x7ff7765ce288+10616]
	BaseThreadInitThunk [0x0x7ffd037fe8d7+23]
	RtlUserThreadStart [0x0x7ffd04d1c34c+44]

2025-08-10 00:53:03,726 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff7765de925+77845]
	GetHandleVerifier [0x0x7ff7765de980+77936]
	(No symbol) [0x0x7ff776399b0c]
	(No symbol) [0x0x7ff776455b46]
	(No symbol) [0x0x7ff7764188ca]
	(No symbol) [0x0x7ff776440b07]
	(No symbol) [0x0x7ff7764186a3]
	(No symbol) [0x0x7ff7763e1791]
	(No symbol) [0x0x7ff7763e2523]
	GetHandleVerifier [0x0x7ff7768b683d+3059501]
	GetHandleVerifier [0x0x7ff7768b0bfd+3035885]
	GetHandleVerifier [0x0x7ff7768d03f0+3164896]
	GetHandleVerifier [0x0x7ff7765f8c2e+185118]
	GetHandleVerifier [0x0x7ff77660053f+216111]
	GetHandleVerifier [0x0x7ff7765e72d4+113092]
	GetHandleVerifier [0x0x7ff7765e7489+113529]
	GetHandleVerifier [0x0x7ff7765ce288+10616]
	BaseThreadInitThunk [0x0x7ffd037fe8d7+23]
	RtlUserThreadStart [0x0x7ffd04d1c34c+44]

2025-08-10 00:53:03,733 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff7765de925+77845]
	GetHandleVerifier [0x0x7ff7765de980+77936]
	(No symbol) [0x0x7ff776399b0c]
	(No symbol) [0x0x7ff776455b46]
	(No symbol) [0x0x7ff7764188ca]
	(No symbol) [0x0x7ff776440b07]
	(No symbol) [0x0x7ff7764186a3]
	(No symbol) [0x0x7ff7763e1791]
	(No symbol) [0x0x7ff7763e2523]
	GetHandleVerifier [0x0x7ff7768b683d+3059501]
	GetHandleVerifier [0x0x7ff7768b0bfd+3035885]
	GetHandleVerifier [0x0x7ff7768d03f0+3164896]
	GetHandleVerifier [0x0x7ff7765f8c2e+185118]
	GetHandleVerifier [0x0x7ff77660053f+216111]
	GetHandleVerifier [0x0x7ff7765e72d4+113092]
	GetHandleVerifier [0x0x7ff7765e7489+113529]
	GetHandleVerifier [0x0x7ff7765ce288+10616]
	BaseThreadInitThunk [0x0x7ffd037fe8d7+23]
	RtlUserThreadStart [0x0x7ffd04d1c34c+44]

2025-08-10 00:53:03,738 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff7765de925+77845]
	GetHandleVerifier [0x0x7ff7765de980+77936]
	(No symbol) [0x0x7ff776399b0c]
	(No symbol) [0x0x7ff776455b46]
	(No symbol) [0x0x7ff7764188ca]
	(No symbol) [0x0x7ff776440b07]
	(No symbol) [0x0x7ff7764186a3]
	(No symbol) [0x0x7ff7763e1791]
	(No symbol) [0x0x7ff7763e2523]
	GetHandleVerifier [0x0x7ff7768b683d+3059501]
	GetHandleVerifier [0x0x7ff7768b0bfd+3035885]
	GetHandleVerifier [0x0x7ff7768d03f0+3164896]
	GetHandleVerifier [0x0x7ff7765f8c2e+185118]
	GetHandleVerifier [0x0x7ff77660053f+216111]
	GetHandleVerifier [0x0x7ff7765e72d4+113092]
	GetHandleVerifier [0x0x7ff7765e7489+113529]
	GetHandleVerifier [0x0x7ff7765ce288+10616]
	BaseThreadInitThunk [0x0x7ffd037fe8d7+23]
	RtlUserThreadStart [0x0x7ffd04d1c34c+44]

2025-08-10 00:53:03,745 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff7765de925+77845]
	GetHandleVerifier [0x0x7ff7765de980+77936]
	(No symbol) [0x0x7ff776399b0c]
	(No symbol) [0x0x7ff776455b46]
	(No symbol) [0x0x7ff7764188ca]
	(No symbol) [0x0x7ff776440b07]
	(No symbol) [0x0x7ff7764186a3]
	(No symbol) [0x0x7ff7763e1791]
	(No symbol) [0x0x7ff7763e2523]
	GetHandleVerifier [0x0x7ff7768b683d+3059501]
	GetHandleVerifier [0x0x7ff7768b0bfd+3035885]
	GetHandleVerifier [0x0x7ff7768d03f0+3164896]
	GetHandleVerifier [0x0x7ff7765f8c2e+185118]
	GetHandleVerifier [0x0x7ff77660053f+216111]
	GetHandleVerifier [0x0x7ff7765e72d4+113092]
	GetHandleVerifier [0x0x7ff7765e7489+113529]
	GetHandleVerifier [0x0x7ff7765ce288+10616]
	BaseThreadInitThunk [0x0x7ffd037fe8d7+23]
	RtlUserThreadStart [0x0x7ffd04d1c34c+44]

2025-08-10 00:53:03,751 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff7765de925+77845]
	GetHandleVerifier [0x0x7ff7765de980+77936]
	(No symbol) [0x0x7ff776399b0c]
	(No symbol) [0x0x7ff776455b46]
	(No symbol) [0x0x7ff7764188ca]
	(No symbol) [0x0x7ff776440b07]
	(No symbol) [0x0x7ff7764186a3]
	(No symbol) [0x0x7ff7763e1791]
	(No symbol) [0x0x7ff7763e2523]
	GetHandleVerifier [0x0x7ff7768b683d+3059501]
	GetHandleVerifier [0x0x7ff7768b0bfd+3035885]
	GetHandleVerifier [0x0x7ff7768d03f0+3164896]
	GetHandleVerifier [0x0x7ff7765f8c2e+185118]
	GetHandleVerifier [0x0x7ff77660053f+216111]
	GetHandleVerifier [0x0x7ff7765e72d4+113092]
	GetHandleVerifier [0x0x7ff7765e7489+113529]
	GetHandleVerifier [0x0x7ff7765ce288+10616]
	BaseThreadInitThunk [0x0x7ffd037fe8d7+23]
	RtlUserThreadStart [0x0x7ffd04d1c34c+44]

2025-08-10 00:53:03,759 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff7765de925+77845]
	GetHandleVerifier [0x0x7ff7765de980+77936]
	(No symbol) [0x0x7ff776399b0c]
	(No symbol) [0x0x7ff776455b46]
	(No symbol) [0x0x7ff7764188ca]
	(No symbol) [0x0x7ff776440b07]
	(No symbol) [0x0x7ff7764186a3]
	(No symbol) [0x0x7ff7763e1791]
	(No symbol) [0x0x7ff7763e2523]
	GetHandleVerifier [0x0x7ff7768b683d+3059501]
	GetHandleVerifier [0x0x7ff7768b0bfd+3035885]
	GetHandleVerifier [0x0x7ff7768d03f0+3164896]
	GetHandleVerifier [0x0x7ff7765f8c2e+185118]
	GetHandleVerifier [0x0x7ff77660053f+216111]
	GetHandleVerifier [0x0x7ff7765e72d4+113092]
	GetHandleVerifier [0x0x7ff7765e7489+113529]
	GetHandleVerifier [0x0x7ff7765ce288+10616]
	BaseThreadInitThunk [0x0x7ffd037fe8d7+23]
	RtlUserThreadStart [0x0x7ffd04d1c34c+44]

2025-08-10 00:53:03,763 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff7765de925+77845]
	GetHandleVerifier [0x0x7ff7765de980+77936]
	(No symbol) [0x0x7ff776399b0c]
	(No symbol) [0x0x7ff776455b46]
	(No symbol) [0x0x7ff7764188ca]
	(No symbol) [0x0x7ff776440b07]
	(No symbol) [0x0x7ff7764186a3]
	(No symbol) [0x0x7ff7763e1791]
	(No symbol) [0x0x7ff7763e2523]
	GetHandleVerifier [0x0x7ff7768b683d+3059501]
	GetHandleVerifier [0x0x7ff7768b0bfd+3035885]
	GetHandleVerifier [0x0x7ff7768d03f0+3164896]
	GetHandleVerifier [0x0x7ff7765f8c2e+185118]
	GetHandleVerifier [0x0x7ff77660053f+216111]
	GetHandleVerifier [0x0x7ff7765e72d4+113092]
	GetHandleVerifier [0x0x7ff7765e7489+113529]
	GetHandleVerifier [0x0x7ff7765ce288+10616]
	BaseThreadInitThunk [0x0x7ffd037fe8d7+23]
	RtlUserThreadStart [0x0x7ffd04d1c34c+44]

2025-08-10 00:53:03,765 - INFO - Cookies已加载
2025-08-10 00:53:03,768 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?channel=global_zx&spm=a21ug5.29159167.6452766900.2ndview_zxmore_click
2025-08-10 00:53:07,889 - INFO - 当前User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-10 00:53:07,890 - INFO - 确认：使用桌面版User-Agent
2025-08-10 00:53:07,900 - INFO - 桌面版User-Agent设置完成: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-10 00:53:08,377 - WARNING - 随机鼠标移动失败: Message: move target out of bounds
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff7765de925+77845]
	GetHandleVerifier [0x0x7ff7765de980+77936]
	(No symbol) [0x0x7ff776399b0c]
	(No symbol) [0x0x7ff77644be53]
	(No symbol) [0x0x7ff7764188ca]
	(No symbol) [0x0x7ff776440b07]
	(No symbol) [0x0x7ff7764186a3]
	(No symbol) [0x0x7ff7763e1791]
	(No symbol) [0x0x7ff7763e2523]
	GetHandleVerifier [0x0x7ff7768b683d+3059501]
	GetHandleVerifier [0x0x7ff7768b0bfd+3035885]
	GetHandleVerifier [0x0x7ff7768d03f0+3164896]
	GetHandleVerifier [0x0x7ff7765f8c2e+185118]
	GetHandleVerifier [0x0x7ff77660053f+216111]
	GetHandleVerifier [0x0x7ff7765e72d4+113092]
	GetHandleVerifier [0x0x7ff7765e7489+113529]
	GetHandleVerifier [0x0x7ff7765ce288+10616]
	BaseThreadInitThunk [0x0x7ffd037fe8d7+23]
	RtlUserThreadStart [0x0x7ffd04d1c34c+44]

2025-08-10 00:53:08,382 - INFO - 成功导航到目标页面
2025-08-10 00:53:08,389 - INFO - 登录成功！现在可以开始铺货任务
2025-08-10 00:53:11,322 - INFO - Cookies已保存
2025-08-10 00:53:13,624 - INFO - 浏览器已关闭
2025-08-10 00:53:13,624 - INFO - 以无界面(headless)模式启动Chrome，仅添加headless、no-sandbox、disable-dev-shm-usage参数
2025-08-10 00:53:19,651 - INFO - 用户请求暂停自动化流程
2025-08-10 00:53:19,789 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff7765de925+77845]
	GetHandleVerifier [0x0x7ff7765de980+77936]
	(No symbol) [0x0x7ff776399cda]
	(No symbol) [0x0x7ff7763a1679]
	(No symbol) [0x0x7ff7763a4a61]
	(No symbol) [0x0x7ff776441e4b]
	(No symbol) [0x0x7ff7764188ca]
	(No symbol) [0x0x7ff776440b07]
	(No symbol) [0x0x7ff7764186a3]
	(No symbol) [0x0x7ff7763e1791]
	(No symbol) [0x0x7ff7763e2523]
	GetHandleVerifier [0x0x7ff7768b683d+3059501]
	GetHandleVerifier [0x0x7ff7768b0bfd+3035885]
	GetHandleVerifier [0x0x7ff7768d03f0+3164896]
	GetHandleVerifier [0x0x7ff7765f8c2e+185118]
	GetHandleVerifier [0x0x7ff77660053f+216111]
	GetHandleVerifier [0x0x7ff7765e72d4+113092]
	GetHandleVerifier [0x0x7ff7765e7489+113529]
	GetHandleVerifier [0x0x7ff7765ce288+10616]
	BaseThreadInitThunk [0x0x7ffd037fe8d7+23]
	RtlUserThreadStart [0x0x7ffd04d1c34c+44]

2025-08-10 00:53:19,790 - INFO - 防检测机制设置完成
2025-08-10 00:53:19,791 - INFO - 浏览器驱动初始化成功
2025-08-10 00:53:19,794 - INFO - 开始自动化铺货流程（无头模式）
2025-08-10 00:53:19,796 - INFO - 第一步：随机选择了【美妆个护】分类页面
2025-08-10 00:53:19,800 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?cateName=%25E7%25BE%258E%25E5%25A6%2586%25E4%25B8%25AA%25E6%258A%25A4&categoryList=50010788%2C50023282%2C1801%2C126762001&keyword=&spm=a21ug5.29159167.9840561350.showIndustry
2025-08-10 00:53:21,653 - INFO - Cookies已保存
2025-08-10 00:53:23,655 - INFO - 第一步：点击登录按钮
2025-08-10 00:53:23,903 - INFO - 浏览器已关闭
