#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
淘宝铺货自动化工具
主程序入口
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import json
import os
from datetime import datetime
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.action_chains import ActionChains
import random
from automation_core import TaobaoAutomationCore

class TaobaoAutomationGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("淘宝铺货自动化工具")
        self.root.geometry("1200x700")  # 增加页面宽度
        
        # 配置变量
        self.config = self.load_config()
        self.is_running = False
        self.is_paused = False
        self.is_manual_verification = False
        self.driver = None
        self.processed_count = 0
        self.is_logged_in = False
        self.automation_core = None
        
        # 连续失败计数相关变量
        self.consecutive_failures = 0  # 连续失败计数
        self.max_consecutive_failures = 20  # 最大连续失败次数
        self.is_paused_by_failures = False  # 是否因连续失败而暂停
        
        # 设置日志
        self.setup_logging()
        
        # 创建GUI界面
        self.create_widgets()
        
        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # 显示初始日志信息
        self.log_message("淘宝铺货自动化工具已启动", "INFO")
        self.log_message("请先登录淘宝账号，然后开始铺货任务", "INFO")
    

    
    def load_config(self):
        """加载配置文件"""
        config_file = "config.json"
        default_config = {
            "min_interval": 3,
            "max_interval": 8,
            "max_products": 50,
            "crawl_count": 1000,
            "enable_batch_management": True,
            "batch_size_min": 10,
            "batch_size_max": 15,
            "chrome_driver_path": "./chromedriver.exe",
            "user_data_dir": "./chrome_user_data"
        }
        
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # 合并默认配置
                    for key, value in default_config.items():
                        if key not in config:
                            config[key] = value
                    return config
            except Exception as e:
                self.log_message(f"加载配置文件失败: {e}", "ERROR")
                return default_config
        else:
            # 创建默认配置文件
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, indent=4, ensure_ascii=False)
            return default_config
    
    def save_config(self):
        """保存配置文件"""
        try:
            with open("config.json", 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)
        except Exception as e:
            self.log_message(f"保存配置文件失败: {e}", "ERROR")

    def save_progress(self):
        """保存当前进度状态"""
        try:
            with open(self.progress_file, 'w', encoding='utf-8') as f:
                json.dump(self.current_progress, f, indent=4, ensure_ascii=False)
            self.log_message("进度状态已保存")
        except Exception as e:
            self.log_message(f"保存进度状态失败: {e}", "ERROR")

    def load_progress(self):
        """加载进度状态"""
        try:
            if os.path.exists(self.progress_file):
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    self.current_progress = json.load(f)
                self.log_message("进度状态已加载")
                return True
            return False
        except Exception as e:
            self.log_message(f"加载进度状态失败: {e}", "ERROR")
            return False

    def clear_progress(self):
        """清除进度状态"""
        try:
            if os.path.exists(self.progress_file):
                os.remove(self.progress_file)
            self.current_progress = {
                "processed_count": 0,
                "attempted_items": 0,
                "batch_count": 0,
                "batch_size": 0,
                "all_products": [],
                "selected_category_url": "",
                "selected_category_name": "",
                "last_processed_index": -1,
                "target_success_count": 0,
                "is_paused": False
            }
            self.log_message("进度状态已清除")
        except Exception as e:
            self.log_message(f"清除进度状态失败: {e}", "ERROR")
    
    def setup_logging(self):
        """设置日志系统"""
        # 创建logs目录
        if not os.path.exists("logs"):
            os.makedirs("logs")
        
        # 配置日志
        log_filename = f"logs/automation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_filename, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def create_widgets(self):
        """创建GUI控件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=0)  # 左侧控制区域固定宽度
        main_frame.columnconfigure(1, weight=1)  # 右侧表格区域可扩展
        main_frame.rowconfigure(3, weight=1)  # 日志区域可扩展
        
        # 左侧控制区域
        left_frame = ttk.Frame(main_frame)
        left_frame.grid(row=0, column=0, sticky=(tk.W, tk.N), padx=(0, 5))

        # 配置区域
        config_frame = ttk.LabelFrame(left_frame, text="配置设置", padding="5")
        config_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        
        # 间隔时间设置
        ttk.Label(config_frame, text="间隔时间范围(秒):").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        
        interval_frame = ttk.Frame(config_frame)
        interval_frame.grid(row=0, column=1, sticky=(tk.W, tk.E))
        
        self.min_interval_var = tk.StringVar(value=str(self.config["min_interval"]))
        self.max_interval_var = tk.StringVar(value=str(self.config["max_interval"]))
        
        ttk.Entry(interval_frame, textvariable=self.min_interval_var, width=8).pack(side=tk.LEFT)
        ttk.Label(interval_frame, text=" - ").pack(side=tk.LEFT)
        ttk.Entry(interval_frame, textvariable=self.max_interval_var, width=8).pack(side=tk.LEFT)
        
        # 读取数据控制
        ttk.Label(config_frame, text="读取数据:").grid(row=1, column=0, sticky=tk.W, padx=(0, 5), pady=(5, 0))
        self.crawl_count_var = tk.StringVar(value=str(self.config.get("crawl_count", 1000)))
        ttk.Entry(config_frame, textvariable=self.crawl_count_var, width=10).grid(row=1, column=1, sticky=tk.W, pady=(5, 0))

        # 铺货数量限制
        ttk.Label(config_frame, text="铺货总数量限制:").grid(row=2, column=0, sticky=tk.W, padx=(0, 5), pady=(5, 0))
        self.max_products_var = tk.StringVar(value=str(self.config["max_products"]))
        ttk.Entry(config_frame, textvariable=self.max_products_var, width=10).grid(row=2, column=1, sticky=tk.W, pady=(5, 0))

        # 批次大小设置
        ttk.Label(config_frame, text="批次大小范围:").grid(row=3, column=0, sticky=tk.W, padx=(0, 5), pady=(5, 0))

        batch_frame = ttk.Frame(config_frame)
        batch_frame.grid(row=3, column=1, sticky=(tk.W, tk.E), pady=(5, 0))

        self.batch_size_min_var = tk.StringVar(value=str(self.config.get("batch_size_min", 10)))
        self.batch_size_max_var = tk.StringVar(value=str(self.config.get("batch_size_max", 15)))

        ttk.Entry(batch_frame, textvariable=self.batch_size_min_var, width=8).pack(side=tk.LEFT)
        ttk.Label(batch_frame, text=" - ").pack(side=tk.LEFT)
        ttk.Entry(batch_frame, textvariable=self.batch_size_max_var, width=8).pack(side=tk.LEFT)

        # 批量管理控制选项
        self.enable_batch_management_var = tk.BooleanVar(value=self.config.get("enable_batch_management", True))
        ttk.Checkbutton(config_frame, text="启用自动批量管理", variable=self.enable_batch_management_var).grid(row=4, column=0, columnspan=2, sticky=tk.W, pady=(5, 0))
        
        # 控制按钮（左侧）
        button_frame = ttk.LabelFrame(left_frame, text="操作控制", padding="5")
        button_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 5))

        self.login_button = ttk.Button(button_frame, text="登录淘宝", command=self.login_taobao)
        self.login_button.grid(row=0, column=0, padx=(0, 5), pady=(0, 5), sticky=tk.W)

        self.start_button = ttk.Button(button_frame, text="开始铺货", command=self.start_automation, state=tk.DISABLED)
        self.start_button.grid(row=0, column=1, padx=(0, 5), pady=(0, 5), sticky=tk.W)

        self.stop_button = ttk.Button(button_frame, text="停止", command=self.stop_automation, state=tk.DISABLED)
        self.stop_button.grid(row=1, column=0, padx=(0, 5), pady=(0, 5), sticky=tk.W)

        self.pause_button = ttk.Button(button_frame, text="暂停", command=self.pause_automation, state=tk.DISABLED)
        self.pause_button.grid(row=1, column=1, padx=(0, 5), pady=(0, 5), sticky=tk.W)

        self.resume_button = ttk.Button(button_frame, text="继续", command=self.resume_automation, state=tk.DISABLED)
        self.resume_button.grid(row=2, column=0, padx=(0, 5), pady=(0, 5), sticky=tk.W)

        self.warehouse_remove_button = ttk.Button(button_frame, text="仓库移除", command=self.test_warehouse_remove)
        self.warehouse_remove_button.grid(row=2, column=1, padx=(0, 5), pady=(0, 5), sticky=tk.W)

        self.verification_complete_button = ttk.Button(button_frame, text="验证完成", command=self.verification_complete, state=tk.DISABLED)
        self.verification_complete_button.grid(row=3, column=0, padx=(0, 5), pady=(0, 5), sticky=tk.W)

        self.continue_after_failures_button = ttk.Button(button_frame, text="继续铺货", command=self.continue_after_failures, state=tk.DISABLED)
        self.continue_after_failures_button.grid(row=4, column=0, padx=(0, 5), pady=(0, 5), sticky=tk.W)

        ttk.Button(button_frame, text="保存配置", command=self.save_current_config).grid(row=4, column=1, pady=(5, 0), sticky=tk.W)

        # 状态显示（左侧）
        status_frame = ttk.LabelFrame(left_frame, text="运行状态", padding="5")
        status_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 5))

        self.login_status_label = ttk.Label(status_frame, text="登录状态: 未登录")
        self.login_status_label.pack(anchor=tk.W)

        self.account_status_label = ttk.Label(status_frame, text="账号获取状态: 未开始")
        self.account_status_label.pack(anchor=tk.W)

        self.account_label = ttk.Label(status_frame, text="当前账号: 未获取")
        self.account_label.pack(anchor=tk.W)

        self.status_label = ttk.Label(status_frame, text="状态: 未开始")
        self.status_label.pack(anchor=tk.W)

        self.progress_label = ttk.Label(status_frame, text="进度: 0/0")
        self.progress_label.pack(anchor=tk.W)
        
        # 进度条
        self.progress_bar = ttk.Progressbar(status_frame, mode='determinate')
        self.progress_bar.pack(fill=tk.X, pady=(5, 0))
        
        # 商品信息表格（右侧）
        table_frame = ttk.LabelFrame(main_frame, text="商品信息", padding="5")
        table_frame.grid(row=0, column=1, rowspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 0))
        table_frame.columnconfigure(0, weight=1)
        table_frame.rowconfigure(0, weight=1)

        # 创建表格（添加序号列）
        self.product_table = ttk.Treeview(table_frame, columns=("seq", "item_id", "name", "status"), show="headings", height=20)
        self.product_table.heading("seq", text="序号")
        self.product_table.heading("item_id", text="商品ID")
        self.product_table.heading("name", text="商品名称")
        self.product_table.heading("status", text="铺货状态")
        self.product_table.column("seq", width=60, anchor=tk.CENTER)
        self.product_table.column("item_id", width=120, anchor=tk.CENTER)
        self.product_table.column("name", width=250, anchor=tk.W)
        self.product_table.column("status", width=100, anchor=tk.CENTER)
        self.product_table.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 滚动条
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.product_table.yview)
        self.product_table.configure(yscroll=scrollbar.set)
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 清空表格按钮
        ttk.Button(table_frame, text="清空表格", command=self.clear_product_table).grid(row=1, column=0, pady=(5, 0))

        # 调试日志显示框（底部）
        log_frame = ttk.LabelFrame(main_frame, text="调试日志", padding="5")
        log_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(5, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

        # 创建日志文本框和滚动条
        log_text_frame = ttk.Frame(log_frame)
        log_text_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_text_frame.columnconfigure(0, weight=1)
        log_text_frame.rowconfigure(0, weight=1)

        # 日志文本框
        self.log_text = tk.Text(log_text_frame, height=8, wrap=tk.WORD, state=tk.DISABLED)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 日志滚动条
        log_scrollbar = ttk.Scrollbar(log_text_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        log_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 日志控制按钮
        log_button_frame = ttk.Frame(log_frame)
        log_button_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(5, 0))

        ttk.Button(log_button_frame, text="清空日志", command=self.clear_log).pack(side=tk.LEFT, padx=(0, 5))
        self.auto_scroll_button = ttk.Button(log_button_frame, text="关闭自动滚动", command=self.toggle_auto_scroll)
        self.auto_scroll_button.pack(side=tk.LEFT)

        # 自动滚动标志
        self.auto_scroll = True

        # 商品状态数据结构
        self.product_data = {}  # {item_id: {"seq": 序号, "name": "商品名称", "status": "状态"}}
        self.product_sequence = 0  # 商品序号计数器

        # 断点续传相关变量
        self.progress_file = "automation_progress.json"
        self.current_progress = {
            "processed_count": 0,
            "attempted_items": 0,
            "batch_count": 0,
            "batch_size": 0,
            "all_products": [],
            "selected_category_url": "",
            "selected_category_name": "",
            "last_processed_index": -1,
            "target_success_count": 0,
            "is_paused": False
        }


    
    def clear_product_table(self):
        """清空商品信息表格"""
        for row in self.product_table.get_children():
            self.product_table.delete(row)
        self.product_data.clear()
        self.product_sequence = 0  # 重置序号计数器

    def add_product_to_table(self, item_id, name, status="未处理"):
        """添加商品到表格"""
        self.product_sequence += 1  # 序号递增
        self.product_table.insert('', 'end', values=(self.product_sequence, item_id, name, status))
        self.product_data[item_id] = {"seq": self.product_sequence, "name": name, "status": status}

    def update_product_status(self, item_id, status):
        """更新表格中商品的状态"""
        if item_id in self.product_data:
            self.product_data[item_id]["status"] = status
            # 更新表格显示
            for row in self.product_table.get_children():
                values = self.product_table.item(row, 'values')
                if values[1] == item_id:  # 现在item_id在第2列（索引1）
                    self.product_table.item(row, values=(values[0], item_id, values[2], status))
                    break
    
    def log_message(self, message, level="INFO"):
        """添加日志消息（写入文件并显示在GUI）"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        # 格式化日志消息
        formatted_message = f"[{timestamp}] {level}: {message}\n"

        # 写入文件日志
        if level == "ERROR":
            self.logger.error(message)
        elif level == "WARNING":
            self.logger.warning(message)
        else:
            self.logger.info(message)

        # 在GUI中显示日志（使用after方法确保线程安全）
        self.root.after(0, lambda: self._update_log_display(formatted_message, level))

    def _update_log_display(self, message, level):
        """更新日志显示（线程安全）"""
        try:
            # 启用文本框编辑
            self.log_text.config(state=tk.NORMAL)
            
            # 根据日志级别设置颜色
            if level == "ERROR":
                self.log_text.insert(tk.END, message, "error")
                self.log_text.tag_config("error", foreground="red")
            elif level == "WARNING":
                self.log_text.insert(tk.END, message, "warning")
                self.log_text.tag_config("warning", foreground="orange")
            else:
                self.log_text.insert(tk.END, message, "info")
                self.log_text.tag_config("info", foreground="black")
            
            # 限制日志显示行数（最多保留1000行）
            lines = self.log_text.get("1.0", tk.END).split('\n')
            if len(lines) > 1000:
                # 删除前500行
                self.log_text.delete("1.0", f"{500}.0")
            
            # 自动滚动到底部
            if self.auto_scroll:
                self.log_text.see(tk.END)
            
            # 禁用文本框编辑
            self.log_text.config(state=tk.DISABLED)
            
        except Exception as e:
            # 如果更新日志显示失败，只记录到文件
            print(f"更新日志显示失败: {e}")

    def clear_log(self):
        """清空日志显示"""
        try:
            self.log_text.config(state=tk.NORMAL)
            self.log_text.delete(1.0, tk.END)
            self.log_text.config(state=tk.DISABLED)
        except Exception as e:
            print(f"清空日志失败: {e}")

    def toggle_auto_scroll(self):
        """切换自动滚动功能"""
        self.auto_scroll = not self.auto_scroll
        button_text = "关闭自动滚动" if self.auto_scroll else "开启自动滚动"
        self.auto_scroll_button.config(text=button_text)
    
    def save_current_config(self):
        """保存当前配置"""
        try:
            # 保存所有用户设置
            self.config["min_interval"] = int(self.min_interval_var.get())
            self.config["max_interval"] = int(self.max_interval_var.get())
            self.config["max_products"] = int(self.max_products_var.get())
            self.config["crawl_count"] = int(self.crawl_count_var.get())
            self.config["batch_size_min"] = int(self.batch_size_min_var.get())
            self.config["batch_size_max"] = int(self.batch_size_max_var.get())
            self.config["enable_batch_management"] = self.enable_batch_management_var.get()

            # 验证配置值的合理性
            if self.config["min_interval"] <= 0 or self.config["max_interval"] <= 0:
                messagebox.showerror("错误", "间隔时间必须大于0")
                return

            if self.config["min_interval"] > self.config["max_interval"]:
                messagebox.showerror("错误", "最小间隔时间不能大于最大间隔时间")
                return

            if self.config["batch_size_min"] <= 0 or self.config["batch_size_max"] <= 0:
                messagebox.showerror("错误", "批次大小必须大于0")
                return

            if self.config["batch_size_min"] > self.config["batch_size_max"]:
                messagebox.showerror("错误", "最小批次大小不能大于最大批次大小")
                return

            if self.config["max_products"] <= 0 or self.config["crawl_count"] <= 0:
                messagebox.showerror("错误", "商品数量必须大于0")
                return

            self.save_config()
            self.log_message("配置已保存")
            messagebox.showinfo("成功", "配置已保存")
        except ValueError:
            messagebox.showerror("错误", "请输入有效的数字")

    def login_taobao(self):
        """登录淘宝"""
        if self.is_logged_in:
            messagebox.showinfo("提示", "已经登录，无需重复登录")
            return

        try:
            # 禁用登录按钮
            self.login_button.config(state=tk.DISABLED, text="正在登录...")
            self.login_status_label.config(text="登录状态: 正在初始化浏览器...")

            # 初始化自动化核心
            self.automation_core = TaobaoAutomationCore(self.config, self.log_message)

            # 在新线程中执行登录
            login_thread = threading.Thread(target=self._login_process, daemon=True)
            login_thread.start()

        except Exception as e:
            self.log_message(f"启动登录流程失败: {e}", "ERROR")
            self.login_button.config(state=tk.NORMAL, text="登录淘宝")
            self.login_status_label.config(text="登录状态: 登录失败")

    def _login_process(self):
        """登录流程的具体实现"""
        try:
            # 初始化浏览器驱动（有界面）
            if not self.automation_core.init_driver(headless=False):
                self.root.after(0, lambda: self._login_failed(close_browser=True))
                return

            self.root.after(0, lambda: self.login_status_label.config(text="登录状态: 浏览器已启动"))

            # 尝试加载已保存的cookies
            self.automation_core.load_cookies()

            # 导航到目标页面
            self.root.after(0, lambda: self.login_status_label.config(text="登录状态: 正在打开登录页面..."))

            navigation_result = self.automation_core.navigate_to_target_page()

            if navigation_result == False:
                # 导航失败
                self.root.after(0, lambda: self._login_failed(close_browser=True))
                return
            elif navigation_result == "need_login":
                # 需要手动登录 - 不要关闭浏览器，等待用户操作
                self.root.after(0, lambda: self.login_status_label.config(text="登录状态: 请在浏览器中完成登录"))
                self.log_message("请在打开的浏览器中完成登录，登录完成后点击'确认登录完成'按钮")

                # 显示确认登录完成的对话框
                self.root.after(0, self._show_login_confirmation)
                return
            else:
                # 已经登录成功
                self.root.after(0, self._login_success)
                return

        except Exception as e:
            self.log_message(f"登录流程出错: {e}", "ERROR")
            self.root.after(0, lambda: self._login_failed(close_browser=True))

    def _show_login_confirmation(self):
        """显示登录确认对话框"""
        result = messagebox.askyesno(
            "登录确认",
            "请在浏览器中完成登录，然后点击'是'确认登录完成。\n\n如果登录失败或取消登录，请点击'否'。",
            icon='question'
        )

        if result:
            # 用户确认登录完成，验证登录状态
            self._verify_login_status()
        else:
            # 用户取消登录
            self._login_failed()

    def _verify_login_status(self):
        """验证登录状态"""
        try:
            if self.automation_core and self.automation_core.driver:
                # 刷新页面检查登录状态
                self.automation_core.driver.refresh()
                time.sleep(3)

                current_url = self.automation_core.driver.current_url
                if "login" not in current_url.lower():
                    # 登录成功
                    self.automation_core.save_cookies()
                    self._login_success()
                else:
                    # 仍然在登录页面，给用户再次尝试的机会
                    self.log_message("登录验证失败，仍在登录页面。请确认是否已完成登录。", "WARNING")
                    retry = messagebox.askyesno(
                        "登录验证失败",
                        "仍然检测到登录页面。\n\n请确认您已完成登录，然后点击'是'重新验证。\n或点击'否'取消登录。",
                        icon='warning'
                    )
                    if retry:
                        # 用户选择重新验证
                        self._verify_login_status()
                    else:
                        # 用户选择取消
                        self._login_failed(close_browser=True)
            else:
                self._login_failed(close_browser=True)

        except Exception as e:
            self.log_message(f"验证登录状态失败: {e}", "ERROR")
            self._login_failed(close_browser=True)

    def _login_success(self):
        """登录成功的处理"""
        self.is_logged_in = True
        self.login_button.config(state=tk.NORMAL, text="重新登录")
        self.start_button.config(state=tk.NORMAL)
        self.warehouse_remove_button.config(state=tk.NORMAL)
        self.login_status_label.config(text="登录状态: 已登录")
        self.log_message("登录成功！现在可以开始铺货任务")
        messagebox.showinfo("成功", "登录成功！现在可以开始铺货任务")

    def _login_failed(self, close_browser=True):
        """登录失败的处理"""
        self.is_logged_in = False
        self.login_button.config(state=tk.NORMAL, text="登录淘宝")
        self.start_button.config(state=tk.DISABLED)
        self.warehouse_remove_button.config(state=tk.DISABLED)
        self.login_status_label.config(text="登录状态: 登录失败")

        # 只有在明确要求时才清理浏览器资源
        if close_browser and self.automation_core:
            self.automation_core.cleanup()
            self.automation_core = None

    def test_warehouse_remove(self):
        """测试仓库移除功能"""
        if not self.is_logged_in:
            messagebox.showerror("错误", "请先登录淘宝账号")
            return

        if not self.automation_core:
            messagebox.showerror("错误", "自动化核心未初始化，请重新登录")
            return

        if self.is_running:
            messagebox.showerror("错误", "自动化流程正在运行中，请先停止")
            return

        # 确认操作
        result = messagebox.askyesno(
            "确认测试",
            "即将测试仓库移除功能，这将：\n\n"
            "1. 导航到商品管理页面\n"
            "2. 点击全选复选框\n"
            "3. 点击批量编辑按钮\n"
            "4. 点击最终确认按钮\n\n"
            "确定要继续吗？",
            icon='question'
        )

        if not result:
            return

        try:
            # 禁用按钮
            self.warehouse_remove_button.config(state=tk.DISABLED, text="测试中...")
            self.status_label.config(text="状态: 正在测试仓库移除功能")

            # 在新线程中执行测试
            test_thread = threading.Thread(target=self._test_warehouse_remove_process, daemon=True)
            test_thread.start()

        except Exception as e:
            self.log_message(f"启动仓库移除测试失败: {e}", "ERROR")
            self.warehouse_remove_button.config(state=tk.NORMAL, text="仓库移除")
            self.status_label.config(text="状态: 测试失败")

    def _test_warehouse_remove_process(self):
        """仓库移除测试的具体实现"""
        try:
            self.log_message("开始测试仓库移除功能...")

            # 执行批量管理操作
            if self.automation_core.perform_batch_management():
                self.log_message("仓库移除测试成功完成！")
                self.root.after(0, lambda: self.status_label.config(text="状态: 仓库移除测试成功"))
                self.root.after(0, lambda: messagebox.showinfo("测试成功", "仓库移除功能测试成功完成！"))
            else:
                self.log_message("仓库移除测试失败", "ERROR")
                self.root.after(0, lambda: self.status_label.config(text="状态: 仓库移除测试失败"))
                self.root.after(0, lambda: messagebox.showerror("测试失败", "仓库移除功能测试失败，请查看日志了解详情"))

        except Exception as e:
            self.log_message(f"仓库移除测试过程中出错: {e}", "ERROR")
            self.root.after(0, lambda: self.status_label.config(text="状态: 测试出错"))
            self.root.after(0, lambda: messagebox.showerror("测试出错", f"仓库移除测试过程中出错: {e}"))
        finally:
            # 恢复按钮状态
            self.root.after(0, lambda: self.warehouse_remove_button.config(state=tk.NORMAL, text="仓库移除"))
    
    def start_automation(self):
        """开始自动化流程"""
        if self.is_running:
            return

        if not self.is_logged_in:
            messagebox.showerror("错误", "请先登录淘宝账号")
            return

        if not self.automation_core:
            messagebox.showerror("错误", "自动化核心未初始化，请重新登录")
            return

        try:
            # 验证配置
            min_interval = int(self.min_interval_var.get())
            max_interval = int(self.max_interval_var.get())
            max_products = int(self.max_products_var.get())

            if min_interval >= max_interval:
                messagebox.showerror("错误", "最小间隔时间必须小于最大间隔时间")
                return

            if max_products <= 0:
                messagebox.showerror("错误", "铺货数量必须大于0")
                return

            # 更新配置
            self.config["min_interval"] = min_interval
            self.config["max_interval"] = max_interval
            self.config["max_products"] = max_products

            # 重置状态
            self.processed_count = 0
            self.is_running = True
            # 重置连续失败计数
            self.consecutive_failures = 0
            self.is_paused_by_failures = False

            # 更新UI状态
            self.start_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.NORMAL)
            self.pause_button.config(state=tk.NORMAL)
            self.resume_button.config(state=tk.DISABLED)
            self.login_button.config(state=tk.DISABLED)
            self.warehouse_remove_button.config(state=tk.DISABLED)
            self.status_label.config(text="状态: 正在启动...")
            self.progress_bar.config(maximum=max_products, value=0)
            self.update_progress()

            # 在新线程中运行自动化流程
            self.automation_thread = threading.Thread(target=self.run_automation, daemon=True)
            self.automation_thread.start()

        except ValueError:
            messagebox.showerror("错误", "请输入有效的数字")
    
    def stop_automation(self):
        """停止自动化流程"""
        self.is_running = False
        self.is_paused = False
        self.is_paused_by_failures = False

        # 如果有进行中的任务，保存进度以便后续续传
        if hasattr(self, 'current_progress') and self.current_progress.get("all_products"):
            self.current_progress["is_paused"] = True
            self.save_progress()
            self.log_message("任务已停止，进度已保存，可通过'继续'按钮恢复")

        self.status_label.config(text="状态: 正在停止...")
        self.log_message("用户请求停止自动化流程")

    def pause_automation(self):
        """暂停自动化流程"""
        self.is_paused = True
        self.status_label.config(text="状态: 已暂停")
        self.log_message("用户请求暂停自动化流程")

        # 更新按钮状态
        self.pause_button.config(state=tk.DISABLED)
        self.resume_button.config(state=tk.NORMAL)
        self.continue_after_failures_button.config(state=tk.DISABLED)

    def resume_automation(self):
        """继续自动化流程（包括断点续传）"""
        # 检查是否有保存的进度
        if self.load_progress() and self.current_progress.get("all_products"):
            # 有保存的进度，询问用户是否要续传
            result = messagebox.askyesno(
                "断点续传",
                f"检测到上次未完成的任务：\n\n"
                f"分类：{self.current_progress.get('selected_category_name', '未知')}\n"
                f"已处理：{self.current_progress.get('processed_count', 0)} 个商品\n"
                f"总进度：{self.current_progress.get('attempted_items', 0)}/{len(self.current_progress.get('all_products', []))}\n\n"
                f"是否要从断点继续？\n"
                f"选择'否'将重新开始新任务。",
                icon='question'
            )

            if result:
                # 用户选择续传
                self._resume_from_checkpoint()
                return
            else:
                # 用户选择重新开始，清除进度
                self.clear_progress()

        # 正常的继续/暂停逻辑
        self.is_paused = False
        self.status_label.config(text="状态: 继续运行")
        self.log_message("用户请求继续自动化流程")

        # 更新按钮状态
        self.pause_button.config(state=tk.NORMAL)
        self.resume_button.config(state=tk.DISABLED)
        self.continue_after_failures_button.config(state=tk.DISABLED)

    def verification_complete(self):
        """用户完成手动验证，验证状态并切换到无头模式继续运行"""
        # 先检测验证是否真正完成
        if not self._verify_captcha_completion():
            # 验证未完成，提示用户
            messagebox.showwarning(
                "验证未完成",
                "检测到滑块验证尚未完成，请先完成验证再点击此按钮。\n\n"
                "如果您已经完成验证但仍看到此提示，请稍等片刻再试。"
            )
            self.log_message("用户点击验证完成但检测到验证尚未完成", "WARNING")
            return

        self.is_manual_verification = False
        self.status_label.config(text="状态: 验证完成，正在切换到无头模式...")
        self.log_message("验证状态检测通过，正在切换到无头模式...")

        # 更新按钮状态
        self.verification_complete_button.config(state=tk.DISABLED)

        # 在新线程中执行切换操作，避免阻塞GUI
        switch_thread = threading.Thread(target=self._switch_to_headless_mode, daemon=True)
        switch_thread.start()

    def continue_after_failures(self):
        """用户点击继续铺货按钮，恢复因连续失败而暂停的流程"""
        if not self.is_paused_by_failures:
            self.log_message("当前未因连续失败而暂停", "WARNING")
            return

        # 重置连续失败计数
        self.consecutive_failures = 0
        self.is_paused_by_failures = False
        
        # 恢复暂停状态
        self.is_paused = False
        self.status_label.config(text="状态: 继续运行")
        self.log_message("用户点击继续铺货，重置连续失败计数并继续流程")

        # 更新按钮状态
        self.continue_after_failures_button.config(state=tk.DISABLED)
        self.pause_button.config(state=tk.NORMAL)
        self.resume_button.config(state=tk.DISABLED)

    def _verify_captcha_completion(self):
        """检测滑块验证是否真正完成"""
        try:
            if not self.automation_core or not self.automation_core.driver:
                return False

            # 检测常见的验证完成标识
            verification_indicators = [
                # 检查是否还有滑块验证元素存在
                "//div[contains(@class, 'nc_wrapper')]",  # 阿里云盾滑块
                "//div[contains(@class, 'captcha')]",     # 通用验证码
                "//div[contains(@class, 'slider')]",      # 滑块元素
                "//div[contains(@class, 'verify')]",      # 验证元素
                "//iframe[contains(@src, 'captcha')]",    # 验证码iframe
            ]

            # 如果还能找到验证元素，说明验证未完成
            for selector in verification_indicators:
                try:
                    elements = self.automation_core.driver.find_elements(By.XPATH, selector)
                    visible_elements = [elem for elem in elements if elem.is_displayed()]
                    if visible_elements:
                        self.log_message(f"检测到未完成的验证元素: {selector}", "WARNING")
                        return False
                except:
                    continue

            # 检查页面URL是否还包含验证相关关键词
            current_url = self.automation_core.driver.current_url
            verification_keywords = ['captcha', 'verify', 'challenge', 'slider']
            if any(keyword in current_url.lower() for keyword in verification_keywords):
                self.log_message(f"页面URL仍包含验证关键词: {current_url}", "WARNING")
                return False

            # 尝试检测页面是否可以正常操作（查找铺货按钮）
            try:
                add_button = self.automation_core.find_add_product_button()
                if add_button and add_button != "already_distributed":
                    self.log_message("验证完成检测：找到铺货按钮，验证应该已完成")
                    return True
            except:
                pass

            # 如果没有发现验证元素，认为验证完成
            self.log_message("验证完成检测：未发现验证元素，认为验证已完成")
            return True

        except Exception as e:
            self.log_message(f"验证状态检测失败: {e}", "ERROR")
            # 检测失败时，为了安全起见，认为验证未完成
            return False

    def _switch_to_headless_mode(self):
        """切换到无头模式的具体实现"""
        try:
            if self.automation_core and self.automation_core.driver:
                # 1. 保存当前状态
                current_url = self.automation_core.driver.current_url
                self.log_message(f"保存当前页面URL: {current_url}")

                # 2. 保存cookies
                self.automation_core.save_cookies()
                self.log_message("已保存cookies")

                # 3. 关闭有界面浏览器
                self.automation_core.driver.quit()
                self.log_message("已关闭有界面浏览器")

                # 4. 重新初始化为无头模式
                if self.automation_core.init_driver(headless=True):
                    self.log_message("无头模式浏览器初始化成功")

                    # 5. 加载cookies
                    self.automation_core.load_cookies()
                    self.log_message("已加载cookies到无头浏览器")

                    # 6. 导航回当前页面
                    self.automation_core.driver.get(current_url)
                    time.sleep(3)
                    self.log_message("已导航回验证页面")

                    # 7. 重新点击登录按钮（无头模式下需要重新执行）
                    self.log_message("无头模式下重新点击登录按钮...")
                    if self.automation_core.auto_click_login_button():
                        self.log_message("无头模式下登录按钮点击成功")
                        time.sleep(3)
                    else:
                        self.log_message("无头模式下登录按钮点击失败，但继续执行", "WARNING")

                    # 8. 更新GUI状态
                    self.root.after(0, lambda: self.status_label.config(text="状态: 已切换到无头模式，继续运行"))
                    self.log_message("成功切换到无头模式，继续自动化流程")
                else:
                    self.log_message("无头模式浏览器初始化失败", "ERROR")
                    self.root.after(0, lambda: self.status_label.config(text="状态: 切换无头模式失败"))
            else:
                self.log_message("automation_core或driver不存在", "ERROR")
                self.root.after(0, lambda: self.status_label.config(text="状态: 切换无头模式失败"))

        except Exception as e:
            self.log_message(f"切换到无头模式失败: {e}", "ERROR")
            self.root.after(0, lambda: self.status_label.config(text="状态: 切换无头模式失败，继续运行"))

    def _switch_to_gui_mode_for_verification(self):
        """切换到有界面模式进行验证"""
        try:
            if self.automation_core and self.automation_core.driver:
                # 1. 保存当前状态
                current_url = self.automation_core.driver.current_url
                self.log_message(f"保存当前页面URL: {current_url}")

                # 2. 保存cookies
                self.automation_core.save_cookies()
                self.log_message("已保存cookies")

                # 3. 关闭无头浏览器
                self.automation_core.driver.quit()
                self.log_message("已关闭无头浏览器")

                # 4. 重新初始化为有界面模式
                if self.automation_core.init_driver(headless=False):
                    self.log_message("有界面模式浏览器初始化成功")

                    # 5. 加载cookies
                    self.automation_core.load_cookies()
                    self.log_message("已加载cookies到有界面浏览器")

                    # 6. 导航回验证页面
                    self.automation_core.driver.get(current_url)
                    time.sleep(3)
                    self.log_message("已导航回验证页面")

                    # 7. 重新点击登录按钮（有界面模式下需要重新执行）
                    self.log_message("有界面模式下重新点击登录按钮...")
                    if self.automation_core.auto_click_login_button():
                        self.log_message("有界面模式下登录按钮点击成功")
                        time.sleep(3)
                    else:
                        self.log_message("有界面模式下登录按钮点击失败，但继续执行", "WARNING")

                    self.log_message("成功切换到有界面模式，等待用户完成验证")
                    return True
                else:
                    self.log_message("有界面模式浏览器初始化失败", "ERROR")
                    return False
            else:
                self.log_message("automation_core或driver不存在", "ERROR")
                return False

        except Exception as e:
            self.log_message(f"切换到有界面模式失败: {e}", "ERROR")
            return False

    def _bring_gui_to_front(self):
        """让GUI在任务栏闪烁提醒用户（不打断用户工作）"""
        try:
            # 发出提示音作为辅助提醒
            self.root.bell()

            # 在Windows系统中让任务栏图标闪烁
            try:
                import ctypes
                from ctypes import wintypes

                # 获取窗口句柄
                hwnd = self.root.winfo_id()

                # 定义FLASHWINFO结构
                class FLASHWINFO(ctypes.Structure):
                    _fields_ = [
                        ('cbSize', wintypes.UINT),
                        ('hwnd', wintypes.HWND),
                        ('dwFlags', wintypes.DWORD),
                        ('uCount', wintypes.UINT),
                        ('dwTimeout', wintypes.DWORD)
                    ]

                # 闪烁标志 - 使用TIMERNOFG确保不会获得焦点
                FLASHW_CAPTION = 0x00000001
                FLASHW_TRAY = 0x00000002
                FLASHW_ALL = FLASHW_CAPTION | FLASHW_TRAY
                FLASHW_TIMERNOFG = 0x0000000C

                # 设置闪烁参数
                flash_info = FLASHWINFO()
                flash_info.cbSize = ctypes.sizeof(FLASHWINFO)
                flash_info.hwnd = hwnd
                flash_info.dwFlags = FLASHW_ALL | FLASHW_TIMERNOFG
                flash_info.uCount = 5  # 闪烁5次
                flash_info.dwTimeout = 0

                # 调用Windows API让任务栏闪烁
                ctypes.windll.user32.FlashWindowEx(ctypes.byref(flash_info))

                if self.is_paused_by_failures:
                    self.log_message("GUI任务栏图标已开始闪烁，连续失败暂停，请检查问题后点击'继续铺货'按钮")
                else:
                    self.log_message("GUI任务栏图标已开始闪烁，请注意查看并完成滑块验证")

            except Exception as flash_error:
                # 如果Windows API调用失败，仅记录日志，不使用任何置顶操作
                self.log_message(f"任务栏闪烁失败: {flash_error}", "WARNING")
                self.log_message("请手动查看GUI窗口完成滑块验证", "INFO")

        except Exception as e:
            self.log_message(f"GUI提醒失败: {e}", "WARNING")

    def _resume_from_checkpoint(self):
        """从断点续传"""
        try:
            self.log_message("开始从断点续传...")

            # 恢复状态变量
            self.processed_count = self.current_progress.get("processed_count", 0)
            self.selected_category_url = self.current_progress.get("selected_category_url", "")
            self.selected_category_name = self.current_progress.get("selected_category_name", "")

            # 更新UI状态
            self.is_running = True
            self.start_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.NORMAL)
            self.pause_button.config(state=tk.NORMAL)
            self.resume_button.config(state=tk.DISABLED)
            self.login_button.config(state=tk.DISABLED)
            self.warehouse_remove_button.config(state=tk.DISABLED)

            # 恢复商品表格
            self.clear_product_table()
            all_products = self.current_progress.get("all_products", [])
            for i, product in enumerate(all_products):
                # 根据进度状态设置商品状态
                attempted_items = self.current_progress.get("attempted_items", 0)
                if i < attempted_items:
                    # 已处理过的商品，检查是否成功
                    if i < self.current_progress.get("processed_count", 0):
                        status = "成功"
                    else:
                        status = "失败"
                else:
                    status = "未处理"

                self.add_product_to_table(product['item_id'], product['name'], status)

            # 更新进度显示
            target_success_count = self.current_progress.get("target_success_count", self.config["max_products"])
            self.progress_bar.config(maximum=target_success_count, value=self.processed_count)
            self.update_progress()

            self.status_label.config(text="状态: 从断点续传中...")
            self.log_message(f"从断点续传：已处理 {self.processed_count} 个商品，继续处理剩余商品")

            # 在新线程中继续自动化流程
            self.automation_thread = threading.Thread(target=self._continue_automation_from_checkpoint, daemon=True)
            self.automation_thread.start()

        except Exception as e:
            self.log_message(f"断点续传失败: {e}", "ERROR")
            self.reset_ui_state()

    def _continue_automation_from_checkpoint(self):
        """从断点继续自动化流程"""
        try:
            # 重新初始化 automation_core，无头模式
            self.automation_core.cleanup() if self.automation_core else None
            self.automation_core = TaobaoAutomationCore(self.config, self.log_message)
            self.automation_core.init_driver(headless=True)
            self.log_message("断点续传：重新初始化浏览器完成")

            # 导航到之前的分类页面
            if self.selected_category_url:
                self.log_message(f"断点续传：导航到 {self.selected_category_name} 分类页面")
                self.automation_core.driver.get(self.selected_category_url)
                time.sleep(3)

                # 点击登录按钮
                if self.automation_core.auto_click_login_button():
                    self.log_message("断点续传：登录按钮点击成功")
                    time.sleep(3)
                else:
                    self.log_message("断点续传：登录按钮点击失败，但继续执行", "WARNING")

            # 继续处理剩余商品
            self._continue_product_processing()

        except Exception as e:
            self.log_message(f"断点续传过程出错: {e}", "ERROR")
            self.root.after(0, lambda: self.status_label.config(text="状态: 断点续传失败"))
        finally:
            self.is_running = False
            self.root.after(0, self.reset_ui_state)

    def _continue_product_processing(self):
        """继续处理商品（断点续传用）"""
        try:
            all_products = self.current_progress.get("all_products", [])
            target_success_count = self.current_progress.get("target_success_count", self.config["max_products"])
            attempted_items = self.current_progress.get("attempted_items", 0)
            batch_count = self.current_progress.get("batch_count", 0)
            batch_size = self.current_progress.get("batch_size", random.randint(self.config["batch_size_min"], self.config["batch_size_max"]))
            last_processed_index = self.current_progress.get("last_processed_index", -1)

            self.log_message(f"断点续传：从第 {last_processed_index + 2} 个商品开始处理")

            # 从断点位置继续处理
            for i, product in enumerate(all_products[last_processed_index + 1:], start=last_processed_index + 1):
                # 检查是否达到目标成功数量
                if self.processed_count >= target_success_count:
                    self.log_message(f"已达到目标成功铺货数量 {target_success_count}，停止处理")
                    break

                if not self.is_running:
                    self.log_message("用户停止了流程")
                    break

                # 检查暂停状态
                while self.is_paused and self.is_running:
                    time.sleep(0.5)

                if not self.is_running:
                    self.log_message("用户停止了流程")
                    break

                attempted_items += 1
                item_id = product['item_id']
                self.root.after(0, lambda: self.status_label.config(text=f"状态: 正在处理第 {attempted_items} 个商品 (成功: {self.processed_count}/{target_success_count})"))
                self.root.after(0, lambda id=item_id: self.update_product_status(id, "处理中"))

                # 处理单个商品
                success = self.automation_core.process_product_by_id(item_id)

                # 处理手动验证情况（与主流程相同的逻辑）
                if success == "manual_verification_required":
                    self.log_message(f"第 {attempted_items} 个商品需要手动验证，正在切换到有界面模式...")
                    self.root.after(0, lambda id=item_id: self.update_product_status(id, "等待验证"))
                    self.root.after(0, lambda: self.status_label.config(text="状态: 检测到滑块验证，正在切换到有界面模式"))

                    # 切换到有界面模式进行验证
                    if self._switch_to_gui_mode_for_verification():
                        # 启用验证完成按钮
                        self.root.after(0, lambda: self.verification_complete_button.config(state=tk.NORMAL))
                        self.is_manual_verification = True

                        # 任务栏闪烁提醒用户
                        self.root.after(0, self._bring_gui_to_front)

                        # 等待用户完成验证
                        while self.is_manual_verification and self.is_running:
                            time.sleep(0.5)

                        if not self.is_running:
                            break

                        # 验证完成后重新处理该商品
                        self.log_message(f"重新处理第 {attempted_items} 个商品...")
                        success = self.automation_core.process_product_by_id(item_id)
                    else:
                        self.log_message("切换到有界面模式失败，跳过此商品", "ERROR")
                        success = False

                # 更新状态和保存进度（与主流程相同）
                if success == True:
                    self.root.after(0, lambda id=item_id: self.update_product_status(id, "成功"))
                    self.processed_count += 1
                    batch_count += 1
                    self.log_message(f"第 {attempted_items} 个商品处理成功 (总成功: {self.processed_count}/{target_success_count}, 批次进度: {batch_count}/{batch_size})")
                    self.root.after(0, self.update_progress)

                    # 保存进度
                    self.current_progress.update({
                        "processed_count": self.processed_count,
                        "attempted_items": attempted_items,
                        "batch_count": batch_count,
                        "last_processed_index": i
                    })
                    self.save_progress()

                elif success == "already_distributed":
                    self.root.after(0, lambda id=item_id: self.update_product_status(id, "已铺货"))
                    self.log_message(f"第 {attempted_items} 个商品已铺货，跳过（不计入成功数量）")
                else:
                    self.root.after(0, lambda id=item_id: self.update_product_status(id, "失败"))
                    self.log_message(f"第 {attempted_items} 个商品处理失败（不计入成功数量）", "WARNING")

                # 批量管理逻辑（与主流程相同）
                if batch_count >= batch_size:
                    enable_batch_management = self.enable_batch_management_var.get()
                    if enable_batch_management:
                        self.log_message(f"达到批次大小 {batch_size}，开始执行批量管理...")
                        self.root.after(0, lambda: self.status_label.config(text="状态: 正在执行批量管理"))

                        if self.automation_core.perform_batch_management():
                            self.log_message("批量管理操作完成")
                        else:
                            self.log_message("批量管理操作失败", "WARNING")

                        # 返回主页面继续处理
                        if not self.automation_core.return_to_main_page(self.selected_category_url):
                            self.log_message("无法返回主页面，停止流程", "ERROR")
                            break
                    else:
                        self.log_message(f"自动批量管理未勾选，跳过批量操作")

                    # 重置批次计数
                    batch_count = 0
                    batch_size = random.randint(self.config["batch_size_min"], self.config["batch_size_max"])
                    self.log_message(f"新批次大小: {batch_size}")

                # 随机间隔时间
                if success == True and self.processed_count < target_success_count and attempted_items < len(all_products):
                    interval = random.uniform(self.config["min_interval"], self.config["max_interval"])
                    self.log_message(f"铺货成功，等待 {interval:.1f} 秒后处理下一个商品")
                    time.sleep(interval)

            # 处理剩余批次
            if batch_count > 0 and self.is_running:
                enable_batch_management = self.enable_batch_management_var.get()
                if enable_batch_management:
                    self.log_message(f"处理剩余的 {batch_count} 个商品...")
                    self.root.after(0, lambda: self.status_label.config(text="状态: 正在执行最终批量管理"))

                    if self.automation_core.perform_batch_management():
                        self.log_message("最终批量管理操作完成")
                    else:
                        self.log_message("最终批量管理操作失败", "WARNING")
                else:
                    self.log_message(f"自动批量管理未勾选，跳过最终批量操作")

            # 完成后清除进度
            if self.is_running:
                self.clear_progress()
                self.log_message(f"断点续传完成！共处理了 {self.processed_count} 个商品")
                self.root.after(0, lambda: self.status_label.config(text="状态: 已完成"))
            else:
                self.log_message("断点续传已停止")
                self.root.after(0, lambda: self.status_label.config(text="状态: 已停止"))

        except Exception as e:
            self.log_message(f"断点续传处理过程出错: {e}", "ERROR")
            self.root.after(0, lambda: self.status_label.config(text="状态: 出错"))
    
    def update_progress(self):
        """更新进度显示"""
        self.progress_label.config(text=f"进度: {self.processed_count}/{self.config['max_products']}")
        self.progress_bar.config(value=self.processed_count)

    def update_account_status(self, status):
        """更新账号获取状态"""
        self.account_status_label.config(text=f"账号获取状态: {status}")

    def update_account_display(self, account_name):
        """更新账号显示"""
        self.account_label.config(text=f"当前账号: {account_name}")
        self.log_message(f"GUI界面已更新账号显示: {account_name}")
    
    def run_automation(self):
        """运行自动化流程的主函数"""
        try:
            # 重新初始化 automation_core，无头模式
            self.automation_core.cleanup() if self.automation_core else None
            self.automation_core = TaobaoAutomationCore(self.config, self.log_message)
            self.automation_core.init_driver(headless=True)
            self.log_message("开始自动化铺货流程（无头模式）")

            # 第一步：随机选择首页URL并导航（只保留美妆个护和家居百货）
            homepage_urls = [
                "https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?cateName=%25E7%25BE%258E%25E5%25A6%2586%25E4%25B8%25AA%25E6%258A%25A4&categoryList=********%2C50023282%2C1801%2C126762001&keyword=&spm=a21ug5.********.**********.showIndustry",  # 美妆个护
                "https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?cateName=%25E5%25AE%25B6%25E5%25B1%2585%25E7%2599%25BE%25E8%25B4%25A7&categoryList=50016349%2C50016348%2C50008163%2C21%2C50020808%2C122928002%2C122950001%2C122952001%2C50008164%2C50025705&keyword=&spm=a21ug5.********.**********.showIndustry"  # 家居百货
            ]

            selected_url = random.choice(homepage_urls)
            category_names = ["美妆个护", "家居百货"]
            selected_category = category_names[homepage_urls.index(selected_url)]

            # 保存选择的URL，供后续使用
            self.selected_category_url = selected_url
            self.selected_category_name = selected_category

            self.log_message(f"第一步：随机选择了【{selected_category}】分类页面")
            self.log_message(f"正在导航到目标页面: {selected_url}")
            self.automation_core.driver.get(selected_url)
            time.sleep(3)

            self.log_message("第一步：点击登录按钮")
            if self.automation_core.auto_click_login_button():
                self.log_message("登录按钮点击成功")
                time.sleep(3)
            else:
                self.log_message("登录按钮点击失败，但继续执行后续流程", "WARNING")

            # 第二步：获取账号信息并显示
            self.log_message("第二步：获取账号信息...")
            self.root.after(0, lambda: self.update_account_status("正在获取..."))
            account_name = self.automation_core.get_account_name()
            if account_name:
                self.log_message(f"获取到账号名称: {account_name}")
                self.root.after(0, lambda: self.update_account_status("获取成功"))
                self.root.after(0, lambda: self.update_account_display(account_name))
            else:
                self.log_message("未能获取账号名称", "WARNING")
                self.root.after(0, lambda: self.update_account_status("获取失败"))

            # 第三步：读取商品信息
            # 从GUI获取读取数量
            try:
                target_crawl_count = int(self.crawl_count_var.get())
                if target_crawl_count <= 0:
                    target_crawl_count = 1000
            except ValueError:
                target_crawl_count = 1000
                self.crawl_count_var.set("1000")

            self.log_message(f"第三步：开始读取{target_crawl_count}个商品信息...")
            self.root.after(0, lambda: self.status_label.config(text=f"状态: 正在读取{target_crawl_count}个商品信息"))

            # 清空表格
            self.root.after(0, self.clear_product_table)

            # 爬取商品信息，传递GUI回调函数实现实时显示
            def gui_add_product(item_id, name):
                """GUI回调函数，实时添加商品到表格"""
                self.root.after(0, lambda: self.add_product_to_table(item_id, name))

            # 使用GUI设置的爬取数量，并传入之前随机选择的分类URL
            all_products = self.automation_core.crawl_all_products(target_crawl_count, gui_add_product, self.selected_category_url)
            if not all_products:
                self.log_message("未能获取到商品信息，停止流程", "ERROR")
                return

            self.log_message(f"成功读取到 {len(all_products)} 个商品（目标{target_crawl_count}个），开始铺货任务")

            # 保存初始进度状态
            target_success_count = self.config["max_products"]
            self.current_progress.update({
                "all_products": all_products,
                "selected_category_url": self.selected_category_url,
                "selected_category_name": self.selected_category_name,
                "target_success_count": target_success_count,
                "processed_count": 0,
                "attempted_items": 0,
                "batch_count": 0,
                "batch_size": random.randint(self.config["batch_size_min"], self.config["batch_size_max"]),
                "last_processed_index": -1
            })
            self.save_progress()

            # 第四步：开始逐个处理商品
            self.log_message("第四步：开始处理商品...")
            self.root.after(0, lambda: self.status_label.config(text="状态: 正在处理商品"))

            # 处理商品 - 只有铺货成功的才计入总限制数量
            target_success_count = self.config["max_products"]  # 目标成功铺货数量
            self.log_message(f"已读取 {len(all_products)} 个商品，目标成功铺货 {target_success_count} 个商品")

            # 按顺序处理商品（移除随机化逻辑）
            self.log_message(f"将按顺序处理商品")

            batch_count = 0  # 当前批次计数
            batch_size = random.randint(self.config["batch_size_min"], self.config["batch_size_max"])
            self.log_message(f"当前批次大小: {batch_size}")

            # 处理每个商品
            attempted_items = 0  # 尝试处理的商品数

            # 按顺序遍历商品列表，直到成功铺货数量达到目标
            for product in all_products:
                # 检查是否达到目标成功数量
                if self.processed_count >= target_success_count:
                    self.log_message(f"已达到目标成功铺货数量 {target_success_count}，停止处理")
                    break

                if not self.is_running:
                    self.log_message("用户停止了流程")
                    break

                # 检查暂停状态
                while self.is_paused and self.is_running:
                    time.sleep(0.5)  # 暂停时每0.5秒检查一次状态

                if not self.is_running:  # 在暂停期间可能被停止
                    self.log_message("用户停止了流程")
                    break

                self.root.after(0, lambda: self.status_label.config(text=f"状态: 正在处理第 {attempted_items+1} 个商品 (成功: {self.processed_count}/{target_success_count})"))

                # 实时更新表格状态为"正在存入中"
                attempted_items += 1
                item_id = product['item_id']
                product_name = product['name']
                self.root.after(0, lambda id=item_id: self.update_product_status(id, "处理中"))

                # 处理单个商品（直接访问商品详情页面）
                success = self.automation_core.process_product_by_id(item_id)

                # 处理手动验证情况
                if success == "manual_verification_required":
                    self.log_message(f"第 {attempted_items} 个商品需要手动验证，正在切换到有界面模式...")
                    self.root.after(0, lambda id=item_id: self.update_product_status(id, "等待验证"))
                    self.root.after(0, lambda: self.status_label.config(text="状态: 检测到滑块验证，正在切换到有界面模式"))

                    # 切换到有界面模式进行验证
                    if self._switch_to_gui_mode_for_verification():
                        # 启用验证完成按钮
                        self.root.after(0, lambda: self.verification_complete_button.config(state=tk.NORMAL))
                        self.is_manual_verification = True

                        # 显示GUI窗口到前台
                        self.root.after(0, self._bring_gui_to_front)

                        # 等待用户完成验证
                        while self.is_manual_verification and self.is_running:
                            time.sleep(0.5)

                        if not self.is_running:
                            break

                        # 验证完成后重新处理该商品
                        self.log_message(f"重新处理第 {attempted_items} 个商品...")
                        success = self.automation_core.process_product_by_id(item_id)
                    else:
                        self.log_message("切换到有界面模式失败，跳过此商品", "ERROR")
                        success = False



                # 更新表格状态
                if success == True:
                    self.root.after(0, lambda id=item_id: self.update_product_status(id, "成功"))
                    self.processed_count += 1
                    batch_count += 1
                    # 重置连续失败计数
                    self.consecutive_failures = 0
                    self.log_message(f"第 {attempted_items} 个商品处理成功 (总成功: {self.processed_count}/{target_success_count}, 批次进度: {batch_count}/{batch_size})")
                    self.root.after(0, self.update_progress)

                    # 保存进度
                    self.current_progress.update({
                        "processed_count": self.processed_count,
                        "attempted_items": attempted_items,
                        "batch_count": batch_count,
                        "last_processed_index": all_products.index(product)
                    })
                    self.save_progress()
                elif success == "already_distributed":
                    self.root.after(0, lambda id=item_id: self.update_product_status(id, "已铺货"))
                    self.log_message(f"第 {attempted_items} 个商品已铺货，跳过（不计入成功数量）")
                    # 已铺货的商品不计入连续失败
                elif success == "manual_verification_required":
                    # 如果重新处理后仍需要手动验证，标记为失败
                    self.root.after(0, lambda id=item_id: self.update_product_status(id, "验证失败"))
                    self.log_message(f"第 {attempted_items} 个商品验证失败（不计入成功数量）", "WARNING")
                    # 验证失败计入连续失败
                    self.consecutive_failures += 1
                else:
                    self.root.after(0, lambda id=item_id: self.update_product_status(id, "失败"))
                    self.log_message(f"第 {attempted_items} 个商品处理失败（不计入成功数量）", "WARNING")
                    # 处理失败计入连续失败
                    self.consecutive_failures += 1

                # 检查连续失败次数
                if self.consecutive_failures >= self.max_consecutive_failures:
                    self.log_message(f"连续失败 {self.consecutive_failures} 次，达到阈值 {self.max_consecutive_failures}，自动暂停程序", "WARNING")
                    self.is_paused_by_failures = True
                    self.is_paused = True
                    self.status_label.config(text="状态: 连续失败暂停")
                    
                    # 启用继续铺货按钮
                    self.root.after(0, lambda: self.continue_after_failures_button.config(state=tk.NORMAL))
                    self.root.after(0, lambda: self.pause_button.config(state=tk.DISABLED))
                    self.root.after(0, lambda: self.resume_button.config(state=tk.DISABLED))
                    
                    # 任务栏闪烁提醒
                    self.root.after(0, self._bring_gui_to_front)
                    
                    # 等待用户点击继续按钮
                    while self.is_paused_by_failures and self.is_running:
                        time.sleep(0.5)
                    
                    if not self.is_running:
                        break

                # 检查是否需要执行批量管理（根据GUI配置决定）
                if batch_count >= batch_size:
                    # 从GUI获取当前的批量管理设置
                    enable_batch_management = self.enable_batch_management_var.get()
                    if enable_batch_management:
                        self.log_message(f"达到批次大小 {batch_size}，开始执行批量管理...")
                        self.root.after(0, lambda: self.status_label.config(text="状态: 正在执行批量管理"))

                        if self.automation_core.perform_batch_management():
                            self.log_message("批量管理操作完成")
                        else:
                            self.log_message("批量管理操作失败", "WARNING")

                        # 返回主页面继续处理，使用之前随机选择的分类URL
                        if not self.automation_core.return_to_main_page(self.selected_category_url):
                            self.log_message("无法返回主页面，停止流程", "ERROR")
                            break

                        # 重新获取商品列表（页面可能已更新）
                        items = self.automation_core.get_product_list_items()
                        if not items:
                            self.log_message("重新获取商品列表失败，停止流程", "ERROR")
                            break
                    else:
                        self.log_message(f"自动批量管理未勾选，跳过批量操作")

                    # 重置批次计数并生成新的批次大小
                    batch_count = 0
                    batch_size = random.randint(self.config["batch_size_min"], self.config["batch_size_max"])
                    self.log_message(f"新批次大小: {batch_size}")

                # 随机间隔时间 - 只有成功铺货时才等待
                if success == True and self.processed_count < target_success_count and attempted_items < len(all_products):
                    interval = random.uniform(self.config["min_interval"], self.config["max_interval"])
                    self.log_message(f"铺货成功，等待 {interval:.1f} 秒后处理下一个商品")
                    time.sleep(interval)
                elif success == "already_distributed":
                    self.log_message("商品已铺货，立即处理下一个商品")
                elif success == False:
                    self.log_message("商品处理失败，立即处理下一个商品")

            # 处理剩余的商品（如果有未完成的批次且启用了批量管理）
            if batch_count > 0 and self.is_running:
                enable_batch_management = self.enable_batch_management_var.get()
                if enable_batch_management:
                    self.log_message(f"处理剩余的 {batch_count} 个商品...")
                    self.root.after(0, lambda: self.status_label.config(text="状态: 正在执行最终批量管理"))

                    if self.automation_core.perform_batch_management():
                        self.log_message("最终批量管理操作完成")
                    else:
                        self.log_message("最终批量管理操作失败", "WARNING")
                else:
                    self.log_message(f"自动批量管理未勾选，跳过最终批量操作")

            if self.is_running:
                self.log_message(f"自动化流程完成！共处理了 {self.processed_count} 个商品")
                self.root.after(0, lambda: self.status_label.config(text="状态: 已完成"))
            else:
                self.log_message("自动化流程已停止")
                self.root.after(0, lambda: self.status_label.config(text="状态: 已停止"))

        except Exception as e:
            self.log_message(f"自动化流程出错: {e}", "ERROR")
            self.root.after(0, lambda: self.status_label.config(text="状态: 出错"))
        finally:
            self.is_running = False
            self.root.after(0, self.reset_ui_state)
    
    def reset_ui_state(self):
        """重置UI状态"""
        if self.is_logged_in:
            self.start_button.config(state=tk.NORMAL)
            self.warehouse_remove_button.config(state=tk.NORMAL)
        else:
            self.start_button.config(state=tk.DISABLED)
            self.warehouse_remove_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.DISABLED)
        self.pause_button.config(state=tk.DISABLED)
        self.resume_button.config(state=tk.DISABLED)
        self.verification_complete_button.config(state=tk.DISABLED)
        self.continue_after_failures_button.config(state=tk.DISABLED)
        self.login_button.config(state=tk.NORMAL)
        self.status_label.config(text="状态: 未开始")
        
        # 重置连续失败相关变量
        self.consecutive_failures = 0
        self.is_paused_by_failures = False
    
    def on_closing(self):
        """程序关闭时的处理"""
        if self.is_running:
            if messagebox.askokcancel("退出", "自动化流程正在运行，确定要退出吗？"):
                self.is_running = False
                if self.automation_core:
                    try:
                        self.automation_core.cleanup()
                    except:
                        pass
                self.root.destroy()
        else:
            if self.automation_core:
                try:
                    self.automation_core.cleanup()
                except:
                    pass
            self.root.destroy()

    def clear_error_log(self):
        self.error_text.config(state=tk.NORMAL)
        self.error_text.delete(1.0, tk.END)
        self.error_text.config(state=tk.DISABLED)

if __name__ == "__main__":
    root = tk.Tk()
    app = TaobaoAutomationGUI(root)
    root.mainloop()
