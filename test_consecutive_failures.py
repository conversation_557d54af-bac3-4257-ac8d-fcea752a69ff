#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test consecutive failures pause functionality
"""

import tkinter as tk
from main import TaobaoAutomationGUI

def test_consecutive_failures():
    """Test consecutive failures pause functionality"""
    root = tk.Tk()
    app = TaobaoAutomationGUI(root)
    
    # Test consecutive failure counting
    print("Testing consecutive failure counting...")
    
    # Simulate consecutive failures
    for i in range(6):
        app.consecutive_failures = i
        print(f"Consecutive failures: {app.consecutive_failures}")
        
        if app.consecutive_failures >= app.max_consecutive_failures:
            print(f"Reached threshold {app.max_consecutive_failures}, should pause")
            break
    
    # Test reset functionality
    print("\nTesting reset functionality...")
    app.consecutive_failures = 0
    app.is_paused_by_failures = False
    print("Consecutive failure count reset")
    
    print("\nTest completed!")
    root.destroy()

if __name__ == "__main__":
    test_consecutive_failures() 