2025-07-29 23:52:01,501 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649cda]
	(No symbol) [0x0x7ff742651679]
	(No symbol) [0x0x7ff742654a61]
	(No symbol) [0x0x7ff7426f1e4b]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:52:01,501 - INFO - 防检测机制设置完成
2025-07-29 23:52:01,502 - INFO - 浏览器驱动初始化成功
2025-07-29 23:52:01,507 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:52:01,512 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:52:01,517 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:52:01,520 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:52:01,523 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:52:01,530 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:52:01,533 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:52:01,535 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:52:01,538 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:52:01,541 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:52:01,544 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:52:01,546 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:52:01,549 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:52:01,550 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:52:01,553 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:52:01,556 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:52:01,560 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:52:01,563 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:52:01,566 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:52:01,568 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:52:01,569 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649b0c]
	(No symbol) [0x0x7ff742705b46]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:52:01,570 - INFO - Cookies已加载
2025-07-29 23:52:01,570 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?channel=global_zx&spm=a21ug5.29159167.6452766900.2ndview_zxmore_click
2025-07-29 23:52:05,717 - INFO - 当前User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-29 23:52:05,718 - INFO - 确认：使用桌面版User-Agent
2025-07-29 23:52:05,722 - INFO - 桌面版User-Agent设置完成: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-29 23:52:05,724 - WARNING - 检测到需要登录，请手动登录后继续
2025-07-29 23:52:05,816 - INFO - 调试截图已保存: debug_logs/login_required_screenshot_20250729_235205.png
2025-07-29 23:52:05,818 - INFO - 浏览器日志已保存: debug_logs/login_required_browser_log_20250729_235205.txt
2025-07-29 23:52:05,829 - INFO - 页面源码已保存: debug_logs/login_required_page_source_20250729_235205.html
2025-07-29 23:52:05,832 - INFO - 调试信息 - 当前URL: https://login.taobao.com/havanaone/login/login.htm?bizName=taobao&ttid=&redirectURL=https%3A%2F%2Fjingya-x.taobao.com%2Fwow%2Fz%2Ftfx%2Fstatic%2FdXCnMcpEntsE375tfHGp%3Fchannel%3Dglobal_zx%26spm%3Da21ug5.29159167.6452766900.2ndview_zxmore_click&sub=true
2025-07-29 23:52:05,832 - INFO - 调试信息 - 错误: 检测到需要登录
2025-07-29 23:52:05,833 - INFO - 请在打开的浏览器中完成登录，登录完成后点击'确认登录完成'按钮
2025-07-29 23:52:11,568 - INFO - Cookies已保存
2025-07-29 23:52:11,569 - INFO - 登录成功！现在可以开始铺货任务
2025-07-29 23:52:13,600 - INFO - Cookies已保存
2025-07-29 23:52:15,866 - INFO - 浏览器已关闭
2025-07-29 23:52:17,199 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649cda]
	(No symbol) [0x0x7ff742651679]
	(No symbol) [0x0x7ff742654a61]
	(No symbol) [0x0x7ff7426f1e4b]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 23:52:17,199 - INFO - 防检测机制设置完成
2025-07-29 23:52:17,200 - INFO - 浏览器驱动初始化成功
2025-07-29 23:52:17,200 - INFO - 开始自动化铺货流程（有界面模式）
2025-07-29 23:52:17,200 - INFO - 第一步：随机选择了【宠物园艺】分类页面
2025-07-29 23:52:17,201 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?cateName=%25E5%25AE%25A0%25E7%2589%25A9%25E5%259B%25AD%25E8%2589%25BA&categoryList=50007216%2C29%2C124466001&keyword=&spm=a21ug5.29159167.9840561350.showIndustry
2025-07-29 23:52:20,351 - INFO - 第一步：点击登录按钮
2025-07-29 23:52:37,651 - INFO - 找到登录按钮，使用选择器: //*[@id='login-form']/div[6]/button
2025-07-29 23:52:38,677 - INFO - 准备点击登录按钮: 标签=button, 文本='登录', 选择器=//*[@id='login-form']/div[6]/button
2025-07-29 23:52:38,690 - INFO - 准备点击元素: tag=button, text='登录', class='fm-button fm-submit password-l', id=''
2025-07-29 23:52:39,986 - INFO - 尝试ActionChains点击...
2025-07-29 23:52:40,381 - INFO - ActionChains点击成功
2025-07-29 23:52:40,636 - INFO - 登录按钮点击成功
2025-07-29 23:52:40,636 - INFO - 登录按钮点击成功
2025-07-29 23:52:43,636 - INFO - 第二步：获取账号信息...
2025-07-29 23:52:43,636 - INFO - 正在跳转到个人中心页面: https://jingya.taobao.com/?v=2
2025-07-29 23:53:08,371 - INFO - 尝试获取账号名称，使用XPath: //*[@id='workbench-user-tool-btn']/div/div[2]
2025-07-29 23:53:08,384 - INFO - 成功获取账号信息: 熙阳13店:茉茉
2025-07-29 23:53:08,385 - INFO - 获取到账号名称: 熙阳13店:茉茉
2025-07-29 23:53:08,385 - INFO - GUI界面已更新账号显示: 熙阳13店:茉茉
2025-07-29 23:53:08,386 - INFO - 第三步：开始读取20个商品信息...
2025-07-29 23:53:08,388 - INFO - 开始爬取商品信息，最大数量: 20
2025-07-29 23:53:08,388 - INFO - 使用目标URL: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?cateName=%25E5%25AE%25A0%25E7%2589%25A9%25E5%259B%25AD%25E8%2589%25BA&categoryList=50007216%2C29%2C124466001&keyword=&spm=a21ug5.29159167.9840561350.showIndustry
2025-07-29 23:53:11,465 - INFO - 正在爬取第 1 页商品信息...
2025-07-29 23:53:13,473 - INFO - 找到商品容器，开始爬取商品，最大数量: 20
2025-07-29 23:53:13,483 - INFO - 找到商品项 1，检查元素信息...
2025-07-29 23:53:13,493 - INFO - 商品项 1 - 元素标签: div, class: , 有data-autolog: False
2025-07-29 23:53:13,494 - INFO - 商品项 1 当前元素无data-autolog，查找子元素...
2025-07-29 23:53:13,505 - INFO - 商品项 1 找到子元素，class: tfx-item
2025-07-29 23:53:13,508 - INFO - 商品项 1 直接获取到data-autolog: item_id=644229800225&path=search_goods_card&type=category
2025-07-29 23:53:13,508 - INFO - 商品项 1 data-autolog: item_id=644229800225&path=search_goods_card&type=category
2025-07-29 23:53:13,508 - INFO - 商品项 1 提取到item_id: 644229800225
2025-07-29 23:53:13,521 - INFO - 商品项 1 商品名称: 食虫植物捕蝇草懒人盆盆栽成株食虫草大型食蝇草超大大嘴巨猪笼草
2025-07-29 23:53:13,532 - INFO - 找到商品项 2，检查元素信息...
2025-07-29 23:53:13,546 - INFO - 商品项 2 - 元素标签: div, class: , 有data-autolog: False
2025-07-29 23:53:13,546 - INFO - 商品项 2 当前元素无data-autolog，查找子元素...
2025-07-29 23:53:13,554 - INFO - 商品项 2 找到子元素，class: tfx-item
2025-07-29 23:53:13,557 - INFO - 商品项 2 直接获取到data-autolog: item_id=745967210158&path=search_goods_card&index=1&type=category
2025-07-29 23:53:13,557 - INFO - 商品项 2 data-autolog: item_id=745967210158&path=search_goods_card&index=1&type=category
2025-07-29 23:53:13,557 - INFO - 商品项 2 提取到item_id: 745967210158
2025-07-29 23:53:13,565 - INFO - 商品项 2 商品名称: CatMagic喵洁客膨润土猫砂去味除臭抑菌低尘14磅25磅【普通分销】
2025-07-29 23:53:13,577 - INFO - 找到商品项 3，检查元素信息...
2025-07-29 23:53:13,586 - INFO - 商品项 3 - 元素标签: div, class: , 有data-autolog: False
2025-07-29 23:53:13,586 - INFO - 商品项 3 当前元素无data-autolog，查找子元素...
2025-07-29 23:53:13,594 - INFO - 商品项 3 找到子元素，class: tfx-item
2025-07-29 23:53:13,597 - INFO - 商品项 3 直接获取到data-autolog: item_id=750237903013&path=search_goods_card&index=2&type=category
2025-07-29 23:53:13,597 - INFO - 商品项 3 data-autolog: item_id=750237903013&path=search_goods_card&index=2&type=category
2025-07-29 23:53:13,597 - INFO - 商品项 3 提取到item_id: 750237903013
2025-07-29 23:53:13,607 - INFO - 商品项 3 商品名称: 【猫粮全规格】Farmina法米娜羊肉鹌鹑鸭肉鸡肉猪肉鲱鱼
2025-07-29 23:53:13,617 - INFO - 找到商品项 4，检查元素信息...
2025-07-29 23:53:13,626 - INFO - 商品项 4 - 元素标签: div, class: , 有data-autolog: False
2025-07-29 23:53:13,627 - INFO - 商品项 4 当前元素无data-autolog，查找子元素...
2025-07-29 23:53:13,639 - INFO - 商品项 4 找到子元素，class: tfx-item
2025-07-29 23:53:13,650 - INFO - 商品项 4 直接获取到data-autolog: item_id=644246307338&path=search_goods_card&index=3&type=category
2025-07-29 23:53:13,650 - INFO - 商品项 4 data-autolog: item_id=644246307338&path=search_goods_card&index=3&type=category
2025-07-29 23:53:13,650 - INFO - 商品项 4 提取到item_id: 644246307338
2025-07-29 23:53:13,658 - INFO - 商品项 4 商品名称: 水培植物种藕周带花苞发货劲松种建国微莲根茎带花碗莲苗荷花种子
2025-07-29 23:53:13,670 - INFO - 找到商品项 5，检查元素信息...
2025-07-29 23:53:13,678 - INFO - 商品项 5 - 元素标签: div, class: , 有data-autolog: False
2025-07-29 23:53:13,678 - INFO - 商品项 5 当前元素无data-autolog，查找子元素...
2025-07-29 23:53:13,684 - INFO - 商品项 5 找到子元素，class: tfx-item
2025-07-29 23:53:13,687 - INFO - 商品项 5 直接获取到data-autolog: item_id=886369776493&path=search_goods_card&index=4&type=category
2025-07-29 23:53:13,688 - INFO - 商品项 5 data-autolog: item_id=886369776493&path=search_goods_card&index=4&type=category
2025-07-29 23:53:13,688 - INFO - 商品项 5 提取到item_id: 886369776493
2025-07-29 23:53:13,695 - INFO - 商品项 5 商品名称: 【店长推荐款】原装进口Fromm福摩经典无谷系列猫粮4磅10磅
2025-07-29 23:53:13,707 - INFO - 找到商品项 6，检查元素信息...
2025-07-29 23:53:13,715 - INFO - 商品项 6 - 元素标签: div, class: , 有data-autolog: False
2025-07-29 23:53:13,715 - INFO - 商品项 6 当前元素无data-autolog，查找子元素...
2025-07-29 23:53:13,723 - INFO - 商品项 6 找到子元素，class: tfx-item
2025-07-29 23:53:13,726 - INFO - 商品项 6 直接获取到data-autolog: item_id=846491904036&path=search_goods_card&index=5&type=category
2025-07-29 23:53:13,726 - INFO - 商品项 6 data-autolog: item_id=846491904036&path=search_goods_card&index=5&type=category
2025-07-29 23:53:13,726 - INFO - 商品项 6 提取到item_id: 846491904036
2025-07-29 23:53:13,793 - INFO - 商品项 6 商品名称: ZIWI滋益巅峰风干猫粮鸡肉牛肉羊肉多口味成幼猫猫主粮1kg
2025-07-29 23:53:13,812 - INFO - 找到商品项 7，检查元素信息...
2025-07-29 23:53:13,823 - INFO - 商品项 7 - 元素标签: div, class: , 有data-autolog: False
2025-07-29 23:53:13,824 - INFO - 商品项 7 当前元素无data-autolog，查找子元素...
2025-07-29 23:53:13,831 - INFO - 商品项 7 找到子元素，class: tfx-item
2025-07-29 23:53:13,834 - INFO - 商品项 7 直接获取到data-autolog: item_id=611064926174&path=search_goods_card&index=6&type=category
2025-07-29 23:53:13,834 - INFO - 商品项 7 data-autolog: item_id=611064926174&path=search_goods_card&index=6&type=category
2025-07-29 23:53:13,834 - INFO - 商品项 7 提取到item_id: 611064926174
2025-07-29 23:53:13,844 - INFO - 商品项 7 商品名称: 朱迪思瓶子草食虫植物盆栽食人花大型超大扑捕蝇草朱迪斯灭蚊室内
2025-07-29 23:53:13,856 - INFO - 找到商品项 8，检查元素信息...
2025-07-29 23:53:13,865 - INFO - 商品项 8 - 元素标签: div, class: , 有data-autolog: False
2025-07-29 23:53:13,865 - INFO - 商品项 8 当前元素无data-autolog，查找子元素...
2025-07-29 23:53:13,876 - INFO - 商品项 8 找到子元素，class: tfx-item
2025-07-29 23:53:13,878 - INFO - 商品项 8 直接获取到data-autolog: item_id=848492544023&path=search_goods_card&index=7&type=category
2025-07-29 23:53:13,878 - INFO - 商品项 8 data-autolog: item_id=848492544023&path=search_goods_card&index=7&type=category
2025-07-29 23:53:13,879 - INFO - 商品项 8 提取到item_id: 848492544023
2025-07-29 23:53:13,887 - INFO - 商品项 8 商品名称: naturemagic自然魔法犬罐头全价175g经典无谷狗狗主食营养罐头
2025-07-29 23:53:13,897 - INFO - 找到商品项 9，检查元素信息...
2025-07-29 23:53:13,906 - INFO - 商品项 9 - 元素标签: div, class: , 有data-autolog: False
2025-07-29 23:53:13,906 - INFO - 商品项 9 当前元素无data-autolog，查找子元素...
2025-07-29 23:53:13,912 - INFO - 商品项 9 找到子元素，class: tfx-item
2025-07-29 23:53:13,915 - INFO - 商品项 9 直接获取到data-autolog: item_id=645321367835&path=search_goods_card&index=8&type=category
2025-07-29 23:53:13,915 - INFO - 商品项 9 data-autolog: item_id=645321367835&path=search_goods_card&index=8&type=category
2025-07-29 23:53:13,915 - INFO - 商品项 9 提取到item_id: 645321367835
2025-07-29 23:53:13,924 - INFO - 商品项 9 商品名称: 禾叶狸藻小虫草堂食虫植物盆栽小白兔蓝挖耳草水养水培花水生花卉
2025-07-29 23:53:13,934 - INFO - 找到商品项 10，检查元素信息...
2025-07-29 23:53:13,944 - INFO - 商品项 10 - 元素标签: div, class: , 有data-autolog: False
2025-07-29 23:53:13,944 - INFO - 商品项 10 当前元素无data-autolog，查找子元素...
2025-07-29 23:53:13,951 - INFO - 商品项 10 找到子元素，class: tfx-item
2025-07-29 23:53:13,955 - INFO - 商品项 10 直接获取到data-autolog: item_id=814622490639&path=search_goods_card&index=9&type=category
2025-07-29 23:53:13,955 - INFO - 商品项 10 data-autolog: item_id=814622490639&path=search_goods_card&index=9&type=category
2025-07-29 23:53:13,955 - INFO - 商品项 10 提取到item_id: 814622490639
2025-07-29 23:53:13,963 - INFO - 商品项 10 商品名称: OXBOW爱宝美国原装进口小宠兔粮幼年兔成年兔老年兔粮兔饲料
2025-07-29 23:53:13,975 - INFO - 找到商品项 11，检查元素信息...
2025-07-29 23:53:13,984 - INFO - 商品项 11 - 元素标签: div, class: , 有data-autolog: False
2025-07-29 23:53:13,985 - INFO - 商品项 11 当前元素无data-autolog，查找子元素...
2025-07-29 23:53:13,994 - INFO - 商品项 11 找到子元素，class: tfx-item
2025-07-29 23:53:13,997 - INFO - 商品项 11 直接获取到data-autolog: item_id=610822965180&path=search_goods_card&index=10&type=category
2025-07-29 23:53:13,997 - INFO - 商品项 11 data-autolog: item_id=610822965180&path=search_goods_card&index=10&type=category
2025-07-29 23:53:13,997 - INFO - 商品项 11 提取到item_id: 610822965180
2025-07-29 23:53:14,006 - INFO - 商品项 11 商品名称: 食虫植物套装组合盆栽食虫草吃虫食人花捕蝇草猪笼草瓶子草捕虫草
2025-07-29 23:53:14,016 - INFO - 找到商品项 12，检查元素信息...
2025-07-29 23:53:14,024 - INFO - 商品项 12 - 元素标签: div, class: , 有data-autolog: False
2025-07-29 23:53:14,025 - INFO - 商品项 12 当前元素无data-autolog，查找子元素...
2025-07-29 23:53:14,031 - INFO - 商品项 12 找到子元素，class: tfx-item
2025-07-29 23:53:14,034 - INFO - 商品项 12 直接获取到data-autolog: item_id=666924522742&path=search_goods_card&index=11&type=category
2025-07-29 23:53:14,034 - INFO - 商品项 12 data-autolog: item_id=666924522742&path=search_goods_card&index=11&type=category
2025-07-29 23:53:14,034 - INFO - 商品项 12 提取到item_id: 666924522742
2025-07-29 23:53:14,042 - INFO - 商品项 12 商品名称: 碗莲1.1（荷影）
2025-07-29 23:53:14,053 - INFO - 找到商品项 13，检查元素信息...
2025-07-29 23:53:14,061 - INFO - 商品项 13 - 元素标签: div, class: , 有data-autolog: False
2025-07-29 23:53:14,061 - INFO - 商品项 13 当前元素无data-autolog，查找子元素...
2025-07-29 23:53:14,067 - INFO - 商品项 13 找到子元素，class: tfx-item
2025-07-29 23:53:14,070 - INFO - 商品项 13 直接获取到data-autolog: item_id=617460361092&path=search_goods_card&index=12&type=category
2025-07-29 23:53:14,070 - INFO - 商品项 13 data-autolog: item_id=617460361092&path=search_goods_card&index=12&type=category
2025-07-29 23:53:14,071 - INFO - 商品项 13 提取到item_id: 617460361092
2025-07-29 23:53:14,079 - INFO - 商品项 13 商品名称: 仙葩家庭园艺颗粒控释肥250克缓释肥花卉多肉通用型肥料4个月持效
2025-07-29 23:53:14,091 - INFO - 找到商品项 14，检查元素信息...
2025-07-29 23:53:14,098 - INFO - 商品项 14 - 元素标签: div, class: , 有data-autolog: False
2025-07-29 23:53:14,098 - INFO - 商品项 14 当前元素无data-autolog，查找子元素...
2025-07-29 23:53:14,107 - INFO - 商品项 14 找到子元素，class: tfx-item
2025-07-29 23:53:14,110 - INFO - 商品项 14 直接获取到data-autolog: item_id=874139450635&path=search_goods_card&index=13&type=category
2025-07-29 23:53:14,110 - INFO - 商品项 14 data-autolog: item_id=874139450635&path=search_goods_card&index=13&type=category
2025-07-29 23:53:14,110 - INFO - 商品项 14 提取到item_id: 874139450635
2025-07-29 23:53:14,118 - INFO - 商品项 14 商品名称: 【百配贸易】Nutrience/纽翠斯黑钻系列混合冻干猫粮（禽/红肉）
2025-07-29 23:53:14,129 - INFO - 找到商品项 15，检查元素信息...
2025-07-29 23:53:14,138 - INFO - 商品项 15 - 元素标签: div, class: , 有data-autolog: False
2025-07-29 23:53:14,139 - INFO - 商品项 15 当前元素无data-autolog，查找子元素...
2025-07-29 23:53:14,147 - INFO - 商品项 15 找到子元素，class: tfx-item
2025-07-29 23:53:14,149 - INFO - 商品项 15 直接获取到data-autolog: item_id=667899307368&path=search_goods_card&index=14&type=category
2025-07-29 23:53:14,149 - INFO - 商品项 15 data-autolog: item_id=667899307368&path=search_goods_card&index=14&type=category
2025-07-29 23:53:14,149 - INFO - 商品项 15 提取到item_id: 667899307368
2025-07-29 23:53:14,159 - INFO - 商品项 15 商品名称: 碗莲水培营养液 碗莲水溶肥 碗莲专用肥 碗莲专用泥 碗莲专用土
2025-07-29 23:53:14,170 - INFO - 找到商品项 16，检查元素信息...
2025-07-29 23:53:14,180 - INFO - 商品项 16 - 元素标签: div, class: , 有data-autolog: False
2025-07-29 23:53:14,181 - INFO - 商品项 16 当前元素无data-autolog，查找子元素...
2025-07-29 23:53:14,189 - INFO - 商品项 16 找到子元素，class: tfx-item
2025-07-29 23:53:14,192 - INFO - 商品项 16 直接获取到data-autolog: item_id=694688102234&path=search_goods_card&index=15&type=category
2025-07-29 23:53:14,192 - INFO - 商品项 16 data-autolog: item_id=694688102234&path=search_goods_card&index=15&type=category
2025-07-29 23:53:14,192 - INFO - 商品项 16 提取到item_id: 694688102234
2025-07-29 23:53:14,200 - INFO - 商品项 16 商品名称: 仙葩多肉专用土颗粒营养土植物种植土铺面石进口泥炭花土通用型
2025-07-29 23:53:14,223 - INFO - 找到商品项 17，检查元素信息...
2025-07-29 23:53:14,230 - INFO - 商品项 17 - 元素标签: div, class: , 有data-autolog: False
2025-07-29 23:53:14,230 - INFO - 商品项 17 当前元素无data-autolog，查找子元素...
2025-07-29 23:53:14,239 - INFO - 商品项 17 找到子元素，class: tfx-item
2025-07-29 23:53:14,242 - INFO - 商品项 17 直接获取到data-autolog: item_id=626188341474&path=search_goods_card&index=16&type=category
2025-07-29 23:53:14,242 - INFO - 商品项 17 data-autolog: item_id=626188341474&path=search_goods_card&index=16&type=category
2025-07-29 23:53:14,242 - INFO - 商品项 17 提取到item_id: 626188341474
2025-07-29 23:53:14,251 - INFO - 商品项 17 商品名称: HB-101活力素日本植物活力液营养液花肥料促生根发芽促开花
2025-07-29 23:53:14,262 - INFO - 找到商品项 18，检查元素信息...
2025-07-29 23:53:14,270 - INFO - 商品项 18 - 元素标签: div, class: , 有data-autolog: False
2025-07-29 23:53:14,270 - INFO - 商品项 18 当前元素无data-autolog，查找子元素...
2025-07-29 23:53:14,278 - INFO - 商品项 18 找到子元素，class: tfx-item
2025-07-29 23:53:14,280 - INFO - 商品项 18 直接获取到data-autolog: item_id=626115534188&path=search_goods_card&index=17&type=category
2025-07-29 23:53:14,281 - INFO - 商品项 18 data-autolog: item_id=626115534188&path=search_goods_card&index=17&type=category
2025-07-29 23:53:14,281 - INFO - 商品项 18 提取到item_id: 626115534188
2025-07-29 23:53:14,291 - INFO - 商品项 18 商品名称: 奇趣食虫植物新手入门练手2盆套餐捕蝇草猪笼草瓶子草茅膏菜易养
2025-07-29 23:53:14,301 - INFO - 找到商品项 19，检查元素信息...
2025-07-29 23:53:14,311 - INFO - 商品项 19 - 元素标签: div, class: , 有data-autolog: False
2025-07-29 23:53:14,311 - INFO - 商品项 19 当前元素无data-autolog，查找子元素...
2025-07-29 23:53:14,317 - INFO - 商品项 19 找到子元素，class: tfx-item
2025-07-29 23:53:14,321 - INFO - 商品项 19 直接获取到data-autolog: item_id=642607200382&path=search_goods_card&index=18&type=category
2025-07-29 23:53:14,321 - INFO - 商品项 19 data-autolog: item_id=642607200382&path=search_goods_card&index=18&type=category
2025-07-29 23:53:14,321 - INFO - 商品项 19 提取到item_id: 642607200382
2025-07-29 23:53:14,345 - INFO - 商品项 19 商品名称: 水培植物四季碗莲花卉室内阳台荷花种藕花草水养睡莲水生植物盆栽
2025-07-29 23:53:14,358 - INFO - 找到商品项 20，检查元素信息...
2025-07-29 23:53:14,366 - INFO - 商品项 20 - 元素标签: div, class: , 有data-autolog: False
2025-07-29 23:53:14,367 - INFO - 商品项 20 当前元素无data-autolog，查找子元素...
2025-07-29 23:53:14,375 - INFO - 商品项 20 找到子元素，class: tfx-item
2025-07-29 23:53:14,377 - INFO - 商品项 20 直接获取到data-autolog: item_id=814665746047&path=search_goods_card&index=19&type=category
2025-07-29 23:53:14,377 - INFO - 商品项 20 data-autolog: item_id=814665746047&path=search_goods_card&index=19&type=category
2025-07-29 23:53:14,378 - INFO - 商品项 20 提取到item_id: 814665746047
2025-07-29 23:53:14,387 - INFO - 商品项 20 商品名称: OXBOW爱宝美国原装进口小宠豚鼠粮幼年豚鼠成年豚鼠老年豚鼠粮
2025-07-29 23:53:14,387 - INFO - 已爬取到 20 个商品，停止当前页面爬取
2025-07-29 23:53:14,387 - INFO - 当前页面爬取完成，找到 20 个商品
2025-07-29 23:53:14,388 - INFO - 第 1 页找到 20 个商品，总计: 20
2025-07-29 23:53:14,388 - INFO - 已达到最大商品数量 20，停止爬取
2025-07-29 23:53:14,388 - INFO - 爬取完成，共找到 20 个商品
2025-07-29 23:53:14,388 - INFO - 成功读取到 20 个商品（目标20个），开始铺货任务
2025-07-29 23:53:14,388 - INFO - 第四步：开始处理商品...
2025-07-29 23:53:14,404 - INFO - 已读取 20 个商品，目标成功铺货 3 个商品
2025-07-29 23:53:14,404 - INFO - 将按顺序处理商品
2025-07-29 23:53:14,404 - INFO - 当前批次大小: 10
2025-07-29 23:53:14,405 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=644229800225
2025-07-29 23:53:18,099 - INFO - 页面加载完成
2025-07-29 23:53:18,100 - INFO - 模拟阅读行为，停顿 1.6 秒
2025-07-29 23:53:20,329 - INFO - 开始第 1 次铺货尝试...
2025-07-29 23:53:20,330 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-29 23:53:20,330 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-29 23:53:20,340 - INFO - span innerHTML: 立即铺货
2025-07-29 23:53:20,443 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-29 23:53:20,443 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-29 23:53:20,459 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-29 23:53:21,671 - INFO - 尝试ActionChains点击...
2025-07-29 23:53:21,961 - INFO - ActionChains点击成功
2025-07-29 23:53:22,240 - INFO - 铺货按钮点击成功
2025-07-29 23:53:24,253 - INFO - 使用配置的XPath找到确认按钮
2025-07-29 23:53:24,265 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-29 23:53:25,659 - INFO - 尝试ActionChains点击...
2025-07-29 23:53:25,931 - INFO - ActionChains点击成功
2025-07-29 23:53:26,203 - INFO - 确认按钮点击成功
2025-07-29 23:53:28,204 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-29 23:53:28,211 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-29 23:53:28,211 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 23:53:29,219 - INFO - 第2次检查 - span innerHTML: 已铺货
2025-07-29 23:53:29,219 - INFO - 验证成功：span已变为'已铺货'，商品铺货成功
2025-07-29 23:53:29,219 - INFO - 第 1 个商品处理成功 (总成功: 1/3, 批次进度: 1/10)
2025-07-29 23:53:29,220 - INFO - 铺货成功，等待 3.4 秒后处理下一个商品
2025-07-29 23:53:32,637 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=745967210158
2025-07-29 23:53:36,321 - INFO - 页面加载完成
2025-07-29 23:53:36,322 - INFO - 模拟阅读行为，停顿 1.1 秒
2025-07-29 23:53:38,586 - INFO - 开始第 1 次铺货尝试...
2025-07-29 23:53:38,586 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-29 23:53:38,586 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-29 23:53:38,593 - INFO - span innerHTML: 暂未授权
2025-07-29 23:53:48,879 - WARNING - 等待元素可点击超时: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-29 23:53:48,879 - WARNING - 使用配置的XPath未找到添加商品按钮
2025-07-29 23:53:48,879 - ERROR - 未找到铺货按钮
2025-07-29 23:53:48,880 - WARNING - 第 2 个商品处理失败（不计入成功数量）
2025-07-29 23:53:48,880 - INFO - 商品处理失败，立即处理下一个商品
2025-07-29 23:53:48,892 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=750237903013
2025-07-29 23:53:51,956 - INFO - 页面加载完成
2025-07-29 23:53:51,956 - INFO - 模拟阅读行为，停顿 1.3 秒
2025-07-29 23:53:55,110 - INFO - 开始第 1 次铺货尝试...
2025-07-29 23:53:55,110 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-29 23:53:55,110 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-29 23:53:55,116 - INFO - span innerHTML: 立即铺货
2025-07-29 23:53:55,136 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-29 23:53:55,136 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-29 23:53:55,150 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-29 23:53:56,477 - INFO - 尝试ActionChains点击...
2025-07-29 23:53:56,767 - INFO - ActionChains点击成功
2025-07-29 23:53:57,163 - INFO - 铺货按钮点击成功
2025-07-29 23:53:59,179 - INFO - 使用配置的XPath找到确认按钮
2025-07-29 23:53:59,195 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-29 23:54:00,286 - INFO - 尝试ActionChains点击...
2025-07-29 23:54:00,554 - INFO - ActionChains点击成功
2025-07-29 23:54:01,000 - INFO - 确认按钮点击成功
2025-07-29 23:54:03,001 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-29 23:54:03,009 - INFO - 第1次检查 - span innerHTML: 已铺货
2025-07-29 23:54:03,010 - INFO - 验证成功：span已变为'已铺货'，商品铺货成功
2025-07-29 23:54:03,011 - INFO - 第 3 个商品处理成功 (总成功: 2/3, 批次进度: 2/10)
2025-07-29 23:54:03,022 - INFO - 铺货成功，等待 4.3 秒后处理下一个商品
2025-07-29 23:54:07,307 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=644246307338
2025-07-29 23:54:11,161 - INFO - 页面加载完成
2025-07-29 23:54:11,161 - INFO - 模拟阅读行为，停顿 1.5 秒
2025-07-29 23:54:14,153 - INFO - 添加额外延迟: 2.5秒
2025-07-29 23:54:17,577 - INFO - 开始第 1 次铺货尝试...
2025-07-29 23:54:17,577 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-29 23:54:17,577 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-29 23:54:17,585 - INFO - span innerHTML: 立即铺货
2025-07-29 23:54:17,631 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-29 23:54:17,631 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-29 23:54:17,644 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-29 23:54:18,729 - INFO - 尝试ActionChains点击...
2025-07-29 23:54:19,016 - INFO - ActionChains点击成功
2025-07-29 23:54:19,488 - INFO - 铺货按钮点击成功
2025-07-29 23:54:21,500 - INFO - 使用配置的XPath找到确认按钮
2025-07-29 23:54:21,512 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-29 23:54:22,529 - INFO - 尝试ActionChains点击...
2025-07-29 23:54:22,808 - INFO - ActionChains点击成功
2025-07-29 23:54:23,041 - INFO - 确认按钮点击成功
2025-07-29 23:54:25,041 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-29 23:54:25,050 - INFO - 第1次检查 - span innerHTML: 已铺货
2025-07-29 23:54:25,051 - INFO - 验证成功：span已变为'已铺货'，商品铺货成功
2025-07-29 23:54:25,051 - INFO - 第 4 个商品处理成功 (总成功: 3/3, 批次进度: 3/10)
2025-07-29 23:54:25,051 - INFO - 已达到目标成功铺货数量 3，停止处理
2025-07-29 23:54:25,052 - INFO - 自动批量管理未勾选，跳过最终批量操作
2025-07-29 23:54:25,052 - INFO - 自动化流程完成！共处理了 3 个商品
2025-07-30 00:01:11,021 - ERROR - 保存cookies失败: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649cda]
	(No symbol) [0x0x7ff742635f35]
	(No symbol) [0x0x7ff74265aabe]
	(No symbol) [0x0x7ff7426cfeb5]
	(No symbol) [0x0x7ff7426f0432]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-30 00:01:13,039 - INFO - 浏览器已关闭
2025-07-30 00:01:14,416 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649cda]
	(No symbol) [0x0x7ff742651679]
	(No symbol) [0x0x7ff742654a61]
	(No symbol) [0x0x7ff7426f1e4b]
	(No symbol) [0x0x7ff7426c88ca]
	(No symbol) [0x0x7ff7426f0b07]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-30 00:01:14,416 - INFO - 防检测机制设置完成
2025-07-30 00:01:14,416 - INFO - 浏览器驱动初始化成功
2025-07-30 00:01:14,417 - INFO - 开始自动化铺货流程（有界面模式）
2025-07-30 00:01:14,417 - INFO - 第一步：随机选择了【服饰箱包】分类页面
2025-07-30 00:01:14,417 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?cateName=%25E6%259C%258D%25E9%25A5%25B0%25E7%25AE%25B1%25E5%258C%2585&categoryList=50011740%2C16%2C50006843%2C1625%2C30%2C50006842%2C50010404%2C50011397%2C28%2C50013864%2C50468001&keyword=&spm=a21ug5.29159167.9840561350.showIndustry
2025-07-30 00:01:17,557 - INFO - 第一步：点击登录按钮
2025-07-30 00:01:17,578 - INFO - 找到登录按钮，使用选择器: //*[@id='login-form']/div[6]/button
2025-07-30 00:01:18,625 - INFO - 准备点击登录按钮: 标签=button, 文本='登录', 选择器=//*[@id='login-form']/div[6]/button
2025-07-30 00:01:18,646 - INFO - 准备点击元素: tag=button, text='登录', class='fm-button fm-submit password-l', id=''
2025-07-30 00:01:19,968 - INFO - 尝试ActionChains点击...
2025-07-30 00:01:29,591 - INFO - ActionChains点击成功
2025-07-30 00:01:29,801 - INFO - 登录按钮点击成功
2025-07-30 00:01:29,801 - INFO - 登录按钮点击成功
2025-07-30 00:01:32,802 - INFO - 第二步：获取账号信息...
2025-07-30 00:01:32,802 - INFO - 正在跳转到个人中心页面: https://jingya.taobao.com/?v=2
2025-07-30 00:01:36,196 - INFO - 尝试获取账号名称，使用XPath: //*[@id='workbench-user-tool-btn']/div/div[2]
2025-07-30 00:01:36,197 - ERROR - 获取账号名称时出错: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649cda]
	(No symbol) [0x0x7ff7426221a1]
	(No symbol) [0x0x7ff7426cfc6e]
	(No symbol) [0x0x7ff7426f0432]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-30 00:01:36,197 - WARNING - 未能获取账号名称
2025-07-30 00:01:36,197 - INFO - 第三步：开始读取20个商品信息...
2025-07-30 00:01:36,198 - INFO - 开始爬取商品信息，最大数量: 20
2025-07-30 00:01:36,198 - INFO - 使用目标URL: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?cateName=%25E6%259C%258D%25E9%25A5%25B0%25E7%25AE%25B1%25E5%258C%2585&categoryList=50011740%2C16%2C50006843%2C1625%2C30%2C50006842%2C50010404%2C50011397%2C28%2C50013864%2C50468001&keyword=&spm=a21ug5.29159167.9840561350.showIndustry
2025-07-30 00:01:36,199 - ERROR - 爬取商品信息时出错: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff74288e925+77845]
	GetHandleVerifier [0x0x7ff74288e980+77936]
	(No symbol) [0x0x7ff742649cda]
	(No symbol) [0x0x7ff7426221a1]
	(No symbol) [0x0x7ff7426cfc6e]
	(No symbol) [0x0x7ff7426f0432]
	(No symbol) [0x0x7ff7426c86a3]
	(No symbol) [0x0x7ff742691791]
	(No symbol) [0x0x7ff742692523]
	GetHandleVerifier [0x0x7ff742b6683d+3059501]
	GetHandleVerifier [0x0x7ff742b60bfd+3035885]
	GetHandleVerifier [0x0x7ff742b803f0+3164896]
	GetHandleVerifier [0x0x7ff7428a8c2e+185118]
	GetHandleVerifier [0x0x7ff7428b053f+216111]
	GetHandleVerifier [0x0x7ff7428972d4+113092]
	GetHandleVerifier [0x0x7ff742897489+113529]
	GetHandleVerifier [0x0x7ff74287e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-30 00:01:36,199 - ERROR - 未能获取到商品信息，停止流程
