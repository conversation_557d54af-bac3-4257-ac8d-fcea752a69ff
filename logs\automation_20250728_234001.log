2025-07-28 23:40:04,916 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059cda]
	(No symbol) [0x0x7ff69a061679]
	(No symbol) [0x0x7ff69a064a61]
	(No symbol) [0x0x7ff69a101e4b]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:40:04,916 - INFO - 防检测机制设置完成
2025-07-28 23:40:04,916 - INFO - 浏览器驱动初始化成功
2025-07-28 23:40:04,921 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:40:04,924 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:40:04,930 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:40:04,933 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:40:04,935 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:40:04,946 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:40:04,949 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:40:04,953 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:40:04,958 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:40:04,960 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:40:04,963 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:40:04,966 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:40:04,969 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:40:04,973 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:40:04,975 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:40:04,977 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:40:04,980 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:40:04,996 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:40:04,999 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:40:05,002 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:40:05,002 - INFO - Cookies已加载
2025-07-28 23:40:05,002 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?channel=global_zx&spm=a21ug5.29159167.6452766900.2ndview_zxmore_click
2025-07-28 23:40:08,948 - INFO - 当前User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-28 23:40:08,948 - INFO - 确认：使用桌面版User-Agent
2025-07-28 23:40:08,952 - INFO - 桌面版User-Agent设置完成: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-28 23:40:10,311 - INFO - 成功导航到目标页面
2025-07-28 23:40:10,311 - INFO - 登录成功！现在可以开始铺货任务
2025-07-28 23:40:12,008 - INFO - Cookies已保存
2025-07-28 23:40:14,269 - INFO - 浏览器已关闭
2025-07-28 23:40:15,680 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059cda]
	(No symbol) [0x0x7ff69a061679]
	(No symbol) [0x0x7ff69a064a61]
	(No symbol) [0x0x7ff69a101e4b]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:40:15,680 - INFO - 防检测机制设置完成
2025-07-28 23:40:15,680 - INFO - 浏览器驱动初始化成功
2025-07-28 23:40:15,680 - INFO - 开始自动化铺货流程（有界面模式）
2025-07-28 23:40:15,680 - INFO - 第一步：正在导航到目标页面...
2025-07-28 23:40:18,827 - INFO - 第一步：点击登录按钮
2025-07-28 23:40:18,848 - INFO - 找到登录按钮，使用选择器: //*[@id='login-form']/div[6]/button
2025-07-28 23:40:19,871 - INFO - 准备点击登录按钮: 标签=button, 文本='登录', 选择器=//*[@id='login-form']/div[6]/button
2025-07-28 23:40:19,886 - INFO - 准备点击元素: tag=button, text='登录', class='fm-button fm-submit password-l', id=''
2025-07-28 23:40:21,156 - INFO - 尝试ActionChains点击...
2025-07-28 23:40:26,666 - INFO - ActionChains点击成功
2025-07-28 23:40:27,138 - INFO - 登录按钮点击成功
2025-07-28 23:40:27,138 - INFO - 登录按钮点击成功
2025-07-28 23:40:30,138 - INFO - 第二步：获取账号信息...
2025-07-28 23:40:30,138 - INFO - 正在跳转到个人中心页面: https://jingya.taobao.com/?v=2
2025-07-28 23:40:55,028 - INFO - 尝试获取账号名称，使用XPath: //*[@id='workbench-user-tool-btn']/div/div[2]
2025-07-28 23:40:55,044 - INFO - 成功获取账号信息: 卓祥22店:冰淇淋
2025-07-28 23:40:55,045 - INFO - 获取到账号名称: 卓祥22店:冰淇淋
2025-07-28 23:40:55,045 - INFO - GUI界面已更新账号显示: 卓祥22店:冰淇淋
2025-07-28 23:40:55,046 - INFO - 第三步：开始读取20个商品信息...
2025-07-28 23:40:55,046 - INFO - 开始爬取商品信息，最大数量: 20
2025-07-28 23:40:58,165 - INFO - 正在爬取第 1 页商品信息...
2025-07-28 23:41:00,173 - INFO - 找到商品容器，开始爬取商品，最大数量: 20
2025-07-28 23:41:00,200 - INFO - 找到商品项 1，检查元素信息...
2025-07-28 23:41:00,211 - INFO - 商品项 1 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:41:00,216 - INFO - 商品项 1 直接获取到data-autolog: item_id=681778175573&path=search_goods_card&type=search
2025-07-28 23:41:00,216 - INFO - 商品项 1 data-autolog: item_id=681778175573&path=search_goods_card&type=search
2025-07-28 23:41:00,217 - INFO - 商品项 1 提取到item_id: 681778175573
2025-07-28 23:41:00,232 - INFO - 商品项 1 商品名称: 保税直供 Dove多芬磨砂膏石榴籽乳木果风味身体磨砂膏298g
2025-07-28 23:41:00,254 - INFO - 找到商品项 2，检查元素信息...
2025-07-28 23:41:00,263 - INFO - 商品项 2 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:41:00,266 - INFO - 商品项 2 直接获取到data-autolog: item_id=690841537159&path=search_goods_card&index=1&type=search
2025-07-28 23:41:00,266 - INFO - 商品项 2 data-autolog: item_id=690841537159&path=search_goods_card&index=1&type=search
2025-07-28 23:41:00,266 - INFO - 商品项 2 提取到item_id: 690841537159
2025-07-28 23:41:00,274 - INFO - 商品项 2 商品名称: 保税直供 凡士林Vaseline烟酰胺身体乳400ml 725ml无压泵款200ml
2025-07-28 23:41:00,286 - INFO - 找到商品项 3，检查元素信息...
2025-07-28 23:41:00,293 - INFO - 商品项 3 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:41:00,296 - INFO - 商品项 3 直接获取到data-autolog: item_id=789447097664&path=search_goods_card&index=2&type=search
2025-07-28 23:41:00,296 - INFO - 商品项 3 data-autolog: item_id=789447097664&path=search_goods_card&index=2&type=search
2025-07-28 23:41:00,296 - INFO - 商品项 3 提取到item_id: 789447097664
2025-07-28 23:41:00,305 - INFO - 商品项 3 商品名称: 【甄选】珂润Curel泡沫洗面奶150ml
2025-07-28 23:41:00,316 - INFO - 找到商品项 4，检查元素信息...
2025-07-28 23:41:00,326 - INFO - 商品项 4 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:41:00,328 - INFO - 商品项 4 直接获取到data-autolog: item_id=694682843417&path=search_goods_card&index=3&type=search
2025-07-28 23:41:00,328 - INFO - 商品项 4 data-autolog: item_id=694682843417&path=search_goods_card&index=3&type=search
2025-07-28 23:41:00,328 - INFO - 商品项 4 提取到item_id: 694682843417
2025-07-28 23:41:00,337 - INFO - 商品项 4 商品名称: 【可开授权】日台混发Fino发膜美容液护发素 发膜230g
2025-07-28 23:41:00,350 - INFO - 找到商品项 5，检查元素信息...
2025-07-28 23:41:00,360 - INFO - 商品项 5 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:41:00,363 - INFO - 商品项 5 直接获取到data-autolog: item_id=681082809892&path=search_goods_card&index=4&type=search
2025-07-28 23:41:00,363 - INFO - 商品项 5 data-autolog: item_id=681082809892&path=search_goods_card&index=4&type=search
2025-07-28 23:41:00,363 - INFO - 商品项 5 提取到item_id: 681082809892
2025-07-28 23:41:00,375 - INFO - 商品项 5 商品名称: 保税直供 Dove多芬大白碗润肤身体乳300ml 【1//2//3罐】
2025-07-28 23:41:00,386 - INFO - 找到商品项 6，检查元素信息...
2025-07-28 23:41:00,394 - INFO - 商品项 6 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:41:00,397 - INFO - 商品项 6 直接获取到data-autolog: item_id=694675776881&path=search_goods_card&index=5&type=search
2025-07-28 23:41:00,397 - INFO - 商品项 6 data-autolog: item_id=694675776881&path=search_goods_card&index=5&type=search
2025-07-28 23:41:00,397 - INFO - 商品项 6 提取到item_id: 694675776881
2025-07-28 23:41:00,406 - INFO - 商品项 6 商品名称: 保税直供 Pantene潘婷护发素三分钟奇迹多效修护发膜级护发素
2025-07-28 23:41:00,417 - INFO - 找到商品项 7，检查元素信息...
2025-07-28 23:41:00,424 - INFO - 商品项 7 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:41:00,427 - INFO - 商品项 7 直接获取到data-autolog: item_id=680729304287&path=search_goods_card&index=6&type=search
2025-07-28 23:41:00,427 - INFO - 商品项 7 data-autolog: item_id=680729304287&path=search_goods_card&index=6&type=search
2025-07-28 23:41:00,427 - INFO - 商品项 7 提取到item_id: 680729304287
2025-07-28 23:41:00,437 - INFO - 商品项 7 商品名称: 保税直供 多芬dove空气感无硅油 洗发水480g 护发素480g护发临期
2025-07-28 23:41:00,446 - INFO - 找到商品项 8，检查元素信息...
2025-07-28 23:41:00,455 - INFO - 商品项 8 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:41:00,458 - INFO - 商品项 8 直接获取到data-autolog: item_id=710355107054&path=search_goods_card&index=7&type=search
2025-07-28 23:41:00,458 - INFO - 商品项 8 data-autolog: item_id=710355107054&path=search_goods_card&index=7&type=search
2025-07-28 23:41:00,458 - INFO - 商品项 8 提取到item_id: 710355107054
2025-07-28 23:41:00,468 - INFO - 商品项 8 商品名称: 保税直供 Dove多芬洗发水护发素空气感洗发水480g 【护发素临期
2025-07-28 23:41:00,480 - INFO - 找到商品项 9，检查元素信息...
2025-07-28 23:41:00,486 - INFO - 商品项 9 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:41:00,490 - INFO - 商品项 9 直接获取到data-autolog: item_id=740463658412&path=search_goods_card&index=8&type=search
2025-07-28 23:41:00,490 - INFO - 商品项 9 data-autolog: item_id=740463658412&path=search_goods_card&index=8&type=search
2025-07-28 23:41:00,490 - INFO - 商品项 9 提取到item_id: 740463658412
2025-07-28 23:41:00,499 - INFO - 商品项 9 商品名称: 【主推链接】花王cape空气感定型喷雾50g/180g 编码：3305300000
2025-07-28 23:41:00,511 - INFO - 找到商品项 10，检查元素信息...
2025-07-28 23:41:00,519 - INFO - 商品项 10 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:41:00,521 - INFO - 商品项 10 直接获取到data-autolog: item_id=821067902127&path=search_goods_card&index=9&type=search
2025-07-28 23:41:00,521 - INFO - 商品项 10 data-autolog: item_id=821067902127&path=search_goods_card&index=9&type=search
2025-07-28 23:41:00,521 - INFO - 商品项 10 提取到item_id: 821067902127
2025-07-28 23:41:00,531 - INFO - 商品项 10 商品名称: 丹碧丝TAMPAX卫生棉条长导管式内置式纯棉月经棉条棉棒游泳卫生巾
2025-07-28 23:41:00,541 - INFO - 找到商品项 11，检查元素信息...
2025-07-28 23:41:00,550 - INFO - 商品项 11 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:41:00,553 - INFO - 商品项 11 直接获取到data-autolog: item_id=789835300215&path=search_goods_card&index=10&type=search
2025-07-28 23:41:00,553 - INFO - 商品项 11 data-autolog: item_id=789835300215&path=search_goods_card&index=10&type=search
2025-07-28 23:41:00,553 - INFO - 商品项 11 提取到item_id: 789835300215
2025-07-28 23:41:00,564 - INFO - 商品项 11 商品名称: 【保税】雅漾喷雾爽肤水300ml
2025-07-28 23:41:00,577 - INFO - 找到商品项 12，检查元素信息...
2025-07-28 23:41:00,592 - INFO - 商品项 12 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:41:00,595 - INFO - 商品项 12 直接获取到data-autolog: item_id=843747605382&path=search_goods_card&index=11&type=search
2025-07-28 23:41:00,595 - INFO - 商品项 12 data-autolog: item_id=843747605382&path=search_goods_card&index=11&type=search
2025-07-28 23:41:00,595 - INFO - 商品项 12 提取到item_id: 843747605382
2025-07-28 23:41:00,605 - INFO - 商品项 12 商品名称: 倩碧紫胖子卸妆膏125ml/瓶
2025-07-28 23:41:00,618 - INFO - 找到商品项 13，检查元素信息...
2025-07-28 23:41:00,626 - INFO - 商品项 13 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:41:00,629 - INFO - 商品项 13 直接获取到data-autolog: item_id=675816539149&path=search_goods_card&index=12&type=search
2025-07-28 23:41:00,629 - INFO - 商品项 13 data-autolog: item_id=675816539149&path=search_goods_card&index=12&type=search
2025-07-28 23:41:00,629 - INFO - 商品项 13 提取到item_id: 675816539149
2025-07-28 23:41:00,638 - INFO - 商品项 13 商品名称: 新加坡Vicopyl维可保益生菌(Pylopass/拒绝幽门菌/抑口臭/调肠胃)
2025-07-28 23:41:00,649 - INFO - 找到商品项 14，检查元素信息...
2025-07-28 23:41:00,657 - INFO - 商品项 14 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:41:00,660 - INFO - 商品项 14 直接获取到data-autolog: item_id=846634805775&path=search_goods_card&index=13&type=search
2025-07-28 23:41:00,660 - INFO - 商品项 14 data-autolog: item_id=846634805775&path=search_goods_card&index=13&type=search
2025-07-28 23:41:00,660 - INFO - 商品项 14 提取到item_id: 846634805775
2025-07-28 23:41:00,670 - INFO - 商品项 14 商品名称: 【甄选】cow牛乳石碱原味/玫瑰花香 瓶装480ml
2025-07-28 23:41:00,682 - INFO - 找到商品项 15，检查元素信息...
2025-07-28 23:41:00,689 - INFO - 商品项 15 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:41:00,693 - INFO - 商品项 15 直接获取到data-autolog: item_id=852510575114&path=search_goods_card&index=14&type=search
2025-07-28 23:41:00,693 - INFO - 商品项 15 data-autolog: item_id=852510575114&path=search_goods_card&index=14&type=search
2025-07-28 23:41:00,693 - INFO - 商品项 15 提取到item_id: 852510575114
2025-07-28 23:41:00,702 - INFO - 商品项 15 商品名称: Nars 纳斯 红壳蜜粉饼16g（含粉扑）、Nars纳斯裸光蜜粉饼10g
2025-07-28 23:41:00,714 - INFO - 找到商品项 16，检查元素信息...
2025-07-28 23:41:00,723 - INFO - 商品项 16 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:41:00,726 - INFO - 商品项 16 直接获取到data-autolog: item_id=743442966826&path=search_goods_card&index=15&type=search
2025-07-28 23:41:00,726 - INFO - 商品项 16 data-autolog: item_id=743442966826&path=search_goods_card&index=15&type=search
2025-07-28 23:41:00,726 - INFO - 商品项 16 提取到item_id: 743442966826
2025-07-28 23:41:00,735 - INFO - 商品项 16 商品名称: ESI卡路里拜拜片白芸豆阻断片大餐救星膳食纤维身材热控管理
2025-07-28 23:41:00,746 - INFO - 找到商品项 17，检查元素信息...
2025-07-28 23:41:00,753 - INFO - 商品项 17 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:41:00,756 - INFO - 商品项 17 直接获取到data-autolog: item_id=848934676698&path=search_goods_card&index=16&type=search
2025-07-28 23:41:00,756 - INFO - 商品项 17 data-autolog: item_id=848934676698&path=search_goods_card&index=16&type=search
2025-07-28 23:41:00,756 - INFO - 商品项 17 提取到item_id: 848934676698
2025-07-28 23:41:00,765 - INFO - 商品项 17 商品名称: Estee Lauder 雅诗兰黛持妆粉底液30ml 2C0 /1w1/1C1
2025-07-28 23:41:00,783 - INFO - 找到商品项 18，检查元素信息...
2025-07-28 23:41:00,798 - INFO - 商品项 18 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:41:00,802 - INFO - 商品项 18 直接获取到data-autolog: item_id=693758717931&path=search_goods_card&index=17&type=search
2025-07-28 23:41:00,802 - INFO - 商品项 18 data-autolog: item_id=693758717931&path=search_goods_card&index=17&type=search
2025-07-28 23:41:00,803 - INFO - 商品项 18 提取到item_id: 693758717931
2025-07-28 23:41:00,814 - INFO - 商品项 18 商品名称: 保税直供 资生堂fino高效渗透发膜 230g （1/2/3罐）日版
2025-07-28 23:41:00,825 - INFO - 找到商品项 19，检查元素信息...
2025-07-28 23:41:00,832 - INFO - 商品项 19 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:41:00,834 - INFO - 商品项 19 直接获取到data-autolog: item_id=691142128229&path=search_goods_card&index=18&type=search
2025-07-28 23:41:00,834 - INFO - 商品项 19 data-autolog: item_id=691142128229&path=search_goods_card&index=18&type=search
2025-07-28 23:41:00,834 - INFO - 商品项 19 提取到item_id: 691142128229
2025-07-28 23:41:00,844 - INFO - 商品项 19 商品名称: 保税 日版Dove多芬洁面洗面奶新版 正装瓶150ml 补充袋125ml 甄选
2025-07-28 23:41:00,856 - INFO - 找到商品项 20，检查元素信息...
2025-07-28 23:41:00,862 - INFO - 商品项 20 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:41:00,865 - INFO - 商品项 20 直接获取到data-autolog: item_id=690739721578&path=search_goods_card&index=19&type=search
2025-07-28 23:41:00,865 - INFO - 商品项 20 data-autolog: item_id=690739721578&path=search_goods_card&index=19&type=search
2025-07-28 23:41:00,866 - INFO - 商品项 20 提取到item_id: 690739721578
2025-07-28 23:41:00,876 - INFO - 商品项 20 商品名称: 保税直供 SUNSILK夏士莲椰子洗发水//护发素450ml 泰产
2025-07-28 23:41:00,876 - INFO - 已爬取到 20 个商品，停止当前页面爬取
2025-07-28 23:41:00,877 - INFO - 当前页面爬取完成，找到 20 个商品
2025-07-28 23:41:00,877 - INFO - 第 1 页找到 20 个商品，总计: 20
2025-07-28 23:41:00,877 - INFO - 已达到最大商品数量 20，停止爬取
2025-07-28 23:41:00,877 - INFO - 爬取完成，共找到 20 个商品
2025-07-28 23:41:00,878 - INFO - 成功读取到 20 个商品（目标20个），开始铺货任务
2025-07-28 23:41:00,878 - INFO - 第四步：开始处理商品...
2025-07-28 23:41:00,878 - INFO - 已读取 20 个商品，目标成功铺货 3 个商品
2025-07-28 23:41:00,878 - INFO - 当前批次大小: 15
2025-07-28 23:41:00,879 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=681778175573
2025-07-28 23:41:03,973 - INFO - 页面加载完成
2025-07-28 23:41:03,973 - INFO - 模拟阅读行为，停顿 2.8 秒
2025-07-28 23:41:06,771 - INFO - 添加额外延迟: 2.1秒
2025-07-28 23:41:09,865 - INFO - 开始第 1 次铺货尝试...
2025-07-28 23:41:09,866 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:41:09,866 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:41:09,874 - INFO - span innerHTML: 立即铺货
2025-07-28 23:41:09,896 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-28 23:41:09,896 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-28 23:41:09,910 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-28 23:41:11,331 - INFO - 尝试ActionChains点击...
2025-07-28 23:41:12,409 - INFO - ActionChains点击成功
2025-07-28 23:41:12,672 - INFO - 铺货按钮点击成功
2025-07-28 23:41:14,686 - INFO - 使用配置的XPath找到确认按钮
2025-07-28 23:41:14,700 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-28 23:41:15,722 - INFO - 尝试ActionChains点击...
2025-07-28 23:41:15,997 - INFO - ActionChains点击成功
2025-07-28 23:41:16,262 - INFO - 确认按钮点击成功
2025-07-28 23:41:18,262 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-28 23:41:18,273 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-28 23:41:18,273 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:41:19,282 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-28 23:41:19,282 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:41:20,290 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-28 23:41:20,290 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:41:21,298 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-28 23:41:21,300 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:41:22,308 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-07-28 23:41:22,308 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:41:23,316 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-07-28 23:41:23,316 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:41:24,325 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-07-28 23:41:24,325 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:41:25,334 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-07-28 23:41:25,334 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:41:26,342 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-07-28 23:41:26,342 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:41:27,353 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-07-28 23:41:27,353 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:41:28,354 - INFO - 等待超时，进行最后一次检查...
2025-07-28 23:41:28,361 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-07-28 23:41:28,362 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-07-28 23:41:28,362 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-07-28 23:41:28,362 - INFO - 检测是否出现滑块验证...
2025-07-28 23:41:28,370 - INFO - 在主文档中未找到滑块，检查iframe...
2025-07-28 23:41:28,378 - INFO - 找到 1 个iframe
2025-07-28 23:41:28,378 - INFO - 检查第 1 个iframe...
2025-07-28 23:41:28,400 - INFO - 在第 1 个iframe中检测到滑块验证，开始处理...
2025-07-28 23:41:28,400 - INFO - 开始处理滑块验证...
2025-07-28 23:41:28,412 - INFO - 滑块容器宽度: 300px
2025-07-28 23:41:28,413 - INFO - 计算拖拽距离: 283px (比例: 0.95)
2025-07-28 23:41:28,413 - INFO - 开始高级人工化拖拽...
2025-07-28 23:41:28,421 - INFO - 模拟人工观察滑块...
2025-07-28 23:41:42,785 - INFO - 高级人工化拖拽完成
2025-07-28 23:41:42,790 - INFO - 滑块验证成功，滑块已消失
2025-07-28 23:41:42,791 - INFO - 滑块验证处理成功，准备重试铺货
2025-07-28 23:41:44,792 - INFO - 开始第 2 次铺货尝试...
2025-07-28 23:41:44,792 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:41:44,792 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:41:44,802 - INFO - span innerHTML: 立即铺货
2025-07-28 23:41:54,994 - WARNING - 等待元素可点击超时: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:41:54,994 - WARNING - 使用配置的XPath未找到添加商品按钮
2025-07-28 23:41:54,994 - ERROR - 未找到铺货按钮
2025-07-28 23:41:54,994 - WARNING - 第 1 个商品处理失败（不计入成功数量）
2025-07-28 23:41:54,995 - INFO - 商品处理失败，立即处理下一个商品
2025-07-28 23:41:54,995 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=690841537159
2025-07-28 23:41:58,660 - INFO - 页面加载完成
2025-07-28 23:41:58,660 - INFO - 模拟阅读行为，停顿 1.7 秒
2025-07-28 23:42:01,180 - INFO - 开始第 1 次铺货尝试...
2025-07-28 23:42:01,181 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:42:01,181 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:42:01,188 - INFO - span innerHTML: 立即铺货
2025-07-28 23:42:01,211 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-28 23:42:01,211 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-28 23:42:01,226 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-28 23:42:02,486 - INFO - 尝试ActionChains点击...
2025-07-28 23:42:02,772 - INFO - ActionChains点击成功
2025-07-28 23:42:03,080 - INFO - 铺货按钮点击成功
2025-07-28 23:42:05,099 - INFO - 使用配置的XPath找到确认按钮
2025-07-28 23:42:05,113 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-28 23:42:06,524 - INFO - 尝试ActionChains点击...
2025-07-28 23:42:06,794 - INFO - ActionChains点击成功
2025-07-28 23:42:07,259 - INFO - 确认按钮点击成功
2025-07-28 23:42:09,259 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-28 23:42:09,268 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-28 23:42:09,268 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:42:10,277 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-28 23:42:10,277 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:42:11,285 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-28 23:42:11,285 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:42:12,292 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-28 23:42:12,292 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:42:13,301 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-07-28 23:42:13,301 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:42:14,314 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-07-28 23:42:14,314 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:42:15,323 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-07-28 23:42:15,324 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:42:16,333 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-07-28 23:42:16,333 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:42:17,340 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-07-28 23:42:17,341 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:42:18,355 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-07-28 23:42:18,355 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:42:19,355 - INFO - 等待超时，进行最后一次检查...
2025-07-28 23:42:19,364 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-07-28 23:42:19,364 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-07-28 23:42:19,364 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-07-28 23:42:19,365 - INFO - 检测是否出现滑块验证...
2025-07-28 23:42:19,372 - INFO - 在主文档中未找到滑块，检查iframe...
2025-07-28 23:42:19,379 - INFO - 找到 1 个iframe
2025-07-28 23:42:19,379 - INFO - 检查第 1 个iframe...
2025-07-28 23:42:19,401 - INFO - 在第 1 个iframe中检测到滑块验证，开始处理...
2025-07-28 23:42:19,402 - INFO - 开始处理滑块验证...
2025-07-28 23:42:19,413 - INFO - 滑块容器宽度: 300px
2025-07-28 23:42:19,414 - INFO - 计算拖拽距离: 272px (比例: 0.91)
2025-07-28 23:42:19,414 - INFO - 开始高级人工化拖拽...
2025-07-28 23:42:19,424 - INFO - 模拟人工观察滑块...
2025-07-28 23:42:31,326 - INFO - 高级人工化拖拽完成
2025-07-28 23:42:31,330 - INFO - 滑块验证成功，滑块已消失
2025-07-28 23:42:31,331 - INFO - 滑块验证处理成功，准备重试铺货
2025-07-28 23:42:33,332 - INFO - 开始第 2 次铺货尝试...
2025-07-28 23:42:33,332 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:42:33,332 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:42:33,343 - INFO - span innerHTML: 立即铺货
2025-07-28 23:42:43,530 - WARNING - 等待元素可点击超时: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:42:43,530 - WARNING - 使用配置的XPath未找到添加商品按钮
2025-07-28 23:42:43,530 - ERROR - 未找到铺货按钮
2025-07-28 23:42:43,530 - WARNING - 第 2 个商品处理失败（不计入成功数量）
2025-07-28 23:42:43,530 - INFO - 商品处理失败，立即处理下一个商品
2025-07-28 23:42:43,532 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=789447097664
2025-07-28 23:42:46,683 - INFO - 页面加载完成
2025-07-28 23:42:46,683 - INFO - 模拟阅读行为，停顿 2.6 秒
2025-07-28 23:42:50,036 - INFO - 开始第 1 次铺货尝试...
2025-07-28 23:42:50,036 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:42:50,036 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:42:50,045 - INFO - span innerHTML: 立即铺货
2025-07-28 23:42:50,068 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-28 23:42:50,068 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-28 23:42:50,085 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-28 23:42:51,237 - INFO - 尝试ActionChains点击...
2025-07-28 23:42:51,531 - INFO - ActionChains点击成功
2025-07-28 23:42:51,871 - INFO - 铺货按钮点击成功
2025-07-28 23:42:53,886 - INFO - 使用配置的XPath找到确认按钮
2025-07-28 23:42:53,900 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-28 23:42:54,988 - INFO - 尝试ActionChains点击...
2025-07-28 23:42:55,262 - INFO - ActionChains点击成功
2025-07-28 23:42:55,752 - INFO - 确认按钮点击成功
2025-07-28 23:42:57,752 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-28 23:42:57,762 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-28 23:42:57,762 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:42:58,772 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-28 23:42:58,773 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:42:59,783 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-28 23:42:59,783 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:43:00,793 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-28 23:43:00,793 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:43:01,802 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-07-28 23:43:01,802 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:43:02,810 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-07-28 23:43:02,810 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:43:03,820 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-07-28 23:43:03,820 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:43:04,827 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-07-28 23:43:04,827 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:43:05,836 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-07-28 23:43:05,837 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:43:06,851 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-07-28 23:43:06,851 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:43:07,853 - INFO - 等待超时，进行最后一次检查...
2025-07-28 23:43:07,863 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-07-28 23:43:07,863 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-07-28 23:43:07,863 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-07-28 23:43:07,863 - INFO - 检测是否出现滑块验证...
2025-07-28 23:43:07,873 - INFO - 在主文档中未找到滑块，检查iframe...
2025-07-28 23:43:07,879 - INFO - 找到 1 个iframe
2025-07-28 23:43:07,879 - INFO - 检查第 1 个iframe...
2025-07-28 23:43:07,904 - INFO - 在第 1 个iframe中检测到滑块验证，开始处理...
2025-07-28 23:43:07,904 - INFO - 开始处理滑块验证...
2025-07-28 23:43:07,923 - INFO - 滑块容器宽度: 300px
2025-07-28 23:43:07,923 - INFO - 计算拖拽距离: 264px (比例: 0.88)
2025-07-28 23:43:07,923 - INFO - 开始高级人工化拖拽...
2025-07-28 23:43:07,938 - INFO - 模拟人工观察滑块...
2025-07-28 23:43:22,843 - INFO - 高级人工化拖拽完成
2025-07-28 23:43:22,850 - INFO - 滑块验证成功，滑块已消失
2025-07-28 23:43:22,851 - INFO - 滑块验证处理成功，准备重试铺货
2025-07-28 23:43:24,852 - INFO - 开始第 2 次铺货尝试...
2025-07-28 23:43:24,852 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:43:24,852 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:43:24,861 - INFO - span innerHTML: 立即铺货
2025-07-28 23:43:24,880 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-28 23:43:24,880 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-28 23:43:24,896 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-28 23:43:26,112 - INFO - 尝试ActionChains点击...
2025-07-28 23:43:26,381 - INFO - ActionChains点击成功
2025-07-28 23:43:26,698 - INFO - 铺货按钮点击成功
2025-07-28 23:43:28,713 - INFO - 使用配置的XPath找到确认按钮
2025-07-28 23:43:28,729 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-28 23:43:30,142 - INFO - 尝试ActionChains点击...
2025-07-28 23:43:30,414 - INFO - ActionChains点击成功
2025-07-28 23:43:30,656 - INFO - 确认按钮点击成功
2025-07-28 23:43:32,656 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-28 23:43:32,665 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-28 23:43:32,665 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:43:33,674 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-28 23:43:33,674 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:43:34,681 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-28 23:43:34,681 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:43:35,690 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-28 23:43:35,690 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:43:36,699 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-07-28 23:43:36,700 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:43:37,707 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-07-28 23:43:37,707 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:43:38,715 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-07-28 23:43:38,715 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:43:39,723 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-07-28 23:43:39,723 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:43:40,731 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-07-28 23:43:40,732 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:43:41,740 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-07-28 23:43:41,740 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:43:42,741 - INFO - 等待超时，进行最后一次检查...
2025-07-28 23:43:42,749 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-07-28 23:43:42,749 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-07-28 23:43:42,749 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-07-28 23:43:42,749 - INFO - 检测是否出现滑块验证...
2025-07-28 23:43:42,756 - INFO - 在主文档中未找到滑块，检查iframe...
2025-07-28 23:43:42,762 - INFO - 找到 1 个iframe
2025-07-28 23:43:42,762 - INFO - 检查第 1 个iframe...
2025-07-28 23:43:42,782 - INFO - 在所有iframe中都未检测到滑块验证
2025-07-28 23:43:42,782 - INFO - 未检测到滑块验证或处理失败
2025-07-28 23:43:42,783 - WARNING - 第 3 个商品处理失败（不计入成功数量）
2025-07-28 23:43:42,783 - INFO - 商品处理失败，立即处理下一个商品
2025-07-28 23:43:42,784 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=694682843417
2025-07-28 23:43:45,894 - INFO - 页面加载完成
2025-07-28 23:43:45,894 - INFO - 模拟阅读行为，停顿 2.3 秒
2025-07-28 23:43:49,092 - INFO - 开始第 1 次铺货尝试...
2025-07-28 23:43:49,092 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:43:49,092 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:43:49,100 - INFO - span innerHTML: 立即铺货
2025-07-28 23:43:49,118 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-28 23:43:49,118 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-28 23:43:49,133 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-28 23:43:50,197 - INFO - 尝试ActionChains点击...
2025-07-28 23:43:50,500 - INFO - ActionChains点击成功
2025-07-28 23:43:50,736 - INFO - 铺货按钮点击成功
2025-07-28 23:43:52,749 - INFO - 使用配置的XPath找到确认按钮
2025-07-28 23:43:52,763 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-28 23:43:53,963 - INFO - 尝试ActionChains点击...
2025-07-28 23:43:54,240 - INFO - ActionChains点击成功
2025-07-28 23:43:54,687 - INFO - 确认按钮点击成功
2025-07-28 23:43:56,688 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-28 23:43:56,696 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-28 23:43:56,696 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:43:57,705 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-28 23:43:57,706 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:43:58,715 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-28 23:43:58,715 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:43:59,723 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-28 23:43:59,723 - INFO - span仍为'立即铺货'，继续等待...
