2025-08-01 10:18:02,855 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49cda]
	(No symbol) [0x0x7ff65bf51679]
	(No symbol) [0x0x7ff65bf54a61]
	(No symbol) [0x0x7ff65bff1e4b]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 10:18:02,856 - INFO - 防检测机制设置完成
2025-08-01 10:18:02,856 - INFO - 浏览器驱动初始化成功
2025-08-01 10:18:02,861 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 10:18:02,864 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 10:18:02,867 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 10:18:02,869 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 10:18:02,874 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 10:18:02,878 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 10:18:02,883 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 10:18:02,887 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 10:18:02,891 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 10:18:02,893 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 10:18:02,895 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 10:18:02,899 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 10:18:02,903 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 10:18:02,907 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 10:18:02,910 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 10:18:02,914 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 10:18:02,917 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 10:18:02,921 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 10:18:02,924 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49b0c]
	(No symbol) [0x0x7ff65c005b46]
	(No symbol) [0x0x7ff65bfc88ca]
	(No symbol) [0x0x7ff65bff0b07]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 10:18:02,924 - INFO - Cookies已加载
2025-08-01 10:18:02,924 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?channel=global_zx&spm=a21ug5.29159167.6452766900.2ndview_zxmore_click
2025-08-01 10:18:08,726 - INFO - 当前User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-01 10:18:08,726 - INFO - 确认：使用桌面版User-Agent
2025-08-01 10:18:08,731 - INFO - 桌面版User-Agent设置完成: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-01 10:18:09,596 - WARNING - 检测到需要登录，请手动登录后继续
2025-08-01 10:18:09,678 - INFO - 调试截图已保存: debug_logs/login_required_screenshot_20250801_101809.png
2025-08-01 10:18:09,682 - INFO - 浏览器日志已保存: debug_logs/login_required_browser_log_20250801_101809.txt
2025-08-01 10:18:09,700 - INFO - 页面源码已保存: debug_logs/login_required_page_source_20250801_101809.html
2025-08-01 10:18:09,705 - INFO - 调试信息 - 当前URL: https://login.taobao.com/havanaone/login/login.htm?bizName=taobao&ttid=&redirectURL=https%3A%2F%2Fjingya-x.taobao.com%2Fwow%2Fz%2Ftfx%2Fstatic%2FdXCnMcpEntsE375tfHGp%3Fchannel%3Dglobal_zx%26spm%3Da21ug5.29159167.6452766900.2ndview_zxmore_click&sub=true
2025-08-01 10:18:09,705 - INFO - 调试信息 - 错误: 检测到需要登录
2025-08-01 10:18:09,706 - INFO - 请在打开的浏览器中完成登录，登录完成后点击'确认登录完成'按钮
2025-08-01 10:18:37,771 - ERROR - 保存cookies失败: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff65c18e925+77845]
	GetHandleVerifier [0x0x7ff65c18e980+77936]
	(No symbol) [0x0x7ff65bf49cda]
	(No symbol) [0x0x7ff65bf35f35]
	(No symbol) [0x0x7ff65bf5aabe]
	(No symbol) [0x0x7ff65bfcfeb5]
	(No symbol) [0x0x7ff65bff0432]
	(No symbol) [0x0x7ff65bfc86a3]
	(No symbol) [0x0x7ff65bf91791]
	(No symbol) [0x0x7ff65bf92523]
	GetHandleVerifier [0x0x7ff65c46683d+3059501]
	GetHandleVerifier [0x0x7ff65c460bfd+3035885]
	GetHandleVerifier [0x0x7ff65c4803f0+3164896]
	GetHandleVerifier [0x0x7ff65c1a8c2e+185118]
	GetHandleVerifier [0x0x7ff65c1b053f+216111]
	GetHandleVerifier [0x0x7ff65c1972d4+113092]
	GetHandleVerifier [0x0x7ff65c197489+113529]
	GetHandleVerifier [0x0x7ff65c17e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-08-01 10:18:39,792 - INFO - 浏览器已关闭
