2025-07-29 00:00:44,368 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059cda]
	(No symbol) [0x0x7ff69a061679]
	(No symbol) [0x0x7ff69a064a61]
	(No symbol) [0x0x7ff69a101e4b]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:00:44,368 - INFO - 防检测机制设置完成
2025-07-29 00:00:44,368 - INFO - 浏览器驱动初始化成功
2025-07-29 00:00:44,373 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:00:44,382 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:00:44,384 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:00:44,387 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:00:44,392 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:00:44,396 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:00:44,399 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:00:44,401 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:00:44,405 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:00:44,408 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:00:44,410 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:00:44,414 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:00:44,417 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:00:44,420 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:00:44,423 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:00:44,426 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:00:44,429 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:00:44,434 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:00:44,437 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:00:44,440 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:00:44,440 - INFO - Cookies已加载
2025-07-29 00:00:44,440 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?channel=global_zx&spm=a21ug5.29159167.6452766900.2ndview_zxmore_click
2025-07-29 00:00:44,440 - INFO - 添加额外延迟: 2.5秒
2025-07-29 00:00:51,489 - INFO - 当前User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-29 00:00:51,489 - INFO - 确认：使用桌面版User-Agent
2025-07-29 00:00:51,494 - INFO - 桌面版User-Agent设置完成: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-29 00:00:51,835 - WARNING - 随机鼠标移动失败: Message: move target out of bounds
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a10be53]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:00:51,837 - WARNING - 检测到需要登录，请手动登录后继续
2025-07-29 00:00:51,903 - INFO - 调试截图已保存: debug_logs/login_required_screenshot_20250729_000051.png
2025-07-29 00:00:51,904 - INFO - 浏览器日志已保存: debug_logs/login_required_browser_log_20250729_000051.txt
2025-07-29 00:00:51,916 - INFO - 页面源码已保存: debug_logs/login_required_page_source_20250729_000051.html
2025-07-29 00:00:51,919 - INFO - 调试信息 - 当前URL: https://login.taobao.com/havanaone/login/login.htm?bizName=taobao&ttid=&redirectURL=https%3A%2F%2Fjingya-x.taobao.com%2Fwow%2Fz%2Ftfx%2Fstatic%2FdXCnMcpEntsE375tfHGp%3Fchannel%3Dglobal_zx%26spm%3Da21ug5.29159167.6452766900.2ndview_zxmore_click&sub=true
2025-07-29 00:00:51,919 - INFO - 调试信息 - 错误: 检测到需要登录
2025-07-29 00:00:51,919 - INFO - 请在打开的浏览器中完成登录，登录完成后点击'确认登录完成'按钮
2025-07-29 00:00:56,385 - INFO - Cookies已保存
2025-07-29 00:00:56,386 - INFO - 登录成功！现在可以开始铺货任务
2025-07-29 00:00:58,554 - INFO - Cookies已保存
2025-07-29 00:01:00,815 - INFO - 浏览器已关闭
2025-07-29 00:01:02,199 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059cda]
	(No symbol) [0x0x7ff69a061679]
	(No symbol) [0x0x7ff69a064a61]
	(No symbol) [0x0x7ff69a101e4b]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:01:02,199 - INFO - 防检测机制设置完成
2025-07-29 00:01:02,199 - INFO - 浏览器驱动初始化成功
2025-07-29 00:01:02,200 - INFO - 开始自动化铺货流程（有界面模式）
2025-07-29 00:01:02,200 - INFO - 第一步：正在导航到目标页面...
2025-07-29 00:01:05,971 - INFO - 第一步：点击登录按钮
2025-07-29 00:01:05,994 - INFO - 找到登录按钮，使用选择器: //*[@id='login-form']/div[6]/button
2025-07-29 00:01:07,023 - INFO - 准备点击登录按钮: 标签=button, 文本='登录', 选择器=//*[@id='login-form']/div[6]/button
2025-07-29 00:01:07,038 - INFO - 准备点击元素: tag=button, text='登录', class='fm-button fm-submit password-l', id=''
2025-07-29 00:01:08,075 - INFO - 尝试ActionChains点击...
2025-07-29 00:01:08,492 - INFO - ActionChains点击成功
2025-07-29 00:01:08,729 - INFO - 登录按钮点击成功
2025-07-29 00:01:08,729 - INFO - 登录按钮点击成功
2025-07-29 00:01:11,730 - INFO - 第二步：获取账号信息...
2025-07-29 00:01:11,730 - INFO - 正在跳转到个人中心页面: https://jingya.taobao.com/?v=2
2025-07-29 00:01:36,950 - INFO - 尝试获取账号名称，使用XPath: //*[@id='workbench-user-tool-btn']/div/div[2]
2025-07-29 00:01:36,963 - INFO - 成功获取账号信息: 卓祥22店:冰淇淋
2025-07-29 00:01:36,964 - INFO - 获取到账号名称: 卓祥22店:冰淇淋
2025-07-29 00:01:36,965 - INFO - GUI界面已更新账号显示: 卓祥22店:冰淇淋
2025-07-29 00:01:36,965 - INFO - 第三步：开始读取20个商品信息...
2025-07-29 00:01:36,967 - INFO - 开始爬取商品信息，最大数量: 20
2025-07-29 00:01:40,041 - INFO - 正在爬取第 1 页商品信息...
2025-07-29 00:01:42,048 - INFO - 找到商品容器，开始爬取商品，最大数量: 20
2025-07-29 00:01:42,062 - INFO - 找到商品项 1，检查元素信息...
2025-07-29 00:01:42,070 - INFO - 商品项 1 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:01:42,075 - INFO - 商品项 1 直接获取到data-autolog: item_id=681778175573&path=search_goods_card&type=search
2025-07-29 00:01:42,075 - INFO - 商品项 1 data-autolog: item_id=681778175573&path=search_goods_card&type=search
2025-07-29 00:01:42,076 - INFO - 商品项 1 提取到item_id: 681778175573
2025-07-29 00:01:42,088 - INFO - 商品项 1 商品名称: 保税直供 Dove多芬磨砂膏石榴籽乳木果风味身体磨砂膏298g
2025-07-29 00:01:42,099 - INFO - 找到商品项 2，检查元素信息...
2025-07-29 00:01:42,107 - INFO - 商品项 2 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:01:42,110 - INFO - 商品项 2 直接获取到data-autolog: item_id=690841537159&path=search_goods_card&index=1&type=search
2025-07-29 00:01:42,110 - INFO - 商品项 2 data-autolog: item_id=690841537159&path=search_goods_card&index=1&type=search
2025-07-29 00:01:42,110 - INFO - 商品项 2 提取到item_id: 690841537159
2025-07-29 00:01:42,121 - INFO - 商品项 2 商品名称: 保税直供 凡士林Vaseline烟酰胺身体乳400ml 725ml无压泵款200ml
2025-07-29 00:01:42,133 - INFO - 找到商品项 3，检查元素信息...
2025-07-29 00:01:42,141 - INFO - 商品项 3 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:01:42,145 - INFO - 商品项 3 直接获取到data-autolog: item_id=789447097664&path=search_goods_card&index=2&type=search
2025-07-29 00:01:42,146 - INFO - 商品项 3 data-autolog: item_id=789447097664&path=search_goods_card&index=2&type=search
2025-07-29 00:01:42,146 - INFO - 商品项 3 提取到item_id: 789447097664
2025-07-29 00:01:42,156 - INFO - 商品项 3 商品名称: 【甄选】珂润Curel泡沫洗面奶150ml
2025-07-29 00:01:42,170 - INFO - 找到商品项 4，检查元素信息...
2025-07-29 00:01:42,198 - INFO - 商品项 4 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:01:42,200 - INFO - 商品项 4 直接获取到data-autolog: item_id=694682843417&path=search_goods_card&index=3&type=search
2025-07-29 00:01:42,201 - INFO - 商品项 4 data-autolog: item_id=694682843417&path=search_goods_card&index=3&type=search
2025-07-29 00:01:42,201 - INFO - 商品项 4 提取到item_id: 694682843417
2025-07-29 00:01:42,211 - INFO - 商品项 4 商品名称: 【可开授权】日台混发Fino发膜美容液护发素 发膜230g
2025-07-29 00:01:42,222 - INFO - 找到商品项 5，检查元素信息...
2025-07-29 00:01:42,234 - INFO - 商品项 5 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:01:42,238 - INFO - 商品项 5 直接获取到data-autolog: item_id=681082809892&path=search_goods_card&index=4&type=search
2025-07-29 00:01:42,239 - INFO - 商品项 5 data-autolog: item_id=681082809892&path=search_goods_card&index=4&type=search
2025-07-29 00:01:42,239 - INFO - 商品项 5 提取到item_id: 681082809892
2025-07-29 00:01:42,250 - INFO - 商品项 5 商品名称: 保税直供 Dove多芬大白碗润肤身体乳300ml 【1//2//3罐】
2025-07-29 00:01:42,326 - INFO - 找到商品项 6，检查元素信息...
2025-07-29 00:01:42,343 - INFO - 商品项 6 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:01:42,346 - INFO - 商品项 6 直接获取到data-autolog: item_id=694675776881&path=search_goods_card&index=5&type=search
2025-07-29 00:01:42,347 - INFO - 商品项 6 data-autolog: item_id=694675776881&path=search_goods_card&index=5&type=search
2025-07-29 00:01:42,347 - INFO - 商品项 6 提取到item_id: 694675776881
2025-07-29 00:01:42,358 - INFO - 商品项 6 商品名称: 保税直供 Pantene潘婷护发素三分钟奇迹多效修护发膜级护发素
2025-07-29 00:01:42,371 - INFO - 找到商品项 7，检查元素信息...
2025-07-29 00:01:42,379 - INFO - 商品项 7 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:01:42,383 - INFO - 商品项 7 直接获取到data-autolog: item_id=680729304287&path=search_goods_card&index=6&type=search
2025-07-29 00:01:42,383 - INFO - 商品项 7 data-autolog: item_id=680729304287&path=search_goods_card&index=6&type=search
2025-07-29 00:01:42,384 - INFO - 商品项 7 提取到item_id: 680729304287
2025-07-29 00:01:42,392 - INFO - 商品项 7 商品名称: 保税直供 多芬dove空气感无硅油 洗发水480g 护发素480g护发临期
2025-07-29 00:01:42,408 - INFO - 找到商品项 8，检查元素信息...
2025-07-29 00:01:42,418 - INFO - 商品项 8 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:01:42,421 - INFO - 商品项 8 直接获取到data-autolog: item_id=710355107054&path=search_goods_card&index=7&type=search
2025-07-29 00:01:42,421 - INFO - 商品项 8 data-autolog: item_id=710355107054&path=search_goods_card&index=7&type=search
2025-07-29 00:01:42,421 - INFO - 商品项 8 提取到item_id: 710355107054
2025-07-29 00:01:42,430 - INFO - 商品项 8 商品名称: 保税直供 Dove多芬洗发水护发素空气感洗发水480g 【护发素临期
2025-07-29 00:01:42,440 - INFO - 找到商品项 9，检查元素信息...
2025-07-29 00:01:42,453 - INFO - 商品项 9 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:01:42,456 - INFO - 商品项 9 直接获取到data-autolog: item_id=740463658412&path=search_goods_card&index=8&type=search
2025-07-29 00:01:42,456 - INFO - 商品项 9 data-autolog: item_id=740463658412&path=search_goods_card&index=8&type=search
2025-07-29 00:01:42,456 - INFO - 商品项 9 提取到item_id: 740463658412
2025-07-29 00:01:42,467 - INFO - 商品项 9 商品名称: 【主推链接】花王cape空气感定型喷雾50g/180g 编码：3305300000
2025-07-29 00:01:42,480 - INFO - 找到商品项 10，检查元素信息...
2025-07-29 00:01:42,488 - INFO - 商品项 10 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:01:42,491 - INFO - 商品项 10 直接获取到data-autolog: item_id=821067902127&path=search_goods_card&index=9&type=search
2025-07-29 00:01:42,492 - INFO - 商品项 10 data-autolog: item_id=821067902127&path=search_goods_card&index=9&type=search
2025-07-29 00:01:42,492 - INFO - 商品项 10 提取到item_id: 821067902127
2025-07-29 00:01:42,499 - INFO - 商品项 10 商品名称: 丹碧丝TAMPAX卫生棉条长导管式内置式纯棉月经棉条棉棒游泳卫生巾
2025-07-29 00:01:42,513 - INFO - 找到商品项 11，检查元素信息...
2025-07-29 00:01:42,526 - INFO - 商品项 11 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:01:42,530 - INFO - 商品项 11 直接获取到data-autolog: item_id=789835300215&path=search_goods_card&index=10&type=search
2025-07-29 00:01:42,530 - INFO - 商品项 11 data-autolog: item_id=789835300215&path=search_goods_card&index=10&type=search
2025-07-29 00:01:42,530 - INFO - 商品项 11 提取到item_id: 789835300215
2025-07-29 00:01:42,561 - INFO - 商品项 11 商品名称: 【保税】雅漾喷雾爽肤水300ml
2025-07-29 00:01:42,575 - INFO - 找到商品项 12，检查元素信息...
2025-07-29 00:01:42,585 - INFO - 商品项 12 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:01:42,589 - INFO - 商品项 12 直接获取到data-autolog: item_id=843747605382&path=search_goods_card&index=11&type=search
2025-07-29 00:01:42,589 - INFO - 商品项 12 data-autolog: item_id=843747605382&path=search_goods_card&index=11&type=search
2025-07-29 00:01:42,589 - INFO - 商品项 12 提取到item_id: 843747605382
2025-07-29 00:01:42,599 - INFO - 商品项 12 商品名称: 倩碧紫胖子卸妆膏125ml/瓶
2025-07-29 00:01:42,611 - INFO - 找到商品项 13，检查元素信息...
2025-07-29 00:01:42,619 - INFO - 商品项 13 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:01:42,622 - INFO - 商品项 13 直接获取到data-autolog: item_id=675816539149&path=search_goods_card&index=12&type=search
2025-07-29 00:01:42,623 - INFO - 商品项 13 data-autolog: item_id=675816539149&path=search_goods_card&index=12&type=search
2025-07-29 00:01:42,623 - INFO - 商品项 13 提取到item_id: 675816539149
2025-07-29 00:01:42,632 - INFO - 商品项 13 商品名称: 新加坡Vicopyl维可保益生菌(Pylopass/拒绝幽门菌/抑口臭/调肠胃)
2025-07-29 00:01:42,660 - INFO - 找到商品项 14，检查元素信息...
2025-07-29 00:01:42,681 - INFO - 商品项 14 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:01:42,684 - INFO - 商品项 14 直接获取到data-autolog: item_id=846634805775&path=search_goods_card&index=13&type=search
2025-07-29 00:01:42,685 - INFO - 商品项 14 data-autolog: item_id=846634805775&path=search_goods_card&index=13&type=search
2025-07-29 00:01:42,685 - INFO - 商品项 14 提取到item_id: 846634805775
2025-07-29 00:01:42,698 - INFO - 商品项 14 商品名称: 【甄选】cow牛乳石碱原味/玫瑰花香 瓶装480ml
2025-07-29 00:01:42,710 - INFO - 找到商品项 15，检查元素信息...
2025-07-29 00:01:42,723 - INFO - 商品项 15 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:01:42,727 - INFO - 商品项 15 直接获取到data-autolog: item_id=852510575114&path=search_goods_card&index=14&type=search
2025-07-29 00:01:42,727 - INFO - 商品项 15 data-autolog: item_id=852510575114&path=search_goods_card&index=14&type=search
2025-07-29 00:01:42,728 - INFO - 商品项 15 提取到item_id: 852510575114
2025-07-29 00:01:42,737 - INFO - 商品项 15 商品名称: Nars 纳斯 红壳蜜粉饼16g（含粉扑）、Nars纳斯裸光蜜粉饼10g
2025-07-29 00:01:42,751 - INFO - 找到商品项 16，检查元素信息...
2025-07-29 00:01:42,762 - INFO - 商品项 16 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:01:42,764 - INFO - 商品项 16 直接获取到data-autolog: item_id=743442966826&path=search_goods_card&index=15&type=search
2025-07-29 00:01:42,764 - INFO - 商品项 16 data-autolog: item_id=743442966826&path=search_goods_card&index=15&type=search
2025-07-29 00:01:42,765 - INFO - 商品项 16 提取到item_id: 743442966826
2025-07-29 00:01:42,776 - INFO - 商品项 16 商品名称: ESI卡路里拜拜片白芸豆阻断片大餐救星膳食纤维身材热控管理
2025-07-29 00:01:42,788 - INFO - 找到商品项 17，检查元素信息...
2025-07-29 00:01:42,798 - INFO - 商品项 17 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:01:42,800 - INFO - 商品项 17 直接获取到data-autolog: item_id=848934676698&path=search_goods_card&index=16&type=search
2025-07-29 00:01:42,800 - INFO - 商品项 17 data-autolog: item_id=848934676698&path=search_goods_card&index=16&type=search
2025-07-29 00:01:42,800 - INFO - 商品项 17 提取到item_id: 848934676698
2025-07-29 00:01:42,811 - INFO - 商品项 17 商品名称: Estee Lauder 雅诗兰黛持妆粉底液30ml 2C0 /1w1/1C1
2025-07-29 00:01:42,831 - INFO - 找到商品项 18，检查元素信息...
2025-07-29 00:01:42,848 - INFO - 商品项 18 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:01:42,852 - INFO - 商品项 18 直接获取到data-autolog: item_id=693758717931&path=search_goods_card&index=17&type=search
2025-07-29 00:01:42,852 - INFO - 商品项 18 data-autolog: item_id=693758717931&path=search_goods_card&index=17&type=search
2025-07-29 00:01:42,852 - INFO - 商品项 18 提取到item_id: 693758717931
2025-07-29 00:01:42,862 - INFO - 商品项 18 商品名称: 保税直供 资生堂fino高效渗透发膜 230g （1/2/3罐）日版
2025-07-29 00:01:42,877 - INFO - 找到商品项 19，检查元素信息...
2025-07-29 00:01:42,889 - INFO - 商品项 19 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:01:42,892 - INFO - 商品项 19 直接获取到data-autolog: item_id=691142128229&path=search_goods_card&index=18&type=search
2025-07-29 00:01:42,893 - INFO - 商品项 19 data-autolog: item_id=691142128229&path=search_goods_card&index=18&type=search
2025-07-29 00:01:42,893 - INFO - 商品项 19 提取到item_id: 691142128229
2025-07-29 00:01:42,902 - INFO - 商品项 19 商品名称: 保税 日版Dove多芬洁面洗面奶新版 正装瓶150ml 补充袋125ml 甄选
2025-07-29 00:01:42,913 - INFO - 找到商品项 20，检查元素信息...
2025-07-29 00:01:42,924 - INFO - 商品项 20 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:01:42,928 - INFO - 商品项 20 直接获取到data-autolog: item_id=690739721578&path=search_goods_card&index=19&type=search
2025-07-29 00:01:42,928 - INFO - 商品项 20 data-autolog: item_id=690739721578&path=search_goods_card&index=19&type=search
2025-07-29 00:01:42,928 - INFO - 商品项 20 提取到item_id: 690739721578
2025-07-29 00:01:42,937 - INFO - 商品项 20 商品名称: 保税直供 SUNSILK夏士莲椰子洗发水//护发素450ml 泰产
2025-07-29 00:01:42,937 - INFO - 已爬取到 20 个商品，停止当前页面爬取
2025-07-29 00:01:42,937 - INFO - 当前页面爬取完成，找到 20 个商品
2025-07-29 00:01:42,937 - INFO - 第 1 页找到 20 个商品，总计: 20
2025-07-29 00:01:42,937 - INFO - 已达到最大商品数量 20，停止爬取
2025-07-29 00:01:42,938 - INFO - 爬取完成，共找到 20 个商品
2025-07-29 00:01:42,938 - INFO - 成功读取到 20 个商品（目标20个），开始铺货任务
2025-07-29 00:01:42,938 - INFO - 第四步：开始处理商品...
2025-07-29 00:01:42,952 - INFO - 已读取 20 个商品，目标成功铺货 3 个商品
2025-07-29 00:01:42,952 - INFO - 当前批次大小: 15
2025-07-29 00:01:42,955 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=681778175573
2025-07-29 00:01:46,626 - INFO - 页面加载完成
2025-07-29 00:01:46,626 - INFO - 模拟阅读行为，停顿 2.4 秒
2025-07-29 00:01:49,726 - INFO - 开始第 1 次铺货尝试...
2025-07-29 00:01:49,727 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-29 00:01:49,727 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-29 00:01:49,735 - INFO - span innerHTML: 立即铺货
2025-07-29 00:01:49,761 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-29 00:01:49,761 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-29 00:01:49,774 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-29 00:01:51,203 - INFO - 尝试ActionChains点击...
2025-07-29 00:01:51,495 - INFO - ActionChains点击成功
2025-07-29 00:01:51,815 - INFO - 铺货按钮点击成功
2025-07-29 00:01:53,829 - INFO - 使用配置的XPath找到确认按钮
2025-07-29 00:01:53,843 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-29 00:01:55,123 - INFO - 尝试ActionChains点击...
2025-07-29 00:01:55,398 - INFO - ActionChains点击成功
2025-07-29 00:01:55,740 - INFO - 确认按钮点击成功
2025-07-29 00:01:57,741 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-29 00:01:57,748 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-29 00:01:57,748 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:01:58,758 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-29 00:01:58,758 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:01:59,768 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-29 00:01:59,768 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:02:00,802 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-29 00:02:00,802 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:02:01,812 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-07-29 00:02:01,812 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:02:02,821 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-07-29 00:02:02,822 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:02:03,830 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-07-29 00:02:03,830 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:02:04,838 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-07-29 00:02:04,839 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:02:05,848 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-07-29 00:02:05,848 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:02:06,855 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-07-29 00:02:06,856 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:02:07,857 - INFO - 等待超时，进行最后一次检查...
2025-07-29 00:02:07,866 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-07-29 00:02:07,866 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-07-29 00:02:07,866 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-07-29 00:02:07,866 - INFO - 检测是否出现滑块验证...
2025-07-29 00:02:07,876 - INFO - 在主文档中未找到滑块，检查iframe...
2025-07-29 00:02:07,881 - INFO - 找到 1 个iframe
2025-07-29 00:02:07,882 - INFO - 检查第 1 个iframe...
2025-07-29 00:02:07,906 - INFO - 在第 1 个iframe中检测到滑块验证，开始处理...
2025-07-29 00:02:07,906 - INFO - 开始处理滑块验证...
2025-07-29 00:02:07,920 - INFO - 滑块容器宽度: 300px
2025-07-29 00:02:07,920 - INFO - 计算拖拽距离: 268px (比例: 0.89)
2025-07-29 00:02:07,920 - INFO - 开始慢速匀速滑块操作...
2025-07-29 00:02:07,921 - INFO - 构建滑块操作链，拖拽距离: 268px
2025-07-29 00:02:07,921 - INFO - 执行匀速滑块拖拽...
2025-07-29 00:02:11,603 - INFO - 匀速滑块操作完成
2025-07-29 00:02:11,607 - INFO - 滑块验证成功，滑块已消失
2025-07-29 00:02:11,609 - INFO - 滑块验证处理成功，准备重试铺货
2025-07-29 00:02:13,610 - INFO - 开始第 2 次铺货尝试...
2025-07-29 00:02:13,610 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-29 00:02:13,610 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-29 00:02:13,617 - INFO - span innerHTML: 立即铺货
2025-07-29 00:02:23,803 - WARNING - 等待元素可点击超时: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-29 00:02:23,804 - WARNING - 使用配置的XPath未找到添加商品按钮
2025-07-29 00:02:23,804 - ERROR - 未找到铺货按钮
2025-07-29 00:02:23,804 - WARNING - 第 1 个商品处理失败（不计入成功数量）
2025-07-29 00:02:23,804 - INFO - 商品处理失败，立即处理下一个商品
2025-07-29 00:02:23,818 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=690841537159
2025-07-29 00:02:26,933 - INFO - 页面加载完成
2025-07-29 00:02:26,933 - INFO - 模拟阅读行为，停顿 2.3 秒
2025-07-29 00:02:30,372 - INFO - 开始第 1 次铺货尝试...
2025-07-29 00:02:30,372 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-29 00:02:30,372 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-29 00:02:30,378 - INFO - span innerHTML: 立即铺货
2025-07-29 00:02:30,399 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-29 00:02:30,400 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-29 00:02:30,414 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-29 00:02:31,565 - INFO - 尝试ActionChains点击...
2025-07-29 00:02:31,859 - INFO - ActionChains点击成功
2025-07-29 00:02:32,087 - INFO - 铺货按钮点击成功
2025-07-29 00:02:34,104 - INFO - 使用配置的XPath找到确认按钮
2025-07-29 00:02:34,118 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-29 00:02:35,309 - INFO - 尝试ActionChains点击...
2025-07-29 00:02:35,589 - INFO - ActionChains点击成功
2025-07-29 00:02:35,796 - INFO - 确认按钮点击成功
2025-07-29 00:02:37,797 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-29 00:02:37,805 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-29 00:02:37,805 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:02:38,814 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-29 00:02:38,814 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:02:39,823 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-29 00:02:39,823 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:02:40,831 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-29 00:02:40,831 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:02:41,841 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-07-29 00:02:41,841 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:02:42,849 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-07-29 00:02:42,849 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:02:43,857 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-07-29 00:02:43,857 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:02:44,866 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-07-29 00:02:44,866 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:02:45,874 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-07-29 00:02:45,874 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:02:46,883 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-07-29 00:02:46,884 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:02:47,885 - INFO - 等待超时，进行最后一次检查...
2025-07-29 00:02:47,894 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-07-29 00:02:47,894 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-07-29 00:02:47,895 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-07-29 00:02:47,896 - INFO - 检测是否出现滑块验证...
2025-07-29 00:02:47,907 - INFO - 在主文档中未找到滑块，检查iframe...
2025-07-29 00:02:47,914 - INFO - 找到 1 个iframe
2025-07-29 00:02:47,914 - INFO - 检查第 1 个iframe...
2025-07-29 00:02:47,941 - INFO - 在第 1 个iframe中检测到滑块验证，开始处理...
2025-07-29 00:02:47,942 - INFO - 开始处理滑块验证...
2025-07-29 00:02:47,962 - INFO - 滑块容器宽度: 300px
2025-07-29 00:02:47,962 - INFO - 计算拖拽距离: 270px (比例: 0.90)
2025-07-29 00:02:47,962 - INFO - 开始慢速匀速滑块操作...
2025-07-29 00:02:47,963 - INFO - 构建滑块操作链，拖拽距离: 270px
2025-07-29 00:02:47,963 - INFO - 执行匀速滑块拖拽...
2025-07-29 00:02:51,600 - INFO - 匀速滑块操作完成
2025-07-29 00:02:51,604 - INFO - 滑块验证成功，滑块已消失
2025-07-29 00:02:51,606 - INFO - 滑块验证处理成功，准备重试铺货
2025-07-29 00:02:53,607 - INFO - 开始第 2 次铺货尝试...
2025-07-29 00:02:53,607 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-29 00:02:53,607 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-29 00:02:53,615 - INFO - span innerHTML: 立即铺货
2025-07-29 00:03:03,836 - WARNING - 等待元素可点击超时: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-29 00:03:03,837 - WARNING - 使用配置的XPath未找到添加商品按钮
2025-07-29 00:03:03,837 - ERROR - 未找到铺货按钮
2025-07-29 00:03:03,838 - WARNING - 第 2 个商品处理失败（不计入成功数量）
2025-07-29 00:03:03,838 - INFO - 商品处理失败，立即处理下一个商品
2025-07-29 00:03:03,851 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=789447097664
2025-07-29 00:03:06,972 - INFO - 页面加载完成
2025-07-29 00:03:06,972 - INFO - 模拟阅读行为，停顿 2.9 秒
2025-07-29 00:03:09,895 - INFO - 添加额外延迟: 1.7秒
2025-07-29 00:03:12,203 - INFO - 开始第 1 次铺货尝试...
2025-07-29 00:03:12,203 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-29 00:03:12,203 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-29 00:03:12,209 - INFO - span innerHTML: 立即铺货
2025-07-29 00:03:12,235 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-29 00:03:12,235 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-29 00:03:12,254 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-29 00:03:13,353 - INFO - 尝试ActionChains点击...
2025-07-29 00:03:13,636 - INFO - ActionChains点击成功
2025-07-29 00:03:14,037 - INFO - 铺货按钮点击成功
2025-07-29 00:03:16,053 - INFO - 使用配置的XPath找到确认按钮
2025-07-29 00:03:16,066 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-29 00:03:17,569 - INFO - 尝试ActionChains点击...
2025-07-29 00:03:17,840 - INFO - ActionChains点击成功
2025-07-29 00:03:18,259 - INFO - 确认按钮点击成功
2025-07-29 00:03:20,259 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-29 00:03:20,267 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-29 00:03:20,267 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:03:21,274 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-29 00:03:21,274 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:03:22,283 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-29 00:03:22,284 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:03:23,292 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-29 00:03:23,292 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:03:24,299 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-07-29 00:03:24,301 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:03:25,309 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-07-29 00:03:25,309 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:03:26,316 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-07-29 00:03:26,316 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:03:27,325 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-07-29 00:03:27,325 - INFO - span仍为'立即铺货'，继续等待...
2025-07-29 00:03:28,341 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-07-29 00:03:28,341 - INFO - span仍为'立即铺货'，继续等待...
