2025-07-28 23:57:17,746 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059cda]
	(No symbol) [0x0x7ff69a061679]
	(No symbol) [0x0x7ff69a064a61]
	(No symbol) [0x0x7ff69a101e4b]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:57:17,747 - INFO - 防检测机制设置完成
2025-07-28 23:57:17,747 - INFO - 浏览器驱动初始化成功
2025-07-28 23:57:17,753 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:57:17,757 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:57:17,760 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:57:17,762 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:57:17,766 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:57:17,772 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:57:17,774 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:57:17,777 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:57:17,780 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:57:17,786 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:57:17,790 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:57:17,794 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:57:17,798 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:57:17,800 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:57:17,802 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:57:17,804 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:57:17,807 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:57:17,811 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:57:17,814 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:57:17,816 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:57:17,816 - INFO - Cookies已加载
2025-07-28 23:57:17,817 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?channel=global_zx&spm=a21ug5.29159167.6452766900.2ndview_zxmore_click
2025-07-28 23:57:22,216 - INFO - 当前User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-28 23:57:22,217 - INFO - 确认：使用桌面版User-Agent
2025-07-28 23:57:22,220 - INFO - 桌面版User-Agent设置完成: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-28 23:57:23,050 - WARNING - 检测到需要登录，请手动登录后继续
2025-07-28 23:57:23,146 - INFO - 调试截图已保存: debug_logs/login_required_screenshot_20250728_235723.png
2025-07-28 23:57:23,148 - INFO - 浏览器日志已保存: debug_logs/login_required_browser_log_20250728_235723.txt
2025-07-28 23:57:23,161 - INFO - 页面源码已保存: debug_logs/login_required_page_source_20250728_235723.html
2025-07-28 23:57:23,164 - INFO - 调试信息 - 当前URL: https://login.taobao.com/havanaone/login/login.htm?bizName=taobao&ttid=&redirectURL=https%3A%2F%2Fjingya-x.taobao.com%2Fwow%2Fz%2Ftfx%2Fstatic%2FdXCnMcpEntsE375tfHGp%3Fchannel%3Dglobal_zx%26spm%3Da21ug5.29159167.6452766900.2ndview_zxmore_click&sub=true
2025-07-28 23:57:23,164 - INFO - 调试信息 - 错误: 检测到需要登录
2025-07-28 23:57:23,165 - INFO - 请在打开的浏览器中完成登录，登录完成后点击'确认登录完成'按钮
2025-07-28 23:57:31,265 - INFO - Cookies已保存
2025-07-28 23:57:31,265 - INFO - 登录成功！现在可以开始铺货任务
2025-07-28 23:57:33,439 - INFO - Cookies已保存
2025-07-28 23:57:35,723 - INFO - 浏览器已关闭
2025-07-28 23:57:37,124 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059cda]
	(No symbol) [0x0x7ff69a061679]
	(No symbol) [0x0x7ff69a064a61]
	(No symbol) [0x0x7ff69a101e4b]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:57:37,124 - INFO - 防检测机制设置完成
2025-07-28 23:57:37,124 - INFO - 浏览器驱动初始化成功
2025-07-28 23:57:37,124 - INFO - 开始自动化铺货流程（有界面模式）
2025-07-28 23:57:37,124 - INFO - 第一步：正在导航到目标页面...
2025-07-28 23:57:40,871 - INFO - 第一步：点击登录按钮
2025-07-28 23:57:40,900 - INFO - 找到登录按钮，使用选择器: //*[@id='login-form']/div[6]/button
2025-07-28 23:57:41,925 - INFO - 准备点击登录按钮: 标签=button, 文本='登录', 选择器=//*[@id='login-form']/div[6]/button
2025-07-28 23:57:41,939 - INFO - 准备点击元素: tag=button, text='登录', class='fm-button fm-submit password-l', id=''
2025-07-28 23:57:43,388 - INFO - 尝试ActionChains点击...
2025-07-28 23:57:47,862 - INFO - ActionChains点击成功
2025-07-28 23:57:48,142 - INFO - 登录按钮点击成功
2025-07-28 23:57:48,142 - INFO - 登录按钮点击成功
2025-07-28 23:57:51,142 - INFO - 第二步：获取账号信息...
2025-07-28 23:57:51,142 - INFO - 正在跳转到个人中心页面: https://jingya.taobao.com/?v=2
2025-07-28 23:58:15,893 - INFO - 尝试获取账号名称，使用XPath: //*[@id='workbench-user-tool-btn']/div/div[2]
2025-07-28 23:58:15,924 - INFO - 成功获取账号信息: 卓祥22店:冰淇淋
2025-07-28 23:58:15,925 - INFO - 获取到账号名称: 卓祥22店:冰淇淋
2025-07-28 23:58:15,926 - INFO - GUI界面已更新账号显示: 卓祥22店:冰淇淋
2025-07-28 23:58:15,926 - INFO - 第三步：开始读取20个商品信息...
2025-07-28 23:58:15,927 - INFO - 开始爬取商品信息，最大数量: 20
2025-07-28 23:58:20,060 - INFO - 正在爬取第 1 页商品信息...
2025-07-28 23:58:22,069 - INFO - 找到商品容器，开始爬取商品，最大数量: 20
2025-07-28 23:58:22,080 - INFO - 找到商品项 1，检查元素信息...
2025-07-28 23:58:22,093 - INFO - 商品项 1 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:58:22,096 - INFO - 商品项 1 直接获取到data-autolog: item_id=681778175573&path=search_goods_card&type=search
2025-07-28 23:58:22,096 - INFO - 商品项 1 data-autolog: item_id=681778175573&path=search_goods_card&type=search
2025-07-28 23:58:22,096 - INFO - 商品项 1 提取到item_id: 681778175573
2025-07-28 23:58:22,109 - INFO - 商品项 1 商品名称: 保税直供 Dove多芬磨砂膏石榴籽乳木果风味身体磨砂膏298g
2025-07-28 23:58:22,119 - INFO - 找到商品项 2，检查元素信息...
2025-07-28 23:58:22,127 - INFO - 商品项 2 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:58:22,131 - INFO - 商品项 2 直接获取到data-autolog: item_id=690841537159&path=search_goods_card&index=1&type=search
2025-07-28 23:58:22,131 - INFO - 商品项 2 data-autolog: item_id=690841537159&path=search_goods_card&index=1&type=search
2025-07-28 23:58:22,131 - INFO - 商品项 2 提取到item_id: 690841537159
2025-07-28 23:58:22,140 - INFO - 商品项 2 商品名称: 保税直供 凡士林Vaseline烟酰胺身体乳400ml 725ml无压泵款200ml
2025-07-28 23:58:22,152 - INFO - 找到商品项 3，检查元素信息...
2025-07-28 23:58:22,161 - INFO - 商品项 3 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:58:22,164 - INFO - 商品项 3 直接获取到data-autolog: item_id=789447097664&path=search_goods_card&index=2&type=search
2025-07-28 23:58:22,164 - INFO - 商品项 3 data-autolog: item_id=789447097664&path=search_goods_card&index=2&type=search
2025-07-28 23:58:22,164 - INFO - 商品项 3 提取到item_id: 789447097664
2025-07-28 23:58:22,267 - INFO - 商品项 3 商品名称: 【甄选】珂润Curel泡沫洗面奶150ml
2025-07-28 23:58:22,279 - INFO - 找到商品项 4，检查元素信息...
2025-07-28 23:58:22,291 - INFO - 商品项 4 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:58:22,295 - INFO - 商品项 4 直接获取到data-autolog: item_id=694682843417&path=search_goods_card&index=3&type=search
2025-07-28 23:58:22,295 - INFO - 商品项 4 data-autolog: item_id=694682843417&path=search_goods_card&index=3&type=search
2025-07-28 23:58:22,295 - INFO - 商品项 4 提取到item_id: 694682843417
2025-07-28 23:58:22,303 - INFO - 商品项 4 商品名称: 【可开授权】日台混发Fino发膜美容液护发素 发膜230g
2025-07-28 23:58:22,317 - INFO - 找到商品项 5，检查元素信息...
2025-07-28 23:58:22,324 - INFO - 商品项 5 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:58:22,327 - INFO - 商品项 5 直接获取到data-autolog: item_id=681082809892&path=search_goods_card&index=4&type=search
2025-07-28 23:58:22,327 - INFO - 商品项 5 data-autolog: item_id=681082809892&path=search_goods_card&index=4&type=search
2025-07-28 23:58:22,327 - INFO - 商品项 5 提取到item_id: 681082809892
2025-07-28 23:58:22,337 - INFO - 商品项 5 商品名称: 保税直供 Dove多芬大白碗润肤身体乳300ml 【1//2//3罐】
2025-07-28 23:58:22,349 - INFO - 找到商品项 6，检查元素信息...
2025-07-28 23:58:22,358 - INFO - 商品项 6 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:58:22,360 - INFO - 商品项 6 直接获取到data-autolog: item_id=694675776881&path=search_goods_card&index=5&type=search
2025-07-28 23:58:22,361 - INFO - 商品项 6 data-autolog: item_id=694675776881&path=search_goods_card&index=5&type=search
2025-07-28 23:58:22,361 - INFO - 商品项 6 提取到item_id: 694675776881
2025-07-28 23:58:22,369 - INFO - 商品项 6 商品名称: 保税直供 Pantene潘婷护发素三分钟奇迹多效修护发膜级护发素
2025-07-28 23:58:22,380 - INFO - 找到商品项 7，检查元素信息...
2025-07-28 23:58:22,389 - INFO - 商品项 7 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:58:22,391 - INFO - 商品项 7 直接获取到data-autolog: item_id=680729304287&path=search_goods_card&index=6&type=search
2025-07-28 23:58:22,391 - INFO - 商品项 7 data-autolog: item_id=680729304287&path=search_goods_card&index=6&type=search
2025-07-28 23:58:22,391 - INFO - 商品项 7 提取到item_id: 680729304287
2025-07-28 23:58:22,400 - INFO - 商品项 7 商品名称: 保税直供 多芬dove空气感无硅油 洗发水480g 护发素480g护发临期
2025-07-28 23:58:22,411 - INFO - 找到商品项 8，检查元素信息...
2025-07-28 23:58:22,419 - INFO - 商品项 8 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:58:22,422 - INFO - 商品项 8 直接获取到data-autolog: item_id=710355107054&path=search_goods_card&index=7&type=search
2025-07-28 23:58:22,422 - INFO - 商品项 8 data-autolog: item_id=710355107054&path=search_goods_card&index=7&type=search
2025-07-28 23:58:22,422 - INFO - 商品项 8 提取到item_id: 710355107054
2025-07-28 23:58:22,431 - INFO - 商品项 8 商品名称: 保税直供 Dove多芬洗发水护发素空气感洗发水480g 【护发素临期
2025-07-28 23:58:22,443 - INFO - 找到商品项 9，检查元素信息...
2025-07-28 23:58:22,451 - INFO - 商品项 9 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:58:22,454 - INFO - 商品项 9 直接获取到data-autolog: item_id=740463658412&path=search_goods_card&index=8&type=search
2025-07-28 23:58:22,454 - INFO - 商品项 9 data-autolog: item_id=740463658412&path=search_goods_card&index=8&type=search
2025-07-28 23:58:22,454 - INFO - 商品项 9 提取到item_id: 740463658412
2025-07-28 23:58:22,462 - INFO - 商品项 9 商品名称: 【主推链接】花王cape空气感定型喷雾50g/180g 编码：3305300000
2025-07-28 23:58:22,472 - INFO - 找到商品项 10，检查元素信息...
2025-07-28 23:58:22,481 - INFO - 商品项 10 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:58:22,484 - INFO - 商品项 10 直接获取到data-autolog: item_id=821067902127&path=search_goods_card&index=9&type=search
2025-07-28 23:58:22,484 - INFO - 商品项 10 data-autolog: item_id=821067902127&path=search_goods_card&index=9&type=search
2025-07-28 23:58:22,484 - INFO - 商品项 10 提取到item_id: 821067902127
2025-07-28 23:58:22,494 - INFO - 商品项 10 商品名称: 丹碧丝TAMPAX卫生棉条长导管式内置式纯棉月经棉条棉棒游泳卫生巾
2025-07-28 23:58:22,505 - INFO - 找到商品项 11，检查元素信息...
2025-07-28 23:58:22,511 - INFO - 商品项 11 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:58:22,514 - INFO - 商品项 11 直接获取到data-autolog: item_id=789835300215&path=search_goods_card&index=10&type=search
2025-07-28 23:58:22,514 - INFO - 商品项 11 data-autolog: item_id=789835300215&path=search_goods_card&index=10&type=search
2025-07-28 23:58:22,514 - INFO - 商品项 11 提取到item_id: 789835300215
2025-07-28 23:58:22,523 - INFO - 商品项 11 商品名称: 【保税】雅漾喷雾爽肤水300ml
2025-07-28 23:58:22,535 - INFO - 找到商品项 12，检查元素信息...
2025-07-28 23:58:22,542 - INFO - 商品项 12 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:58:22,545 - INFO - 商品项 12 直接获取到data-autolog: item_id=843747605382&path=search_goods_card&index=11&type=search
2025-07-28 23:58:22,545 - INFO - 商品项 12 data-autolog: item_id=843747605382&path=search_goods_card&index=11&type=search
2025-07-28 23:58:22,545 - INFO - 商品项 12 提取到item_id: 843747605382
2025-07-28 23:58:22,553 - INFO - 商品项 12 商品名称: 倩碧紫胖子卸妆膏125ml/瓶
2025-07-28 23:58:22,564 - INFO - 找到商品项 13，检查元素信息...
2025-07-28 23:58:22,573 - INFO - 商品项 13 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:58:22,576 - INFO - 商品项 13 直接获取到data-autolog: item_id=675816539149&path=search_goods_card&index=12&type=search
2025-07-28 23:58:22,576 - INFO - 商品项 13 data-autolog: item_id=675816539149&path=search_goods_card&index=12&type=search
2025-07-28 23:58:22,576 - INFO - 商品项 13 提取到item_id: 675816539149
2025-07-28 23:58:22,585 - INFO - 商品项 13 商品名称: 新加坡Vicopyl维可保益生菌(Pylopass/拒绝幽门菌/抑口臭/调肠胃)
2025-07-28 23:58:22,595 - INFO - 找到商品项 14，检查元素信息...
2025-07-28 23:58:22,603 - INFO - 商品项 14 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:58:22,606 - INFO - 商品项 14 直接获取到data-autolog: item_id=846634805775&path=search_goods_card&index=13&type=search
2025-07-28 23:58:22,606 - INFO - 商品项 14 data-autolog: item_id=846634805775&path=search_goods_card&index=13&type=search
2025-07-28 23:58:22,606 - INFO - 商品项 14 提取到item_id: 846634805775
2025-07-28 23:58:22,638 - INFO - 商品项 14 商品名称: 【甄选】cow牛乳石碱原味/玫瑰花香 瓶装480ml
2025-07-28 23:58:22,648 - INFO - 找到商品项 15，检查元素信息...
2025-07-28 23:58:22,656 - INFO - 商品项 15 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:58:22,659 - INFO - 商品项 15 直接获取到data-autolog: item_id=852510575114&path=search_goods_card&index=14&type=search
2025-07-28 23:58:22,659 - INFO - 商品项 15 data-autolog: item_id=852510575114&path=search_goods_card&index=14&type=search
2025-07-28 23:58:22,659 - INFO - 商品项 15 提取到item_id: 852510575114
2025-07-28 23:58:22,668 - INFO - 商品项 15 商品名称: Nars 纳斯 红壳蜜粉饼16g（含粉扑）、Nars纳斯裸光蜜粉饼10g
2025-07-28 23:58:22,679 - INFO - 找到商品项 16，检查元素信息...
2025-07-28 23:58:22,688 - INFO - 商品项 16 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:58:22,691 - INFO - 商品项 16 直接获取到data-autolog: item_id=743442966826&path=search_goods_card&index=15&type=search
2025-07-28 23:58:22,692 - INFO - 商品项 16 data-autolog: item_id=743442966826&path=search_goods_card&index=15&type=search
2025-07-28 23:58:22,692 - INFO - 商品项 16 提取到item_id: 743442966826
2025-07-28 23:58:22,700 - INFO - 商品项 16 商品名称: ESI卡路里拜拜片白芸豆阻断片大餐救星膳食纤维身材热控管理
2025-07-28 23:58:22,713 - INFO - 找到商品项 17，检查元素信息...
2025-07-28 23:58:22,723 - INFO - 商品项 17 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:58:22,726 - INFO - 商品项 17 直接获取到data-autolog: item_id=848934676698&path=search_goods_card&index=16&type=search
2025-07-28 23:58:22,726 - INFO - 商品项 17 data-autolog: item_id=848934676698&path=search_goods_card&index=16&type=search
2025-07-28 23:58:22,726 - INFO - 商品项 17 提取到item_id: 848934676698
2025-07-28 23:58:22,735 - INFO - 商品项 17 商品名称: Estee Lauder 雅诗兰黛持妆粉底液30ml 2C0 /1w1/1C1
2025-07-28 23:58:22,748 - INFO - 找到商品项 18，检查元素信息...
2025-07-28 23:58:22,757 - INFO - 商品项 18 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:58:22,760 - INFO - 商品项 18 直接获取到data-autolog: item_id=693758717931&path=search_goods_card&index=17&type=search
2025-07-28 23:58:22,760 - INFO - 商品项 18 data-autolog: item_id=693758717931&path=search_goods_card&index=17&type=search
2025-07-28 23:58:22,760 - INFO - 商品项 18 提取到item_id: 693758717931
2025-07-28 23:58:22,769 - INFO - 商品项 18 商品名称: 保税直供 资生堂fino高效渗透发膜 230g （1/2/3罐）日版
2025-07-28 23:58:22,783 - INFO - 找到商品项 19，检查元素信息...
2025-07-28 23:58:22,792 - INFO - 商品项 19 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:58:22,794 - INFO - 商品项 19 直接获取到data-autolog: item_id=691142128229&path=search_goods_card&index=18&type=search
2025-07-28 23:58:22,795 - INFO - 商品项 19 data-autolog: item_id=691142128229&path=search_goods_card&index=18&type=search
2025-07-28 23:58:22,795 - INFO - 商品项 19 提取到item_id: 691142128229
2025-07-28 23:58:22,805 - INFO - 商品项 19 商品名称: 保税 日版Dove多芬洁面洗面奶新版 正装瓶150ml 补充袋125ml 甄选
2025-07-28 23:58:22,819 - INFO - 找到商品项 20，检查元素信息...
2025-07-28 23:58:22,829 - INFO - 商品项 20 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:58:22,832 - INFO - 商品项 20 直接获取到data-autolog: item_id=690739721578&path=search_goods_card&index=19&type=search
2025-07-28 23:58:22,832 - INFO - 商品项 20 data-autolog: item_id=690739721578&path=search_goods_card&index=19&type=search
2025-07-28 23:58:22,832 - INFO - 商品项 20 提取到item_id: 690739721578
2025-07-28 23:58:22,842 - INFO - 商品项 20 商品名称: 保税直供 SUNSILK夏士莲椰子洗发水//护发素450ml 泰产
2025-07-28 23:58:22,842 - INFO - 已爬取到 20 个商品，停止当前页面爬取
2025-07-28 23:58:22,843 - INFO - 当前页面爬取完成，找到 20 个商品
2025-07-28 23:58:22,843 - INFO - 第 1 页找到 20 个商品，总计: 20
2025-07-28 23:58:22,843 - INFO - 已达到最大商品数量 20，停止爬取
2025-07-28 23:58:22,843 - INFO - 爬取完成，共找到 20 个商品
2025-07-28 23:58:22,843 - INFO - 成功读取到 20 个商品（目标20个），开始铺货任务
2025-07-28 23:58:22,843 - INFO - 第四步：开始处理商品...
2025-07-28 23:58:22,853 - INFO - 已读取 20 个商品，目标成功铺货 3 个商品
2025-07-28 23:58:22,854 - INFO - 当前批次大小: 11
2025-07-28 23:58:22,854 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=681778175573
2025-07-28 23:58:26,495 - INFO - 页面加载完成
2025-07-28 23:58:26,495 - INFO - 模拟阅读行为，停顿 1.2 秒
2025-07-28 23:58:28,998 - INFO - 添加额外延迟: 1.3秒
2025-07-28 23:58:31,448 - INFO - 开始第 1 次铺货尝试...
2025-07-28 23:58:31,448 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:58:31,449 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:58:31,458 - INFO - span innerHTML: 立即铺货
2025-07-28 23:58:31,477 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-28 23:58:31,477 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-28 23:58:31,492 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-28 23:58:32,595 - INFO - 尝试ActionChains点击...
2025-07-28 23:58:32,879 - INFO - ActionChains点击成功
2025-07-28 23:58:33,360 - INFO - 铺货按钮点击成功
2025-07-28 23:58:35,376 - INFO - 使用配置的XPath找到确认按钮
2025-07-28 23:58:35,405 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-28 23:58:36,800 - INFO - 尝试ActionChains点击...
2025-07-28 23:58:37,073 - INFO - ActionChains点击成功
2025-07-28 23:58:37,363 - INFO - 确认按钮点击成功
2025-07-28 23:58:39,364 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-28 23:58:39,375 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-28 23:58:39,375 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:58:40,382 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-28 23:58:40,383 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:58:41,392 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-28 23:58:41,392 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:58:42,401 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-28 23:58:42,401 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:58:43,409 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-07-28 23:58:43,409 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:58:44,416 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-07-28 23:58:44,417 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:58:45,427 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-07-28 23:58:45,427 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:58:46,435 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-07-28 23:58:46,435 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:58:47,444 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-07-28 23:58:47,444 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:58:48,452 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-07-28 23:58:48,452 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:58:49,453 - INFO - 等待超时，进行最后一次检查...
2025-07-28 23:58:49,460 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-07-28 23:58:49,460 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-07-28 23:58:49,460 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-07-28 23:58:49,460 - INFO - 检测是否出现滑块验证...
2025-07-28 23:58:49,469 - INFO - 在主文档中未找到滑块，检查iframe...
2025-07-28 23:58:49,475 - INFO - 找到 1 个iframe
2025-07-28 23:58:49,476 - INFO - 检查第 1 个iframe...
2025-07-28 23:58:49,499 - INFO - 在第 1 个iframe中检测到滑块验证，开始处理...
2025-07-28 23:58:49,499 - INFO - 开始处理滑块验证...
2025-07-28 23:58:49,512 - INFO - 滑块容器宽度: 300px
2025-07-28 23:58:49,512 - INFO - 计算拖拽距离: 274px (比例: 0.91)
2025-07-28 23:58:49,512 - INFO - 开始慢速匀速滑块操作...
2025-07-28 23:58:49,513 - INFO - 构建慢速滑块操作链，拖拽距离: 274px
2025-07-28 23:58:49,513 - INFO - 慢速匀速滑块操作失败: ActionChains.move_by_offset() got an unexpected keyword argument 'duration'
2025-07-28 23:58:49,514 - INFO - 未检测到滑块验证或处理失败
2025-07-28 23:58:49,514 - WARNING - 第 1 个商品处理失败（不计入成功数量）
2025-07-28 23:58:49,514 - INFO - 商品处理失败，立即处理下一个商品
2025-07-28 23:58:49,526 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=690841537159
2025-07-28 23:58:53,431 - INFO - 页面加载完成
2025-07-28 23:58:53,431 - INFO - 模拟阅读行为，停顿 2.8 秒
2025-07-28 23:58:56,205 - INFO - 添加额外延迟: 1.8秒
2025-07-28 23:58:59,431 - INFO - 开始第 1 次铺货尝试...
2025-07-28 23:58:59,431 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:58:59,431 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:58:59,437 - INFO - span innerHTML: 立即铺货
2025-07-28 23:58:59,459 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-28 23:58:59,460 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-28 23:58:59,474 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-28 23:59:00,662 - INFO - 尝试ActionChains点击...
2025-07-28 23:59:00,949 - INFO - ActionChains点击成功
2025-07-28 23:59:01,346 - INFO - 铺货按钮点击成功
2025-07-28 23:59:03,361 - INFO - 使用配置的XPath找到确认按钮
2025-07-28 23:59:03,374 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-28 23:59:04,769 - INFO - 尝试ActionChains点击...
2025-07-28 23:59:05,043 - INFO - ActionChains点击成功
2025-07-28 23:59:05,533 - INFO - 确认按钮点击成功
2025-07-28 23:59:07,534 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-28 23:59:07,541 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-28 23:59:07,541 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:59:08,549 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-28 23:59:08,549 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:59:09,557 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-28 23:59:09,558 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:59:10,566 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-28 23:59:10,566 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:59:11,575 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-07-28 23:59:11,576 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:59:12,584 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-07-28 23:59:12,585 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:59:13,593 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-07-28 23:59:13,593 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:59:14,602 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-07-28 23:59:14,602 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:59:15,608 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-07-28 23:59:15,608 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:59:16,617 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-07-28 23:59:16,617 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:59:17,618 - INFO - 等待超时，进行最后一次检查...
2025-07-28 23:59:17,627 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-07-28 23:59:17,627 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-07-28 23:59:17,627 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-07-28 23:59:17,627 - INFO - 检测是否出现滑块验证...
2025-07-28 23:59:17,632 - INFO - 在主文档中未找到滑块，检查iframe...
2025-07-28 23:59:17,637 - INFO - 找到 1 个iframe
2025-07-28 23:59:17,637 - INFO - 检查第 1 个iframe...
2025-07-28 23:59:17,657 - INFO - 在第 1 个iframe中检测到滑块验证，开始处理...
2025-07-28 23:59:17,657 - INFO - 开始处理滑块验证...
2025-07-28 23:59:17,665 - INFO - 滑块容器宽度: 300px
2025-07-28 23:59:17,665 - INFO - 计算拖拽距离: 284px (比例: 0.95)
2025-07-28 23:59:17,665 - INFO - 开始慢速匀速滑块操作...
2025-07-28 23:59:17,665 - INFO - 构建慢速滑块操作链，拖拽距离: 284px
2025-07-28 23:59:17,666 - INFO - 慢速匀速滑块操作失败: ActionChains.move_by_offset() got an unexpected keyword argument 'duration'
2025-07-28 23:59:17,668 - INFO - 未检测到滑块验证或处理失败
2025-07-28 23:59:17,668 - WARNING - 第 2 个商品处理失败（不计入成功数量）
2025-07-28 23:59:17,668 - INFO - 商品处理失败，立即处理下一个商品
2025-07-28 23:59:17,680 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=789447097664
2025-07-28 23:59:21,427 - INFO - 页面加载完成
2025-07-28 23:59:21,428 - INFO - 模拟阅读行为，停顿 2.5 秒
2025-07-28 23:59:24,674 - INFO - 开始第 1 次铺货尝试...
2025-07-28 23:59:24,675 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:59:24,675 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:59:24,682 - INFO - span innerHTML: 立即铺货
2025-07-28 23:59:24,702 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-28 23:59:24,702 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-28 23:59:24,716 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-28 23:59:25,947 - INFO - 尝试ActionChains点击...
2025-07-28 23:59:26,240 - INFO - ActionChains点击成功
2025-07-28 23:59:26,640 - INFO - 铺货按钮点击成功
2025-07-28 23:59:28,651 - INFO - 使用配置的XPath找到确认按钮
2025-07-28 23:59:28,663 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-28 23:59:29,935 - INFO - 尝试ActionChains点击...
2025-07-28 23:59:30,213 - INFO - ActionChains点击成功
2025-07-28 23:59:30,710 - INFO - 确认按钮点击成功
2025-07-28 23:59:32,710 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-28 23:59:32,717 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-28 23:59:32,717 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:59:33,726 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-28 23:59:33,726 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:59:34,735 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-28 23:59:34,736 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:59:35,742 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-28 23:59:35,743 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:59:36,751 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-07-28 23:59:36,751 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:59:37,760 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-07-28 23:59:37,760 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:59:38,767 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-07-28 23:59:38,768 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:59:39,778 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-07-28 23:59:39,779 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:59:40,787 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-07-28 23:59:40,787 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:59:41,795 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-07-28 23:59:41,795 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:59:42,796 - INFO - 等待超时，进行最后一次检查...
2025-07-28 23:59:42,804 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-07-28 23:59:42,804 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-07-28 23:59:42,804 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-07-28 23:59:42,804 - INFO - 检测是否出现滑块验证...
2025-07-28 23:59:42,812 - INFO - 在主文档中未找到滑块，检查iframe...
2025-07-28 23:59:42,819 - INFO - 找到 1 个iframe
2025-07-28 23:59:42,819 - INFO - 检查第 1 个iframe...
2025-07-28 23:59:42,842 - INFO - 在第 1 个iframe中检测到滑块验证，开始处理...
2025-07-28 23:59:42,842 - INFO - 开始处理滑块验证...
2025-07-28 23:59:42,855 - INFO - 滑块容器宽度: 300px
2025-07-28 23:59:42,855 - INFO - 计算拖拽距离: 273px (比例: 0.91)
2025-07-28 23:59:42,855 - INFO - 开始慢速匀速滑块操作...
2025-07-28 23:59:42,855 - INFO - 构建慢速滑块操作链，拖拽距离: 273px
2025-07-28 23:59:42,855 - INFO - 慢速匀速滑块操作失败: ActionChains.move_by_offset() got an unexpected keyword argument 'duration'
2025-07-28 23:59:42,857 - INFO - 未检测到滑块验证或处理失败
2025-07-28 23:59:42,857 - WARNING - 第 3 个商品处理失败（不计入成功数量）
2025-07-28 23:59:42,857 - INFO - 商品处理失败，立即处理下一个商品
2025-07-28 23:59:42,870 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=694682843417
2025-07-28 23:59:45,978 - INFO - 页面加载完成
2025-07-28 23:59:45,978 - INFO - 模拟阅读行为，停顿 2.2 秒
2025-07-28 23:59:50,389 - INFO - 开始第 1 次铺货尝试...
2025-07-28 23:59:50,389 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:59:50,390 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:59:50,396 - INFO - span innerHTML: 立即铺货
2025-07-28 23:59:50,420 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-28 23:59:50,420 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-28 23:59:50,435 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-28 23:59:51,539 - INFO - 尝试ActionChains点击...
2025-07-28 23:59:51,836 - INFO - ActionChains点击成功
2025-07-28 23:59:52,335 - INFO - 铺货按钮点击成功
2025-07-28 23:59:54,358 - INFO - 使用配置的XPath找到确认按钮
2025-07-28 23:59:54,371 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-28 23:59:55,740 - INFO - 尝试ActionChains点击...
2025-07-28 23:59:56,024 - INFO - ActionChains点击成功
2025-07-28 23:59:56,386 - INFO - 确认按钮点击成功
2025-07-28 23:59:58,386 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-28 23:59:58,416 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-28 23:59:58,416 - INFO - span仍为'立即铺货'，继续等待...
