2025-07-27 22:54:48,924 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169cda]
	(No symbol) [0x0x7ff7cd171679]
	(No symbol) [0x0x7ff7cd174a61]
	(No symbol) [0x0x7ff7cd211e4b]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:54:48,925 - INFO - 防检测机制设置完成
2025-07-27 22:54:48,925 - INFO - 浏览器驱动初始化成功
2025-07-27 22:54:48,930 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:54:48,933 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:54:48,938 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:54:48,941 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:54:48,943 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:54:48,950 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:54:48,954 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:54:48,957 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:54:48,959 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:54:48,962 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:54:48,966 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:54:48,968 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:54:48,970 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:54:48,973 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:54:48,976 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:54:48,980 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:54:48,983 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:54:48,985 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:54:48,987 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:54:48,990 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:54:48,990 - INFO - Cookies已加载
2025-07-27 22:54:48,991 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?channel=global_zx&spm=a21ug5.29159167.6452766900.2ndview_zxmore_click
2025-07-27 22:54:48,991 - INFO - 添加额外延迟: 2.2秒
2025-07-27 22:54:56,610 - INFO - 当前User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-27 22:54:56,610 - INFO - 确认：使用桌面版User-Agent
2025-07-27 22:54:56,616 - INFO - 桌面版User-Agent设置完成: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-27 22:54:57,164 - INFO - 成功导航到目标页面
2025-07-27 22:54:57,165 - INFO - 登录成功！现在可以开始铺货任务
2025-07-27 22:55:00,085 - INFO - Cookies已保存
2025-07-27 22:55:02,344 - INFO - 浏览器已关闭
2025-07-27 22:55:03,737 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169cda]
	(No symbol) [0x0x7ff7cd171679]
	(No symbol) [0x0x7ff7cd174a61]
	(No symbol) [0x0x7ff7cd211e4b]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:55:03,739 - INFO - 防检测机制设置完成
2025-07-27 22:55:03,739 - INFO - 浏览器驱动初始化成功
2025-07-27 22:55:03,739 - INFO - 开始自动化铺货流程（有窗口模式）
2025-07-27 22:55:03,739 - INFO - 第一步：正在导航到目标页面...
2025-07-27 22:55:07,407 - INFO - 第一步：点击登录按钮
2025-07-27 22:55:07,706 - INFO - 找到登录按钮，使用选择器: //form//button[last()]
2025-07-27 22:55:08,730 - INFO - 准备点击登录按钮: 标签=button, 文本='重置', 选择器=//form//button[last()]
2025-07-27 22:55:08,744 - INFO - 准备点击元素: tag=button, text='重置', class='next-btn next-medium next-btn-', id=''
2025-07-27 22:55:10,246 - INFO - 尝试ActionChains点击...
2025-07-27 22:55:10,549 - INFO - ActionChains点击成功
2025-07-27 22:55:10,914 - INFO - 登录按钮点击成功
2025-07-27 22:55:10,914 - INFO - 登录按钮点击成功
2025-07-27 22:55:13,915 - INFO - 第二步：获取账号信息...
2025-07-27 22:55:13,915 - INFO - 正在跳转到个人中心页面: https://jingya.taobao.com/?v=2
2025-07-27 22:55:38,696 - INFO - 尝试获取账号名称，使用XPath: //*[@id='workbench-user-tool-btn']/div/div[2]
2025-07-27 22:55:38,709 - INFO - 成功获取账号信息: tb997603987291:书生
2025-07-27 22:55:38,709 - INFO - 获取到账号名称: tb997603987291:书生
2025-07-27 22:55:38,710 - INFO - 第三步：开始爬取商品信息...
2025-07-27 22:55:38,710 - INFO - GUI界面已更新账号显示: tb997603987291:书生
2025-07-27 22:55:38,710 - INFO - 开始爬取商品信息...
2025-07-27 22:55:42,710 - INFO - 正在爬取第 1 页商品信息...
2025-07-27 22:55:44,718 - INFO - 找到商品容器，开始爬取商品...
2025-07-27 22:55:44,731 - INFO - 找到商品项 1，查找tfx-item元素...
2025-07-27 22:55:44,738 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:55:44,746 - INFO - 商品项 1 未找到data-autolog属性，跳过
2025-07-27 22:55:44,759 - INFO - 找到商品项 2，查找tfx-item元素...
2025-07-27 22:55:44,763 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:55:44,771 - INFO - 商品项 2 未找到data-autolog属性，跳过
2025-07-27 22:55:44,782 - INFO - 找到商品项 3，查找tfx-item元素...
2025-07-27 22:55:44,871 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:55:44,882 - INFO - 商品项 3 未找到data-autolog属性，跳过
2025-07-27 22:55:44,897 - INFO - 找到商品项 4，查找tfx-item元素...
2025-07-27 22:55:44,901 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:55:44,907 - INFO - 商品项 4 未找到data-autolog属性，跳过
2025-07-27 22:55:44,921 - INFO - 找到商品项 5，查找tfx-item元素...
2025-07-27 22:55:44,926 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:55:44,934 - INFO - 商品项 5 未找到data-autolog属性，跳过
2025-07-27 22:55:44,945 - INFO - 找到商品项 6，查找tfx-item元素...
2025-07-27 22:55:44,949 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:55:44,955 - INFO - 商品项 6 未找到data-autolog属性，跳过
2025-07-27 22:55:44,965 - INFO - 找到商品项 7，查找tfx-item元素...
2025-07-27 22:55:44,970 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:55:44,975 - INFO - 商品项 7 未找到data-autolog属性，跳过
2025-07-27 22:55:44,987 - INFO - 找到商品项 8，查找tfx-item元素...
2025-07-27 22:55:44,990 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:55:44,997 - INFO - 商品项 8 未找到data-autolog属性，跳过
2025-07-27 22:55:45,008 - INFO - 找到商品项 9，查找tfx-item元素...
2025-07-27 22:55:45,012 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:55:45,017 - INFO - 商品项 9 未找到data-autolog属性，跳过
2025-07-27 22:55:45,035 - INFO - 找到商品项 10，查找tfx-item元素...
2025-07-27 22:55:45,039 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:55:45,045 - INFO - 商品项 10 未找到data-autolog属性，跳过
2025-07-27 22:55:45,056 - INFO - 找到商品项 11，查找tfx-item元素...
2025-07-27 22:55:45,060 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:55:45,067 - INFO - 商品项 11 未找到data-autolog属性，跳过
2025-07-27 22:55:45,079 - INFO - 找到商品项 12，查找tfx-item元素...
2025-07-27 22:55:45,083 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:55:45,089 - INFO - 商品项 12 未找到data-autolog属性，跳过
2025-07-27 22:55:45,111 - INFO - 找到商品项 13，查找tfx-item元素...
2025-07-27 22:55:45,116 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:55:45,122 - INFO - 商品项 13 未找到data-autolog属性，跳过
2025-07-27 22:55:45,132 - INFO - 找到商品项 14，查找tfx-item元素...
2025-07-27 22:55:45,137 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:55:45,142 - INFO - 商品项 14 未找到data-autolog属性，跳过
2025-07-27 22:55:45,152 - INFO - 找到商品项 15，查找tfx-item元素...
2025-07-27 22:55:45,156 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:55:45,161 - INFO - 商品项 15 未找到data-autolog属性，跳过
2025-07-27 22:55:45,171 - INFO - 找到商品项 16，查找tfx-item元素...
2025-07-27 22:55:45,175 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:55:45,182 - INFO - 商品项 16 未找到data-autolog属性，跳过
2025-07-27 22:55:45,191 - INFO - 找到商品项 17，查找tfx-item元素...
2025-07-27 22:55:45,196 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:55:45,201 - INFO - 商品项 17 未找到data-autolog属性，跳过
2025-07-27 22:55:45,211 - INFO - 找到商品项 18，查找tfx-item元素...
2025-07-27 22:55:45,216 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:55:45,222 - INFO - 商品项 18 未找到data-autolog属性，跳过
2025-07-27 22:55:45,233 - INFO - 找到商品项 19，查找tfx-item元素...
2025-07-27 22:55:45,238 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:55:45,243 - INFO - 商品项 19 未找到data-autolog属性，跳过
2025-07-27 22:55:45,253 - INFO - 找到商品项 20，查找tfx-item元素...
2025-07-27 22:55:45,257 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:55:45,264 - INFO - 商品项 20 未找到data-autolog属性，跳过
2025-07-27 22:55:45,274 - INFO - 找到商品项 21，查找tfx-item元素...
2025-07-27 22:55:45,278 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:55:45,283 - INFO - 商品项 21 未找到data-autolog属性，跳过
2025-07-27 22:55:45,294 - INFO - 找到商品项 22，查找tfx-item元素...
2025-07-27 22:55:45,297 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:55:45,304 - INFO - 商品项 22 未找到data-autolog属性，跳过
2025-07-27 22:55:45,314 - INFO - 找到商品项 23，查找tfx-item元素...
2025-07-27 22:55:45,319 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:55:45,324 - INFO - 商品项 23 未找到data-autolog属性，跳过
2025-07-27 22:55:45,336 - INFO - 找到商品项 24，查找tfx-item元素...
2025-07-27 22:55:45,341 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:55:45,347 - INFO - 商品项 24 未找到data-autolog属性，跳过
2025-07-27 22:55:45,357 - INFO - 找到商品项 25，查找tfx-item元素...
2025-07-27 22:55:45,362 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:55:45,367 - INFO - 商品项 25 未找到data-autolog属性，跳过
2025-07-27 22:55:45,377 - INFO - 找到商品项 26，查找tfx-item元素...
2025-07-27 22:55:45,382 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:55:49,458 - INFO - 商品项 26 未找到data-autolog属性，跳过
2025-07-27 22:55:49,468 - INFO - 找到商品项 27，查找tfx-item元素...
2025-07-27 22:55:49,489 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:55:49,493 - INFO - 商品项 27 未找到data-autolog属性，跳过
2025-07-27 22:55:49,505 - INFO - 找到商品项 28，查找tfx-item元素...
2025-07-27 22:55:49,509 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:55:49,514 - INFO - 商品项 28 未找到data-autolog属性，跳过
2025-07-27 22:55:49,525 - INFO - 找到商品项 29，查找tfx-item元素...
2025-07-27 22:55:49,529 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:55:49,535 - INFO - 商品项 29 未找到data-autolog属性，跳过
2025-07-27 22:55:49,545 - INFO - 找到商品项 30，查找tfx-item元素...
2025-07-27 22:55:49,549 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:55:49,555 - INFO - 商品项 30 未找到data-autolog属性，跳过
2025-07-27 22:55:49,565 - INFO - 找到商品项 31，查找tfx-item元素...
2025-07-27 22:55:49,571 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:55:49,576 - INFO - 商品项 31 未找到data-autolog属性，跳过
2025-07-27 22:55:49,586 - INFO - 找到商品项 32，查找tfx-item元素...
2025-07-27 22:55:49,591 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:55:49,598 - INFO - 商品项 32 未找到data-autolog属性，跳过
2025-07-27 22:55:49,608 - INFO - 找到商品项 33，查找tfx-item元素...
2025-07-27 22:55:49,612 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:55:49,617 - INFO - 商品项 33 未找到data-autolog属性，跳过
2025-07-27 22:55:49,634 - INFO - 找到商品项 34，查找tfx-item元素...
2025-07-27 22:55:49,644 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:55:49,651 - INFO - 商品项 34 未找到data-autolog属性，跳过
2025-07-27 22:55:49,661 - INFO - 找到商品项 35，查找tfx-item元素...
2025-07-27 22:55:49,665 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:55:49,671 - INFO - 商品项 35 未找到data-autolog属性，跳过
2025-07-27 22:55:49,682 - INFO - 找到商品项 36，查找tfx-item元素...
2025-07-27 22:55:49,686 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:55:49,693 - INFO - 商品项 36 未找到data-autolog属性，跳过
2025-07-27 22:55:49,703 - INFO - 找到商品项 37，查找tfx-item元素...
2025-07-27 22:55:49,708 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:55:49,713 - INFO - 商品项 37 未找到data-autolog属性，跳过
2025-07-27 22:55:49,724 - INFO - 找到商品项 38，查找tfx-item元素...
2025-07-27 22:55:49,729 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:55:49,734 - INFO - 商品项 38 未找到data-autolog属性，跳过
2025-07-27 22:55:49,745 - INFO - 找到商品项 39，查找tfx-item元素...
2025-07-27 22:55:49,749 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:55:49,755 - INFO - 商品项 39 未找到data-autolog属性，跳过
2025-07-27 22:55:49,765 - INFO - 找到商品项 40，查找tfx-item元素...
2025-07-27 22:55:49,770 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:55:49,774 - INFO - 商品项 40 未找到data-autolog属性，跳过
2025-07-27 22:55:49,774 - INFO - 当前页面爬取完成，找到 0 个商品
2025-07-27 22:55:49,774 - INFO - 第 1 页未找到商品，停止爬取
2025-07-27 22:55:49,775 - INFO - 爬取完成，共找到 0 个商品
2025-07-27 22:55:49,775 - ERROR - 未能获取到商品信息，停止流程
