2025-07-28 23:23:52,236 - INFO - 配置已保存
2025-07-28 23:23:58,508 - INFO - 配置已保存
2025-07-28 23:24:02,521 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059cda]
	(No symbol) [0x0x7ff69a061679]
	(No symbol) [0x0x7ff69a064a61]
	(No symbol) [0x0x7ff69a101e4b]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:24:02,522 - INFO - 防检测机制设置完成
2025-07-28 23:24:02,522 - INFO - 浏览器驱动初始化成功
2025-07-28 23:24:02,528 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:24:02,531 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:24:02,537 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:24:02,541 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:24:02,545 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:24:02,552 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:24:02,555 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:24:02,558 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:24:02,562 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:24:02,565 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:24:02,569 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:24:02,572 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:24:02,576 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:24:02,579 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:24:02,582 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:24:02,586 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:24:02,590 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:24:02,594 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:24:02,598 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:24:02,602 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:24:02,602 - INFO - Cookies已加载
2025-07-28 23:24:02,602 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?channel=global_zx&spm=a21ug5.29159167.6452766900.2ndview_zxmore_click
2025-07-28 23:24:09,187 - INFO - 当前User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-28 23:24:09,188 - INFO - 确认：使用桌面版User-Agent
2025-07-28 23:24:09,192 - INFO - 桌面版User-Agent设置完成: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-28 23:24:09,738 - WARNING - 检测到需要登录，请手动登录后继续
2025-07-28 23:24:09,813 - INFO - 调试截图已保存: debug_logs/login_required_screenshot_20250728_232409.png
2025-07-28 23:24:09,816 - INFO - 浏览器日志已保存: debug_logs/login_required_browser_log_20250728_232409.txt
2025-07-28 23:24:09,846 - INFO - 页面源码已保存: debug_logs/login_required_page_source_20250728_232409.html
2025-07-28 23:24:09,881 - INFO - 调试信息 - 当前URL: https://login.taobao.com/havanaone/login/login.htm?bizName=taobao&ttid=&redirectURL=https%3A%2F%2Fjingya-x.taobao.com%2Fwow%2Fz%2Ftfx%2Fstatic%2FdXCnMcpEntsE375tfHGp%3Fchannel%3Dglobal_zx%26spm%3Da21ug5.29159167.6452766900.2ndview_zxmore_click&sub=true
2025-07-28 23:24:09,881 - INFO - 调试信息 - 错误: 检测到需要登录
2025-07-28 23:24:09,881 - INFO - 请在打开的浏览器中完成登录，登录完成后点击'确认登录完成'按钮
2025-07-28 23:24:22,850 - INFO - Cookies已保存
2025-07-28 23:24:22,851 - INFO - 登录成功！现在可以开始铺货任务
2025-07-28 23:24:40,594 - INFO - Cookies已保存
2025-07-28 23:24:42,928 - INFO - 浏览器已关闭
2025-07-28 23:24:44,315 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059cda]
	(No symbol) [0x0x7ff69a061679]
	(No symbol) [0x0x7ff69a064a61]
	(No symbol) [0x0x7ff69a101e4b]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:24:44,316 - INFO - 防检测机制设置完成
2025-07-28 23:24:44,316 - INFO - 浏览器驱动初始化成功
2025-07-28 23:24:44,316 - INFO - 开始自动化铺货流程（有界面模式）
2025-07-28 23:24:44,316 - INFO - 第一步：正在导航到目标页面...
2025-07-28 23:24:47,457 - INFO - 第一步：点击登录按钮
2025-07-28 23:24:47,477 - INFO - 找到登录按钮，使用选择器: //*[@id='login-form']/div[6]/button
2025-07-28 23:24:48,501 - INFO - 准备点击登录按钮: 标签=button, 文本='登录', 选择器=//*[@id='login-form']/div[6]/button
2025-07-28 23:24:48,516 - INFO - 准备点击元素: tag=button, text='登录', class='fm-button fm-submit password-l', id=''
2025-07-28 23:24:49,745 - INFO - 尝试ActionChains点击...
2025-07-28 23:24:50,168 - INFO - ActionChains点击成功
2025-07-28 23:24:50,576 - INFO - 登录按钮点击成功
2025-07-28 23:24:50,576 - INFO - 登录按钮点击成功
2025-07-28 23:24:53,576 - INFO - 第二步：获取账号信息...
2025-07-28 23:24:53,576 - INFO - 正在跳转到个人中心页面: https://jingya.taobao.com/?v=2
2025-07-28 23:25:18,669 - INFO - 尝试获取账号名称，使用XPath: //*[@id='workbench-user-tool-btn']/div/div[2]
2025-07-28 23:25:18,680 - INFO - 成功获取账号信息: 卓祥22店:冰淇淋
2025-07-28 23:25:18,680 - INFO - 获取到账号名称: 卓祥22店:冰淇淋
2025-07-28 23:25:18,682 - INFO - GUI界面已更新账号显示: 卓祥22店:冰淇淋
2025-07-28 23:25:18,682 - INFO - 第三步：开始读取20个商品信息...
2025-07-28 23:25:18,686 - INFO - 开始爬取商品信息，最大数量: 20
2025-07-28 23:25:21,786 - INFO - 正在爬取第 1 页商品信息...
2025-07-28 23:25:23,794 - INFO - 找到商品容器，开始爬取商品，最大数量: 20
2025-07-28 23:25:23,806 - INFO - 找到商品项 1，检查元素信息...
2025-07-28 23:25:23,821 - INFO - 商品项 1 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:25:23,826 - INFO - 商品项 1 直接获取到data-autolog: item_id=681778175573&path=search_goods_card&type=search
2025-07-28 23:25:23,826 - INFO - 商品项 1 data-autolog: item_id=681778175573&path=search_goods_card&type=search
2025-07-28 23:25:23,826 - INFO - 商品项 1 提取到item_id: 681778175573
2025-07-28 23:25:23,838 - INFO - 商品项 1 商品名称: 保税直供 Dove多芬磨砂膏石榴籽乳木果风味身体磨砂膏298g
2025-07-28 23:25:23,858 - INFO - 找到商品项 2，检查元素信息...
2025-07-28 23:25:23,880 - INFO - 商品项 2 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:25:23,885 - INFO - 商品项 2 直接获取到data-autolog: item_id=690841537159&path=search_goods_card&index=1&type=search
2025-07-28 23:25:23,885 - INFO - 商品项 2 data-autolog: item_id=690841537159&path=search_goods_card&index=1&type=search
2025-07-28 23:25:23,885 - INFO - 商品项 2 提取到item_id: 690841537159
2025-07-28 23:25:23,894 - INFO - 商品项 2 商品名称: 保税直供 凡士林Vaseline烟酰胺身体乳400ml 725ml无压泵款200ml
2025-07-28 23:25:23,907 - INFO - 找到商品项 3，检查元素信息...
2025-07-28 23:25:23,918 - INFO - 商品项 3 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:25:23,923 - INFO - 商品项 3 直接获取到data-autolog: item_id=789447097664&path=search_goods_card&index=2&type=search
2025-07-28 23:25:23,923 - INFO - 商品项 3 data-autolog: item_id=789447097664&path=search_goods_card&index=2&type=search
2025-07-28 23:25:23,923 - INFO - 商品项 3 提取到item_id: 789447097664
2025-07-28 23:25:23,942 - INFO - 商品项 3 商品名称: 【甄选】珂润Curel泡沫洗面奶150ml
2025-07-28 23:25:23,958 - INFO - 找到商品项 4，检查元素信息...
2025-07-28 23:25:23,971 - INFO - 商品项 4 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:25:23,975 - INFO - 商品项 4 直接获取到data-autolog: item_id=694682843417&path=search_goods_card&index=3&type=search
2025-07-28 23:25:23,975 - INFO - 商品项 4 data-autolog: item_id=694682843417&path=search_goods_card&index=3&type=search
2025-07-28 23:25:23,976 - INFO - 商品项 4 提取到item_id: 694682843417
2025-07-28 23:25:23,986 - INFO - 商品项 4 商品名称: 【可开授权】日台混发Fino发膜美容液护发素 发膜230g
2025-07-28 23:25:24,000 - INFO - 找到商品项 5，检查元素信息...
2025-07-28 23:25:24,097 - INFO - 商品项 5 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:25:24,101 - INFO - 商品项 5 直接获取到data-autolog: item_id=681082809892&path=search_goods_card&index=4&type=search
2025-07-28 23:25:24,102 - INFO - 商品项 5 data-autolog: item_id=681082809892&path=search_goods_card&index=4&type=search
2025-07-28 23:25:24,102 - INFO - 商品项 5 提取到item_id: 681082809892
2025-07-28 23:25:24,118 - INFO - 商品项 5 商品名称: 保税直供 Dove多芬大白碗润肤身体乳300ml 【1//2//3罐】
2025-07-28 23:25:24,129 - INFO - 找到商品项 6，检查元素信息...
2025-07-28 23:25:24,138 - INFO - 商品项 6 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:25:24,141 - INFO - 商品项 6 直接获取到data-autolog: item_id=694675776881&path=search_goods_card&index=5&type=search
2025-07-28 23:25:24,142 - INFO - 商品项 6 data-autolog: item_id=694675776881&path=search_goods_card&index=5&type=search
2025-07-28 23:25:24,142 - INFO - 商品项 6 提取到item_id: 694675776881
2025-07-28 23:25:24,156 - INFO - 商品项 6 商品名称: 保税直供 Pantene潘婷护发素三分钟奇迹多效修护发膜级护发素
2025-07-28 23:25:24,167 - INFO - 找到商品项 7，检查元素信息...
2025-07-28 23:25:24,178 - INFO - 商品项 7 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:25:24,182 - INFO - 商品项 7 直接获取到data-autolog: item_id=680729304287&path=search_goods_card&index=6&type=search
2025-07-28 23:25:24,182 - INFO - 商品项 7 data-autolog: item_id=680729304287&path=search_goods_card&index=6&type=search
2025-07-28 23:25:24,182 - INFO - 商品项 7 提取到item_id: 680729304287
2025-07-28 23:25:24,192 - INFO - 商品项 7 商品名称: 保税直供 多芬dove空气感无硅油 洗发水480g 护发素480g护发临期
2025-07-28 23:25:24,206 - INFO - 找到商品项 8，检查元素信息...
2025-07-28 23:25:24,216 - INFO - 商品项 8 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:25:24,218 - INFO - 商品项 8 直接获取到data-autolog: item_id=710355107054&path=search_goods_card&index=7&type=search
2025-07-28 23:25:24,219 - INFO - 商品项 8 data-autolog: item_id=710355107054&path=search_goods_card&index=7&type=search
2025-07-28 23:25:24,219 - INFO - 商品项 8 提取到item_id: 710355107054
2025-07-28 23:25:24,228 - INFO - 商品项 8 商品名称: 保税直供 Dove多芬洗发水护发素空气感洗发水480g 【护发素临期
2025-07-28 23:25:24,239 - INFO - 找到商品项 9，检查元素信息...
2025-07-28 23:25:24,248 - INFO - 商品项 9 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:25:24,251 - INFO - 商品项 9 直接获取到data-autolog: item_id=740463658412&path=search_goods_card&index=8&type=search
2025-07-28 23:25:24,251 - INFO - 商品项 9 data-autolog: item_id=740463658412&path=search_goods_card&index=8&type=search
2025-07-28 23:25:24,251 - INFO - 商品项 9 提取到item_id: 740463658412
2025-07-28 23:25:24,260 - INFO - 商品项 9 商品名称: 【主推链接】花王cape空气感定型喷雾50g/180g 编码：3305300000
2025-07-28 23:25:24,271 - INFO - 找到商品项 10，检查元素信息...
2025-07-28 23:25:24,278 - INFO - 商品项 10 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:25:24,282 - INFO - 商品项 10 直接获取到data-autolog: item_id=821067902127&path=search_goods_card&index=9&type=search
2025-07-28 23:25:24,282 - INFO - 商品项 10 data-autolog: item_id=821067902127&path=search_goods_card&index=9&type=search
2025-07-28 23:25:24,282 - INFO - 商品项 10 提取到item_id: 821067902127
2025-07-28 23:25:24,291 - INFO - 商品项 10 商品名称: 丹碧丝TAMPAX卫生棉条长导管式内置式纯棉月经棉条棉棒游泳卫生巾
2025-07-28 23:25:24,302 - INFO - 找到商品项 11，检查元素信息...
2025-07-28 23:25:24,310 - INFO - 商品项 11 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:25:24,313 - INFO - 商品项 11 直接获取到data-autolog: item_id=789835300215&path=search_goods_card&index=10&type=search
2025-07-28 23:25:24,314 - INFO - 商品项 11 data-autolog: item_id=789835300215&path=search_goods_card&index=10&type=search
2025-07-28 23:25:24,314 - INFO - 商品项 11 提取到item_id: 789835300215
2025-07-28 23:25:24,322 - INFO - 商品项 11 商品名称: 【保税】雅漾喷雾爽肤水300ml
2025-07-28 23:25:24,335 - INFO - 找到商品项 12，检查元素信息...
2025-07-28 23:25:24,344 - INFO - 商品项 12 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:25:24,347 - INFO - 商品项 12 直接获取到data-autolog: item_id=843747605382&path=search_goods_card&index=11&type=search
2025-07-28 23:25:24,348 - INFO - 商品项 12 data-autolog: item_id=843747605382&path=search_goods_card&index=11&type=search
2025-07-28 23:25:24,348 - INFO - 商品项 12 提取到item_id: 843747605382
2025-07-28 23:25:24,356 - INFO - 商品项 12 商品名称: 倩碧紫胖子卸妆膏125ml/瓶
2025-07-28 23:25:24,368 - INFO - 找到商品项 13，检查元素信息...
2025-07-28 23:25:24,376 - INFO - 商品项 13 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:25:24,379 - INFO - 商品项 13 直接获取到data-autolog: item_id=675816539149&path=search_goods_card&index=12&type=search
2025-07-28 23:25:24,379 - INFO - 商品项 13 data-autolog: item_id=675816539149&path=search_goods_card&index=12&type=search
2025-07-28 23:25:24,379 - INFO - 商品项 13 提取到item_id: 675816539149
2025-07-28 23:25:24,387 - INFO - 商品项 13 商品名称: 新加坡Vicopyl维可保益生菌(Pylopass/拒绝幽门菌/抑口臭/调肠胃)
2025-07-28 23:25:24,398 - INFO - 找到商品项 14，检查元素信息...
2025-07-28 23:25:24,408 - INFO - 商品项 14 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:25:24,410 - INFO - 商品项 14 直接获取到data-autolog: item_id=846634805775&path=search_goods_card&index=13&type=search
2025-07-28 23:25:24,410 - INFO - 商品项 14 data-autolog: item_id=846634805775&path=search_goods_card&index=13&type=search
2025-07-28 23:25:24,410 - INFO - 商品项 14 提取到item_id: 846634805775
2025-07-28 23:25:24,418 - INFO - 商品项 14 商品名称: 【甄选】cow牛乳石碱原味/玫瑰花香 瓶装480ml
2025-07-28 23:25:24,430 - INFO - 找到商品项 15，检查元素信息...
2025-07-28 23:25:24,438 - INFO - 商品项 15 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:25:24,440 - INFO - 商品项 15 直接获取到data-autolog: item_id=852510575114&path=search_goods_card&index=14&type=search
2025-07-28 23:25:24,440 - INFO - 商品项 15 data-autolog: item_id=852510575114&path=search_goods_card&index=14&type=search
2025-07-28 23:25:24,441 - INFO - 商品项 15 提取到item_id: 852510575114
2025-07-28 23:25:24,452 - INFO - 商品项 15 商品名称: Nars 纳斯 红壳蜜粉饼16g（含粉扑）、Nars纳斯裸光蜜粉饼10g
2025-07-28 23:25:24,463 - INFO - 找到商品项 16，检查元素信息...
2025-07-28 23:25:24,473 - INFO - 商品项 16 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:25:24,475 - INFO - 商品项 16 直接获取到data-autolog: item_id=743442966826&path=search_goods_card&index=15&type=search
2025-07-28 23:25:24,476 - INFO - 商品项 16 data-autolog: item_id=743442966826&path=search_goods_card&index=15&type=search
2025-07-28 23:25:24,476 - INFO - 商品项 16 提取到item_id: 743442966826
2025-07-28 23:25:24,487 - INFO - 商品项 16 商品名称: ESI卡路里拜拜片白芸豆阻断片大餐救星膳食纤维身材热控管理
2025-07-28 23:25:24,498 - INFO - 找到商品项 17，检查元素信息...
2025-07-28 23:25:24,505 - INFO - 商品项 17 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:25:24,508 - INFO - 商品项 17 直接获取到data-autolog: item_id=848934676698&path=search_goods_card&index=16&type=search
2025-07-28 23:25:24,508 - INFO - 商品项 17 data-autolog: item_id=848934676698&path=search_goods_card&index=16&type=search
2025-07-28 23:25:24,508 - INFO - 商品项 17 提取到item_id: 848934676698
2025-07-28 23:25:24,518 - INFO - 商品项 17 商品名称: Estee Lauder 雅诗兰黛持妆粉底液30ml 2C0 /1w1/1C1
2025-07-28 23:25:24,529 - INFO - 找到商品项 18，检查元素信息...
2025-07-28 23:25:24,537 - INFO - 商品项 18 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:25:24,540 - INFO - 商品项 18 直接获取到data-autolog: item_id=693758717931&path=search_goods_card&index=17&type=search
2025-07-28 23:25:24,540 - INFO - 商品项 18 data-autolog: item_id=693758717931&path=search_goods_card&index=17&type=search
2025-07-28 23:25:24,540 - INFO - 商品项 18 提取到item_id: 693758717931
2025-07-28 23:25:24,547 - INFO - 商品项 18 商品名称: 保税直供 资生堂fino高效渗透发膜 230g （1/2/3罐）日版
2025-07-28 23:25:24,559 - INFO - 找到商品项 19，检查元素信息...
2025-07-28 23:25:24,567 - INFO - 商品项 19 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:25:24,571 - INFO - 商品项 19 直接获取到data-autolog: item_id=691142128229&path=search_goods_card&index=18&type=search
2025-07-28 23:25:24,571 - INFO - 商品项 19 data-autolog: item_id=691142128229&path=search_goods_card&index=18&type=search
2025-07-28 23:25:24,571 - INFO - 商品项 19 提取到item_id: 691142128229
2025-07-28 23:25:24,578 - INFO - 商品项 19 商品名称: 保税 日版Dove多芬洁面洗面奶新版 正装瓶150ml 补充袋125ml 甄选
2025-07-28 23:25:24,591 - INFO - 找到商品项 20，检查元素信息...
2025-07-28 23:25:24,598 - INFO - 商品项 20 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:25:24,601 - INFO - 商品项 20 直接获取到data-autolog: item_id=690739721578&path=search_goods_card&index=19&type=search
2025-07-28 23:25:24,601 - INFO - 商品项 20 data-autolog: item_id=690739721578&path=search_goods_card&index=19&type=search
2025-07-28 23:25:24,601 - INFO - 商品项 20 提取到item_id: 690739721578
2025-07-28 23:25:24,609 - INFO - 商品项 20 商品名称: 保税直供 SUNSILK夏士莲椰子洗发水//护发素450ml 泰产
2025-07-28 23:25:24,609 - INFO - 已爬取到 20 个商品，停止当前页面爬取
2025-07-28 23:25:24,609 - INFO - 当前页面爬取完成，找到 20 个商品
2025-07-28 23:25:24,610 - INFO - 第 1 页找到 20 个商品，总计: 20
2025-07-28 23:25:24,610 - INFO - 已达到最大商品数量 20，停止爬取
2025-07-28 23:25:24,610 - INFO - 爬取完成，共找到 20 个商品
2025-07-28 23:25:24,610 - INFO - 成功读取到 20 个商品（目标20个），开始铺货任务
2025-07-28 23:25:24,610 - INFO - 第四步：开始处理商品...
2025-07-28 23:25:24,622 - INFO - 已读取 20 个商品，目标成功铺货 3 个商品
2025-07-28 23:25:24,622 - INFO - 当前批次大小: 11
2025-07-28 23:25:24,623 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=681778175573
2025-07-28 23:25:28,217 - INFO - 页面加载完成
2025-07-28 23:25:28,217 - INFO - 模拟阅读行为，停顿 1.8 秒
2025-07-28 23:25:31,333 - INFO - 添加额外延迟: 2.6秒
2025-07-28 23:25:34,895 - INFO - 开始第 1 次铺货尝试...
2025-07-28 23:25:34,895 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:25:34,896 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:25:34,905 - INFO - span innerHTML: 立即铺货
2025-07-28 23:25:34,926 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-28 23:25:34,927 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-28 23:25:34,941 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-28 23:25:36,114 - INFO - 尝试ActionChains点击...
2025-07-28 23:25:36,398 - INFO - ActionChains点击成功
2025-07-28 23:25:36,782 - INFO - 铺货按钮点击成功
2025-07-28 23:25:38,810 - INFO - 使用配置的XPath找到确认按钮
2025-07-28 23:25:38,823 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-28 23:25:40,110 - INFO - 尝试ActionChains点击...
2025-07-28 23:25:40,386 - INFO - ActionChains点击成功
2025-07-28 23:25:40,686 - INFO - 确认按钮点击成功
2025-07-28 23:25:42,687 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-28 23:25:42,694 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-28 23:25:42,694 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:25:43,703 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-28 23:25:43,703 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:25:44,712 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-28 23:25:44,713 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:25:45,720 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-28 23:25:45,720 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:25:46,754 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-07-28 23:25:46,754 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:25:47,762 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-07-28 23:25:47,762 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:25:48,773 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-07-28 23:25:48,773 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:25:49,781 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-07-28 23:25:49,782 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:25:50,789 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-07-28 23:25:50,789 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:25:51,798 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-07-28 23:25:51,798 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:25:52,799 - INFO - 等待超时，进行最后一次检查...
2025-07-28 23:25:52,807 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-07-28 23:25:52,807 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-07-28 23:25:52,808 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-07-28 23:25:52,808 - INFO - 检测是否出现滑块验证...
2025-07-28 23:25:52,815 - INFO - 在主文档中未找到滑块，检查iframe...
2025-07-28 23:25:52,843 - INFO - 找到 1 个iframe
2025-07-28 23:25:52,843 - INFO - 检查第 1 个iframe...
2025-07-28 23:25:52,865 - INFO - 在第 1 个iframe中检测到滑块验证，开始处理...
2025-07-28 23:25:52,865 - INFO - 开始处理滑块验证...
2025-07-28 23:25:52,877 - INFO - 滑块容器宽度: 300px
2025-07-28 23:25:58,213 - INFO - 滑块验证失败，滑块仍然可见
2025-07-28 23:25:58,214 - INFO - 未检测到滑块验证或处理失败
2025-07-28 23:25:58,215 - WARNING - 第 1 个商品处理失败（不计入成功数量）
2025-07-28 23:25:58,215 - INFO - 商品处理失败，立即处理下一个商品
2025-07-28 23:25:58,228 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=690841537159
2025-07-28 23:26:01,317 - INFO - 页面加载完成
2025-07-28 23:26:01,317 - INFO - 模拟阅读行为，停顿 2.3 秒
2025-07-28 23:26:05,352 - INFO - 开始第 1 次铺货尝试...
2025-07-28 23:26:05,352 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:26:05,352 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:26:05,358 - INFO - span innerHTML: 立即铺货
2025-07-28 23:26:05,379 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-28 23:26:05,379 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-28 23:26:05,393 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-28 23:26:06,736 - INFO - 尝试ActionChains点击...
2025-07-28 23:26:07,021 - INFO - ActionChains点击成功
2025-07-28 23:26:07,497 - INFO - 铺货按钮点击成功
2025-07-28 23:26:09,509 - INFO - 使用配置的XPath找到确认按钮
2025-07-28 23:26:09,522 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-28 23:26:10,567 - INFO - 尝试ActionChains点击...
2025-07-28 23:26:10,840 - INFO - ActionChains点击成功
2025-07-28 23:26:11,208 - INFO - 确认按钮点击成功
2025-07-28 23:26:13,209 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-28 23:26:13,216 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-28 23:26:13,216 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:26:14,226 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-28 23:26:14,226 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:26:15,235 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-28 23:26:15,235 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:26:16,243 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-28 23:26:16,243 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:26:17,252 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-07-28 23:26:17,252 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:26:18,262 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-07-28 23:26:18,262 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:26:19,271 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-07-28 23:26:19,271 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:26:20,281 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-07-28 23:26:20,281 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:26:21,291 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-07-28 23:26:21,291 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:26:22,298 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-07-28 23:26:22,298 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:26:23,300 - INFO - 等待超时，进行最后一次检查...
2025-07-28 23:26:23,308 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-07-28 23:26:23,308 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-07-28 23:26:23,308 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-07-28 23:26:23,308 - INFO - 检测是否出现滑块验证...
2025-07-28 23:26:23,318 - INFO - 在主文档中未找到滑块，检查iframe...
2025-07-28 23:26:23,327 - INFO - 找到 1 个iframe
2025-07-28 23:26:23,328 - INFO - 检查第 1 个iframe...
2025-07-28 23:26:23,349 - INFO - 在第 1 个iframe中检测到滑块验证，开始处理...
2025-07-28 23:26:23,349 - INFO - 开始处理滑块验证...
2025-07-28 23:26:23,358 - INFO - 滑块容器宽度: 300px
2025-07-28 23:26:28,659 - INFO - 滑块验证失败，滑块仍然可见
2025-07-28 23:26:28,661 - INFO - 未检测到滑块验证或处理失败
2025-07-28 23:26:28,661 - WARNING - 第 2 个商品处理失败（不计入成功数量）
2025-07-28 23:26:28,662 - INFO - 商品处理失败，立即处理下一个商品
2025-07-28 23:26:28,675 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=789447097664
2025-07-28 23:26:31,792 - INFO - 页面加载完成
2025-07-28 23:26:31,792 - INFO - 模拟阅读行为，停顿 1.0 秒
2025-07-28 23:26:32,812 - INFO - 添加额外延迟: 2.2秒
2025-07-28 23:26:36,452 - INFO - 开始第 1 次铺货尝试...
2025-07-28 23:26:36,452 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:26:36,452 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:26:36,458 - INFO - span innerHTML: 立即铺货
2025-07-28 23:26:36,478 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-28 23:26:36,478 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-28 23:26:36,491 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-28 23:26:37,851 - INFO - 尝试ActionChains点击...
2025-07-28 23:26:38,139 - INFO - ActionChains点击成功
2025-07-28 23:26:38,545 - INFO - 铺货按钮点击成功
2025-07-28 23:26:40,557 - INFO - 使用配置的XPath找到确认按钮
2025-07-28 23:26:40,568 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-28 23:26:41,973 - INFO - 尝试ActionChains点击...
2025-07-28 23:26:42,241 - INFO - ActionChains点击成功
2025-07-28 23:26:42,598 - INFO - 确认按钮点击成功
2025-07-28 23:26:44,599 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-28 23:26:44,608 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-28 23:26:44,608 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:26:45,614 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-28 23:26:45,615 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:26:46,624 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-28 23:26:46,624 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:26:47,631 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-28 23:26:47,631 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:26:48,638 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-07-28 23:26:48,640 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:26:49,647 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-07-28 23:26:49,647 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:26:50,657 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-07-28 23:26:50,658 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:26:51,666 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-07-28 23:26:51,666 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:26:52,675 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-07-28 23:26:52,675 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:26:53,683 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-07-28 23:26:53,684 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:26:54,684 - INFO - 等待超时，进行最后一次检查...
2025-07-28 23:26:54,693 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-07-28 23:26:54,693 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-07-28 23:26:54,693 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-07-28 23:26:54,693 - INFO - 检测是否出现滑块验证...
2025-07-28 23:26:54,697 - INFO - 在主文档中未找到滑块，检查iframe...
2025-07-28 23:26:54,705 - INFO - 找到 1 个iframe
2025-07-28 23:26:54,705 - INFO - 检查第 1 个iframe...
2025-07-28 23:26:54,723 - INFO - 在第 1 个iframe中检测到滑块验证，开始处理...
2025-07-28 23:26:54,723 - INFO - 开始处理滑块验证...
2025-07-28 23:26:54,734 - INFO - 滑块容器宽度: 300px
2025-07-28 23:26:59,753 - INFO - 滑块验证失败，滑块仍然可见
2025-07-28 23:26:59,756 - INFO - 未检测到滑块验证或处理失败
2025-07-28 23:26:59,756 - WARNING - 第 3 个商品处理失败（不计入成功数量）
2025-07-28 23:26:59,756 - INFO - 商品处理失败，立即处理下一个商品
2025-07-28 23:26:59,770 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=694682843417
2025-07-28 23:27:02,858 - INFO - 页面加载完成
2025-07-28 23:27:02,858 - INFO - 模拟阅读行为，停顿 2.6 秒
2025-07-28 23:27:06,500 - INFO - 开始第 1 次铺货尝试...
2025-07-28 23:27:06,500 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:27:06,500 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:27:06,507 - INFO - span innerHTML: 立即铺货
2025-07-28 23:27:06,524 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-28 23:27:06,524 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-28 23:27:06,539 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-28 23:27:08,045 - INFO - 尝试ActionChains点击...
2025-07-28 23:27:08,331 - INFO - ActionChains点击成功
2025-07-28 23:27:08,760 - INFO - 铺货按钮点击成功
2025-07-28 23:27:10,773 - INFO - 使用配置的XPath找到确认按钮
2025-07-28 23:27:10,786 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-28 23:27:12,014 - INFO - 尝试ActionChains点击...
2025-07-28 23:27:12,290 - INFO - ActionChains点击成功
2025-07-28 23:27:12,754 - INFO - 确认按钮点击成功
2025-07-28 23:27:14,755 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-28 23:27:14,757 - INFO - 第1次检查span时出错: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059cda]
	(No symbol) [0x0x7ff69a045f35]
	(No symbol) [0x0x7ff69a06aabe]
	(No symbol) [0x0x7ff69a0dfeb5]
	(No symbol) [0x0x7ff69a100432]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:27:15,758 - INFO - 第2次检查span时出错: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a0a08bf]
	(No symbol) [0x0x7ff69a0d8792]
	(No symbol) [0x0x7ff69a0d3293]
	(No symbol) [0x0x7ff69a0d2359]
	(No symbol) [0x0x7ff69a024b05]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	(No symbol) [0x0x7ff69a023b00]
	GetHandleVerifier [0x0x7ff69a69bd88+4260984]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:27:16,678 - ERROR - 保存cookies失败: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a0a08bf]
	(No symbol) [0x0x7ff69a0d8792]
	(No symbol) [0x0x7ff69a0d3293]
	(No symbol) [0x0x7ff69a0d2359]
	(No symbol) [0x0x7ff69a024b05]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	(No symbol) [0x0x7ff69a023b00]
	GetHandleVerifier [0x0x7ff69a69bd88+4260984]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:27:18,700 - INFO - 浏览器已关闭
