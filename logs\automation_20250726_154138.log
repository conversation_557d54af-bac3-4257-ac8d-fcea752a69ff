2025-07-26 15:41:40,652 - INFO - 使用固定桌面版User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-26 15:41:42,090 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9cda]
	(No symbol) [0x0x7ff7a71f1679]
	(No symbol) [0x0x7ff7a71f4a61]
	(No symbol) [0x0x7ff7a7291e4b]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:41:42,106 - INFO - 防检测机制设置完成
2025-07-26 15:41:42,150 - INFO - 浏览器窗口已最大化
2025-07-26 15:41:42,237 - INFO - 已尝试进入全屏模式（F11）
2025-07-26 15:41:42,285 - INFO - 已通过JavaScript请求全屏
2025-07-26 15:41:42,294 - INFO - 浏览器窗口最终尺寸: 2560x1440
2025-07-26 15:41:42,304 - INFO - 浏览器驱动初始化成功
2025-07-26 15:41:42,316 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:41:42,324 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:41:42,339 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:41:42,350 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:41:42,363 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:41:42,373 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:41:42,385 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:41:42,396 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:41:42,406 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:41:42,416 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:41:42,427 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:41:42,438 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:41:42,448 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:41:42,460 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:41:42,470 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:41:42,481 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:41:42,492 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:41:42,503 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:41:42,513 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:41:42,523 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:41:42,533 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:41:42,541 - INFO - Cookies已加载
2025-07-26 15:41:42,555 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?channel=global_zx&spm=a21ug5.29159167.6452766900.2ndview_zxmore_click
2025-07-26 15:41:42,564 - INFO - 添加额外延迟: 1.3秒
2025-07-26 15:41:49,913 - INFO - 当前User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-26 15:41:49,927 - INFO - 确认：使用桌面版User-Agent
2025-07-26 15:41:49,944 - INFO - 桌面版User-Agent设置完成: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-26 15:41:49,998 - INFO - 浏览器窗口已最大化
2025-07-26 15:41:50,109 - INFO - 已尝试进入全屏模式（F11）
2025-07-26 15:41:50,163 - INFO - 已通过JavaScript请求全屏
2025-07-26 15:41:50,184 - INFO - 浏览器窗口最终尺寸: 2560x1440
2025-07-26 15:41:50,755 - WARNING - 随机鼠标移动失败: Message: move target out of bounds
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a729be53]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:41:52,200 - INFO - 成功导航到目标页面
2025-07-26 15:41:52,208 - INFO - 登录成功！现在可以开始铺货任务
2025-07-26 15:42:00,692 - INFO - 开始自动化铺货流程
2025-07-26 15:42:00,699 - INFO - 正在返回主页面...
2025-07-26 15:42:03,751 - INFO - 已返回主页面
2025-07-26 15:42:03,773 - INFO - 找到 40 个直接子div
2025-07-26 15:42:03,805 - INFO - 商品 1: 位置={'x': 678, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=681778175573&path=search_goods_card&type=s
2025-07-26 15:42:03,852 - INFO - 商品 2: 位置={'x': 915, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=690841537159&path=search_goods_card&index=
2025-07-26 15:42:03,882 - INFO - 商品 3: 位置={'x': 1152, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=789447097664&path=search_goods_card&index=
2025-07-26 15:42:03,914 - INFO - 商品 4: 位置={'x': 1389, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=694682843417&path=search_goods_card&index=
2025-07-26 15:42:03,949 - INFO - 商品 5: 位置={'x': 1626, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=681082809892&path=search_goods_card&index=
2025-07-26 15:42:04,003 - INFO - 商品 6: 位置={'x': 678, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=694675776881&path=search_goods_card&index=
2025-07-26 15:42:04,045 - INFO - 商品 7: 位置={'x': 915, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=680729304287&path=search_goods_card&index=
2025-07-26 15:42:04,087 - INFO - 商品 8: 位置={'x': 1152, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=710355107054&path=search_goods_card&index=
2025-07-26 15:42:04,132 - INFO - 商品 9: 位置={'x': 1389, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=740463658412&path=search_goods_card&index=
2025-07-26 15:42:04,172 - INFO - 商品 10: 位置={'x': 1626, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=821067902127&path=search_goods_card&index=
2025-07-26 15:42:04,213 - INFO - 商品 11: 位置={'x': 678, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=789835300215&path=search_goods_card&index=
2025-07-26 15:42:04,257 - INFO - 商品 12: 位置={'x': 915, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=843747605382&path=search_goods_card&index=
2025-07-26 15:42:04,296 - INFO - 商品 13: 位置={'x': 1152, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=675816539149&path=search_goods_card&index=
2025-07-26 15:42:04,336 - INFO - 商品 14: 位置={'x': 1389, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=846634805775&path=search_goods_card&index=
2025-07-26 15:42:04,376 - INFO - 商品 15: 位置={'x': 1626, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=852510575114&path=search_goods_card&index=
2025-07-26 15:42:04,417 - INFO - 商品 16: 位置={'x': 678, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=743442966826&path=search_goods_card&index=
2025-07-26 15:42:04,454 - INFO - 商品 17: 位置={'x': 915, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=848934676698&path=search_goods_card&index=
2025-07-26 15:42:04,492 - INFO - 商品 18: 位置={'x': 1152, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=693758717931&path=search_goods_card&index=
2025-07-26 15:42:04,531 - INFO - 商品 19: 位置={'x': 1389, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=691142128229&path=search_goods_card&index=
2025-07-26 15:42:04,567 - INFO - 商品 20: 位置={'x': 1626, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=690739721578&path=search_goods_card&index=
2025-07-26 15:42:04,606 - INFO - 商品 21: 位置={'x': 678, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=691918824108&path=search_goods_card&index=
2025-07-26 15:42:04,644 - INFO - 商品 22: 位置={'x': 915, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=875045022715&path=search_goods_card&index=
2025-07-26 15:42:04,687 - INFO - 商品 23: 位置={'x': 1152, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=873911671570&path=search_goods_card&index=
2025-07-26 15:42:04,726 - INFO - 商品 24: 位置={'x': 1389, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=722937163980&path=search_goods_card&index=
2025-07-26 15:42:04,766 - INFO - 商品 25: 位置={'x': 1626, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=739090443937&path=search_goods_card&index=
2025-07-26 15:42:04,808 - INFO - 商品 26: 位置={'x': 678, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=777862601010&path=search_goods_card&index=
2025-07-26 15:42:04,849 - INFO - 商品 27: 位置={'x': 915, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=686870174450&path=search_goods_card&index=
2025-07-26 15:42:04,889 - INFO - 商品 28: 位置={'x': 1152, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=814542665480&path=search_goods_card&index=
2025-07-26 15:42:04,929 - INFO - 商品 29: 位置={'x': 1389, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=852625718015&path=search_goods_card&index=
2025-07-26 15:42:04,969 - INFO - 商品 30: 位置={'x': 1626, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=681472049289&path=search_goods_card&index=
2025-07-26 15:42:05,004 - INFO - 商品 31: 位置={'x': 678, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=683142617961&path=search_goods_card&index=
2025-07-26 15:42:05,045 - INFO - 商品 32: 位置={'x': 915, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=763659951211&path=search_goods_card&index=
2025-07-26 15:42:05,085 - INFO - 商品 33: 位置={'x': 1152, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=675478966678&path=search_goods_card&index=
2025-07-26 15:42:05,126 - INFO - 商品 34: 位置={'x': 1389, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=800365611234&path=search_goods_card&index=
2025-07-26 15:42:05,168 - INFO - 商品 35: 位置={'x': 1626, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=896721954982&path=search_goods_card&index=
2025-07-26 15:42:05,221 - INFO - 商品 36: 位置={'x': 678, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=795389643023&path=search_goods_card&index=
2025-07-26 15:42:05,260 - INFO - 商品 37: 位置={'x': 915, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=689584396900&path=search_goods_card&index=
2025-07-26 15:42:05,299 - INFO - 商品 38: 位置={'x': 1152, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=788516102627&path=search_goods_card&index=
2025-07-26 15:42:05,342 - INFO - 商品 39: 位置={'x': 1389, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=825025224622&path=search_goods_card&index=
2025-07-26 15:42:05,405 - INFO - 商品 40: 位置={'x': 1626, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=842638336638&path=search_goods_card&index=
2025-07-26 15:42:05,437 - INFO - 找到 40 个有效的商品容器
2025-07-26 15:42:05,465 - INFO - 找到 40 个商品，将处理 20 个
2025-07-26 15:42:05,496 - INFO - 当前批次大小: 15
2025-07-26 15:42:05,523 - INFO - 开始处理第 1/40 个商品
2025-07-26 15:42:05,552 - INFO - 找到 40 个直接子div
2025-07-26 15:42:05,586 - INFO - 商品 1: 位置={'x': 678, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=681778175573&path=search_goods_card&type=s
2025-07-26 15:42:05,622 - INFO - 商品 2: 位置={'x': 915, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=690841537159&path=search_goods_card&index=
2025-07-26 15:42:05,659 - INFO - 商品 3: 位置={'x': 1152, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=789447097664&path=search_goods_card&index=
2025-07-26 15:42:05,697 - INFO - 商品 4: 位置={'x': 1389, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=694682843417&path=search_goods_card&index=
2025-07-26 15:42:05,736 - INFO - 商品 5: 位置={'x': 1626, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=681082809892&path=search_goods_card&index=
2025-07-26 15:42:05,790 - INFO - 商品 6: 位置={'x': 678, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=694675776881&path=search_goods_card&index=
2025-07-26 15:42:05,836 - INFO - 商品 7: 位置={'x': 915, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=680729304287&path=search_goods_card&index=
2025-07-26 15:42:05,875 - INFO - 商品 8: 位置={'x': 1152, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=710355107054&path=search_goods_card&index=
2025-07-26 15:42:05,912 - INFO - 商品 9: 位置={'x': 1389, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=740463658412&path=search_goods_card&index=
2025-07-26 15:42:05,951 - INFO - 商品 10: 位置={'x': 1626, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=821067902127&path=search_goods_card&index=
2025-07-26 15:42:06,013 - INFO - 商品 11: 位置={'x': 678, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=789835300215&path=search_goods_card&index=
2025-07-26 15:42:06,126 - INFO - 商品 12: 位置={'x': 915, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=843747605382&path=search_goods_card&index=
2025-07-26 15:42:06,170 - INFO - 商品 13: 位置={'x': 1152, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=675816539149&path=search_goods_card&index=
2025-07-26 15:42:06,213 - INFO - 商品 14: 位置={'x': 1389, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=846634805775&path=search_goods_card&index=
2025-07-26 15:42:06,255 - INFO - 商品 15: 位置={'x': 1626, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=852510575114&path=search_goods_card&index=
2025-07-26 15:42:06,296 - INFO - 商品 16: 位置={'x': 678, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=743442966826&path=search_goods_card&index=
2025-07-26 15:42:06,338 - INFO - 商品 17: 位置={'x': 915, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=848934676698&path=search_goods_card&index=
2025-07-26 15:42:06,376 - INFO - 商品 18: 位置={'x': 1152, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=693758717931&path=search_goods_card&index=
2025-07-26 15:42:06,413 - INFO - 商品 19: 位置={'x': 1389, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=691142128229&path=search_goods_card&index=
2025-07-26 15:42:06,452 - INFO - 商品 20: 位置={'x': 1626, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=690739721578&path=search_goods_card&index=
2025-07-26 15:42:06,492 - INFO - 商品 21: 位置={'x': 678, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=691918824108&path=search_goods_card&index=
2025-07-26 15:42:06,532 - INFO - 商品 22: 位置={'x': 915, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=875045022715&path=search_goods_card&index=
2025-07-26 15:42:06,570 - INFO - 商品 23: 位置={'x': 1152, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=873911671570&path=search_goods_card&index=
2025-07-26 15:42:06,609 - INFO - 商品 24: 位置={'x': 1389, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=722937163980&path=search_goods_card&index=
2025-07-26 15:42:06,648 - INFO - 商品 25: 位置={'x': 1626, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=739090443937&path=search_goods_card&index=
2025-07-26 15:42:06,686 - INFO - 商品 26: 位置={'x': 678, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=777862601010&path=search_goods_card&index=
2025-07-26 15:42:06,733 - INFO - 商品 27: 位置={'x': 915, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=686870174450&path=search_goods_card&index=
2025-07-26 15:42:06,773 - INFO - 商品 28: 位置={'x': 1152, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=814542665480&path=search_goods_card&index=
2025-07-26 15:42:06,815 - INFO - 商品 29: 位置={'x': 1389, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=852625718015&path=search_goods_card&index=
2025-07-26 15:42:06,858 - INFO - 商品 30: 位置={'x': 1626, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=681472049289&path=search_goods_card&index=
2025-07-26 15:42:06,898 - INFO - 商品 31: 位置={'x': 678, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=683142617961&path=search_goods_card&index=
2025-07-26 15:42:06,938 - INFO - 商品 32: 位置={'x': 915, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=763659951211&path=search_goods_card&index=
2025-07-26 15:42:06,976 - INFO - 商品 33: 位置={'x': 1152, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=675478966678&path=search_goods_card&index=
2025-07-26 15:42:07,015 - INFO - 商品 34: 位置={'x': 1389, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=800365611234&path=search_goods_card&index=
2025-07-26 15:42:07,054 - INFO - 商品 35: 位置={'x': 1626, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=896721954982&path=search_goods_card&index=
2025-07-26 15:42:07,092 - INFO - 商品 36: 位置={'x': 678, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=795389643023&path=search_goods_card&index=
2025-07-26 15:42:07,131 - INFO - 商品 37: 位置={'x': 915, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=689584396900&path=search_goods_card&index=
2025-07-26 15:42:07,170 - INFO - 商品 38: 位置={'x': 1152, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=788516102627&path=search_goods_card&index=
2025-07-26 15:42:07,209 - INFO - 商品 39: 位置={'x': 1389, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=825025224622&path=search_goods_card&index=
2025-07-26 15:42:07,248 - INFO - 商品 40: 位置={'x': 1626, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=842638336638&path=search_goods_card&index=
2025-07-26 15:42:07,283 - INFO - 找到 40 个有效的商品容器
2025-07-26 15:42:07,311 - WARNING - 警告：第 1 个元素可能不是商品项，class=''
2025-07-26 15:42:07,356 - INFO - 商品 1 信息: 文本='保税直供 Dove多芬磨砂膏石榴籽乳木果风味身体磨砂膏298g
采购价¥35.00销量11.2万+件', 位置={'x': 678, 'y': 737}
2025-07-26 15:42:08,894 - INFO - 商品 1 位置: {'x': 678, 'y': 737}, 尺寸: {'height': 347, 'width': 226}
2025-07-26 15:42:08,913 - INFO - 模拟阅读行为，停顿 1.7 秒
2025-07-26 15:42:13,198 - INFO - 点击前标签页数量: 1
2025-07-26 15:42:13,214 - INFO - 点击前窗口句柄: ['A88773EC8E2D032A5F0D34BCC37AC22D']
2025-07-26 15:42:13,233 - INFO - 尝试策略: ActionChains点击
2025-07-26 15:42:15,007 - INFO - 点击后标签页数量: 2
2025-07-26 15:42:15,026 - INFO - 点击后窗口句柄: ['A88773EC8E2D032A5F0D34BCC37AC22D', 'EA380ACCBF87E465CD2FCFE22DA5E03B']
2025-07-26 15:42:15,042 - INFO - ActionChains点击成功，新标签页已打开
2025-07-26 15:42:15,064 - INFO - 已点击第 1 个商品，等待页面响应...
2025-07-26 15:42:17,073 - INFO - 点击前窗口句柄数: 2
2025-07-26 15:42:22,096 - INFO - 等待超时，但检测到多个窗口，切换到最后一个
2025-07-26 15:42:22,112 - INFO - 已切换到最后一个窗口，URL: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=681778175573
2025-07-26 15:42:22,146 - INFO - 浏览器窗口已最大化
2025-07-26 15:42:22,199 - INFO - 已尝试进入全屏模式（F11）
2025-07-26 15:42:22,232 - INFO - 已通过JavaScript请求全屏
2025-07-26 15:42:22,244 - INFO - 浏览器窗口最终尺寸: 2560x1440
2025-07-26 15:42:22,252 - INFO - 页面加载完成
2025-07-26 15:42:22,300 - INFO - 浏览器窗口已最大化
2025-07-26 15:42:22,349 - INFO - 已尝试进入全屏模式（F11）
2025-07-26 15:42:22,383 - INFO - 已通过JavaScript请求全屏
2025-07-26 15:42:22,388 - INFO - 浏览器窗口最终尺寸: 2560x1440
2025-07-26 15:42:22,398 - INFO - 模拟阅读行为，停顿 2.6 秒
2025-07-26 15:42:26,574 - INFO - 找到 0 个iframe
2025-07-26 15:42:26,581 - INFO - 未找到包含目标元素的iframe，保持在主文档
2025-07-26 15:42:27,150 - WARNING - 随机鼠标移动失败: Message: move target out of bounds
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a729be53]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:42:29,775 - INFO - 当前在新标签页中，URL: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=681778175573
2025-07-26 15:42:29,786 - INFO - 已切换到主文档，开始查找立即铺货按钮...
2025-07-26 15:42:29,798 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-26 15:42:29,833 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-26 15:42:29,845 - INFO - 使用配置的XPath找到立即铺货按钮
2025-07-26 15:42:29,854 - INFO - 找到立即铺货按钮，准备点击...
2025-07-26 15:42:29,865 - INFO - 模拟阅读行为，停顿 1.3 秒
2025-07-26 15:42:33,511 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-26 15:42:34,824 - INFO - 尝试ActionChains点击...
2025-07-26 15:42:35,128 - INFO - ActionChains点击成功
2025-07-26 15:42:35,513 - INFO - 已点击立即铺货按钮，等待确认对话框...
2025-07-26 15:42:35,524 - INFO - 等待确认对话框出现，停顿 3.1 秒
2025-07-26 15:42:38,697 - INFO - 准备点击元素: tag=div, text='新发宝贝
关联已有宝贝
供货商：芝兰国际贸易供货商
商品名称：保税直供 Dove多芬磨砂膏石榴籽乳木果风味身体磨砂膏298g
铺货时宝贝取建议零售价为一口价（SKU销售价默认取SKU建议零售价）， 可以在千牛卖家后台进行价格调整，低于最低限价销售时，存在资金倒挂无法下单风险， 也可能被供货商取消产品授权
SKU列表
SKU名称
最低限价
建议零售价
香味:石榴籽乳木果风味298g
¥35
¥45
香味:石榴籽乳木果风味298g*2盒
¥67
¥79
香味:薰衣草椰奶风味298g
¥35
¥45
香味:薰衣草椰奶风味298g*2盒
¥67
¥79
香味:夏威夷果米浆风味298g
¥35
¥45
香味:夏威夷果米浆风味298g*2盒
¥67
¥79
该商品铺货后需要登录2.0(跨境贸易)分销商后台查看消息。
立即铺货', class='next-dialog next-closeable nex', id=''
2025-07-26 15:42:40,156 - INFO - 尝试ActionChains点击...
2025-07-26 15:42:40,426 - INFO - ActionChains点击成功
2025-07-26 15:42:40,919 - INFO - 已关闭弹窗: //div[contains(@class, 'close')]
2025-07-26 15:42:45,983 - WARNING - 等待元素可点击超时: /html/body/div[8]/div[2]/div[2]/button
2025-07-26 15:42:48,018 - WARNING - 等待元素可点击超时: //button[contains(text(), '确认') or contains(text(), '确定') or contains(text(), '提交')]
2025-07-26 15:42:50,095 - WARNING - 等待元素可点击超时: //button[contains(@class, 'confirm') or contains(@class, 'submit')]
2025-07-26 15:42:50,127 - INFO - 使用选择器找到确认按钮: //div[contains(@class, 'dialog')]//button[last()]
2025-07-26 15:42:51,081 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-26 15:42:52,396 - INFO - 尝试ActionChains点击...
2025-07-26 15:42:52,683 - INFO - ActionChains点击成功
2025-07-26 15:42:52,947 - INFO - 商品添加成功
2025-07-26 15:42:52,955 - INFO - 等待操作完成，停顿 2.1 秒
2025-07-26 15:42:55,148 - INFO - 已关闭当前标签页并切换回主页面
2025-07-26 15:42:55,162 - INFO - 第 1 个商品处理成功 (总成功: 1, 批次进度: 1/15)
2025-07-26 15:42:55,175 - INFO - 等待 6.1 秒后处理下一个商品
2025-07-26 15:43:01,292 - INFO - 开始处理第 2/40 个商品
2025-07-26 15:43:01,311 - INFO - 找到 40 个直接子div
2025-07-26 15:43:01,336 - INFO - 商品 1: 位置={'x': 678, 'y': 737}, 尺寸={'height': 347, 'width': 226}, ID=item_id=681778175573&path=search_goods_card&type=s
2025-07-26 15:43:01,364 - INFO - 商品 2: 位置={'x': 916, 'y': 737}, 尺寸={'height': 347, 'width': 225}, ID=item_id=690841537159&path=search_goods_card&index=
2025-07-26 15:43:01,396 - INFO - 商品 3: 位置={'x': 1152, 'y': 737}, 尺寸={'height': 347, 'width': 225}, ID=item_id=789447097664&path=search_goods_card&index=
2025-07-26 15:43:01,427 - INFO - 商品 4: 位置={'x': 1390, 'y': 737}, 尺寸={'height': 347, 'width': 225}, ID=item_id=694682843417&path=search_goods_card&index=
2025-07-26 15:43:01,459 - INFO - 商品 5: 位置={'x': 1626, 'y': 737}, 尺寸={'height': 347, 'width': 225}, ID=item_id=681082809892&path=search_goods_card&index=
2025-07-26 15:43:01,495 - INFO - 商品 6: 位置={'x': 678, 'y': 1096}, 尺寸={'height': 341, 'width': 226}, ID=item_id=694675776881&path=search_goods_card&index=
2025-07-26 15:43:01,529 - INFO - 商品 7: 位置={'x': 916, 'y': 1096}, 尺寸={'height': 341, 'width': 225}, ID=item_id=680729304287&path=search_goods_card&index=
2025-07-26 15:43:01,567 - INFO - 商品 8: 位置={'x': 1152, 'y': 1096}, 尺寸={'height': 341, 'width': 225}, ID=item_id=710355107054&path=search_goods_card&index=
2025-07-26 15:43:01,605 - INFO - 商品 9: 位置={'x': 1390, 'y': 1096}, 尺寸={'height': 341, 'width': 225}, ID=item_id=740463658412&path=search_goods_card&index=
2025-07-26 15:43:01,644 - INFO - 商品 10: 位置={'x': 1626, 'y': 1096}, 尺寸={'height': 341, 'width': 225}, ID=item_id=821067902127&path=search_goods_card&index=
2025-07-26 15:43:01,683 - INFO - 商品 11: 位置={'x': 678, 'y': 1449}, 尺寸={'height': 341, 'width': 226}, ID=item_id=789835300215&path=search_goods_card&index=
2025-07-26 15:43:01,721 - INFO - 商品 12: 位置={'x': 916, 'y': 1449}, 尺寸={'height': 341, 'width': 225}, ID=item_id=843747605382&path=search_goods_card&index=
2025-07-26 15:43:01,762 - INFO - 商品 13: 位置={'x': 1152, 'y': 1449}, 尺寸={'height': 341, 'width': 225}, ID=item_id=675816539149&path=search_goods_card&index=
2025-07-26 15:43:01,802 - INFO - 商品 14: 位置={'x': 1390, 'y': 1449}, 尺寸={'height': 341, 'width': 225}, ID=item_id=846634805775&path=search_goods_card&index=
2025-07-26 15:43:01,842 - INFO - 商品 15: 位置={'x': 1626, 'y': 1449}, 尺寸={'height': 341, 'width': 225}, ID=item_id=852510575114&path=search_goods_card&index=
2025-07-26 15:43:01,883 - INFO - 商品 16: 位置={'x': 678, 'y': 1802}, 尺寸={'height': 341, 'width': 226}, ID=item_id=743442966826&path=search_goods_card&index=
2025-07-26 15:43:01,922 - INFO - 商品 17: 位置={'x': 916, 'y': 1802}, 尺寸={'height': 341, 'width': 225}, ID=item_id=848934676698&path=search_goods_card&index=
2025-07-26 15:43:01,959 - INFO - 商品 18: 位置={'x': 1152, 'y': 1802}, 尺寸={'height': 341, 'width': 225}, ID=item_id=693758717931&path=search_goods_card&index=
2025-07-26 15:43:02,000 - INFO - 商品 19: 位置={'x': 1390, 'y': 1802}, 尺寸={'height': 341, 'width': 225}, ID=item_id=691142128229&path=search_goods_card&index=
2025-07-26 15:43:02,040 - INFO - 商品 20: 位置={'x': 1626, 'y': 1802}, 尺寸={'height': 341, 'width': 225}, ID=item_id=690739721578&path=search_goods_card&index=
2025-07-26 15:43:02,080 - INFO - 商品 21: 位置={'x': 678, 'y': 2155}, 尺寸={'height': 341, 'width': 226}, ID=item_id=691918824108&path=search_goods_card&index=
2025-07-26 15:43:02,114 - INFO - 商品 22: 位置={'x': 916, 'y': 2155}, 尺寸={'height': 341, 'width': 225}, ID=item_id=875045022715&path=search_goods_card&index=
2025-07-26 15:43:02,155 - INFO - 商品 23: 位置={'x': 1152, 'y': 2155}, 尺寸={'height': 341, 'width': 225}, ID=item_id=873911671570&path=search_goods_card&index=
2025-07-26 15:43:02,191 - INFO - 商品 24: 位置={'x': 1390, 'y': 2155}, 尺寸={'height': 341, 'width': 225}, ID=item_id=722937163980&path=search_goods_card&index=
2025-07-26 15:43:02,230 - INFO - 商品 25: 位置={'x': 1626, 'y': 2155}, 尺寸={'height': 341, 'width': 225}, ID=item_id=739090443937&path=search_goods_card&index=
2025-07-26 15:43:02,270 - INFO - 商品 26: 位置={'x': 678, 'y': 2508}, 尺寸={'height': 341, 'width': 226}, ID=item_id=777862601010&path=search_goods_card&index=
2025-07-26 15:43:02,313 - INFO - 商品 27: 位置={'x': 916, 'y': 2508}, 尺寸={'height': 341, 'width': 225}, ID=item_id=686870174450&path=search_goods_card&index=
2025-07-26 15:43:02,382 - INFO - 商品 28: 位置={'x': 1152, 'y': 2508}, 尺寸={'height': 341, 'width': 225}, ID=item_id=814542665480&path=search_goods_card&index=
2025-07-26 15:43:02,422 - INFO - 商品 29: 位置={'x': 1390, 'y': 2508}, 尺寸={'height': 341, 'width': 225}, ID=item_id=852625718015&path=search_goods_card&index=
2025-07-26 15:43:02,459 - INFO - 商品 30: 位置={'x': 1626, 'y': 2508}, 尺寸={'height': 341, 'width': 225}, ID=item_id=681472049289&path=search_goods_card&index=
2025-07-26 15:43:02,498 - INFO - 商品 31: 位置={'x': 678, 'y': 2861}, 尺寸={'height': 341, 'width': 226}, ID=item_id=683142617961&path=search_goods_card&index=
2025-07-26 15:43:02,538 - INFO - 商品 32: 位置={'x': 916, 'y': 2861}, 尺寸={'height': 341, 'width': 225}, ID=item_id=763659951211&path=search_goods_card&index=
2025-07-26 15:43:02,578 - INFO - 商品 33: 位置={'x': 1152, 'y': 2861}, 尺寸={'height': 341, 'width': 225}, ID=item_id=675478966678&path=search_goods_card&index=
2025-07-26 15:43:02,615 - INFO - 商品 34: 位置={'x': 1390, 'y': 2861}, 尺寸={'height': 341, 'width': 225}, ID=item_id=800365611234&path=search_goods_card&index=
2025-07-26 15:43:02,655 - INFO - 商品 35: 位置={'x': 1626, 'y': 2861}, 尺寸={'height': 341, 'width': 225}, ID=item_id=896721954982&path=search_goods_card&index=
2025-07-26 15:43:02,693 - INFO - 商品 36: 位置={'x': 678, 'y': 3214}, 尺寸={'height': 341, 'width': 226}, ID=item_id=795389643023&path=search_goods_card&index=
2025-07-26 15:43:02,734 - INFO - 商品 37: 位置={'x': 916, 'y': 3214}, 尺寸={'height': 341, 'width': 225}, ID=item_id=689584396900&path=search_goods_card&index=
2025-07-26 15:43:02,772 - INFO - 商品 38: 位置={'x': 1152, 'y': 3214}, 尺寸={'height': 341, 'width': 225}, ID=item_id=788516102627&path=search_goods_card&index=
2025-07-26 15:43:02,810 - INFO - 商品 39: 位置={'x': 1390, 'y': 3214}, 尺寸={'height': 341, 'width': 225}, ID=item_id=825025224622&path=search_goods_card&index=
2025-07-26 15:43:02,850 - INFO - 商品 40: 位置={'x': 1626, 'y': 3214}, 尺寸={'height': 341, 'width': 225}, ID=item_id=842638336638&path=search_goods_card&index=
2025-07-26 15:43:02,884 - INFO - 找到 40 个有效的商品容器
2025-07-26 15:43:02,913 - WARNING - 警告：第 2 个元素可能不是商品项，class=''
2025-07-26 15:43:02,953 - INFO - 商品 2 信息: 文本='保税直供 凡士林Vaseline烟酰胺身体乳400ml 725ml无压泵款200ml
采购价¥19.', 位置={'x': 916, 'y': 737}
2025-07-26 15:43:04,491 - INFO - 商品 2 位置: {'x': 916, 'y': 737}, 尺寸: {'height': 347, 'width': 226}
2025-07-26 15:43:04,513 - INFO - 模拟阅读行为，停顿 2.1 秒
2025-07-26 15:43:08,772 - INFO - 点击前标签页数量: 1
2025-07-26 15:43:08,788 - INFO - 点击前窗口句柄: ['A88773EC8E2D032A5F0D34BCC37AC22D']
2025-07-26 15:43:08,807 - INFO - 尝试策略: ActionChains点击
2025-07-26 15:43:10,554 - INFO - 点击后标签页数量: 2
2025-07-26 15:43:10,570 - INFO - 点击后窗口句柄: ['A88773EC8E2D032A5F0D34BCC37AC22D', 'D615BC1C8724D38C51C626840B73910B']
2025-07-26 15:43:10,584 - INFO - ActionChains点击成功，新标签页已打开
2025-07-26 15:43:10,599 - INFO - 已点击第 2 个商品，等待页面响应...
2025-07-26 15:43:12,605 - INFO - 点击前窗口句柄数: 2
2025-07-26 15:43:17,628 - INFO - 等待超时，但检测到多个窗口，切换到最后一个
2025-07-26 15:43:17,643 - INFO - 已切换到最后一个窗口，URL: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_1&supplierItemId=690841537159
2025-07-26 15:43:17,673 - INFO - 浏览器窗口已最大化
2025-07-26 15:43:17,737 - INFO - 已尝试进入全屏模式（F11）
2025-07-26 15:43:17,779 - INFO - 已通过JavaScript请求全屏
2025-07-26 15:43:17,794 - INFO - 浏览器窗口最终尺寸: 2560x1440
2025-07-26 15:43:17,804 - INFO - 页面加载完成
2025-07-26 15:43:17,877 - INFO - 浏览器窗口已最大化
2025-07-26 15:43:17,922 - INFO - 已尝试进入全屏模式（F11）
2025-07-26 15:43:17,960 - INFO - 已通过JavaScript请求全屏
2025-07-26 15:43:17,969 - INFO - 浏览器窗口最终尺寸: 2560x1440
2025-07-26 15:43:17,981 - INFO - 模拟阅读行为，停顿 2.7 秒
2025-07-26 15:43:20,720 - INFO - 添加额外延迟: 2.3秒
2025-07-26 15:43:24,498 - INFO - 找到 0 个iframe
2025-07-26 15:43:24,504 - INFO - 未找到包含目标元素的iframe，保持在主文档
2025-07-26 15:43:26,240 - WARNING - 随机鼠标移动失败: Message: move target out of bounds
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a729be53]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 15:43:29,561 - INFO - 当前在新标签页中，URL: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_1&supplierItemId=690841537159
2025-07-26 15:43:29,571 - INFO - 已切换到主文档，开始查找立即铺货按钮...
2025-07-26 15:43:29,583 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-26 15:43:29,612 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-26 15:43:29,627 - INFO - 使用配置的XPath找到立即铺货按钮
2025-07-26 15:43:29,637 - INFO - 找到立即铺货按钮，准备点击...
2025-07-26 15:43:29,649 - INFO - 模拟阅读行为，停顿 2.7 秒
2025-07-26 15:43:33,443 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-26 15:43:34,517 - INFO - 尝试ActionChains点击...
2025-07-26 15:43:34,823 - INFO - ActionChains点击成功
2025-07-26 15:43:35,274 - INFO - 已点击立即铺货按钮，等待确认对话框...
2025-07-26 15:43:35,284 - INFO - 等待确认对话框出现，停顿 3.1 秒
2025-07-26 15:43:38,407 - INFO - 准备点击元素: tag=div, text='新发宝贝
关联已有宝贝
供货商：芝兰国际贸易供货商
商品名称：保税直供 凡士林Vaseline烟酰胺身体乳400ml 725ml无压泵款200ml
铺货时宝贝取建议零售价为一口价（SKU销售价默认取SKU建议零售价）， 可以在千牛卖家后台进行价格调整，低于最低限价销售时，存在资金倒挂无法下单风险， 也可能被供货商取消产品授权
SKU列表
SKU名称
最低限价
建议零售价
净含量:400ml;香味:烟酰胺【200ml*2瓶】
¥22
¥35
净含量:400ml;香味:烟酰胺400ml*1瓶
¥19.5
¥29
净含量:400ml;香味:烟酰胺400ml*2瓶
¥36
¥49
净含量:400ml;香味:烟酰胺400ml*3瓶
¥53
¥65
净含量:400ml;香味:烟酰胺725ml*1瓶
¥29
¥39
该商品铺货后需要登录2.0(跨境贸易)分销商后台查看消息。
立即铺货', class='next-dialog next-closeable nex', id=''
2025-07-26 15:43:39,660 - INFO - 尝试ActionChains点击...
2025-07-26 15:43:39,938 - INFO - ActionChains点击成功
2025-07-26 15:43:40,441 - INFO - 已关闭弹窗: //div[contains(@class, 'close')]
2025-07-26 15:43:45,521 - WARNING - 等待元素可点击超时: /html/body/div[8]/div[2]/div[2]/button
2025-07-26 15:43:47,552 - WARNING - 等待元素可点击超时: //button[contains(text(), '确认') or contains(text(), '确定') or contains(text(), '提交')]
2025-07-26 15:43:49,584 - WARNING - 等待元素可点击超时: //button[contains(@class, 'confirm') or contains(@class, 'submit')]
2025-07-26 15:43:49,610 - INFO - 使用选择器找到确认按钮: //div[contains(@class, 'dialog')]//button[last()]
2025-07-26 15:43:50,093 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-26 15:43:51,372 - INFO - 尝试ActionChains点击...
2025-07-26 15:43:51,646 - INFO - ActionChains点击成功
2025-07-26 15:43:51,975 - INFO - 商品添加成功
2025-07-26 15:43:51,982 - INFO - 等待操作完成，停顿 1.6 秒
2025-07-26 15:43:53,633 - INFO - 已关闭当前标签页并切换回主页面
2025-07-26 15:43:53,646 - INFO - 第 2 个商品处理成功 (总成功: 2, 批次进度: 2/15)
2025-07-26 15:43:53,658 - INFO - 等待 3.5 秒后处理下一个商品
2025-07-26 15:43:57,153 - INFO - 开始处理第 3/40 个商品
2025-07-26 15:43:57,171 - INFO - 找到 40 个直接子div
2025-07-26 15:43:57,203 - INFO - 商品 1: 位置={'x': 678, 'y': 737}, 尺寸={'height': 347, 'width': 226}, ID=item_id=681778175573&path=search_goods_card&type=s
2025-07-26 15:43:57,236 - INFO - 商品 2: 位置={'x': 916, 'y': 737}, 尺寸={'height': 347, 'width': 226}, ID=item_id=690841537159&path=search_goods_card&index=
2025-07-26 15:43:57,275 - INFO - 商品 3: 位置={'x': 1154, 'y': 737}, 尺寸={'height': 347, 'width': 225}, ID=item_id=789447097664&path=search_goods_card&index=
2025-07-26 15:43:57,312 - INFO - 商品 4: 位置={'x': 1390, 'y': 737}, 尺寸={'height': 347, 'width': 225}, ID=item_id=694682843417&path=search_goods_card&index=
2025-07-26 15:43:57,346 - INFO - 商品 5: 位置={'x': 1627, 'y': 737}, 尺寸={'height': 347, 'width': 225}, ID=item_id=681082809892&path=search_goods_card&index=
2025-07-26 15:43:57,382 - INFO - 商品 6: 位置={'x': 678, 'y': 1096}, 尺寸={'height': 341, 'width': 226}, ID=item_id=694675776881&path=search_goods_card&index=
2025-07-26 15:43:57,424 - INFO - 商品 7: 位置={'x': 916, 'y': 1096}, 尺寸={'height': 341, 'width': 226}, ID=item_id=680729304287&path=search_goods_card&index=
2025-07-26 15:43:57,473 - INFO - 商品 8: 位置={'x': 1154, 'y': 1096}, 尺寸={'height': 341, 'width': 225}, ID=item_id=710355107054&path=search_goods_card&index=
2025-07-26 15:43:57,513 - INFO - 商品 9: 位置={'x': 1390, 'y': 1096}, 尺寸={'height': 341, 'width': 225}, ID=item_id=740463658412&path=search_goods_card&index=
2025-07-26 15:43:57,552 - INFO - 商品 10: 位置={'x': 1627, 'y': 1096}, 尺寸={'height': 341, 'width': 225}, ID=item_id=821067902127&path=search_goods_card&index=
2025-07-26 15:43:57,591 - INFO - 商品 11: 位置={'x': 678, 'y': 1449}, 尺寸={'height': 341, 'width': 226}, ID=item_id=789835300215&path=search_goods_card&index=
2025-07-26 15:43:57,634 - INFO - 商品 12: 位置={'x': 916, 'y': 1449}, 尺寸={'height': 341, 'width': 226}, ID=item_id=843747605382&path=search_goods_card&index=
2025-07-26 15:43:57,673 - INFO - 商品 13: 位置={'x': 1154, 'y': 1449}, 尺寸={'height': 341, 'width': 225}, ID=item_id=675816539149&path=search_goods_card&index=
2025-07-26 15:43:57,712 - INFO - 商品 14: 位置={'x': 1390, 'y': 1449}, 尺寸={'height': 341, 'width': 225}, ID=item_id=846634805775&path=search_goods_card&index=
2025-07-26 15:43:57,754 - INFO - 商品 15: 位置={'x': 1627, 'y': 1449}, 尺寸={'height': 341, 'width': 225}, ID=item_id=852510575114&path=search_goods_card&index=
2025-07-26 15:43:57,795 - INFO - 商品 16: 位置={'x': 678, 'y': 1802}, 尺寸={'height': 341, 'width': 226}, ID=item_id=743442966826&path=search_goods_card&index=
2025-07-26 15:43:57,855 - INFO - 商品 17: 位置={'x': 916, 'y': 1802}, 尺寸={'height': 341, 'width': 226}, ID=item_id=848934676698&path=search_goods_card&index=
2025-07-26 15:43:57,896 - INFO - 商品 18: 位置={'x': 1154, 'y': 1802}, 尺寸={'height': 341, 'width': 225}, ID=item_id=693758717931&path=search_goods_card&index=
2025-07-26 15:43:57,933 - INFO - 商品 19: 位置={'x': 1390, 'y': 1802}, 尺寸={'height': 341, 'width': 225}, ID=item_id=691142128229&path=search_goods_card&index=
2025-07-26 15:43:57,970 - INFO - 商品 20: 位置={'x': 1627, 'y': 1802}, 尺寸={'height': 341, 'width': 225}, ID=item_id=690739721578&path=search_goods_card&index=
2025-07-26 15:43:58,020 - INFO - 商品 21: 位置={'x': 678, 'y': 2155}, 尺寸={'height': 341, 'width': 226}, ID=item_id=691918824108&path=search_goods_card&index=
2025-07-26 15:43:58,075 - INFO - 商品 22: 位置={'x': 916, 'y': 2155}, 尺寸={'height': 341, 'width': 226}, ID=item_id=875045022715&path=search_goods_card&index=
2025-07-26 15:43:58,154 - INFO - 商品 23: 位置={'x': 1154, 'y': 2155}, 尺寸={'height': 341, 'width': 225}, ID=item_id=873911671570&path=search_goods_card&index=
2025-07-26 15:43:58,280 - INFO - 商品 24: 位置={'x': 1390, 'y': 2155}, 尺寸={'height': 341, 'width': 225}, ID=item_id=722937163980&path=search_goods_card&index=
2025-07-26 15:43:58,326 - INFO - 商品 25: 位置={'x': 1627, 'y': 2155}, 尺寸={'height': 341, 'width': 225}, ID=item_id=739090443937&path=search_goods_card&index=
2025-07-26 15:43:58,424 - INFO - 商品 26: 位置={'x': 678, 'y': 2508}, 尺寸={'height': 341, 'width': 226}, ID=item_id=777862601010&path=search_goods_card&index=
2025-07-26 15:43:58,473 - INFO - 商品 27: 位置={'x': 916, 'y': 2508}, 尺寸={'height': 341, 'width': 226}, ID=item_id=686870174450&path=search_goods_card&index=
2025-07-26 15:43:58,524 - INFO - 商品 28: 位置={'x': 1154, 'y': 2508}, 尺寸={'height': 341, 'width': 225}, ID=item_id=814542665480&path=search_goods_card&index=
2025-07-26 15:43:58,567 - INFO - 商品 29: 位置={'x': 1390, 'y': 2508}, 尺寸={'height': 341, 'width': 225}, ID=item_id=852625718015&path=search_goods_card&index=
2025-07-26 15:43:58,609 - INFO - 商品 30: 位置={'x': 1627, 'y': 2508}, 尺寸={'height': 341, 'width': 225}, ID=item_id=681472049289&path=search_goods_card&index=
2025-07-26 15:43:58,660 - INFO - 商品 31: 位置={'x': 678, 'y': 2861}, 尺寸={'height': 341, 'width': 226}, ID=item_id=683142617961&path=search_goods_card&index=
2025-07-26 15:43:58,732 - INFO - 商品 32: 位置={'x': 916, 'y': 2861}, 尺寸={'height': 341, 'width': 226}, ID=item_id=763659951211&path=search_goods_card&index=
2025-07-26 15:43:58,781 - INFO - 商品 33: 位置={'x': 1154, 'y': 2861}, 尺寸={'height': 341, 'width': 225}, ID=item_id=675478966678&path=search_goods_card&index=
2025-07-26 15:43:58,821 - INFO - 商品 34: 位置={'x': 1390, 'y': 2861}, 尺寸={'height': 341, 'width': 225}, ID=item_id=800365611234&path=search_goods_card&index=
2025-07-26 15:43:58,860 - INFO - 商品 35: 位置={'x': 1627, 'y': 2861}, 尺寸={'height': 341, 'width': 225}, ID=item_id=896721954982&path=search_goods_card&index=
2025-07-26 15:43:58,900 - INFO - 商品 36: 位置={'x': 678, 'y': 3214}, 尺寸={'height': 341, 'width': 226}, ID=item_id=795389643023&path=search_goods_card&index=
2025-07-26 15:43:58,941 - INFO - 商品 37: 位置={'x': 916, 'y': 3214}, 尺寸={'height': 341, 'width': 226}, ID=item_id=689584396900&path=search_goods_card&index=
2025-07-26 15:43:58,980 - INFO - 商品 38: 位置={'x': 1154, 'y': 3214}, 尺寸={'height': 341, 'width': 225}, ID=item_id=788516102627&path=search_goods_card&index=
2025-07-26 15:43:59,018 - INFO - 商品 39: 位置={'x': 1390, 'y': 3214}, 尺寸={'height': 341, 'width': 225}, ID=item_id=825025224622&path=search_goods_card&index=
2025-07-26 15:43:59,059 - INFO - 商品 40: 位置={'x': 1627, 'y': 3214}, 尺寸={'height': 341, 'width': 225}, ID=item_id=842638336638&path=search_goods_card&index=
2025-07-26 15:43:59,091 - INFO - 找到 40 个有效的商品容器
2025-07-26 15:43:59,118 - WARNING - 警告：第 3 个元素可能不是商品项，class=''
2025-07-26 15:43:59,162 - INFO - 商品 3 信息: 文本='【甄选】珂润Curel泡沫洗面奶150ml
采购价¥52.00销量7.9万+件
佣金率5.50%|1', 位置={'x': 1154, 'y': 737}
2025-07-26 15:44:00,696 - INFO - 商品 3 位置: {'x': 1154, 'y': 737}, 尺寸: {'height': 347, 'width': 226}
2025-07-26 15:44:00,720 - INFO - 模拟阅读行为，停顿 1.2 秒
2025-07-26 15:44:03,232 - INFO - Cookies已保存
2025-07-26 15:44:05,556 - INFO - 浏览器已关闭
