2025-07-27 23:03:53,986 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169cda]
	(No symbol) [0x0x7ff7cd171679]
	(No symbol) [0x0x7ff7cd174a61]
	(No symbol) [0x0x7ff7cd211e4b]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:03:53,986 - INFO - 防检测机制设置完成
2025-07-27 23:03:53,986 - INFO - 浏览器驱动初始化成功
2025-07-27 23:03:53,993 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:03:53,997 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:03:54,001 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:03:54,006 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:03:54,010 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:03:54,012 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:03:54,018 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:03:54,026 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:03:54,028 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:03:54,033 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:03:54,036 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:03:54,038 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:03:54,041 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:03:54,045 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:03:54,050 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:03:54,052 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:03:54,054 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:03:54,058 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:03:54,061 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:03:54,065 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:03:54,065 - INFO - Cookies已加载
2025-07-27 23:03:54,065 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?channel=global_zx&spm=a21ug5.29159167.6452766900.2ndview_zxmore_click
2025-07-27 23:03:59,382 - INFO - 当前User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-27 23:03:59,382 - INFO - 确认：使用桌面版User-Agent
2025-07-27 23:03:59,386 - INFO - 桌面版User-Agent设置完成: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-27 23:04:00,522 - WARNING - 随机鼠标移动失败: Message: move target out of bounds
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd21be53]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:04:00,525 - INFO - 成功导航到目标页面
2025-07-27 23:04:00,526 - INFO - 登录成功！现在可以开始铺货任务
2025-07-27 23:04:02,932 - INFO - Cookies已保存
2025-07-27 23:04:05,218 - INFO - 浏览器已关闭
2025-07-27 23:04:06,662 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169cda]
	(No symbol) [0x0x7ff7cd171679]
	(No symbol) [0x0x7ff7cd174a61]
	(No symbol) [0x0x7ff7cd211e4b]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:04:06,664 - INFO - 防检测机制设置完成
2025-07-27 23:04:06,664 - INFO - 浏览器驱动初始化成功
2025-07-27 23:04:06,664 - INFO - 开始自动化铺货流程（有窗口模式）
2025-07-27 23:04:06,664 - INFO - 第一步：正在导航到目标页面...
2025-07-27 23:04:09,914 - INFO - 第一步：点击登录按钮
2025-07-27 23:04:09,937 - INFO - 找到登录按钮，使用选择器: //*[@id='login-form']/div[6]/button
2025-07-27 23:04:10,978 - INFO - 准备点击登录按钮: 标签=button, 文本='登录', 选择器=//*[@id='login-form']/div[6]/button
2025-07-27 23:04:10,995 - INFO - 准备点击元素: tag=button, text='登录', class='fm-button fm-submit password-l', id=''
2025-07-27 23:04:12,412 - INFO - 尝试ActionChains点击...
2025-07-27 23:04:12,827 - INFO - ActionChains点击成功
2025-07-27 23:04:13,062 - INFO - 登录按钮点击成功
2025-07-27 23:04:13,062 - INFO - 登录按钮点击成功
2025-07-27 23:04:16,063 - INFO - 第二步：获取账号信息...
2025-07-27 23:04:16,063 - INFO - 正在跳转到个人中心页面: https://jingya.taobao.com/?v=2
2025-07-27 23:04:41,218 - INFO - 尝试获取账号名称，使用XPath: //*[@id='workbench-user-tool-btn']/div/div[2]
2025-07-27 23:04:41,230 - INFO - 成功获取账号信息: tb997603987291:书生
2025-07-27 23:04:41,230 - INFO - 获取到账号名称: tb997603987291:书生
2025-07-27 23:04:41,231 - INFO - 第三步：开始爬取商品信息...
2025-07-27 23:04:41,231 - INFO - GUI界面已更新账号显示: tb997603987291:书生
2025-07-27 23:04:41,231 - INFO - 开始爬取商品信息...
2025-07-27 23:04:45,516 - INFO - 正在爬取第 1 页商品信息...
2025-07-27 23:04:47,524 - INFO - 找到商品容器，开始爬取商品...
2025-07-27 23:04:47,538 - INFO - 找到商品项 1，查找tfx-item元素...
2025-07-27 23:04:47,548 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:04:47,557 - INFO - 商品项 1 outerHTML长度: 199
2025-07-27 23:04:47,557 - INFO - 商品项 1 outerHTML中不包含data-autolog
2025-07-27 23:04:47,557 - INFO - 商品项 1 未找到data-autolog属性，跳过
2025-07-27 23:04:47,567 - INFO - 找到商品项 2，查找tfx-item元素...
2025-07-27 23:04:47,572 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:04:47,577 - INFO - 商品项 2 outerHTML长度: 199
2025-07-27 23:04:47,577 - INFO - 商品项 2 outerHTML中不包含data-autolog
2025-07-27 23:04:47,577 - INFO - 商品项 2 未找到data-autolog属性，跳过
2025-07-27 23:04:47,588 - INFO - 找到商品项 3，查找tfx-item元素...
2025-07-27 23:04:47,594 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:04:47,600 - INFO - 商品项 3 outerHTML长度: 199
2025-07-27 23:04:47,600 - INFO - 商品项 3 outerHTML中不包含data-autolog
2025-07-27 23:04:47,600 - INFO - 商品项 3 未找到data-autolog属性，跳过
2025-07-27 23:04:47,611 - INFO - 找到商品项 4，查找tfx-item元素...
2025-07-27 23:04:47,616 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:04:47,621 - INFO - 商品项 4 outerHTML长度: 199
2025-07-27 23:04:47,621 - INFO - 商品项 4 outerHTML中不包含data-autolog
2025-07-27 23:04:47,622 - INFO - 商品项 4 未找到data-autolog属性，跳过
2025-07-27 23:04:47,745 - INFO - 找到商品项 5，查找tfx-item元素...
2025-07-27 23:04:47,754 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:04:47,765 - INFO - 商品项 5 outerHTML长度: 199
2025-07-27 23:04:47,765 - INFO - 商品项 5 outerHTML中不包含data-autolog
2025-07-27 23:04:47,765 - INFO - 商品项 5 未找到data-autolog属性，跳过
2025-07-27 23:04:47,777 - INFO - 找到商品项 6，查找tfx-item元素...
2025-07-27 23:04:47,784 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:04:47,796 - INFO - 商品项 6 outerHTML长度: 199
2025-07-27 23:04:47,796 - INFO - 商品项 6 outerHTML中不包含data-autolog
2025-07-27 23:04:47,796 - INFO - 商品项 6 未找到data-autolog属性，跳过
2025-07-27 23:04:47,811 - INFO - 找到商品项 7，查找tfx-item元素...
2025-07-27 23:04:47,816 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:04:47,822 - INFO - 商品项 7 outerHTML长度: 161
2025-07-27 23:04:47,823 - INFO - 商品项 7 outerHTML中不包含data-autolog
2025-07-27 23:04:47,823 - INFO - 商品项 7 未找到data-autolog属性，跳过
2025-07-27 23:04:47,833 - INFO - 找到商品项 8，查找tfx-item元素...
2025-07-27 23:04:47,838 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:04:47,844 - INFO - 商品项 8 outerHTML长度: 161
2025-07-27 23:04:47,845 - INFO - 商品项 8 outerHTML中不包含data-autolog
2025-07-27 23:04:47,845 - INFO - 商品项 8 未找到data-autolog属性，跳过
2025-07-27 23:04:47,872 - INFO - 找到商品项 9，查找tfx-item元素...
2025-07-27 23:04:47,897 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:04:47,904 - INFO - 商品项 9 outerHTML长度: 155
2025-07-27 23:04:47,904 - INFO - 商品项 9 outerHTML中不包含data-autolog
2025-07-27 23:04:47,904 - INFO - 商品项 9 未找到data-autolog属性，跳过
2025-07-27 23:04:47,916 - INFO - 找到商品项 10，查找tfx-item元素...
2025-07-27 23:04:47,921 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:04:47,928 - INFO - 商品项 10 outerHTML长度: 161
2025-07-27 23:04:47,928 - INFO - 商品项 10 outerHTML中不包含data-autolog
2025-07-27 23:04:47,928 - INFO - 商品项 10 未找到data-autolog属性，跳过
2025-07-27 23:04:47,938 - INFO - 找到商品项 11，查找tfx-item元素...
2025-07-27 23:04:47,943 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:04:47,948 - INFO - 商品项 11 outerHTML长度: 155
2025-07-27 23:04:47,948 - INFO - 商品项 11 outerHTML中不包含data-autolog
2025-07-27 23:04:47,949 - INFO - 商品项 11 未找到data-autolog属性，跳过
2025-07-27 23:04:47,959 - INFO - 找到商品项 12，查找tfx-item元素...
2025-07-27 23:04:47,963 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:04:47,969 - INFO - 商品项 12 outerHTML长度: 161
2025-07-27 23:04:47,969 - INFO - 商品项 12 outerHTML中不包含data-autolog
2025-07-27 23:04:47,969 - INFO - 商品项 12 未找到data-autolog属性，跳过
2025-07-27 23:04:47,979 - INFO - 找到商品项 13，查找tfx-item元素...
2025-07-27 23:04:47,985 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:04:47,990 - INFO - 商品项 13 outerHTML长度: 161
2025-07-27 23:04:47,990 - INFO - 商品项 13 outerHTML中不包含data-autolog
2025-07-27 23:04:47,990 - INFO - 商品项 13 未找到data-autolog属性，跳过
2025-07-27 23:04:48,013 - INFO - 找到商品项 14，查找tfx-item元素...
2025-07-27 23:04:48,017 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:04:48,024 - INFO - 商品项 14 outerHTML长度: 161
2025-07-27 23:04:48,024 - INFO - 商品项 14 outerHTML中不包含data-autolog
2025-07-27 23:04:48,024 - INFO - 商品项 14 未找到data-autolog属性，跳过
2025-07-27 23:04:48,035 - INFO - 找到商品项 15，查找tfx-item元素...
2025-07-27 23:04:48,039 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:04:48,043 - INFO - 商品项 15 outerHTML长度: 161
2025-07-27 23:04:48,044 - INFO - 商品项 15 outerHTML中不包含data-autolog
2025-07-27 23:04:48,044 - INFO - 商品项 15 未找到data-autolog属性，跳过
2025-07-27 23:04:48,055 - INFO - 找到商品项 16，查找tfx-item元素...
2025-07-27 23:04:48,059 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:04:48,066 - INFO - 商品项 16 outerHTML长度: 161
2025-07-27 23:04:48,067 - INFO - 商品项 16 outerHTML中不包含data-autolog
2025-07-27 23:04:48,067 - INFO - 商品项 16 未找到data-autolog属性，跳过
2025-07-27 23:04:48,077 - INFO - 找到商品项 17，查找tfx-item元素...
2025-07-27 23:04:48,081 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:04:48,086 - INFO - 商品项 17 outerHTML长度: 161
2025-07-27 23:04:48,087 - INFO - 商品项 17 outerHTML中不包含data-autolog
2025-07-27 23:04:48,087 - INFO - 商品项 17 未找到data-autolog属性，跳过
2025-07-27 23:04:48,097 - INFO - 找到商品项 18，查找tfx-item元素...
2025-07-27 23:04:48,102 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:04:48,107 - INFO - 商品项 18 outerHTML长度: 161
2025-07-27 23:04:48,107 - INFO - 商品项 18 outerHTML中不包含data-autolog
2025-07-27 23:04:48,108 - INFO - 商品项 18 未找到data-autolog属性，跳过
2025-07-27 23:04:48,118 - INFO - 找到商品项 19，查找tfx-item元素...
2025-07-27 23:04:48,122 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:04:48,128 - INFO - 商品项 19 outerHTML长度: 161
2025-07-27 23:04:48,129 - INFO - 商品项 19 outerHTML中不包含data-autolog
2025-07-27 23:04:48,129 - INFO - 商品项 19 未找到data-autolog属性，跳过
2025-07-27 23:04:48,138 - INFO - 找到商品项 20，查找tfx-item元素...
2025-07-27 23:04:48,143 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:04:48,148 - INFO - 商品项 20 outerHTML长度: 161
2025-07-27 23:04:48,149 - INFO - 商品项 20 outerHTML中不包含data-autolog
2025-07-27 23:04:48,149 - INFO - 商品项 20 未找到data-autolog属性，跳过
2025-07-27 23:04:48,185 - INFO - 找到商品项 21，查找tfx-item元素...
2025-07-27 23:04:48,189 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:04:48,195 - INFO - 商品项 21 outerHTML长度: 161
2025-07-27 23:04:48,195 - INFO - 商品项 21 outerHTML中不包含data-autolog
2025-07-27 23:04:48,195 - INFO - 商品项 21 未找到data-autolog属性，跳过
2025-07-27 23:04:48,204 - INFO - 找到商品项 22，查找tfx-item元素...
2025-07-27 23:04:48,209 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:04:48,215 - INFO - 商品项 22 outerHTML长度: 161
2025-07-27 23:04:48,215 - INFO - 商品项 22 outerHTML中不包含data-autolog
2025-07-27 23:04:48,215 - INFO - 商品项 22 未找到data-autolog属性，跳过
2025-07-27 23:04:48,226 - INFO - 找到商品项 23，查找tfx-item元素...
2025-07-27 23:04:48,229 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:04:48,236 - INFO - 商品项 23 outerHTML长度: 161
2025-07-27 23:04:48,236 - INFO - 商品项 23 outerHTML中不包含data-autolog
2025-07-27 23:04:48,236 - INFO - 商品项 23 未找到data-autolog属性，跳过
2025-07-27 23:04:48,246 - INFO - 找到商品项 24，查找tfx-item元素...
2025-07-27 23:04:48,251 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:04:48,257 - INFO - 商品项 24 outerHTML长度: 161
2025-07-27 23:04:48,257 - INFO - 商品项 24 outerHTML中不包含data-autolog
2025-07-27 23:04:48,257 - INFO - 商品项 24 未找到data-autolog属性，跳过
2025-07-27 23:04:48,293 - INFO - 找到商品项 25，查找tfx-item元素...
2025-07-27 23:04:48,297 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:04:48,305 - INFO - 商品项 25 outerHTML长度: 155
2025-07-27 23:04:48,306 - INFO - 商品项 25 outerHTML中不包含data-autolog
2025-07-27 23:04:48,306 - INFO - 商品项 25 未找到data-autolog属性，跳过
2025-07-27 23:04:48,317 - INFO - 找到商品项 26，查找tfx-item元素...
2025-07-27 23:04:48,320 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:04:48,325 - INFO - 商品项 26 outerHTML长度: 161
2025-07-27 23:04:48,325 - INFO - 商品项 26 outerHTML中不包含data-autolog
2025-07-27 23:04:48,325 - INFO - 商品项 26 未找到data-autolog属性，跳过
2025-07-27 23:04:48,336 - INFO - 找到商品项 27，查找tfx-item元素...
2025-07-27 23:04:48,342 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:04:48,348 - INFO - 商品项 27 outerHTML长度: 161
2025-07-27 23:04:48,348 - INFO - 商品项 27 outerHTML中不包含data-autolog
2025-07-27 23:04:48,348 - INFO - 商品项 27 未找到data-autolog属性，跳过
2025-07-27 23:04:48,358 - INFO - 找到商品项 28，查找tfx-item元素...
2025-07-27 23:04:48,363 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:04:48,369 - INFO - 商品项 28 outerHTML长度: 155
2025-07-27 23:04:48,369 - INFO - 商品项 28 outerHTML中不包含data-autolog
2025-07-27 23:04:48,369 - INFO - 商品项 28 未找到data-autolog属性，跳过
2025-07-27 23:04:48,378 - INFO - 找到商品项 29，查找tfx-item元素...
2025-07-27 23:04:48,383 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:04:48,388 - INFO - 商品项 29 outerHTML长度: 161
2025-07-27 23:04:48,388 - INFO - 商品项 29 outerHTML中不包含data-autolog
2025-07-27 23:04:48,388 - INFO - 商品项 29 未找到data-autolog属性，跳过
2025-07-27 23:04:48,399 - INFO - 找到商品项 30，查找tfx-item元素...
2025-07-27 23:04:48,403 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:04:48,409 - INFO - 商品项 30 outerHTML长度: 161
2025-07-27 23:04:48,409 - INFO - 商品项 30 outerHTML中不包含data-autolog
2025-07-27 23:04:48,409 - INFO - 商品项 30 未找到data-autolog属性，跳过
2025-07-27 23:04:48,419 - INFO - 找到商品项 31，查找tfx-item元素...
2025-07-27 23:04:48,424 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:04:48,429 - INFO - 商品项 31 outerHTML长度: 161
2025-07-27 23:04:48,429 - INFO - 商品项 31 outerHTML中不包含data-autolog
2025-07-27 23:04:48,429 - INFO - 商品项 31 未找到data-autolog属性，跳过
2025-07-27 23:04:48,439 - INFO - 找到商品项 32，查找tfx-item元素...
2025-07-27 23:04:48,444 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:04:48,449 - INFO - 商品项 32 outerHTML长度: 161
2025-07-27 23:04:48,451 - INFO - 商品项 32 outerHTML中不包含data-autolog
2025-07-27 23:04:48,451 - INFO - 商品项 32 未找到data-autolog属性，跳过
2025-07-27 23:04:48,461 - INFO - 找到商品项 33，查找tfx-item元素...
2025-07-27 23:04:48,465 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:04:48,470 - INFO - 商品项 33 outerHTML长度: 161
2025-07-27 23:04:48,471 - INFO - 商品项 33 outerHTML中不包含data-autolog
2025-07-27 23:04:48,471 - INFO - 商品项 33 未找到data-autolog属性，跳过
2025-07-27 23:04:48,497 - INFO - 找到商品项 34，查找tfx-item元素...
2025-07-27 23:04:48,521 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:04:48,527 - INFO - 商品项 34 outerHTML长度: 155
2025-07-27 23:04:48,527 - INFO - 商品项 34 outerHTML中不包含data-autolog
2025-07-27 23:04:48,527 - INFO - 商品项 34 未找到data-autolog属性，跳过
2025-07-27 23:04:48,537 - INFO - 找到商品项 35，查找tfx-item元素...
2025-07-27 23:04:48,542 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:04:48,548 - INFO - 商品项 35 outerHTML长度: 161
2025-07-27 23:04:48,548 - INFO - 商品项 35 outerHTML中不包含data-autolog
2025-07-27 23:04:48,548 - INFO - 商品项 35 未找到data-autolog属性，跳过
2025-07-27 23:04:48,559 - INFO - 找到商品项 36，查找tfx-item元素...
2025-07-27 23:04:48,563 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:04:48,570 - INFO - 商品项 36 outerHTML长度: 161
2025-07-27 23:04:48,570 - INFO - 商品项 36 outerHTML中不包含data-autolog
2025-07-27 23:04:48,570 - INFO - 商品项 36 未找到data-autolog属性，跳过
2025-07-27 23:04:48,581 - INFO - 找到商品项 37，查找tfx-item元素...
2025-07-27 23:04:48,586 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:04:48,590 - INFO - 商品项 37 outerHTML长度: 161
2025-07-27 23:04:48,590 - INFO - 商品项 37 outerHTML中不包含data-autolog
2025-07-27 23:04:48,591 - INFO - 商品项 37 未找到data-autolog属性，跳过
2025-07-27 23:04:48,601 - INFO - 找到商品项 38，查找tfx-item元素...
2025-07-27 23:04:48,605 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:04:48,612 - INFO - 商品项 38 outerHTML长度: 161
2025-07-27 23:04:48,612 - INFO - 商品项 38 outerHTML中不包含data-autolog
2025-07-27 23:04:48,612 - INFO - 商品项 38 未找到data-autolog属性，跳过
2025-07-27 23:04:48,624 - INFO - 找到商品项 39，查找tfx-item元素...
2025-07-27 23:04:48,628 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:04:48,634 - INFO - 商品项 39 outerHTML长度: 161
2025-07-27 23:04:48,634 - INFO - 商品项 39 outerHTML中不包含data-autolog
2025-07-27 23:04:48,634 - INFO - 商品项 39 未找到data-autolog属性，跳过
2025-07-27 23:04:48,646 - INFO - 找到商品项 40，查找tfx-item元素...
2025-07-27 23:04:48,651 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:04:48,657 - INFO - 商品项 40 outerHTML长度: 161
2025-07-27 23:04:48,657 - INFO - 商品项 40 outerHTML中不包含data-autolog
2025-07-27 23:04:48,657 - INFO - 商品项 40 未找到data-autolog属性，跳过
2025-07-27 23:04:48,657 - INFO - 当前页面爬取完成，找到 0 个商品
2025-07-27 23:04:48,657 - INFO - 第 1 页未找到商品，停止爬取
2025-07-27 23:04:48,657 - INFO - 爬取完成，共找到 0 个商品
2025-07-27 23:04:48,658 - ERROR - 未能获取到商品信息，停止流程
