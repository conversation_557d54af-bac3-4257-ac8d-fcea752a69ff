2025-07-27 22:51:13,294 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169cda]
	(No symbol) [0x0x7ff7cd171679]
	(No symbol) [0x0x7ff7cd174a61]
	(No symbol) [0x0x7ff7cd211e4b]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:51:13,295 - INFO - 防检测机制设置完成
2025-07-27 22:51:13,295 - INFO - 浏览器驱动初始化成功
2025-07-27 22:51:13,300 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:51:13,304 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:51:13,308 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:51:13,310 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:51:13,316 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:51:13,327 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:51:13,332 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:51:13,340 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:51:13,346 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:51:13,350 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:51:13,354 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:51:13,357 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:51:13,360 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:51:13,362 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:51:13,364 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:51:13,369 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:51:13,371 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:51:13,374 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:51:13,376 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:51:13,380 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:51:13,380 - INFO - Cookies已加载
2025-07-27 22:51:13,380 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?channel=global_zx&spm=a21ug5.29159167.6452766900.2ndview_zxmore_click
2025-07-27 22:51:18,282 - INFO - 当前User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-27 22:51:18,282 - INFO - 确认：使用桌面版User-Agent
2025-07-27 22:51:18,287 - INFO - 桌面版User-Agent设置完成: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-27 22:51:18,291 - INFO - 成功导航到目标页面
2025-07-27 22:51:18,292 - INFO - 登录成功！现在可以开始铺货任务
2025-07-27 22:51:21,942 - INFO - Cookies已保存
2025-07-27 22:51:24,233 - INFO - 浏览器已关闭
2025-07-27 22:51:25,663 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169cda]
	(No symbol) [0x0x7ff7cd171679]
	(No symbol) [0x0x7ff7cd174a61]
	(No symbol) [0x0x7ff7cd211e4b]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:51:25,663 - INFO - 防检测机制设置完成
2025-07-27 22:51:25,663 - INFO - 浏览器驱动初始化成功
2025-07-27 22:51:25,663 - INFO - 开始自动化铺货流程（有窗口模式）
2025-07-27 22:51:25,663 - INFO - 第一步：正在导航到目标页面...
2025-07-27 22:51:30,116 - INFO - 第一步：点击登录按钮
2025-07-27 22:51:30,151 - INFO - 找到登录按钮，使用选择器: //*[@id='login-form']/div[6]/button
2025-07-27 22:51:31,200 - INFO - 准备点击登录按钮: 标签=button, 文本='登录', 选择器=//*[@id='login-form']/div[6]/button
2025-07-27 22:51:31,214 - INFO - 准备点击元素: tag=button, text='登录', class='fm-button fm-submit password-l', id=''
2025-07-27 22:51:32,597 - INFO - 尝试ActionChains点击...
2025-07-27 22:51:32,989 - INFO - ActionChains点击成功
2025-07-27 22:51:33,370 - INFO - 登录按钮点击成功
2025-07-27 22:51:33,370 - INFO - 登录按钮点击成功
2025-07-27 22:51:36,371 - INFO - 第二步：获取账号信息...
2025-07-27 22:51:36,371 - INFO - 正在跳转到个人中心页面: https://jingya.taobao.com/?v=2
2025-07-27 22:52:01,135 - INFO - 尝试获取账号名称，使用XPath: //*[@id='workbench-user-tool-btn']/div/div[2]
2025-07-27 22:52:01,147 - INFO - 成功获取账号信息: tb997603987291:书生
2025-07-27 22:52:01,149 - INFO - 获取到账号名称: tb997603987291:书生
2025-07-27 22:52:01,150 - INFO - 第三步：开始爬取商品信息...
2025-07-27 22:52:01,150 - INFO - GUI界面已更新账号显示: tb997603987291:书生
2025-07-27 22:52:01,150 - INFO - 开始爬取商品信息...
2025-07-27 22:52:04,229 - INFO - 正在爬取第 1 页商品信息...
2025-07-27 22:52:06,268 - INFO - 找到商品容器，开始爬取商品...
2025-07-27 22:52:06,280 - INFO - 找到商品项 1，查找tfx-item元素...
2025-07-27 22:52:06,286 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:52:06,292 - INFO - 商品项 1 未找到data-autolog属性，跳过
2025-07-27 22:52:06,321 - INFO - 找到商品项 2，查找tfx-item元素...
2025-07-27 22:52:06,325 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:52:06,329 - INFO - 商品项 2 未找到data-autolog属性，跳过
2025-07-27 22:52:06,341 - INFO - 找到商品项 3，查找tfx-item元素...
2025-07-27 22:52:06,346 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:52:06,350 - INFO - 商品项 3 未找到data-autolog属性，跳过
2025-07-27 22:52:06,361 - INFO - 找到商品项 4，查找tfx-item元素...
2025-07-27 22:52:06,365 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:52:06,369 - INFO - 商品项 4 未找到data-autolog属性，跳过
2025-07-27 22:52:06,380 - INFO - 找到商品项 5，查找tfx-item元素...
2025-07-27 22:52:06,385 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:52:06,387 - INFO - 商品项 5 未找到data-autolog属性，跳过
2025-07-27 22:52:06,399 - INFO - 找到商品项 6，查找tfx-item元素...
2025-07-27 22:52:06,403 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:52:06,407 - INFO - 商品项 6 未找到data-autolog属性，跳过
2025-07-27 22:52:06,437 - INFO - 找到商品项 7，查找tfx-item元素...
2025-07-27 22:52:06,442 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:52:06,444 - INFO - 商品项 7 未找到data-autolog属性，跳过
2025-07-27 22:52:06,454 - INFO - 找到商品项 8，查找tfx-item元素...
2025-07-27 22:52:06,458 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:52:06,460 - INFO - 商品项 8 未找到data-autolog属性，跳过
2025-07-27 22:52:06,472 - INFO - 找到商品项 9，查找tfx-item元素...
2025-07-27 22:52:06,545 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:52:06,549 - INFO - 商品项 9 未找到data-autolog属性，跳过
2025-07-27 22:52:06,570 - INFO - 找到商品项 10，查找tfx-item元素...
2025-07-27 22:52:06,576 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:52:06,579 - INFO - 商品项 10 未找到data-autolog属性，跳过
2025-07-27 22:52:06,589 - INFO - 找到商品项 11，查找tfx-item元素...
2025-07-27 22:52:06,594 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:52:06,599 - INFO - 商品项 11 未找到data-autolog属性，跳过
2025-07-27 22:52:06,609 - INFO - 找到商品项 12，查找tfx-item元素...
2025-07-27 22:52:06,615 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:52:06,619 - INFO - 商品项 12 未找到data-autolog属性，跳过
2025-07-27 22:52:06,630 - INFO - 找到商品项 13，查找tfx-item元素...
2025-07-27 22:52:06,635 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:52:06,639 - INFO - 商品项 13 未找到data-autolog属性，跳过
2025-07-27 22:52:06,649 - INFO - 找到商品项 14，查找tfx-item元素...
2025-07-27 22:52:06,653 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:52:06,657 - INFO - 商品项 14 未找到data-autolog属性，跳过
2025-07-27 22:52:06,667 - INFO - 找到商品项 15，查找tfx-item元素...
2025-07-27 22:52:06,672 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:52:06,677 - INFO - 商品项 15 未找到data-autolog属性，跳过
2025-07-27 22:52:06,688 - INFO - 找到商品项 16，查找tfx-item元素...
2025-07-27 22:52:06,693 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:52:06,696 - INFO - 商品项 16 未找到data-autolog属性，跳过
2025-07-27 22:52:06,708 - INFO - 找到商品项 17，查找tfx-item元素...
2025-07-27 22:52:06,712 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:52:06,714 - INFO - 商品项 17 未找到data-autolog属性，跳过
2025-07-27 22:52:06,725 - INFO - 找到商品项 18，查找tfx-item元素...
2025-07-27 22:52:06,733 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:52:06,736 - INFO - 商品项 18 未找到data-autolog属性，跳过
2025-07-27 22:52:06,746 - INFO - 找到商品项 19，查找tfx-item元素...
2025-07-27 22:52:06,750 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:52:06,753 - INFO - 商品项 19 未找到data-autolog属性，跳过
2025-07-27 22:52:06,779 - INFO - 找到商品项 20，查找tfx-item元素...
2025-07-27 22:52:06,784 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:52:06,787 - INFO - 商品项 20 未找到data-autolog属性，跳过
2025-07-27 22:52:06,814 - INFO - 找到商品项 21，查找tfx-item元素...
2025-07-27 22:52:06,819 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:52:06,822 - INFO - 商品项 21 未找到data-autolog属性，跳过
2025-07-27 22:52:06,833 - INFO - 找到商品项 22，查找tfx-item元素...
2025-07-27 22:52:06,838 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:52:06,841 - INFO - 商品项 22 未找到data-autolog属性，跳过
2025-07-27 22:52:06,852 - INFO - 找到商品项 23，查找tfx-item元素...
2025-07-27 22:52:06,856 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:52:06,859 - INFO - 商品项 23 未找到data-autolog属性，跳过
2025-07-27 22:52:06,870 - INFO - 找到商品项 24，查找tfx-item元素...
2025-07-27 22:52:06,874 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:52:06,879 - INFO - 商品项 24 未找到data-autolog属性，跳过
2025-07-27 22:52:06,889 - INFO - 找到商品项 25，查找tfx-item元素...
2025-07-27 22:52:06,894 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:52:06,896 - INFO - 商品项 25 未找到data-autolog属性，跳过
2025-07-27 22:52:06,908 - INFO - 找到商品项 26，查找tfx-item元素...
2025-07-27 22:52:06,912 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:52:06,915 - INFO - 商品项 26 未找到data-autolog属性，跳过
2025-07-27 22:52:06,925 - INFO - 找到商品项 27，查找tfx-item元素...
2025-07-27 22:52:06,930 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:52:06,933 - INFO - 商品项 27 未找到data-autolog属性，跳过
2025-07-27 22:52:06,944 - INFO - 找到商品项 28，查找tfx-item元素...
2025-07-27 22:52:06,948 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:52:06,951 - INFO - 商品项 28 未找到data-autolog属性，跳过
2025-07-27 22:52:06,977 - INFO - 找到商品项 29，查找tfx-item元素...
2025-07-27 22:52:06,981 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:52:06,985 - INFO - 商品项 29 未找到data-autolog属性，跳过
2025-07-27 22:52:06,995 - INFO - 找到商品项 30，查找tfx-item元素...
2025-07-27 22:52:07,001 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:52:07,003 - INFO - 商品项 30 未找到data-autolog属性，跳过
2025-07-27 22:52:07,014 - INFO - 找到商品项 31，查找tfx-item元素...
2025-07-27 22:52:07,019 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:52:07,024 - INFO - 商品项 31 未找到data-autolog属性，跳过
2025-07-27 22:52:07,035 - INFO - 找到商品项 32，查找tfx-item元素...
2025-07-27 22:52:07,040 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:52:07,042 - INFO - 商品项 32 未找到data-autolog属性，跳过
2025-07-27 22:52:07,053 - INFO - 找到商品项 33，查找tfx-item元素...
2025-07-27 22:52:07,057 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:52:07,061 - INFO - 商品项 33 未找到data-autolog属性，跳过
2025-07-27 22:52:07,073 - INFO - 找到商品项 34，查找tfx-item元素...
2025-07-27 22:52:07,078 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:52:07,081 - INFO - 商品项 34 未找到data-autolog属性，跳过
2025-07-27 22:52:07,091 - INFO - 找到商品项 35，查找tfx-item元素...
2025-07-27 22:52:07,095 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:52:07,098 - INFO - 商品项 35 未找到data-autolog属性，跳过
2025-07-27 22:52:07,109 - INFO - 找到商品项 36，查找tfx-item元素...
2025-07-27 22:52:07,113 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:52:07,115 - INFO - 商品项 36 未找到data-autolog属性，跳过
2025-07-27 22:52:07,126 - INFO - 找到商品项 37，查找tfx-item元素...
2025-07-27 22:52:07,131 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:52:07,134 - INFO - 商品项 37 未找到data-autolog属性，跳过
2025-07-27 22:52:07,145 - INFO - 找到商品项 38，查找tfx-item元素...
2025-07-27 22:52:07,150 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:52:07,153 - INFO - 商品项 38 未找到data-autolog属性，跳过
2025-07-27 22:52:07,179 - INFO - 找到商品项 39，查找tfx-item元素...
2025-07-27 22:52:07,183 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:52:07,187 - INFO - 商品项 39 未找到data-autolog属性，跳过
2025-07-27 22:52:07,196 - INFO - 找到商品项 40，查找tfx-item元素...
2025-07-27 22:52:07,201 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 22:52:07,204 - INFO - 商品项 40 未找到data-autolog属性，跳过
2025-07-27 22:52:07,205 - INFO - 当前页面爬取完成，找到 0 个商品
2025-07-27 22:52:07,205 - INFO - 第 1 页未找到商品，停止爬取
2025-07-27 22:52:07,205 - INFO - 爬取完成，共找到 0 个商品
2025-07-27 22:52:07,205 - ERROR - 未能获取到商品信息，停止流程
