2025-07-26 20:53:09,717 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9cda]
	(No symbol) [0x0x7ff7243d1679]
	(No symbol) [0x0x7ff7243d4a61]
	(No symbol) [0x0x7ff724471e4b]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:53:09,718 - INFO - 防检测机制设置完成
2025-07-26 20:53:09,718 - INFO - 浏览器驱动初始化成功
2025-07-26 20:53:09,727 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9b0c]
	(No symbol) [0x0x7ff724485b46]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:53:09,734 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9b0c]
	(No symbol) [0x0x7ff724485b46]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:53:09,738 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9b0c]
	(No symbol) [0x0x7ff724485b46]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:53:09,747 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9b0c]
	(No symbol) [0x0x7ff724485b46]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:53:09,752 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9b0c]
	(No symbol) [0x0x7ff724485b46]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:53:09,756 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9b0c]
	(No symbol) [0x0x7ff724485b46]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:53:09,761 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9b0c]
	(No symbol) [0x0x7ff724485b46]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:53:09,764 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9b0c]
	(No symbol) [0x0x7ff724485b46]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:53:09,767 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9b0c]
	(No symbol) [0x0x7ff724485b46]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:53:09,775 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9b0c]
	(No symbol) [0x0x7ff724485b46]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:53:09,779 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9b0c]
	(No symbol) [0x0x7ff724485b46]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:53:09,784 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9b0c]
	(No symbol) [0x0x7ff724485b46]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:53:09,788 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9b0c]
	(No symbol) [0x0x7ff724485b46]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:53:09,791 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9b0c]
	(No symbol) [0x0x7ff724485b46]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:53:09,794 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9b0c]
	(No symbol) [0x0x7ff724485b46]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:53:09,801 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9b0c]
	(No symbol) [0x0x7ff724485b46]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:53:09,805 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9b0c]
	(No symbol) [0x0x7ff724485b46]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:53:09,812 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9b0c]
	(No symbol) [0x0x7ff724485b46]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:53:09,816 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9b0c]
	(No symbol) [0x0x7ff724485b46]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:53:09,820 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9b0c]
	(No symbol) [0x0x7ff724485b46]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:53:09,821 - INFO - Cookies已加载
2025-07-26 20:53:09,822 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?channel=global_zx&spm=a21ug5.29159167.6452766900.2ndview_zxmore_click
2025-07-26 20:53:13,827 - INFO - 检测到登录页，等待3秒后自动点击登录按钮
2025-07-26 20:53:16,887 - INFO - 找到登录按钮，使用选择器: //form//button[last()]
2025-07-26 20:53:17,920 - INFO - 准备点击元素: tag=button, text='重置', class='next-btn next-medium next-btn-', id=''
2025-07-26 20:53:19,311 - INFO - 尝试ActionChains点击...
2025-07-26 20:53:19,616 - INFO - ActionChains点击成功
2025-07-26 20:53:19,964 - INFO - 已自动点击登录按钮
2025-07-26 20:53:21,967 - INFO - 当前User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-26 20:53:21,968 - INFO - 确认：使用桌面版User-Agent
2025-07-26 20:53:21,972 - INFO - 桌面版User-Agent设置完成: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-26 20:53:21,975 - INFO - 成功导航到目标页面
2025-07-26 20:53:21,976 - INFO - 登录成功！现在可以开始铺货任务
2025-07-26 20:53:24,980 - INFO - 开始测试仓库移除功能...
2025-07-26 20:53:24,980 - INFO - 开始执行批量管理操作...
2025-07-26 20:53:24,980 - INFO - 正在导航到管理页面: https://myseller.taobao.com/home.htm/SellManage/in_stock?current=1&pageSize=20
2025-07-26 20:53:26,212 - INFO - 页面加载完成
2025-07-26 20:53:27,538 - INFO - 准备点击元素: tag=span, text='', class='next-checkbox-inner', id=''
2025-07-26 20:53:28,929 - INFO - 尝试ActionChains点击...
2025-07-26 20:53:29,254 - INFO - ActionChains点击成功
2025-07-26 20:53:29,557 - INFO - 已点击全选复选框
2025-07-26 20:53:29,557 - INFO - 等待 2.0 秒
2025-07-26 20:53:31,553 - INFO - 准备点击元素: tag=button, text='批量删除', class='next-btn next-medium next-btn-', id=''
2025-07-26 20:53:32,662 - INFO - 尝试ActionChains点击...
2025-07-26 20:53:32,927 - INFO - ActionChains点击成功
2025-07-26 20:53:33,354 - INFO - 已点击批量编辑按钮
2025-07-26 20:53:33,354 - INFO - 等待 1.5 秒
2025-07-26 20:53:34,887 - INFO - 准备点击元素: tag=div, text='批量删除
是否确认删除？删除宝贝后可到‘回收站’查看并恢复被删除的宝贝，但删除后需要24小时后才能操作恢复，非必要请不要删除
确定', class='next-dialog next-closeable nex', id=''
2025-07-26 20:53:36,375 - INFO - 尝试ActionChains点击...
2025-07-26 20:53:36,645 - INFO - ActionChains点击成功
2025-07-26 20:53:36,878 - INFO - 已关闭弹窗: //div[contains(@class, 'close')]
2025-07-26 20:53:36,908 - INFO - 准备点击元素: tag=span, text='确定', class='next-btn-helper', id=''
2025-07-26 20:53:38,352 - INFO - 尝试ActionChains点击...
2025-07-26 20:53:38,633 - INFO - ActionChains点击成功
2025-07-26 20:53:38,841 - INFO - 批量管理操作完成
2025-07-26 20:53:41,842 - INFO - 仓库移除测试成功完成！
2025-07-26 20:53:59,548 - ERROR - 保存cookies失败: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9cda]
	(No symbol) [0x0x7ff7243b5f35]
	(No symbol) [0x0x7ff7243daabe]
	(No symbol) [0x0x7ff72444feb5]
	(No symbol) [0x0x7ff724470432]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:54:01,573 - INFO - 浏览器已关闭
2025-07-26 20:54:01,574 - INFO - 以无界面(headless)模式启动Chrome，仅添加headless、no-sandbox、disable-dev-shm-usage参数
2025-07-26 20:54:02,903 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff72460e925+77845]
	GetHandleVerifier [0x0x7ff72460e980+77936]
	(No symbol) [0x0x7ff7243c9cda]
	(No symbol) [0x0x7ff7243d1679]
	(No symbol) [0x0x7ff7243d4a61]
	(No symbol) [0x0x7ff724471e4b]
	(No symbol) [0x0x7ff7244488ca]
	(No symbol) [0x0x7ff724470b07]
	(No symbol) [0x0x7ff7244486a3]
	(No symbol) [0x0x7ff724411791]
	(No symbol) [0x0x7ff724412523]
	GetHandleVerifier [0x0x7ff7248e683d+3059501]
	GetHandleVerifier [0x0x7ff7248e0bfd+3035885]
	GetHandleVerifier [0x0x7ff7249003f0+3164896]
	GetHandleVerifier [0x0x7ff724628c2e+185118]
	GetHandleVerifier [0x0x7ff72463053f+216111]
	GetHandleVerifier [0x0x7ff7246172d4+113092]
	GetHandleVerifier [0x0x7ff724617489+113529]
	GetHandleVerifier [0x0x7ff7245fe288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 20:54:02,904 - INFO - 防检测机制设置完成
2025-07-26 20:54:02,904 - INFO - 浏览器驱动初始化成功
2025-07-26 20:54:02,904 - INFO - 开始自动化铺货流程（无头模式）
2025-07-26 20:54:02,904 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?channel=global_zx&spm=a21ug5.29159167.6452766900.2ndview_zxmore_click
2025-07-26 20:54:06,730 - INFO - 执行第一个操作：点击登录按钮
2025-07-26 20:54:06,751 - INFO - 找到登录按钮，使用选择器: //*[@id='login-form']/div[6]/button
2025-07-26 20:54:07,774 - INFO - 准备点击登录按钮: 标签=button, 文本='登录', 选择器=//*[@id='login-form']/div[6]/button
2025-07-26 20:54:07,791 - INFO - 准备点击元素: tag=button, text='登录', class='fm-button fm-submit password-l', id=''
2025-07-26 20:54:09,002 - INFO - 尝试ActionChains点击...
2025-07-26 20:54:09,405 - INFO - ActionChains点击成功
2025-07-26 20:54:09,606 - INFO - 登录按钮点击成功
2025-07-26 20:54:09,606 - INFO - 登录按钮点击成功
2025-07-26 20:54:12,611 - INFO - 当前页面URL: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?channel=global_zx&spm=a21ug5.29159167.6452766900.2ndview_zxmore_click
2025-07-26 20:54:12,611 - INFO - 正在导航到主页面...
2025-07-26 20:54:16,795 - INFO - 已返回主页面
2025-07-26 20:54:20,894 - INFO - 找到 40 个直接子div
2025-07-26 20:54:20,916 - INFO - 商品 1: 位置={'x': 316, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=681778175573&path=search_goods_card&type=s
2025-07-26 20:54:20,934 - INFO - 商品 2: 位置={'x': 553, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=690841537159&path=search_goods_card&index=
2025-07-26 20:54:20,951 - INFO - 商品 3: 位置={'x': 790, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=789447097664&path=search_goods_card&index=
2025-07-26 20:54:20,968 - INFO - 商品 4: 位置={'x': 1028, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=694682843417&path=search_goods_card&index=
2025-07-26 20:54:20,988 - INFO - 商品 5: 位置={'x': 1265, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=681082809892&path=search_goods_card&index=
2025-07-26 20:54:21,007 - INFO - 商品 6: 位置={'x': 316, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=694675776881&path=search_goods_card&index=
2025-07-26 20:54:21,024 - INFO - 商品 7: 位置={'x': 553, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=680729304287&path=search_goods_card&index=
2025-07-26 20:54:21,042 - INFO - 商品 8: 位置={'x': 790, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=710355107054&path=search_goods_card&index=
2025-07-26 20:54:21,058 - INFO - 商品 9: 位置={'x': 1028, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=740463658412&path=search_goods_card&index=
2025-07-26 20:54:21,075 - INFO - 商品 10: 位置={'x': 1265, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=821067902127&path=search_goods_card&index=
2025-07-26 20:54:21,093 - INFO - 商品 11: 位置={'x': 316, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=789835300215&path=search_goods_card&index=
2025-07-26 20:54:21,112 - INFO - 商品 12: 位置={'x': 553, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=843747605382&path=search_goods_card&index=
2025-07-26 20:54:21,129 - INFO - 商品 13: 位置={'x': 790, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=675816539149&path=search_goods_card&index=
2025-07-26 20:54:21,145 - INFO - 商品 14: 位置={'x': 1028, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=846634805775&path=search_goods_card&index=
2025-07-26 20:54:21,164 - INFO - 商品 15: 位置={'x': 1265, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=852510575114&path=search_goods_card&index=
2025-07-26 20:54:21,179 - INFO - 商品 16: 位置={'x': 316, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=743442966826&path=search_goods_card&index=
2025-07-26 20:54:21,195 - INFO - 商品 17: 位置={'x': 553, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=848934676698&path=search_goods_card&index=
2025-07-26 20:54:21,211 - INFO - 商品 18: 位置={'x': 790, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=693758717931&path=search_goods_card&index=
2025-07-26 20:54:21,228 - INFO - 商品 19: 位置={'x': 1028, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=691142128229&path=search_goods_card&index=
2025-07-26 20:54:21,245 - INFO - 商品 20: 位置={'x': 1265, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=690739721578&path=search_goods_card&index=
2025-07-26 20:54:21,261 - INFO - 商品 21: 位置={'x': 316, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=691918824108&path=search_goods_card&index=
2025-07-26 20:54:21,277 - INFO - 商品 22: 位置={'x': 553, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=875045022715&path=search_goods_card&index=
2025-07-26 20:54:21,294 - INFO - 商品 23: 位置={'x': 790, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=873911671570&path=search_goods_card&index=
2025-07-26 20:54:21,312 - INFO - 商品 24: 位置={'x': 1028, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=722937163980&path=search_goods_card&index=
2025-07-26 20:54:21,329 - INFO - 商品 25: 位置={'x': 1265, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=739090443937&path=search_goods_card&index=
2025-07-26 20:54:21,348 - INFO - 商品 26: 位置={'x': 316, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=777862601010&path=search_goods_card&index=
2025-07-26 20:54:21,365 - INFO - 商品 27: 位置={'x': 553, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=686870174450&path=search_goods_card&index=
2025-07-26 20:54:21,381 - INFO - 商品 28: 位置={'x': 790, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=814542665480&path=search_goods_card&index=
2025-07-26 20:54:21,397 - INFO - 商品 29: 位置={'x': 1028, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=852625718015&path=search_goods_card&index=
2025-07-26 20:54:21,415 - INFO - 商品 30: 位置={'x': 1265, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=681472049289&path=search_goods_card&index=
2025-07-26 20:54:21,433 - INFO - 商品 31: 位置={'x': 316, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=683142617961&path=search_goods_card&index=
2025-07-26 20:54:21,449 - INFO - 商品 32: 位置={'x': 553, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=763659951211&path=search_goods_card&index=
2025-07-26 20:54:21,468 - INFO - 商品 33: 位置={'x': 790, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=675478966678&path=search_goods_card&index=
2025-07-26 20:54:21,485 - INFO - 商品 34: 位置={'x': 1028, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=800365611234&path=search_goods_card&index=
2025-07-26 20:54:21,502 - INFO - 商品 35: 位置={'x': 1265, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=896721954982&path=search_goods_card&index=
2025-07-26 20:54:21,517 - INFO - 商品 36: 位置={'x': 316, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=795389643023&path=search_goods_card&index=
2025-07-26 20:54:21,537 - INFO - 商品 37: 位置={'x': 553, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=689584396900&path=search_goods_card&index=
2025-07-26 20:54:21,555 - INFO - 商品 38: 位置={'x': 790, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=788516102627&path=search_goods_card&index=
2025-07-26 20:54:21,573 - INFO - 商品 39: 位置={'x': 1028, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=825025224622&path=search_goods_card&index=
2025-07-26 20:54:21,591 - INFO - 商品 40: 位置={'x': 1265, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=842638336638&path=search_goods_card&index=
2025-07-26 20:54:21,591 - INFO - 找到 40 个有效的商品容器
2025-07-26 20:54:21,591 - INFO - 找到 40 个商品，将处理 20 个
2025-07-26 20:54:21,592 - INFO - 当前批次大小: 10
2025-07-26 20:54:21,593 - INFO - 开始处理第 1/40 个商品
2025-07-26 20:54:21,604 - INFO - 找到 40 个直接子div
2025-07-26 20:54:21,625 - INFO - 商品 1: 位置={'x': 316, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=681778175573&path=search_goods_card&type=s
2025-07-26 20:54:21,643 - INFO - 商品 2: 位置={'x': 553, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=690841537159&path=search_goods_card&index=
2025-07-26 20:54:21,660 - INFO - 商品 3: 位置={'x': 790, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=789447097664&path=search_goods_card&index=
2025-07-26 20:54:21,677 - INFO - 商品 4: 位置={'x': 1028, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=694682843417&path=search_goods_card&index=
2025-07-26 20:54:21,693 - INFO - 商品 5: 位置={'x': 1265, 'y': 737}, 尺寸={'height': 341, 'width': 225}, ID=item_id=681082809892&path=search_goods_card&index=
2025-07-26 20:54:21,708 - INFO - 商品 6: 位置={'x': 316, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=694675776881&path=search_goods_card&index=
2025-07-26 20:54:21,725 - INFO - 商品 7: 位置={'x': 553, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=680729304287&path=search_goods_card&index=
2025-07-26 20:54:21,741 - INFO - 商品 8: 位置={'x': 790, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=710355107054&path=search_goods_card&index=
2025-07-26 20:54:21,765 - INFO - 商品 9: 位置={'x': 1028, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=740463658412&path=search_goods_card&index=
2025-07-26 20:54:21,793 - INFO - 商品 10: 位置={'x': 1265, 'y': 1090}, 尺寸={'height': 341, 'width': 225}, ID=item_id=821067902127&path=search_goods_card&index=
2025-07-26 20:54:21,819 - INFO - 商品 11: 位置={'x': 316, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=789835300215&path=search_goods_card&index=
2025-07-26 20:54:21,834 - INFO - 商品 12: 位置={'x': 553, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=843747605382&path=search_goods_card&index=
2025-07-26 20:54:21,851 - INFO - 商品 13: 位置={'x': 790, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=675816539149&path=search_goods_card&index=
2025-07-26 20:54:21,867 - INFO - 商品 14: 位置={'x': 1028, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=846634805775&path=search_goods_card&index=
2025-07-26 20:54:21,888 - INFO - 商品 15: 位置={'x': 1265, 'y': 1443}, 尺寸={'height': 341, 'width': 225}, ID=item_id=852510575114&path=search_goods_card&index=
2025-07-26 20:54:21,924 - INFO - 商品 16: 位置={'x': 316, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=743442966826&path=search_goods_card&index=
2025-07-26 20:54:21,943 - INFO - 商品 17: 位置={'x': 553, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=848934676698&path=search_goods_card&index=
2025-07-26 20:54:21,958 - INFO - 商品 18: 位置={'x': 790, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=693758717931&path=search_goods_card&index=
2025-07-26 20:54:21,976 - INFO - 商品 19: 位置={'x': 1028, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=691142128229&path=search_goods_card&index=
2025-07-26 20:54:21,999 - INFO - 商品 20: 位置={'x': 1265, 'y': 1796}, 尺寸={'height': 341, 'width': 225}, ID=item_id=690739721578&path=search_goods_card&index=
2025-07-26 20:54:22,015 - INFO - 商品 21: 位置={'x': 316, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=691918824108&path=search_goods_card&index=
2025-07-26 20:54:22,030 - INFO - 商品 22: 位置={'x': 553, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=875045022715&path=search_goods_card&index=
2025-07-26 20:54:22,046 - INFO - 商品 23: 位置={'x': 790, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=873911671570&path=search_goods_card&index=
2025-07-26 20:54:22,062 - INFO - 商品 24: 位置={'x': 1028, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=722937163980&path=search_goods_card&index=
2025-07-26 20:54:22,077 - INFO - 商品 25: 位置={'x': 1265, 'y': 2149}, 尺寸={'height': 341, 'width': 225}, ID=item_id=739090443937&path=search_goods_card&index=
2025-07-26 20:54:22,104 - INFO - 商品 26: 位置={'x': 316, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=777862601010&path=search_goods_card&index=
2025-07-26 20:54:22,120 - INFO - 商品 27: 位置={'x': 553, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=686870174450&path=search_goods_card&index=
2025-07-26 20:54:22,136 - INFO - 商品 28: 位置={'x': 790, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=814542665480&path=search_goods_card&index=
2025-07-26 20:54:22,152 - INFO - 商品 29: 位置={'x': 1028, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=852625718015&path=search_goods_card&index=
2025-07-26 20:54:22,167 - INFO - 商品 30: 位置={'x': 1265, 'y': 2502}, 尺寸={'height': 341, 'width': 225}, ID=item_id=681472049289&path=search_goods_card&index=
2025-07-26 20:54:22,185 - INFO - 商品 31: 位置={'x': 316, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=683142617961&path=search_goods_card&index=
2025-07-26 20:54:22,203 - INFO - 商品 32: 位置={'x': 553, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=763659951211&path=search_goods_card&index=
2025-07-26 20:54:22,218 - INFO - 商品 33: 位置={'x': 790, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=675478966678&path=search_goods_card&index=
2025-07-26 20:54:22,234 - INFO - 商品 34: 位置={'x': 1028, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=800365611234&path=search_goods_card&index=
2025-07-26 20:54:22,249 - INFO - 商品 35: 位置={'x': 1265, 'y': 2855}, 尺寸={'height': 341, 'width': 225}, ID=item_id=896721954982&path=search_goods_card&index=
2025-07-26 20:54:22,292 - INFO - 商品 36: 位置={'x': 316, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=795389643023&path=search_goods_card&index=
2025-07-26 20:54:22,313 - INFO - 商品 37: 位置={'x': 553, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=689584396900&path=search_goods_card&index=
2025-07-26 20:54:22,330 - INFO - 商品 38: 位置={'x': 790, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=788516102627&path=search_goods_card&index=
2025-07-26 20:54:22,351 - INFO - 商品 39: 位置={'x': 1028, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=825025224622&path=search_goods_card&index=
2025-07-26 20:54:22,369 - INFO - 商品 40: 位置={'x': 1265, 'y': 3208}, 尺寸={'height': 341, 'width': 225}, ID=item_id=842638336638&path=search_goods_card&index=
2025-07-26 20:54:22,369 - INFO - 找到 40 个有效的商品容器
2025-07-26 20:54:22,373 - WARNING - 警告：第 1 个元素可能不是商品项，class=''
2025-07-26 20:54:22,398 - INFO - 商品 1 信息: 文本='保税直供 Dove多芬磨砂膏石榴籽乳木果风味身体磨砂膏298g
采购价¥35.00销量11.2万+件', 位置={'x': 316, 'y': 737}
2025-07-26 20:54:23,918 - INFO - 商品 1 位置: {'x': 316, 'y': 737}, 尺寸: {'height': 347, 'width': 226}
2025-07-26 20:54:23,919 - INFO - 模拟阅读行为，停顿 2.8 秒
2025-07-26 20:54:27,776 - INFO - 点击前标签页数量: 1
2025-07-26 20:54:27,776 - INFO - 点击前窗口句柄: ['64B29A51A768889012EA152CB8404BDF']
2025-07-26 20:54:27,776 - INFO - 尝试策略: ActionChains点击
2025-07-26 20:54:29,876 - INFO - 点击后标签页数量: 2
2025-07-26 20:54:29,876 - INFO - 点击后窗口句柄: ['64B29A51A768889012EA152CB8404BDF', '4BFFB55328A121C38D0B445AB453B69A']
2025-07-26 20:54:29,877 - INFO - ActionChains点击成功，新标签页已打开
2025-07-26 20:54:29,877 - INFO - 已点击第 1 个商品，等待页面响应...
2025-07-26 20:54:31,879 - INFO - 点击前窗口句柄数: 2
2025-07-26 20:54:36,895 - INFO - 等待超时，但检测到多个窗口，切换到最后一个
2025-07-26 20:54:36,900 - INFO - 已切换到最后一个窗口，URL: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=681778175573
2025-07-26 20:54:36,905 - INFO - 页面加载完成
2025-07-26 20:54:36,906 - INFO - 模拟阅读行为，停顿 2.0 秒
2025-07-26 20:54:39,584 - INFO - 找到 0 个iframe
2025-07-26 20:54:39,586 - INFO - 未找到包含目标元素的iframe，保持在主文档
2025-07-26 20:54:39,589 - INFO - 当前在新标签页中，URL: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=681778175573
2025-07-26 20:54:39,591 - INFO - 已切换到主文档，开始查找立即铺货按钮...
2025-07-26 20:54:39,592 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-26 20:54:39,620 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-26 20:54:39,620 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-26 20:54:39,620 - INFO - 找到立即铺货按钮，准备点击...
2025-07-26 20:54:39,620 - INFO - 模拟阅读行为，停顿 1.1 秒
2025-07-26 20:54:42,930 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-26 20:54:44,381 - INFO - 尝试ActionChains点击...
2025-07-26 20:54:44,674 - INFO - ActionChains点击成功
2025-07-26 20:54:45,127 - INFO - 已点击立即铺货按钮，等待确认对话框...
2025-07-26 20:54:45,127 - INFO - 等待确认对话框出现，停顿 2.5 秒
2025-07-26 20:54:47,653 - INFO - 准备点击元素: tag=div, text='新发宝贝
关联已有宝贝
供货商：芝兰国际贸易供货商
商品名称：保税直供 Dove多芬磨砂膏石榴籽乳木果风味身体磨砂膏298g
铺货时宝贝取建议零售价为一口价（SKU销售价默认取SKU建议零售价）， 可以在千牛卖家后台进行价格调整，低于最低限价销售时，存在资金倒挂无法下单风险， 也可能被供货商取消产品授权
SKU列表
SKU名称
最低限价
建议零售价
香味:石榴籽乳木果风味298g
¥35
¥45
香味:石榴籽乳木果风味298g*2盒
¥67
¥79
香味:薰衣草椰奶风味298g
¥35
¥45
香味:薰衣草椰奶风味298g*2盒
¥67
¥79
香味:夏威夷果米浆风味298g
¥35
¥45
香味:夏威夷果米浆风味298g*2盒
¥67
¥79
该商品铺货后需要登录2.0(跨境贸易)分销商后台查看消息。
立即铺货', class='next-dialog next-closeable nex', id=''
2025-07-26 20:54:48,774 - INFO - 尝试ActionChains点击...
2025-07-26 20:54:49,037 - INFO - ActionChains点击成功
2025-07-26 20:54:49,402 - INFO - 已关闭弹窗: //div[contains(@class, 'close')]
2025-07-26 20:54:54,461 - WARNING - 等待元素可点击超时: /html/body/div[8]/div[2]/div[2]/button
2025-07-26 20:54:54,461 - WARNING - 使用配置的XPath未找到确认按钮
2025-07-26 20:54:54,461 - INFO - 操作失败，2秒后重试 (尝试 1/2)
2025-07-26 20:54:56,522 - INFO - 准备点击元素: tag=div, text='新发宝贝
关联已有宝贝
供货商：芝兰国际贸易供货商
商品名称：保税直供 Dove多芬磨砂膏石榴籽乳木果风味身体磨砂膏298g
铺货时宝贝取建议零售价为一口价（SKU销售价默认取SKU建议零售价）， 可以在千牛卖家后台进行价格调整，低于最低限价销售时，存在资金倒挂无法下单风险， 也可能被供货商取消产品授权
SKU列表
SKU名称
最低限价
建议零售价
香味:石榴籽乳木果风味298g
¥35
¥45
香味:石榴籽乳木果风味298g*2盒
¥67
¥79
香味:薰衣草椰奶风味298g
¥35
¥45
香味:薰衣草椰奶风味298g*2盒
¥67
¥79
香味:夏威夷果米浆风味298g
¥35
¥45
香味:夏威夷果米浆风味298g*2盒
¥67
¥79
该商品铺货后需要登录2.0(跨境贸易)分销商后台查看消息。
立即铺货', class='next-dialog next-closeable nex', id=''
2025-07-26 20:54:57,608 - INFO - 尝试ActionChains点击...
2025-07-26 20:54:57,874 - INFO - ActionChains点击成功
2025-07-26 20:54:58,181 - INFO - 已关闭弹窗: //div[contains(@class, 'close')]
2025-07-26 20:55:03,255 - WARNING - 等待元素可点击超时: /html/body/div[8]/div[2]/div[2]/button
2025-07-26 20:55:03,255 - WARNING - 使用配置的XPath未找到确认按钮
2025-07-26 20:55:03,257 - ERROR - 操作失败，已达到最大重试次数 (2)
2025-07-26 20:55:03,260 - ERROR - 无法找到或点击确认按钮
2025-07-26 20:55:03,325 - INFO - 已关闭当前标签页并切换回主页面
2025-07-26 20:55:03,345 - WARNING - 第 1 个商品处理失败
2025-07-26 20:55:03,345 - INFO - 等待 6.4 秒后处理下一个商品
2025-07-26 20:55:09,730 - INFO - 开始处理第 2/40 个商品
2025-07-26 20:55:09,742 - INFO - 找到 40 个直接子div
2025-07-26 20:55:09,760 - INFO - 商品 1: 位置={'x': 316, 'y': 737}, 尺寸={'height': 347, 'width': 226}, ID=item_id=681778175573&path=search_goods_card&type=s
2025-07-26 20:55:09,775 - INFO - 商品 2: 位置={'x': 554, 'y': 737}, 尺寸={'height': 347, 'width': 225}, ID=item_id=690841537159&path=search_goods_card&index=
2025-07-26 20:55:09,792 - INFO - 商品 3: 位置={'x': 791, 'y': 737}, 尺寸={'height': 347, 'width': 225}, ID=item_id=789447097664&path=search_goods_card&index=
2025-07-26 20:55:09,809 - INFO - 商品 4: 位置={'x': 1028, 'y': 737}, 尺寸={'height': 347, 'width': 225}, ID=item_id=694682843417&path=search_goods_card&index=
2025-07-26 20:55:09,827 - INFO - 商品 5: 位置={'x': 1265, 'y': 737}, 尺寸={'height': 347, 'width': 225}, ID=item_id=681082809892&path=search_goods_card&index=
2025-07-26 20:55:09,845 - INFO - 商品 6: 位置={'x': 316, 'y': 1096}, 尺寸={'height': 341, 'width': 226}, ID=item_id=694675776881&path=search_goods_card&index=
2025-07-26 20:55:09,862 - INFO - 商品 7: 位置={'x': 554, 'y': 1096}, 尺寸={'height': 341, 'width': 225}, ID=item_id=680729304287&path=search_goods_card&index=
2025-07-26 20:55:09,878 - INFO - 商品 8: 位置={'x': 791, 'y': 1096}, 尺寸={'height': 341, 'width': 225}, ID=item_id=710355107054&path=search_goods_card&index=
2025-07-26 20:55:09,893 - INFO - 商品 9: 位置={'x': 1028, 'y': 1096}, 尺寸={'height': 341, 'width': 225}, ID=item_id=740463658412&path=search_goods_card&index=
2025-07-26 20:55:09,912 - INFO - 商品 10: 位置={'x': 1265, 'y': 1096}, 尺寸={'height': 341, 'width': 225}, ID=item_id=821067902127&path=search_goods_card&index=
2025-07-26 20:55:09,928 - INFO - 商品 11: 位置={'x': 316, 'y': 1449}, 尺寸={'height': 341, 'width': 226}, ID=item_id=789835300215&path=search_goods_card&index=
2025-07-26 20:55:09,945 - INFO - 商品 12: 位置={'x': 554, 'y': 1449}, 尺寸={'height': 341, 'width': 225}, ID=item_id=843747605382&path=search_goods_card&index=
2025-07-26 20:55:09,960 - INFO - 商品 13: 位置={'x': 791, 'y': 1449}, 尺寸={'height': 341, 'width': 225}, ID=item_id=675816539149&path=search_goods_card&index=
2025-07-26 20:55:09,979 - INFO - 商品 14: 位置={'x': 1028, 'y': 1449}, 尺寸={'height': 341, 'width': 225}, ID=item_id=846634805775&path=search_goods_card&index=
2025-07-26 20:55:09,996 - INFO - 商品 15: 位置={'x': 1265, 'y': 1449}, 尺寸={'height': 341, 'width': 225}, ID=item_id=852510575114&path=search_goods_card&index=
2025-07-26 20:55:10,011 - INFO - 商品 16: 位置={'x': 316, 'y': 1802}, 尺寸={'height': 341, 'width': 226}, ID=item_id=743442966826&path=search_goods_card&index=
2025-07-26 20:55:10,028 - INFO - 商品 17: 位置={'x': 554, 'y': 1802}, 尺寸={'height': 341, 'width': 225}, ID=item_id=848934676698&path=search_goods_card&index=
2025-07-26 20:55:10,045 - INFO - 商品 18: 位置={'x': 791, 'y': 1802}, 尺寸={'height': 341, 'width': 225}, ID=item_id=693758717931&path=search_goods_card&index=
2025-07-26 20:55:10,060 - INFO - 商品 19: 位置={'x': 1028, 'y': 1802}, 尺寸={'height': 341, 'width': 225}, ID=item_id=691142128229&path=search_goods_card&index=
2025-07-26 20:55:10,076 - INFO - 商品 20: 位置={'x': 1265, 'y': 1802}, 尺寸={'height': 341, 'width': 225}, ID=item_id=690739721578&path=search_goods_card&index=
2025-07-26 20:55:10,091 - INFO - 商品 21: 位置={'x': 316, 'y': 2155}, 尺寸={'height': 341, 'width': 226}, ID=item_id=691918824108&path=search_goods_card&index=
2025-07-26 20:55:10,108 - INFO - 商品 22: 位置={'x': 554, 'y': 2155}, 尺寸={'height': 341, 'width': 225}, ID=item_id=875045022715&path=search_goods_card&index=
2025-07-26 20:55:10,124 - INFO - 商品 23: 位置={'x': 791, 'y': 2155}, 尺寸={'height': 341, 'width': 225}, ID=item_id=873911671570&path=search_goods_card&index=
2025-07-26 20:55:10,141 - INFO - 商品 24: 位置={'x': 1028, 'y': 2155}, 尺寸={'height': 341, 'width': 225}, ID=item_id=722937163980&path=search_goods_card&index=
2025-07-26 20:55:10,158 - INFO - 商品 25: 位置={'x': 1265, 'y': 2155}, 尺寸={'height': 341, 'width': 225}, ID=item_id=739090443937&path=search_goods_card&index=
2025-07-26 20:55:10,198 - INFO - 商品 26: 位置={'x': 316, 'y': 2508}, 尺寸={'height': 341, 'width': 226}, ID=item_id=777862601010&path=search_goods_card&index=
2025-07-26 20:55:10,213 - INFO - 商品 27: 位置={'x': 554, 'y': 2508}, 尺寸={'height': 341, 'width': 225}, ID=item_id=686870174450&path=search_goods_card&index=
2025-07-26 20:55:10,231 - INFO - 商品 28: 位置={'x': 791, 'y': 2508}, 尺寸={'height': 341, 'width': 225}, ID=item_id=814542665480&path=search_goods_card&index=
2025-07-26 20:55:10,247 - INFO - 商品 29: 位置={'x': 1028, 'y': 2508}, 尺寸={'height': 341, 'width': 225}, ID=item_id=852625718015&path=search_goods_card&index=
2025-07-26 20:55:10,267 - INFO - 商品 30: 位置={'x': 1265, 'y': 2508}, 尺寸={'height': 341, 'width': 225}, ID=item_id=681472049289&path=search_goods_card&index=
2025-07-26 20:55:10,285 - INFO - 商品 31: 位置={'x': 316, 'y': 2861}, 尺寸={'height': 341, 'width': 226}, ID=item_id=683142617961&path=search_goods_card&index=
2025-07-26 20:55:10,303 - INFO - 商品 32: 位置={'x': 554, 'y': 2861}, 尺寸={'height': 341, 'width': 225}, ID=item_id=763659951211&path=search_goods_card&index=
2025-07-26 20:55:10,319 - INFO - 商品 33: 位置={'x': 791, 'y': 2861}, 尺寸={'height': 341, 'width': 225}, ID=item_id=675478966678&path=search_goods_card&index=
2025-07-26 20:55:10,338 - INFO - 商品 34: 位置={'x': 1028, 'y': 2861}, 尺寸={'height': 341, 'width': 225}, ID=item_id=800365611234&path=search_goods_card&index=
2025-07-26 20:55:10,354 - INFO - 商品 35: 位置={'x': 1265, 'y': 2861}, 尺寸={'height': 341, 'width': 225}, ID=item_id=896721954982&path=search_goods_card&index=
2025-07-26 20:55:10,371 - INFO - 商品 36: 位置={'x': 316, 'y': 3214}, 尺寸={'height': 341, 'width': 226}, ID=item_id=795389643023&path=search_goods_card&index=
2025-07-26 20:55:10,388 - INFO - 商品 37: 位置={'x': 554, 'y': 3214}, 尺寸={'height': 341, 'width': 225}, ID=item_id=689584396900&path=search_goods_card&index=
2025-07-26 20:55:10,406 - INFO - 商品 38: 位置={'x': 791, 'y': 3214}, 尺寸={'height': 341, 'width': 225}, ID=item_id=788516102627&path=search_goods_card&index=
2025-07-26 20:55:10,422 - INFO - 商品 39: 位置={'x': 1028, 'y': 3214}, 尺寸={'height': 341, 'width': 225}, ID=item_id=825025224622&path=search_goods_card&index=
2025-07-26 20:55:10,438 - INFO - 商品 40: 位置={'x': 1265, 'y': 3214}, 尺寸={'height': 341, 'width': 225}, ID=item_id=842638336638&path=search_goods_card&index=
2025-07-26 20:55:10,439 - INFO - 找到 40 个有效的商品容器
2025-07-26 20:55:10,441 - WARNING - 警告：第 2 个元素可能不是商品项，class=''
2025-07-26 20:55:10,465 - INFO - 商品 2 信息: 文本='保税直供 凡士林Vaseline烟酰胺身体乳400ml 725ml无压泵款200ml
采购价¥19.', 位置={'x': 554, 'y': 737}
2025-07-26 20:55:11,989 - INFO - 商品 2 位置: {'x': 554, 'y': 737}, 尺寸: {'height': 347, 'width': 226}
2025-07-26 20:55:11,989 - INFO - 模拟阅读行为，停顿 3.0 秒
