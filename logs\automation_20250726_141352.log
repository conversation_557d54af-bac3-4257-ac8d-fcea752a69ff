2025-07-26 14:13:55,537 - INFO - 浏览器驱动初始化成功
2025-07-26 14:13:55,561 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:13:55,598 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:13:55,609 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:13:55,621 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:13:55,632 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:13:55,644 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:13:55,654 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:13:55,667 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:13:55,680 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:13:55,691 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:13:55,700 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:13:55,712 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:13:55,724 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:13:55,736 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:13:55,747 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:13:55,756 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:13:55,770 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:13:55,783 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:13:55,790 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:13:55,804 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x7ff7a742e925+77845]
	GetHandleVerifier [0x0x7ff7a742e980+77936]
	(No symbol) [0x0x7ff7a71e9b0c]
	(No symbol) [0x0x7ff7a72a5b46]
	(No symbol) [0x0x7ff7a72688ca]
	(No symbol) [0x0x7ff7a7290b07]
	(No symbol) [0x0x7ff7a72686a3]
	(No symbol) [0x0x7ff7a7231791]
	(No symbol) [0x0x7ff7a7232523]
	GetHandleVerifier [0x0x7ff7a770683d+3059501]
	GetHandleVerifier [0x0x7ff7a7700bfd+3035885]
	GetHandleVerifier [0x0x7ff7a77203f0+3164896]
	GetHandleVerifier [0x0x7ff7a7448c2e+185118]
	GetHandleVerifier [0x0x7ff7a745053f+216111]
	GetHandleVerifier [0x0x7ff7a74372d4+113092]
	GetHandleVerifier [0x0x7ff7a7437489+113529]
	GetHandleVerifier [0x0x7ff7a741e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-26 14:13:55,815 - INFO - Cookies已加载
2025-07-26 14:13:55,829 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?channel=global_zx&spm=a21ug5.29159167.6452766900.2ndview_zxmore_click
2025-07-26 14:13:59,599 - INFO - 成功导航到目标页面
2025-07-26 14:13:59,609 - INFO - 登录成功！现在可以开始铺货任务
2025-07-26 14:14:03,470 - INFO - 开始自动化铺货流程
2025-07-26 14:14:03,477 - INFO - 正在返回主页面...
2025-07-26 14:14:07,456 - INFO - 已返回主页面
2025-07-26 14:14:18,745 - INFO - 使用选择器 .//div[position()] 找到 160 个有效商品项
2025-07-26 14:14:19,352 - INFO - 去重后找到 120 个唯一商品项
2025-07-26 14:14:19,374 - INFO - 商品 1: class='无类名', data-spm='无data-spm', 位置={'x': 358, 'y': 737}
2025-07-26 14:14:19,397 - INFO - 商品 2: class='tfx-item--top', data-spm='无data-spm', 位置={'x': 358, 'y': 738}
2025-07-26 14:14:19,413 - INFO - 商品 3: class='tfx-item__info', data-spm='无data-spm', 位置={'x': 358, 'y': 958}
2025-07-26 14:14:19,429 - INFO - 商品 4: class='无类名', data-spm='无data-spm', 位置={'x': 595, 'y': 737}
2025-07-26 14:14:19,442 - INFO - 商品 5: class='tfx-item--top', data-spm='无data-spm', 位置={'x': 596, 'y': 738}
2025-07-26 14:14:19,448 - INFO - 找到 120 个商品，将处理 15 个
2025-07-26 14:14:19,455 - INFO - 当前批次大小: 14
2025-07-26 14:14:19,460 - INFO - 开始处理第 1/120 个商品
2025-07-26 14:14:30,752 - INFO - 使用选择器 .//div[position()] 找到 160 个有效商品项
2025-07-26 14:14:31,380 - INFO - 去重后找到 120 个唯一商品项
2025-07-26 14:14:31,393 - INFO - 商品 1: class='无类名', data-spm='无data-spm', 位置={'x': 358, 'y': 737}
2025-07-26 14:14:31,407 - INFO - 商品 2: class='tfx-item--top', data-spm='无data-spm', 位置={'x': 358, 'y': 738}
2025-07-26 14:14:31,419 - INFO - 商品 3: class='tfx-item__info', data-spm='无data-spm', 位置={'x': 358, 'y': 958}
2025-07-26 14:14:31,433 - INFO - 商品 4: class='无类名', data-spm='无data-spm', 位置={'x': 595, 'y': 737}
2025-07-26 14:14:31,447 - INFO - 商品 5: class='tfx-item--top', data-spm='无data-spm', 位置={'x': 596, 'y': 738}
2025-07-26 14:14:31,454 - WARNING - 警告：第 1 个元素可能不是商品项，class=''
2025-07-26 14:14:31,461 - INFO - 第 1 个商品已铺货，跳过
2025-07-26 14:14:31,468 - WARNING - 第 1 个商品处理失败
2025-07-26 14:14:31,475 - INFO - 等待 7.0 秒后处理下一个商品
2025-07-26 14:14:38,496 - INFO - 开始处理第 2/120 个商品
2025-07-26 14:14:49,866 - INFO - 使用选择器 .//div[position()] 找到 160 个有效商品项
2025-07-26 14:14:50,492 - INFO - 去重后找到 120 个唯一商品项
2025-07-26 14:14:50,507 - INFO - 商品 1: class='无类名', data-spm='无data-spm', 位置={'x': 358, 'y': 737}
2025-07-26 14:14:50,522 - INFO - 商品 2: class='tfx-item--top', data-spm='无data-spm', 位置={'x': 358, 'y': 738}
2025-07-26 14:14:50,536 - INFO - 商品 3: class='tfx-item__info', data-spm='无data-spm', 位置={'x': 358, 'y': 958}
2025-07-26 14:14:50,550 - INFO - 商品 4: class='无类名', data-spm='无data-spm', 位置={'x': 595, 'y': 737}
2025-07-26 14:14:50,565 - INFO - 商品 5: class='tfx-item--top', data-spm='无data-spm', 位置={'x': 596, 'y': 738}
2025-07-26 14:14:50,576 - INFO - 第 2 个商品已铺货，跳过
2025-07-26 14:14:50,582 - WARNING - 第 2 个商品处理失败
2025-07-26 14:14:50,587 - INFO - 等待 3.4 秒后处理下一个商品
2025-07-26 14:14:54,042 - INFO - 开始处理第 3/120 个商品
2025-07-26 14:15:05,789 - INFO - 使用选择器 .//div[position()] 找到 160 个有效商品项
2025-07-26 14:15:06,439 - INFO - 去重后找到 120 个唯一商品项
2025-07-26 14:15:06,454 - INFO - 商品 1: class='无类名', data-spm='无data-spm', 位置={'x': 358, 'y': 737}
2025-07-26 14:15:06,472 - INFO - 商品 2: class='tfx-item--top', data-spm='无data-spm', 位置={'x': 358, 'y': 738}
2025-07-26 14:15:06,489 - INFO - 商品 3: class='tfx-item__info', data-spm='无data-spm', 位置={'x': 358, 'y': 958}
2025-07-26 14:15:06,503 - INFO - 商品 4: class='无类名', data-spm='无data-spm', 位置={'x': 595, 'y': 737}
2025-07-26 14:15:06,517 - INFO - 商品 5: class='tfx-item--top', data-spm='无data-spm', 位置={'x': 596, 'y': 738}
2025-07-26 14:15:06,549 - INFO - 商品 3 信息: 文本='保税直供 Dove多芬磨砂膏石榴籽乳木果风味身体磨砂膏298g
采购价¥35.00销量11.2万+件', 位置={'x': 358, 'y': 958}
2025-07-26 14:15:08,075 - INFO - 商品 3 位置: {'x': 358, 'y': 956}, 尺寸: {'height': 121, 'width': 218}, 中心点: (467, 1016)
2025-07-26 14:15:08,408 - INFO - 使用鼠标模拟点击成功点击第 3 个商品
2025-07-26 14:15:08,419 - INFO - 已点击第 3 个商品，等待页面响应...
2025-07-26 14:15:12,441 - INFO - 已切换到新标签页
2025-07-26 14:15:12,458 - INFO - 页面加载完成
2025-07-26 14:15:12,529 - INFO - 找到 0 个iframe
2025-07-26 14:15:12,540 - INFO - 未找到包含目标元素的iframe，保持在主文档
2025-07-26 14:15:17,721 - WARNING - 等待元素可点击超时: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-26 14:15:19,768 - WARNING - 等待元素可点击超时: //button[contains(text(), '添加') or contains(text(), '加入') or contains(text(), '铺货')]
2025-07-26 14:15:21,832 - WARNING - 等待元素可点击超时: //button[contains(@class, 'add') or contains(@class, 'submit')]
2025-07-26 14:15:23,880 - WARNING - 等待元素可点击超时: //a[contains(text(), '添加') or contains(text(), '加入') or contains(text(), '铺货')]
2025-07-26 14:15:25,921 - WARNING - 等待元素可点击超时: //div[contains(@class, 'add-btn')]//button
2025-07-26 14:15:28,003 - WARNING - 等待元素可点击超时: //button[contains(@class, 'primary')]
2025-07-26 14:15:30,087 - WARNING - 等待元素可点击超时: //*[@role='button'][contains(text(), '添加') or contains(text(), '加入')]
2025-07-26 14:15:32,150 - WARNING - 等待元素可点击超时: //button[contains(text(), '立即')]
2025-07-26 14:15:34,193 - WARNING - 等待元素可点击超时: //a[contains(text(), '立即')]
2025-07-26 14:15:34,253 - WARNING - 未能找到添加商品按钮
2025-07-26 14:15:34,267 - INFO - 操作失败，2秒后重试 (尝试 1/2)
2025-07-26 14:15:41,418 - WARNING - 等待元素可点击超时: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-26 14:15:43,460 - WARNING - 等待元素可点击超时: //button[contains(text(), '添加') or contains(text(), '加入') or contains(text(), '铺货')]
2025-07-26 14:15:45,498 - WARNING - 等待元素可点击超时: //button[contains(@class, 'add') or contains(@class, 'submit')]
2025-07-26 14:15:47,543 - WARNING - 等待元素可点击超时: //a[contains(text(), '添加') or contains(text(), '加入') or contains(text(), '铺货')]
2025-07-26 14:15:49,602 - WARNING - 等待元素可点击超时: //div[contains(@class, 'add-btn')]//button
2025-07-26 14:15:51,681 - WARNING - 等待元素可点击超时: //button[contains(@class, 'primary')]
2025-07-26 14:15:53,743 - WARNING - 等待元素可点击超时: //*[@role='button'][contains(text(), '添加') or contains(text(), '加入')]
2025-07-26 14:15:55,784 - WARNING - 等待元素可点击超时: //button[contains(text(), '立即')]
2025-07-26 14:15:57,829 - WARNING - 等待元素可点击超时: //a[contains(text(), '立即')]
2025-07-26 14:15:57,869 - WARNING - 未能找到添加商品按钮
2025-07-26 14:15:57,885 - ERROR - 操作失败，已达到最大重试次数 (2)
2025-07-26 14:15:57,904 - ERROR - 无法找到或点击添加商品按钮
2025-07-26 14:15:57,973 - INFO - 已关闭当前标签页并切换回主页面
2025-07-26 14:15:57,991 - WARNING - 第 3 个商品处理失败
2025-07-26 14:15:58,011 - INFO - 等待 5.4 秒后处理下一个商品
2025-07-26 14:16:03,429 - INFO - 开始处理第 4/120 个商品
2025-07-26 14:16:14,424 - INFO - 使用选择器 .//div[position()] 找到 160 个有效商品项
2025-07-26 14:16:15,052 - INFO - 去重后找到 120 个唯一商品项
2025-07-26 14:16:15,070 - INFO - 商品 1: class='无类名', data-spm='无data-spm', 位置={'x': 358, 'y': 737}
2025-07-26 14:16:15,090 - INFO - 商品 2: class='tfx-item--top', data-spm='无data-spm', 位置={'x': 358, 'y': 736}
2025-07-26 14:16:15,105 - INFO - 商品 3: class='tfx-item__info', data-spm='无data-spm', 位置={'x': 358, 'y': 956}
2025-07-26 14:16:15,122 - INFO - 商品 4: class='无类名', data-spm='无data-spm', 位置={'x': 595, 'y': 737}
2025-07-26 14:16:15,137 - INFO - 商品 5: class='tfx-item--top', data-spm='无data-spm', 位置={'x': 596, 'y': 738}
2025-07-26 14:16:15,146 - WARNING - 警告：第 4 个元素可能不是商品项，class=''
2025-07-26 14:16:15,176 - INFO - 商品 4 信息: 文本='保税直供 凡士林Vaseline烟酰胺身体乳400ml 725ml无压泵款200ml
采购价¥19.', 位置={'x': 595, 'y': 737}
2025-07-26 14:16:16,700 - INFO - 商品 4 位置: {'x': 594, 'y': 737}, 尺寸: {'height': 347, 'width': 226}, 中心点: (707, 910)
2025-07-26 14:16:17,015 - INFO - 使用鼠标模拟点击成功点击第 4 个商品
2025-07-26 14:16:17,022 - INFO - 已点击第 4 个商品，等待页面响应...
2025-07-26 14:16:21,035 - INFO - 已切换到新标签页
2025-07-26 14:16:21,046 - INFO - 页面加载完成
2025-07-26 14:16:21,154 - INFO - 找到 0 个iframe
2025-07-26 14:16:21,162 - INFO - 未找到包含目标元素的iframe，保持在主文档
2025-07-26 14:16:21,184 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-26 14:16:21,986 - INFO - 已点击添加商品按钮，等待确认对话框...
2025-07-26 14:16:25,783 - INFO - 已关闭弹窗: //div[contains(@class, 'close')]
2025-07-26 14:16:30,847 - WARNING - 等待元素可点击超时: /html/body/div[8]/div[2]/div[2]/button
2025-07-26 14:16:32,880 - WARNING - 等待元素可点击超时: //button[contains(text(), '确认') or contains(text(), '确定') or contains(text(), '提交')]
2025-07-26 14:16:34,923 - WARNING - 等待元素可点击超时: //button[contains(@class, 'confirm') or contains(@class, 'submit')]
2025-07-26 14:16:34,953 - INFO - 使用选择器找到确认按钮: //div[contains(@class, 'dialog')]//button[last()]
2025-07-26 14:16:35,736 - INFO - 商品添加成功
2025-07-26 14:16:37,822 - INFO - 已关闭当前标签页并切换回主页面
2025-07-26 14:16:37,836 - INFO - 第 4 个商品处理成功 (总成功: 1, 批次进度: 1/14)
2025-07-26 14:16:37,847 - INFO - 等待 5.4 秒后处理下一个商品
2025-07-26 14:16:43,239 - INFO - 开始处理第 5/120 个商品
2025-07-26 14:16:55,213 - INFO - 使用选择器 .//div[position()] 找到 160 个有效商品项
2025-07-26 14:16:55,970 - INFO - 去重后找到 121 个唯一商品项
2025-07-26 14:16:55,987 - INFO - 商品 1: class='无类名', data-spm='无data-spm', 位置={'x': 358, 'y': 737}
2025-07-26 14:16:56,005 - INFO - 商品 2: class='tfx-item--top', data-spm='无data-spm', 位置={'x': 358, 'y': 736}
2025-07-26 14:16:56,022 - INFO - 商品 3: class='tfx-item__info', data-spm='无data-spm', 位置={'x': 358, 'y': 956}
2025-07-26 14:16:56,040 - INFO - 商品 4: class='无类名', data-spm='无data-spm', 位置={'x': 594, 'y': 737}
2025-07-26 14:16:56,061 - INFO - 商品 5: class='tfx-item', data-spm='search_goods_card_tfx_item_1', 位置={'x': 598, 'y': 740}
2025-07-26 14:16:56,101 - INFO - 商品 5 信息: 文本='保税直供 凡士林Vaseline烟酰胺身体乳400ml 725ml无压泵款200ml
采购价¥19.', 位置={'x': 598, 'y': 740}
2025-07-26 14:16:57,631 - INFO - 商品 5 位置: {'x': 598, 'y': 740}, 尺寸: {'height': 341, 'width': 220}, 中心点: (708, 910)
2025-07-26 14:17:02,942 - INFO - 使用鼠标模拟点击成功点击第 5 个商品
2025-07-26 14:17:02,956 - INFO - 已点击第 5 个商品，等待页面响应...
2025-07-26 14:17:06,972 - INFO - 已切换到新标签页
2025-07-26 14:17:06,987 - INFO - 页面加载完成
2025-07-26 14:17:07,076 - INFO - 找到 0 个iframe
2025-07-26 14:17:07,087 - INFO - 未找到包含目标元素的iframe，保持在主文档
2025-07-26 14:17:07,112 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-26 14:17:07,924 - INFO - 已点击添加商品按钮，等待确认对话框...
2025-07-26 14:17:11,722 - INFO - 已关闭弹窗: //div[contains(@class, 'close')]
2025-07-26 14:17:16,833 - WARNING - 等待元素可点击超时: /html/body/div[8]/div[2]/div[2]/button
2025-07-26 14:17:18,873 - WARNING - 等待元素可点击超时: //button[contains(text(), '确认') or contains(text(), '确定') or contains(text(), '提交')]
2025-07-26 14:17:20,913 - WARNING - 等待元素可点击超时: //button[contains(@class, 'confirm') or contains(@class, 'submit')]
2025-07-26 14:17:20,944 - INFO - 使用选择器找到确认按钮: //div[contains(@class, 'dialog')]//button[last()]
2025-07-26 14:17:21,742 - INFO - 商品添加成功
2025-07-26 14:17:23,842 - INFO - 已关闭当前标签页并切换回主页面
2025-07-26 14:17:23,860 - INFO - 第 5 个商品处理成功 (总成功: 2, 批次进度: 2/14)
2025-07-26 14:17:23,880 - INFO - 等待 3.1 秒后处理下一个商品
2025-07-26 14:17:27,003 - INFO - 开始处理第 6/120 个商品
2025-07-26 14:17:38,466 - INFO - 使用选择器 .//div[position()] 找到 160 个有效商品项
2025-07-26 14:17:39,091 - INFO - 去重后找到 121 个唯一商品项
2025-07-26 14:17:39,116 - INFO - 商品 1: class='无类名', data-spm='无data-spm', 位置={'x': 358, 'y': 737}
2025-07-26 14:17:39,134 - INFO - 商品 2: class='tfx-item--top', data-spm='无data-spm', 位置={'x': 358, 'y': 736}
2025-07-26 14:17:39,154 - INFO - 商品 3: class='tfx-item__info', data-spm='无data-spm', 位置={'x': 358, 'y': 956}
2025-07-26 14:17:39,171 - INFO - 商品 4: class='无类名', data-spm='无data-spm', 位置={'x': 594, 'y': 737}
2025-07-26 14:17:39,193 - INFO - 商品 5: class='tfx-item', data-spm='search_goods_card_tfx_item_1', 位置={'x': 598, 'y': 740}
2025-07-26 14:17:39,222 - INFO - 商品 6 信息: 文本='无文本', 位置={'x': 600, 'y': 743}
2025-07-26 14:17:40,761 - INFO - 商品 6 位置: {'x': 600, 'y': 743}, 尺寸: {'height': 226, 'width': 214}, 中心点: (707, 856)
2025-07-26 14:17:41,072 - INFO - 使用鼠标模拟点击成功点击第 6 个商品
2025-07-26 14:17:41,085 - INFO - 已点击第 6 个商品，等待页面响应...
2025-07-26 14:17:45,097 - INFO - 已切换到新标签页
2025-07-26 14:17:45,115 - INFO - 页面加载完成
2025-07-26 14:17:45,182 - INFO - 找到 0 个iframe
2025-07-26 14:17:45,190 - INFO - 未找到包含目标元素的iframe，保持在主文档
2025-07-26 14:17:45,215 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-26 14:17:46,024 - INFO - 已点击添加商品按钮，等待确认对话框...
2025-07-26 14:17:49,829 - INFO - 已关闭弹窗: //div[contains(@class, 'close')]
2025-07-26 14:17:54,924 - WARNING - 等待元素可点击超时: /html/body/div[8]/div[2]/div[2]/button
2025-07-26 14:17:56,975 - WARNING - 等待元素可点击超时: //button[contains(text(), '确认') or contains(text(), '确定') or contains(text(), '提交')]
2025-07-26 14:17:59,041 - WARNING - 等待元素可点击超时: //button[contains(@class, 'confirm') or contains(@class, 'submit')]
2025-07-26 14:17:59,072 - INFO - 使用选择器找到确认按钮: //div[contains(@class, 'dialog')]//button[last()]
2025-07-26 14:17:59,865 - INFO - 商品添加成功
2025-07-26 14:18:01,941 - INFO - 已关闭当前标签页并切换回主页面
2025-07-26 14:18:01,959 - INFO - 第 6 个商品处理成功 (总成功: 3, 批次进度: 3/14)
2025-07-26 14:18:01,971 - INFO - 等待 4.2 秒后处理下一个商品
2025-07-26 14:18:06,147 - INFO - 开始处理第 7/120 个商品
2025-07-26 14:18:17,376 - INFO - 使用选择器 .//div[position()] 找到 160 个有效商品项
2025-07-26 14:18:17,989 - INFO - 去重后找到 121 个唯一商品项
2025-07-26 14:18:18,011 - INFO - 商品 1: class='无类名', data-spm='无data-spm', 位置={'x': 358, 'y': 737}
2025-07-26 14:18:18,030 - INFO - 商品 2: class='tfx-item--top', data-spm='无data-spm', 位置={'x': 358, 'y': 736}
2025-07-26 14:18:18,051 - INFO - 商品 3: class='tfx-item__info', data-spm='无data-spm', 位置={'x': 358, 'y': 956}
2025-07-26 14:18:18,068 - INFO - 商品 4: class='无类名', data-spm='无data-spm', 位置={'x': 594, 'y': 737}
2025-07-26 14:18:18,086 - INFO - 商品 5: class='tfx-item', data-spm='search_goods_card_tfx_item_1', 位置={'x': 598, 'y': 740}
2025-07-26 14:18:18,129 - INFO - 商品 7 信息: 文本='保税直供 凡士林Vaseline烟酰胺身体乳400ml 725ml无压泵款200ml
采购价¥19.', 位置={'x': 600, 'y': 969}
2025-07-26 14:18:19,657 - INFO - 商品 7 位置: {'x': 600, 'y': 957}, 尺寸: {'height': 121, 'width': 214}, 中心点: (707, 1017)
2025-07-26 14:18:19,963 - INFO - 使用鼠标模拟点击成功点击第 7 个商品
2025-07-26 14:18:19,975 - INFO - 已点击第 7 个商品，等待页面响应...
2025-07-26 14:18:23,986 - INFO - 已切换到新标签页
2025-07-26 14:18:23,999 - INFO - 页面加载完成
2025-07-26 14:18:24,069 - INFO - 找到 0 个iframe
2025-07-26 14:18:24,078 - INFO - 未找到包含目标元素的iframe，保持在主文档
2025-07-26 14:18:24,103 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-26 14:18:24,905 - INFO - 已点击添加商品按钮，等待确认对话框...
2025-07-26 14:18:30,016 - INFO - 用户请求停止自动化流程
2025-07-26 14:18:33,708 - INFO - 已关闭弹窗: //div[contains(@class, 'close')]
2025-07-26 14:18:39,064 - WARNING - 等待元素可点击超时: /html/body/div[8]/div[2]/div[2]/button
2025-07-26 14:18:41,118 - WARNING - 等待元素可点击超时: //button[contains(text(), '确认') or contains(text(), '确定') or contains(text(), '提交')]
2025-07-26 14:18:43,166 - WARNING - 等待元素可点击超时: //button[contains(@class, 'confirm') or contains(@class, 'submit')]
2025-07-26 14:18:43,203 - INFO - 使用选择器找到确认按钮: //div[contains(@class, 'dialog')]//button[last()]
2025-07-26 14:18:49,088 - INFO - 商品添加成功
2025-07-26 14:18:51,166 - INFO - 已关闭当前标签页并切换回主页面
2025-07-26 14:18:51,178 - INFO - 第 7 个商品处理成功 (总成功: 4, 批次进度: 4/14)
2025-07-26 14:18:51,191 - INFO - 等待 4.1 秒后处理下一个商品
2025-07-26 14:18:55,308 - INFO - 用户停止了流程
2025-07-26 14:18:55,320 - INFO - 自动化流程已停止
