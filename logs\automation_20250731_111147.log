2025-07-31 11:12:01,665 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff60783e925+77845]
	GetHandleVerifier [0x0x7ff60783e980+77936]
	(No symbol) [0x0x7ff6075f9cda]
	(No symbol) [0x0x7ff607601679]
	(No symbol) [0x0x7ff607604a61]
	(No symbol) [0x0x7ff6076a1e4b]
	(No symbol) [0x0x7ff6076788ca]
	(No symbol) [0x0x7ff6076a0b07]
	(No symbol) [0x0x7ff6076786a3]
	(No symbol) [0x0x7ff607641791]
	(No symbol) [0x0x7ff607642523]
	GetHandleVerifier [0x0x7ff607b1683d+3059501]
	GetHandleVerifier [0x0x7ff607b10bfd+3035885]
	GetHandleVerifier [0x0x7ff607b303f0+3164896]
	GetHandleVerifier [0x0x7ff607858c2e+185118]
	GetHandleVerifier [0x0x7ff60786053f+216111]
	GetHandleVerifier [0x0x7ff6078472d4+113092]
	GetHandleVerifier [0x0x7ff607847489+113529]
	GetHandleVerifier [0x0x7ff60782e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-07-31 11:12:01,665 - INFO - 防检测机制设置完成
2025-07-31 11:12:01,665 - INFO - 浏览器驱动初始化成功
2025-07-31 11:12:01,672 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff60783e925+77845]
	GetHandleVerifier [0x0x7ff60783e980+77936]
	(No symbol) [0x0x7ff6075f9b0c]
	(No symbol) [0x0x7ff6076b5b46]
	(No symbol) [0x0x7ff6076788ca]
	(No symbol) [0x0x7ff6076a0b07]
	(No symbol) [0x0x7ff6076786a3]
	(No symbol) [0x0x7ff607641791]
	(No symbol) [0x0x7ff607642523]
	GetHandleVerifier [0x0x7ff607b1683d+3059501]
	GetHandleVerifier [0x0x7ff607b10bfd+3035885]
	GetHandleVerifier [0x0x7ff607b303f0+3164896]
	GetHandleVerifier [0x0x7ff607858c2e+185118]
	GetHandleVerifier [0x0x7ff60786053f+216111]
	GetHandleVerifier [0x0x7ff6078472d4+113092]
	GetHandleVerifier [0x0x7ff607847489+113529]
	GetHandleVerifier [0x0x7ff60782e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-07-31 11:12:01,675 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff60783e925+77845]
	GetHandleVerifier [0x0x7ff60783e980+77936]
	(No symbol) [0x0x7ff6075f9b0c]
	(No symbol) [0x0x7ff6076b5b46]
	(No symbol) [0x0x7ff6076788ca]
	(No symbol) [0x0x7ff6076a0b07]
	(No symbol) [0x0x7ff6076786a3]
	(No symbol) [0x0x7ff607641791]
	(No symbol) [0x0x7ff607642523]
	GetHandleVerifier [0x0x7ff607b1683d+3059501]
	GetHandleVerifier [0x0x7ff607b10bfd+3035885]
	GetHandleVerifier [0x0x7ff607b303f0+3164896]
	GetHandleVerifier [0x0x7ff607858c2e+185118]
	GetHandleVerifier [0x0x7ff60786053f+216111]
	GetHandleVerifier [0x0x7ff6078472d4+113092]
	GetHandleVerifier [0x0x7ff607847489+113529]
	GetHandleVerifier [0x0x7ff60782e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-07-31 11:12:01,679 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff60783e925+77845]
	GetHandleVerifier [0x0x7ff60783e980+77936]
	(No symbol) [0x0x7ff6075f9b0c]
	(No symbol) [0x0x7ff6076b5b46]
	(No symbol) [0x0x7ff6076788ca]
	(No symbol) [0x0x7ff6076a0b07]
	(No symbol) [0x0x7ff6076786a3]
	(No symbol) [0x0x7ff607641791]
	(No symbol) [0x0x7ff607642523]
	GetHandleVerifier [0x0x7ff607b1683d+3059501]
	GetHandleVerifier [0x0x7ff607b10bfd+3035885]
	GetHandleVerifier [0x0x7ff607b303f0+3164896]
	GetHandleVerifier [0x0x7ff607858c2e+185118]
	GetHandleVerifier [0x0x7ff60786053f+216111]
	GetHandleVerifier [0x0x7ff6078472d4+113092]
	GetHandleVerifier [0x0x7ff607847489+113529]
	GetHandleVerifier [0x0x7ff60782e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-07-31 11:12:01,682 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff60783e925+77845]
	GetHandleVerifier [0x0x7ff60783e980+77936]
	(No symbol) [0x0x7ff6075f9b0c]
	(No symbol) [0x0x7ff6076b5b46]
	(No symbol) [0x0x7ff6076788ca]
	(No symbol) [0x0x7ff6076a0b07]
	(No symbol) [0x0x7ff6076786a3]
	(No symbol) [0x0x7ff607641791]
	(No symbol) [0x0x7ff607642523]
	GetHandleVerifier [0x0x7ff607b1683d+3059501]
	GetHandleVerifier [0x0x7ff607b10bfd+3035885]
	GetHandleVerifier [0x0x7ff607b303f0+3164896]
	GetHandleVerifier [0x0x7ff607858c2e+185118]
	GetHandleVerifier [0x0x7ff60786053f+216111]
	GetHandleVerifier [0x0x7ff6078472d4+113092]
	GetHandleVerifier [0x0x7ff607847489+113529]
	GetHandleVerifier [0x0x7ff60782e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-07-31 11:12:01,686 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff60783e925+77845]
	GetHandleVerifier [0x0x7ff60783e980+77936]
	(No symbol) [0x0x7ff6075f9b0c]
	(No symbol) [0x0x7ff6076b5b46]
	(No symbol) [0x0x7ff6076788ca]
	(No symbol) [0x0x7ff6076a0b07]
	(No symbol) [0x0x7ff6076786a3]
	(No symbol) [0x0x7ff607641791]
	(No symbol) [0x0x7ff607642523]
	GetHandleVerifier [0x0x7ff607b1683d+3059501]
	GetHandleVerifier [0x0x7ff607b10bfd+3035885]
	GetHandleVerifier [0x0x7ff607b303f0+3164896]
	GetHandleVerifier [0x0x7ff607858c2e+185118]
	GetHandleVerifier [0x0x7ff60786053f+216111]
	GetHandleVerifier [0x0x7ff6078472d4+113092]
	GetHandleVerifier [0x0x7ff607847489+113529]
	GetHandleVerifier [0x0x7ff60782e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-07-31 11:12:01,690 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff60783e925+77845]
	GetHandleVerifier [0x0x7ff60783e980+77936]
	(No symbol) [0x0x7ff6075f9b0c]
	(No symbol) [0x0x7ff6076b5b46]
	(No symbol) [0x0x7ff6076788ca]
	(No symbol) [0x0x7ff6076a0b07]
	(No symbol) [0x0x7ff6076786a3]
	(No symbol) [0x0x7ff607641791]
	(No symbol) [0x0x7ff607642523]
	GetHandleVerifier [0x0x7ff607b1683d+3059501]
	GetHandleVerifier [0x0x7ff607b10bfd+3035885]
	GetHandleVerifier [0x0x7ff607b303f0+3164896]
	GetHandleVerifier [0x0x7ff607858c2e+185118]
	GetHandleVerifier [0x0x7ff60786053f+216111]
	GetHandleVerifier [0x0x7ff6078472d4+113092]
	GetHandleVerifier [0x0x7ff607847489+113529]
	GetHandleVerifier [0x0x7ff60782e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-07-31 11:12:01,693 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff60783e925+77845]
	GetHandleVerifier [0x0x7ff60783e980+77936]
	(No symbol) [0x0x7ff6075f9b0c]
	(No symbol) [0x0x7ff6076b5b46]
	(No symbol) [0x0x7ff6076788ca]
	(No symbol) [0x0x7ff6076a0b07]
	(No symbol) [0x0x7ff6076786a3]
	(No symbol) [0x0x7ff607641791]
	(No symbol) [0x0x7ff607642523]
	GetHandleVerifier [0x0x7ff607b1683d+3059501]
	GetHandleVerifier [0x0x7ff607b10bfd+3035885]
	GetHandleVerifier [0x0x7ff607b303f0+3164896]
	GetHandleVerifier [0x0x7ff607858c2e+185118]
	GetHandleVerifier [0x0x7ff60786053f+216111]
	GetHandleVerifier [0x0x7ff6078472d4+113092]
	GetHandleVerifier [0x0x7ff607847489+113529]
	GetHandleVerifier [0x0x7ff60782e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-07-31 11:12:01,699 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff60783e925+77845]
	GetHandleVerifier [0x0x7ff60783e980+77936]
	(No symbol) [0x0x7ff6075f9b0c]
	(No symbol) [0x0x7ff6076b5b46]
	(No symbol) [0x0x7ff6076788ca]
	(No symbol) [0x0x7ff6076a0b07]
	(No symbol) [0x0x7ff6076786a3]
	(No symbol) [0x0x7ff607641791]
	(No symbol) [0x0x7ff607642523]
	GetHandleVerifier [0x0x7ff607b1683d+3059501]
	GetHandleVerifier [0x0x7ff607b10bfd+3035885]
	GetHandleVerifier [0x0x7ff607b303f0+3164896]
	GetHandleVerifier [0x0x7ff607858c2e+185118]
	GetHandleVerifier [0x0x7ff60786053f+216111]
	GetHandleVerifier [0x0x7ff6078472d4+113092]
	GetHandleVerifier [0x0x7ff607847489+113529]
	GetHandleVerifier [0x0x7ff60782e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-07-31 11:12:01,703 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff60783e925+77845]
	GetHandleVerifier [0x0x7ff60783e980+77936]
	(No symbol) [0x0x7ff6075f9b0c]
	(No symbol) [0x0x7ff6076b5b46]
	(No symbol) [0x0x7ff6076788ca]
	(No symbol) [0x0x7ff6076a0b07]
	(No symbol) [0x0x7ff6076786a3]
	(No symbol) [0x0x7ff607641791]
	(No symbol) [0x0x7ff607642523]
	GetHandleVerifier [0x0x7ff607b1683d+3059501]
	GetHandleVerifier [0x0x7ff607b10bfd+3035885]
	GetHandleVerifier [0x0x7ff607b303f0+3164896]
	GetHandleVerifier [0x0x7ff607858c2e+185118]
	GetHandleVerifier [0x0x7ff60786053f+216111]
	GetHandleVerifier [0x0x7ff6078472d4+113092]
	GetHandleVerifier [0x0x7ff607847489+113529]
	GetHandleVerifier [0x0x7ff60782e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-07-31 11:12:01,706 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff60783e925+77845]
	GetHandleVerifier [0x0x7ff60783e980+77936]
	(No symbol) [0x0x7ff6075f9b0c]
	(No symbol) [0x0x7ff6076b5b46]
	(No symbol) [0x0x7ff6076788ca]
	(No symbol) [0x0x7ff6076a0b07]
	(No symbol) [0x0x7ff6076786a3]
	(No symbol) [0x0x7ff607641791]
	(No symbol) [0x0x7ff607642523]
	GetHandleVerifier [0x0x7ff607b1683d+3059501]
	GetHandleVerifier [0x0x7ff607b10bfd+3035885]
	GetHandleVerifier [0x0x7ff607b303f0+3164896]
	GetHandleVerifier [0x0x7ff607858c2e+185118]
	GetHandleVerifier [0x0x7ff60786053f+216111]
	GetHandleVerifier [0x0x7ff6078472d4+113092]
	GetHandleVerifier [0x0x7ff607847489+113529]
	GetHandleVerifier [0x0x7ff60782e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-07-31 11:12:01,710 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff60783e925+77845]
	GetHandleVerifier [0x0x7ff60783e980+77936]
	(No symbol) [0x0x7ff6075f9b0c]
	(No symbol) [0x0x7ff6076b5b46]
	(No symbol) [0x0x7ff6076788ca]
	(No symbol) [0x0x7ff6076a0b07]
	(No symbol) [0x0x7ff6076786a3]
	(No symbol) [0x0x7ff607641791]
	(No symbol) [0x0x7ff607642523]
	GetHandleVerifier [0x0x7ff607b1683d+3059501]
	GetHandleVerifier [0x0x7ff607b10bfd+3035885]
	GetHandleVerifier [0x0x7ff607b303f0+3164896]
	GetHandleVerifier [0x0x7ff607858c2e+185118]
	GetHandleVerifier [0x0x7ff60786053f+216111]
	GetHandleVerifier [0x0x7ff6078472d4+113092]
	GetHandleVerifier [0x0x7ff607847489+113529]
	GetHandleVerifier [0x0x7ff60782e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-07-31 11:12:01,714 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff60783e925+77845]
	GetHandleVerifier [0x0x7ff60783e980+77936]
	(No symbol) [0x0x7ff6075f9b0c]
	(No symbol) [0x0x7ff6076b5b46]
	(No symbol) [0x0x7ff6076788ca]
	(No symbol) [0x0x7ff6076a0b07]
	(No symbol) [0x0x7ff6076786a3]
	(No symbol) [0x0x7ff607641791]
	(No symbol) [0x0x7ff607642523]
	GetHandleVerifier [0x0x7ff607b1683d+3059501]
	GetHandleVerifier [0x0x7ff607b10bfd+3035885]
	GetHandleVerifier [0x0x7ff607b303f0+3164896]
	GetHandleVerifier [0x0x7ff607858c2e+185118]
	GetHandleVerifier [0x0x7ff60786053f+216111]
	GetHandleVerifier [0x0x7ff6078472d4+113092]
	GetHandleVerifier [0x0x7ff607847489+113529]
	GetHandleVerifier [0x0x7ff60782e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-07-31 11:12:01,717 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff60783e925+77845]
	GetHandleVerifier [0x0x7ff60783e980+77936]
	(No symbol) [0x0x7ff6075f9b0c]
	(No symbol) [0x0x7ff6076b5b46]
	(No symbol) [0x0x7ff6076788ca]
	(No symbol) [0x0x7ff6076a0b07]
	(No symbol) [0x0x7ff6076786a3]
	(No symbol) [0x0x7ff607641791]
	(No symbol) [0x0x7ff607642523]
	GetHandleVerifier [0x0x7ff607b1683d+3059501]
	GetHandleVerifier [0x0x7ff607b10bfd+3035885]
	GetHandleVerifier [0x0x7ff607b303f0+3164896]
	GetHandleVerifier [0x0x7ff607858c2e+185118]
	GetHandleVerifier [0x0x7ff60786053f+216111]
	GetHandleVerifier [0x0x7ff6078472d4+113092]
	GetHandleVerifier [0x0x7ff607847489+113529]
	GetHandleVerifier [0x0x7ff60782e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-07-31 11:12:01,722 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff60783e925+77845]
	GetHandleVerifier [0x0x7ff60783e980+77936]
	(No symbol) [0x0x7ff6075f9b0c]
	(No symbol) [0x0x7ff6076b5b46]
	(No symbol) [0x0x7ff6076788ca]
	(No symbol) [0x0x7ff6076a0b07]
	(No symbol) [0x0x7ff6076786a3]
	(No symbol) [0x0x7ff607641791]
	(No symbol) [0x0x7ff607642523]
	GetHandleVerifier [0x0x7ff607b1683d+3059501]
	GetHandleVerifier [0x0x7ff607b10bfd+3035885]
	GetHandleVerifier [0x0x7ff607b303f0+3164896]
	GetHandleVerifier [0x0x7ff607858c2e+185118]
	GetHandleVerifier [0x0x7ff60786053f+216111]
	GetHandleVerifier [0x0x7ff6078472d4+113092]
	GetHandleVerifier [0x0x7ff607847489+113529]
	GetHandleVerifier [0x0x7ff60782e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-07-31 11:12:01,726 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff60783e925+77845]
	GetHandleVerifier [0x0x7ff60783e980+77936]
	(No symbol) [0x0x7ff6075f9b0c]
	(No symbol) [0x0x7ff6076b5b46]
	(No symbol) [0x0x7ff6076788ca]
	(No symbol) [0x0x7ff6076a0b07]
	(No symbol) [0x0x7ff6076786a3]
	(No symbol) [0x0x7ff607641791]
	(No symbol) [0x0x7ff607642523]
	GetHandleVerifier [0x0x7ff607b1683d+3059501]
	GetHandleVerifier [0x0x7ff607b10bfd+3035885]
	GetHandleVerifier [0x0x7ff607b303f0+3164896]
	GetHandleVerifier [0x0x7ff607858c2e+185118]
	GetHandleVerifier [0x0x7ff60786053f+216111]
	GetHandleVerifier [0x0x7ff6078472d4+113092]
	GetHandleVerifier [0x0x7ff607847489+113529]
	GetHandleVerifier [0x0x7ff60782e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-07-31 11:12:01,729 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff60783e925+77845]
	GetHandleVerifier [0x0x7ff60783e980+77936]
	(No symbol) [0x0x7ff6075f9b0c]
	(No symbol) [0x0x7ff6076b5b46]
	(No symbol) [0x0x7ff6076788ca]
	(No symbol) [0x0x7ff6076a0b07]
	(No symbol) [0x0x7ff6076786a3]
	(No symbol) [0x0x7ff607641791]
	(No symbol) [0x0x7ff607642523]
	GetHandleVerifier [0x0x7ff607b1683d+3059501]
	GetHandleVerifier [0x0x7ff607b10bfd+3035885]
	GetHandleVerifier [0x0x7ff607b303f0+3164896]
	GetHandleVerifier [0x0x7ff607858c2e+185118]
	GetHandleVerifier [0x0x7ff60786053f+216111]
	GetHandleVerifier [0x0x7ff6078472d4+113092]
	GetHandleVerifier [0x0x7ff607847489+113529]
	GetHandleVerifier [0x0x7ff60782e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-07-31 11:12:01,731 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff60783e925+77845]
	GetHandleVerifier [0x0x7ff60783e980+77936]
	(No symbol) [0x0x7ff6075f9b0c]
	(No symbol) [0x0x7ff6076b5b46]
	(No symbol) [0x0x7ff6076788ca]
	(No symbol) [0x0x7ff6076a0b07]
	(No symbol) [0x0x7ff6076786a3]
	(No symbol) [0x0x7ff607641791]
	(No symbol) [0x0x7ff607642523]
	GetHandleVerifier [0x0x7ff607b1683d+3059501]
	GetHandleVerifier [0x0x7ff607b10bfd+3035885]
	GetHandleVerifier [0x0x7ff607b303f0+3164896]
	GetHandleVerifier [0x0x7ff607858c2e+185118]
	GetHandleVerifier [0x0x7ff60786053f+216111]
	GetHandleVerifier [0x0x7ff6078472d4+113092]
	GetHandleVerifier [0x0x7ff607847489+113529]
	GetHandleVerifier [0x0x7ff60782e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-07-31 11:12:01,734 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff60783e925+77845]
	GetHandleVerifier [0x0x7ff60783e980+77936]
	(No symbol) [0x0x7ff6075f9b0c]
	(No symbol) [0x0x7ff6076b5b46]
	(No symbol) [0x0x7ff6076788ca]
	(No symbol) [0x0x7ff6076a0b07]
	(No symbol) [0x0x7ff6076786a3]
	(No symbol) [0x0x7ff607641791]
	(No symbol) [0x0x7ff607642523]
	GetHandleVerifier [0x0x7ff607b1683d+3059501]
	GetHandleVerifier [0x0x7ff607b10bfd+3035885]
	GetHandleVerifier [0x0x7ff607b303f0+3164896]
	GetHandleVerifier [0x0x7ff607858c2e+185118]
	GetHandleVerifier [0x0x7ff60786053f+216111]
	GetHandleVerifier [0x0x7ff6078472d4+113092]
	GetHandleVerifier [0x0x7ff607847489+113529]
	GetHandleVerifier [0x0x7ff60782e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-07-31 11:12:01,737 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff60783e925+77845]
	GetHandleVerifier [0x0x7ff60783e980+77936]
	(No symbol) [0x0x7ff6075f9b0c]
	(No symbol) [0x0x7ff6076b5b46]
	(No symbol) [0x0x7ff6076788ca]
	(No symbol) [0x0x7ff6076a0b07]
	(No symbol) [0x0x7ff6076786a3]
	(No symbol) [0x0x7ff607641791]
	(No symbol) [0x0x7ff607642523]
	GetHandleVerifier [0x0x7ff607b1683d+3059501]
	GetHandleVerifier [0x0x7ff607b10bfd+3035885]
	GetHandleVerifier [0x0x7ff607b303f0+3164896]
	GetHandleVerifier [0x0x7ff607858c2e+185118]
	GetHandleVerifier [0x0x7ff60786053f+216111]
	GetHandleVerifier [0x0x7ff6078472d4+113092]
	GetHandleVerifier [0x0x7ff607847489+113529]
	GetHandleVerifier [0x0x7ff60782e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-07-31 11:12:01,741 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff60783e925+77845]
	GetHandleVerifier [0x0x7ff60783e980+77936]
	(No symbol) [0x0x7ff6075f9b0c]
	(No symbol) [0x0x7ff6076b5b46]
	(No symbol) [0x0x7ff6076788ca]
	(No symbol) [0x0x7ff6076a0b07]
	(No symbol) [0x0x7ff6076786a3]
	(No symbol) [0x0x7ff607641791]
	(No symbol) [0x0x7ff607642523]
	GetHandleVerifier [0x0x7ff607b1683d+3059501]
	GetHandleVerifier [0x0x7ff607b10bfd+3035885]
	GetHandleVerifier [0x0x7ff607b303f0+3164896]
	GetHandleVerifier [0x0x7ff607858c2e+185118]
	GetHandleVerifier [0x0x7ff60786053f+216111]
	GetHandleVerifier [0x0x7ff6078472d4+113092]
	GetHandleVerifier [0x0x7ff607847489+113529]
	GetHandleVerifier [0x0x7ff60782e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-07-31 11:12:01,744 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff60783e925+77845]
	GetHandleVerifier [0x0x7ff60783e980+77936]
	(No symbol) [0x0x7ff6075f9b0c]
	(No symbol) [0x0x7ff6076b5b46]
	(No symbol) [0x0x7ff6076788ca]
	(No symbol) [0x0x7ff6076a0b07]
	(No symbol) [0x0x7ff6076786a3]
	(No symbol) [0x0x7ff607641791]
	(No symbol) [0x0x7ff607642523]
	GetHandleVerifier [0x0x7ff607b1683d+3059501]
	GetHandleVerifier [0x0x7ff607b10bfd+3035885]
	GetHandleVerifier [0x0x7ff607b303f0+3164896]
	GetHandleVerifier [0x0x7ff607858c2e+185118]
	GetHandleVerifier [0x0x7ff60786053f+216111]
	GetHandleVerifier [0x0x7ff6078472d4+113092]
	GetHandleVerifier [0x0x7ff607847489+113529]
	GetHandleVerifier [0x0x7ff60782e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-07-31 11:12:01,744 - INFO - Cookies已加载
2025-07-31 11:12:01,745 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?channel=global_zx&spm=a21ug5.29159167.6452766900.2ndview_zxmore_click
2025-07-31 11:12:07,108 - INFO - 当前User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-31 11:12:07,108 - INFO - 确认：使用桌面版User-Agent
2025-07-31 11:12:07,113 - INFO - 桌面版User-Agent设置完成: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-31 11:12:08,097 - INFO - 成功导航到目标页面
2025-07-31 11:12:08,098 - INFO - 登录成功！现在可以开始铺货任务
2025-07-31 11:12:14,060 - INFO - Cookies已保存
2025-07-31 11:12:16,351 - INFO - 浏览器已关闭
2025-07-31 11:12:16,352 - INFO - 以无界面(headless)模式启动Chrome，仅添加headless、no-sandbox、disable-dev-shm-usage参数
2025-07-31 11:12:18,181 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff60783e925+77845]
	GetHandleVerifier [0x0x7ff60783e980+77936]
	(No symbol) [0x0x7ff6075f9cda]
	(No symbol) [0x0x7ff607601679]
	(No symbol) [0x0x7ff607604a61]
	(No symbol) [0x0x7ff6076a1e4b]
	(No symbol) [0x0x7ff6076788ca]
	(No symbol) [0x0x7ff6076a0b07]
	(No symbol) [0x0x7ff6076786a3]
	(No symbol) [0x0x7ff607641791]
	(No symbol) [0x0x7ff607642523]
	GetHandleVerifier [0x0x7ff607b1683d+3059501]
	GetHandleVerifier [0x0x7ff607b10bfd+3035885]
	GetHandleVerifier [0x0x7ff607b303f0+3164896]
	GetHandleVerifier [0x0x7ff607858c2e+185118]
	GetHandleVerifier [0x0x7ff60786053f+216111]
	GetHandleVerifier [0x0x7ff6078472d4+113092]
	GetHandleVerifier [0x0x7ff607847489+113529]
	GetHandleVerifier [0x0x7ff60782e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-07-31 11:12:18,181 - INFO - 防检测机制设置完成
2025-07-31 11:12:18,181 - INFO - 浏览器驱动初始化成功
2025-07-31 11:12:18,181 - INFO - 开始自动化铺货流程（无头模式）
2025-07-31 11:12:18,181 - INFO - 第一步：随机选择了【运动户外】分类页面
2025-07-31 11:12:18,182 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?cateName=%25E8%25BF%2590%25E5%258A%25A8%25E6%2588%25B7%25E5%25A4%2596&categoryList=50010728%2C50013886%2C50011699%2C50012029%2C50510002&keyword=&spm=a21ug5.29159167.9840561350.showIndustry
2025-07-31 11:12:21,693 - INFO - 第一步：点击登录按钮
2025-07-31 11:12:21,718 - INFO - 找到登录按钮，使用选择器: //*[@id='login-form']/div[6]/button
2025-07-31 11:12:22,743 - INFO - 准备点击登录按钮: 标签=button, 文本='登录', 选择器=//*[@id='login-form']/div[6]/button
2025-07-31 11:12:26,853 - INFO - 准备点击元素: tag=button, text='登录', class='fm-button fm-submit password-l', id=''
2025-07-31 11:12:28,360 - INFO - 尝试ActionChains点击...
2025-07-31 11:12:28,852 - INFO - ActionChains点击成功
2025-07-31 11:12:29,102 - INFO - 登录按钮点击成功
2025-07-31 11:12:29,102 - INFO - 登录按钮点击成功
2025-07-31 11:12:32,103 - INFO - 第二步：获取账号信息...
2025-07-31 11:12:32,103 - INFO - 正在跳转到个人中心页面: https://jingya.taobao.com/?v=2
2025-07-31 11:12:57,113 - INFO - 尝试获取账号名称，使用XPath: //*[@id='workbench-user-tool-btn']/div/div[2]
2025-07-31 11:12:57,129 - INFO - 成功获取账号信息: 熙阳13店:茉茉
2025-07-31 11:12:57,129 - INFO - 获取到账号名称: 熙阳13店:茉茉
2025-07-31 11:12:57,132 - INFO - GUI界面已更新账号显示: 熙阳13店:茉茉
2025-07-31 11:12:57,133 - INFO - 第三步：开始读取60个商品信息...
2025-07-31 11:12:57,134 - INFO - 开始爬取商品信息，最大数量: 60
2025-07-31 11:12:57,134 - INFO - 使用目标URL: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?cateName=%25E8%25BF%2590%25E5%258A%25A8%25E6%2588%25B7%25E5%25A4%2596&categoryList=50010728%2C50013886%2C50011699%2C50012029%2C50510002&keyword=&spm=a21ug5.29159167.9840561350.showIndustry
2025-07-31 11:13:00,223 - INFO - 正在爬取第 1 页商品信息...
2025-07-31 11:13:02,230 - INFO - 找到商品容器，开始爬取商品，最大数量: 60
2025-07-31 11:13:02,249 - INFO - 找到商品项 1，检查元素信息...
2025-07-31 11:13:02,263 - INFO - 商品项 1 - 元素标签: div, class: , 有data-autolog: False
2025-07-31 11:13:02,263 - INFO - 商品项 1 当前元素无data-autolog，查找子元素...
2025-07-31 11:13:02,277 - INFO - 商品项 1 找到子元素，class: tfx-item
2025-07-31 11:13:02,279 - INFO - 商品项 1 直接获取到data-autolog: item_id=745745162451&path=search_goods_card&type=category
2025-07-31 11:13:02,280 - INFO - 商品项 1 data-autolog: item_id=745745162451&path=search_goods_card&type=category
2025-07-31 11:13:02,280 - INFO - 商品项 1 提取到item_id: 745745162451
2025-07-31 11:13:02,292 - INFO - 商品项 1 商品名称: 保税直发阿迪达斯Adidas Samba 德训男休闲经典黑白女板鞋 B75806
2025-07-31 11:13:02,305 - INFO - 找到商品项 2，检查元素信息...
2025-07-31 11:13:02,315 - INFO - 商品项 2 - 元素标签: div, class: , 有data-autolog: False
2025-07-31 11:13:02,315 - INFO - 商品项 2 当前元素无data-autolog，查找子元素...
2025-07-31 11:13:02,321 - INFO - 商品项 2 找到子元素，class: tfx-item
2025-07-31 11:13:02,324 - INFO - 商品项 2 直接获取到data-autolog: item_id=734534774113&path=search_goods_card&index=1&type=category
2025-07-31 11:13:02,324 - INFO - 商品项 2 data-autolog: item_id=734534774113&path=search_goods_card&index=1&type=category
2025-07-31 11:13:02,325 - INFO - 商品项 2 提取到item_id: 734534774113
2025-07-31 11:13:02,334 - INFO - 商品项 2 商品名称: 保税直发Adidas originals Superstar经典金标贝壳头女鞋FU7712
2025-07-31 11:13:02,346 - INFO - 找到商品项 3，检查元素信息...
2025-07-31 11:13:02,354 - INFO - 商品项 3 - 元素标签: div, class: , 有data-autolog: False
2025-07-31 11:13:02,354 - INFO - 商品项 3 当前元素无data-autolog，查找子元素...
2025-07-31 11:13:02,362 - INFO - 商品项 3 找到子元素，class: tfx-item
2025-07-31 11:13:02,365 - INFO - 商品项 3 直接获取到data-autolog: item_id=743822760544&path=search_goods_card&index=2&type=category
2025-07-31 11:13:02,366 - INFO - 商品项 3 data-autolog: item_id=743822760544&path=search_goods_card&index=2&type=category
2025-07-31 11:13:02,366 - INFO - 商品项 3 提取到item_id: 743822760544
2025-07-31 11:13:02,374 - INFO - 商品项 3 商品名称: 保税直发阿迪达斯Adidas Samba 德训男女休闲黑白经典板鞋B75806
2025-07-31 11:13:02,386 - INFO - 找到商品项 4，检查元素信息...
2025-07-31 11:13:02,394 - INFO - 商品项 4 - 元素标签: div, class: , 有data-autolog: False
2025-07-31 11:13:02,394 - INFO - 商品项 4 当前元素无data-autolog，查找子元素...
2025-07-31 11:13:02,401 - INFO - 商品项 4 找到子元素，class: tfx-item
2025-07-31 11:13:02,404 - INFO - 商品项 4 直接获取到data-autolog: item_id=769056228826&path=search_goods_card&index=3&type=category
2025-07-31 11:13:02,404 - INFO - 商品项 4 data-autolog: item_id=769056228826&path=search_goods_card&index=3&type=category
2025-07-31 11:13:02,404 - INFO - 商品项 4 提取到item_id: 769056228826
2025-07-31 11:13:02,414 - INFO - 商品项 4 商品名称: 保税直发Adidas阿迪达斯 SAMBA OG经典德训男女休闲鞋板鞋 B75807
2025-07-31 11:13:02,426 - INFO - 找到商品项 5，检查元素信息...
2025-07-31 11:13:02,434 - INFO - 商品项 5 - 元素标签: div, class: , 有data-autolog: False
2025-07-31 11:13:02,434 - INFO - 商品项 5 当前元素无data-autolog，查找子元素...
2025-07-31 11:13:02,441 - INFO - 商品项 5 找到子元素，class: tfx-item
2025-07-31 11:13:02,444 - INFO - 商品项 5 直接获取到data-autolog: item_id=744687180064&path=search_goods_card&index=4&type=category
2025-07-31 11:13:02,444 - INFO - 商品项 5 data-autolog: item_id=744687180064&path=search_goods_card&index=4&type=category
2025-07-31 11:13:02,445 - INFO - 商品项 5 提取到item_id: 744687180064
2025-07-31 11:13:02,536 - INFO - 商品项 5 商品名称: 保税直发阿迪达斯Adidas Samba 德训男女黑白经典休闲板鞋 B75806
2025-07-31 11:13:02,560 - INFO - 找到商品项 6，检查元素信息...
2025-07-31 11:13:02,578 - INFO - 商品项 6 - 元素标签: div, class: , 有data-autolog: False
2025-07-31 11:13:02,578 - INFO - 商品项 6 当前元素无data-autolog，查找子元素...
2025-07-31 11:13:02,587 - INFO - 商品项 6 找到子元素，class: tfx-item
2025-07-31 11:13:02,591 - INFO - 商品项 6 直接获取到data-autolog: item_id=731371010222&path=search_goods_card&index=5&type=category
2025-07-31 11:13:02,591 - INFO - 商品项 6 data-autolog: item_id=731371010222&path=search_goods_card&index=5&type=category
2025-07-31 11:13:02,591 - INFO - 商品项 6 提取到item_id: 731371010222
2025-07-31 11:13:02,600 - INFO - 商品项 6 商品名称: 保税直发Adidas originals Superstar金标贝壳头经典男女鞋FU7712
2025-07-31 11:13:02,614 - INFO - 找到商品项 7，检查元素信息...
2025-07-31 11:13:02,623 - INFO - 商品项 7 - 元素标签: div, class: , 有data-autolog: False
2025-07-31 11:13:02,623 - INFO - 商品项 7 当前元素无data-autolog，查找子元素...
2025-07-31 11:13:02,631 - INFO - 商品项 7 找到子元素，class: tfx-item
2025-07-31 11:13:02,634 - INFO - 商品项 7 直接获取到data-autolog: item_id=688379934306&path=search_goods_card&index=6&type=category
2025-07-31 11:13:02,635 - INFO - 商品项 7 data-autolog: item_id=688379934306&path=search_goods_card&index=6&type=category
2025-07-31 11:13:02,635 - INFO - 商品项 7 提取到item_id: 688379934306
2025-07-31 11:13:02,643 - INFO - 商品项 7 商品名称: Fila女士长裤1560450
2025-07-31 11:13:02,654 - INFO - 找到商品项 8，检查元素信息...
2025-07-31 11:13:02,664 - INFO - 商品项 8 - 元素标签: div, class: , 有data-autolog: False
2025-07-31 11:13:02,664 - INFO - 商品项 8 当前元素无data-autolog，查找子元素...
2025-07-31 11:13:02,671 - INFO - 商品项 8 找到子元素，class: tfx-item
2025-07-31 11:13:02,674 - INFO - 商品项 8 直接获取到data-autolog: item_id=745585097406&path=search_goods_card&index=7&type=category
2025-07-31 11:13:02,675 - INFO - 商品项 8 data-autolog: item_id=745585097406&path=search_goods_card&index=7&type=category
2025-07-31 11:13:02,675 - INFO - 商品项 8 提取到item_id: 745585097406
2025-07-31 11:13:02,682 - INFO - 商品项 8 商品名称: 保税直发Adidas originals Superstar金标贝壳头经典女GS鞋FU7712
2025-07-31 11:13:02,695 - INFO - 找到商品项 9，检查元素信息...
2025-07-31 11:13:02,703 - INFO - 商品项 9 - 元素标签: div, class: , 有data-autolog: False
2025-07-31 11:13:02,703 - INFO - 商品项 9 当前元素无data-autolog，查找子元素...
2025-07-31 11:13:02,709 - INFO - 商品项 9 找到子元素，class: tfx-item
2025-07-31 11:13:02,714 - INFO - 商品项 9 直接获取到data-autolog: item_id=692964505052&path=search_goods_card&index=8&type=category
2025-07-31 11:13:02,714 - INFO - 商品项 9 data-autolog: item_id=692964505052&path=search_goods_card&index=8&type=category
2025-07-31 11:13:02,714 - INFO - 商品项 9 提取到item_id: 692964505052
2025-07-31 11:13:02,722 - INFO - 商品项 9 商品名称: Pu*ma裤子1552279
2025-07-31 11:13:02,758 - INFO - 找到商品项 10，检查元素信息...
2025-07-31 11:13:02,766 - INFO - 商品项 10 - 元素标签: div, class: , 有data-autolog: False
2025-07-31 11:13:02,766 - INFO - 商品项 10 当前元素无data-autolog，查找子元素...
2025-07-31 11:13:02,774 - INFO - 商品项 10 找到子元素，class: tfx-item
2025-07-31 11:13:02,776 - INFO - 商品项 10 直接获取到data-autolog: item_id=747893360498&path=search_goods_card&index=9&type=category
2025-07-31 11:13:02,776 - INFO - 商品项 10 data-autolog: item_id=747893360498&path=search_goods_card&index=9&type=category
2025-07-31 11:13:02,776 - INFO - 商品项 10 提取到item_id: 747893360498
2025-07-31 11:13:02,786 - INFO - 商品项 10 商品名称: 保税直发Adidas originals Superstar金标贝壳头经典女鞋FU7712
2025-07-31 11:13:02,796 - INFO - 找到商品项 11，检查元素信息...
2025-07-31 11:13:02,804 - INFO - 商品项 11 - 元素标签: div, class: , 有data-autolog: False
2025-07-31 11:13:02,804 - INFO - 商品项 11 当前元素无data-autolog，查找子元素...
2025-07-31 11:13:02,812 - INFO - 商品项 11 找到子元素，class: tfx-item
2025-07-31 11:13:02,815 - INFO - 商品项 11 直接获取到data-autolog: item_id=635434108085&path=search_goods_card&index=10&type=category
2025-07-31 11:13:02,815 - INFO - 商品项 11 data-autolog: item_id=635434108085&path=search_goods_card&index=10&type=category
2025-07-31 11:13:02,815 - INFO - 商品项 11 提取到item_id: 635434108085
2025-07-31 11:13:02,825 - INFO - 商品项 11 商品名称: 美国NeilMed成人儿童洗鼻壶鼻腔冲洗器海盐水清洗鼻子瑜伽鼻喷
2025-07-31 11:13:02,837 - INFO - 找到商品项 12，检查元素信息...
2025-07-31 11:13:02,845 - INFO - 商品项 12 - 元素标签: div, class: , 有data-autolog: False
2025-07-31 11:13:02,845 - INFO - 商品项 12 当前元素无data-autolog，查找子元素...
2025-07-31 11:13:02,852 - INFO - 商品项 12 找到子元素，class: tfx-item
2025-07-31 11:13:02,855 - INFO - 商品项 12 直接获取到data-autolog: item_id=743748247840&path=search_goods_card&index=11&type=category
2025-07-31 11:13:02,855 - INFO - 商品项 12 data-autolog: item_id=743748247840&path=search_goods_card&index=11&type=category
2025-07-31 11:13:02,855 - INFO - 商品项 12 提取到item_id: 743748247840
2025-07-31 11:13:02,866 - INFO - 商品项 12 商品名称: 保税直发阿迪达斯Adidas Samba 德训男女休闲黑白经典板鞋B75806
2025-07-31 11:13:02,878 - INFO - 找到商品项 13，检查元素信息...
2025-07-31 11:13:02,885 - INFO - 商品项 13 - 元素标签: div, class: , 有data-autolog: False
2025-07-31 11:13:02,885 - INFO - 商品项 13 当前元素无data-autolog，查找子元素...
2025-07-31 11:13:02,892 - INFO - 商品项 13 找到子元素，class: tfx-item
2025-07-31 11:13:02,896 - INFO - 商品项 13 直接获取到data-autolog: item_id=749228740074&path=search_goods_card&index=12&type=category
2025-07-31 11:13:02,896 - INFO - 商品项 13 data-autolog: item_id=749228740074&path=search_goods_card&index=12&type=category
2025-07-31 11:13:02,896 - INFO - 商品项 13 提取到item_id: 749228740074
2025-07-31 11:13:02,906 - INFO - 商品项 13 商品名称: 保税直发Adidas Superstar金标贝壳头黑白经典休闲男女鞋EG4959
2025-07-31 11:13:02,918 - INFO - 找到商品项 14，检查元素信息...
2025-07-31 11:13:02,925 - INFO - 商品项 14 - 元素标签: div, class: , 有data-autolog: False
2025-07-31 11:13:02,925 - INFO - 商品项 14 当前元素无data-autolog，查找子元素...
2025-07-31 11:13:02,933 - INFO - 商品项 14 找到子元素，class: tfx-item
2025-07-31 11:13:02,935 - INFO - 商品项 14 直接获取到data-autolog: item_id=763723216473&path=search_goods_card&index=13&type=category
2025-07-31 11:13:02,935 - INFO - 商品项 14 data-autolog: item_id=763723216473&path=search_goods_card&index=13&type=category
2025-07-31 11:13:02,935 - INFO - 商品项 14 提取到item_id: 763723216473
2025-07-31 11:13:02,944 - INFO - 商品项 14 商品名称: Adidas originals Superstar经典女鞋金标贝壳头鞋FU7712
2025-07-31 11:13:02,957 - INFO - 找到商品项 15，检查元素信息...
2025-07-31 11:13:02,963 - INFO - 商品项 15 - 元素标签: div, class: , 有data-autolog: False
2025-07-31 11:13:02,964 - INFO - 商品项 15 当前元素无data-autolog，查找子元素...
2025-07-31 11:13:02,972 - INFO - 商品项 15 找到子元素，class: tfx-item
2025-07-31 11:13:02,974 - INFO - 商品项 15 直接获取到data-autolog: item_id=683431862384&path=search_goods_card&index=14&type=category
2025-07-31 11:13:02,974 - INFO - 商品项 15 data-autolog: item_id=683431862384&path=search_goods_card&index=14&type=category
2025-07-31 11:13:02,975 - INFO - 商品项 15 提取到item_id: 683431862384
2025-07-31 11:13:02,983 - INFO - 商品项 15 商品名称: Cha*mpion男士内裤5件套#1292887
2025-07-31 11:13:02,994 - INFO - 找到商品项 16，检查元素信息...
2025-07-31 11:13:03,002 - INFO - 商品项 16 - 元素标签: div, class: , 有data-autolog: False
2025-07-31 11:13:03,002 - INFO - 商品项 16 当前元素无data-autolog，查找子元素...
2025-07-31 11:13:03,009 - INFO - 商品项 16 找到子元素，class: tfx-item
2025-07-31 11:13:03,012 - INFO - 商品项 16 直接获取到data-autolog: item_id=745963018139&path=search_goods_card&index=15&type=category
2025-07-31 11:13:03,012 - INFO - 商品项 16 data-autolog: item_id=745963018139&path=search_goods_card&index=15&type=category
2025-07-31 11:13:03,012 - INFO - 商品项 16 提取到item_id: 745963018139
2025-07-31 11:13:03,021 - INFO - 商品项 16 商品名称: 保税直发Adidas originals Superstar经典金标贝壳头女GS鞋FU7712
2025-07-31 11:13:03,033 - INFO - 找到商品项 17，检查元素信息...
2025-07-31 11:13:03,040 - INFO - 商品项 17 - 元素标签: div, class: , 有data-autolog: False
2025-07-31 11:13:03,040 - INFO - 商品项 17 当前元素无data-autolog，查找子元素...
2025-07-31 11:13:03,048 - INFO - 商品项 17 找到子元素，class: tfx-item
2025-07-31 11:13:03,050 - INFO - 商品项 17 直接获取到data-autolog: item_id=794106118702&path=search_goods_card&index=16&type=category
2025-07-31 11:13:03,050 - INFO - 商品项 17 data-autolog: item_id=794106118702&path=search_goods_card&index=16&type=category
2025-07-31 11:13:03,051 - INFO - 商品项 17 提取到item_id: 794106118702
2025-07-31 11:13:03,059 - INFO - 商品项 17 商品名称: 始祖鸟/Arcteryx男士Cormac40+防晒吸湿排汗透气速干短袖T恤X9718
2025-07-31 11:13:03,070 - INFO - 找到商品项 18，检查元素信息...
2025-07-31 11:13:03,079 - INFO - 商品项 18 - 元素标签: div, class: , 有data-autolog: False
2025-07-31 11:13:03,079 - INFO - 商品项 18 当前元素无data-autolog，查找子元素...
2025-07-31 11:13:03,086 - INFO - 商品项 18 找到子元素，class: tfx-item
2025-07-31 11:13:03,089 - INFO - 商品项 18 直接获取到data-autolog: item_id=796196467020&path=search_goods_card&index=17&type=category
2025-07-31 11:13:03,089 - INFO - 商品项 18 data-autolog: item_id=796196467020&path=search_goods_card&index=17&type=category
2025-07-31 11:13:03,089 - INFO - 商品项 18 提取到item_id: 796196467020
2025-07-31 11:13:03,097 - INFO - 商品项 18 商品名称: 鞋子 6378熙熙2
2025-07-31 11:13:03,108 - INFO - 找到商品项 19，检查元素信息...
2025-07-31 11:13:03,116 - INFO - 商品项 19 - 元素标签: div, class: , 有data-autolog: False
2025-07-31 11:13:03,116 - INFO - 商品项 19 当前元素无data-autolog，查找子元素...
2025-07-31 11:13:03,124 - INFO - 商品项 19 找到子元素，class: tfx-item
2025-07-31 11:13:03,127 - INFO - 商品项 19 直接获取到data-autolog: item_id=744726263938&path=search_goods_card&index=18&type=category
2025-07-31 11:13:03,129 - INFO - 商品项 19 data-autolog: item_id=744726263938&path=search_goods_card&index=18&type=category
2025-07-31 11:13:03,129 - INFO - 商品项 19 提取到item_id: 744726263938
2025-07-31 11:13:03,137 - INFO - 商品项 19 商品名称: 保税直发Adidas Superstar金标贝壳头黑白经典运动男女鞋EG4959
2025-07-31 11:13:03,163 - INFO - 找到商品项 20，检查元素信息...
2025-07-31 11:13:03,174 - INFO - 商品项 20 - 元素标签: div, class: , 有data-autolog: False
2025-07-31 11:13:03,174 - INFO - 商品项 20 当前元素无data-autolog，查找子元素...
2025-07-31 11:13:03,181 - INFO - 商品项 20 找到子元素，class: tfx-item
2025-07-31 11:13:03,184 - INFO - 商品项 20 直接获取到data-autolog: item_id=736263199809&path=search_goods_card&index=19&type=category
2025-07-31 11:13:03,184 - INFO - 商品项 20 data-autolog: item_id=736263199809&path=search_goods_card&index=19&type=category
2025-07-31 11:13:03,184 - INFO - 商品项 20 提取到item_id: 736263199809
2025-07-31 11:13:03,193 - INFO - 商品项 20 商品名称: 保税直发Adidas originals Superstar经典女鞋金标贝壳头鞋FU7712
2025-07-31 11:13:03,206 - INFO - 找到商品项 21，检查元素信息...
2025-07-31 11:13:03,213 - INFO - 商品项 21 - 元素标签: div, class: , 有data-autolog: False
2025-07-31 11:13:03,214 - INFO - 商品项 21 当前元素无data-autolog，查找子元素...
2025-07-31 11:13:03,222 - INFO - 商品项 21 找到子元素，class: tfx-item
2025-07-31 11:13:03,225 - INFO - 商品项 21 直接获取到data-autolog: item_id=736217777246&path=search_goods_card&index=20&type=category
2025-07-31 11:13:03,225 - INFO - 商品项 21 data-autolog: item_id=736217777246&path=search_goods_card&index=20&type=category
2025-07-31 11:13:03,225 - INFO - 商品项 21 提取到item_id: 736217777246
2025-07-31 11:13:03,250 - INFO - 商品项 21 商品名称: 保税直发Adidas三叶草Superstar金标贝壳头板鞋休闲小白鞋EG4958
2025-07-31 11:13:03,262 - INFO - 找到商品项 22，检查元素信息...
2025-07-31 11:13:03,274 - INFO - 商品项 22 - 元素标签: div, class: , 有data-autolog: False
2025-07-31 11:13:03,274 - INFO - 商品项 22 当前元素无data-autolog，查找子元素...
2025-07-31 11:13:03,281 - INFO - 商品项 22 找到子元素，class: tfx-item
2025-07-31 11:13:03,285 - INFO - 商品项 22 直接获取到data-autolog: item_id=828279306108&path=search_goods_card&index=21&type=category
2025-07-31 11:13:03,285 - INFO - 商品项 22 data-autolog: item_id=828279306108&path=search_goods_card&index=21&type=category
2025-07-31 11:13:03,286 - INFO - 商品项 22 提取到item_id: 828279306108
2025-07-31 11:13:03,295 - INFO - 商品项 22 商品名称: FILA斐乐鱼刺女鞋复古老爹厚底时尚反光休闲运动鞋1RM02723G-050
2025-07-31 11:13:03,309 - INFO - 找到商品项 23，检查元素信息...
2025-07-31 11:13:03,320 - INFO - 商品项 23 - 元素标签: div, class: , 有data-autolog: False
2025-07-31 11:13:03,320 - INFO - 商品项 23 当前元素无data-autolog，查找子元素...
2025-07-31 11:13:03,329 - INFO - 商品项 23 找到子元素，class: tfx-item
2025-07-31 11:13:03,332 - INFO - 商品项 23 直接获取到data-autolog: item_id=680643456974&path=search_goods_card&index=22&type=category
2025-07-31 11:13:03,332 - INFO - 商品项 23 data-autolog: item_id=680643456974&path=search_goods_card&index=22&type=category
2025-07-31 11:13:03,332 - INFO - 商品项 23 提取到item_id: 680643456974
2025-07-31 11:13:03,342 - INFO - 商品项 23 商品名称: 【2.0运动瑜伽】美国NeilMed成人儿童盐水洗鼻器盐洗鼻子鼻腔冲洗
2025-07-31 11:13:03,353 - INFO - 找到商品项 24，检查元素信息...
2025-07-31 11:13:03,361 - INFO - 商品项 24 - 元素标签: div, class: , 有data-autolog: False
2025-07-31 11:13:03,361 - INFO - 商品项 24 当前元素无data-autolog，查找子元素...
2025-07-31 11:13:03,369 - INFO - 商品项 24 找到子元素，class: tfx-item
2025-07-31 11:13:03,372 - INFO - 商品项 24 直接获取到data-autolog: item_id=744763830589&path=search_goods_card&index=23&type=category
2025-07-31 11:13:03,372 - INFO - 商品项 24 data-autolog: item_id=744763830589&path=search_goods_card&index=23&type=category
2025-07-31 11:13:03,372 - INFO - 商品项 24 提取到item_id: 744763830589
2025-07-31 11:13:03,393 - INFO - 商品项 24 商品名称: 保税直发阿迪达斯Adidas Samba 德训男女休闲经典黑白板鞋 B75806
2025-07-31 11:13:03,403 - INFO - 找到商品项 25，检查元素信息...
2025-07-31 11:13:03,414 - INFO - 商品项 25 - 元素标签: div, class: , 有data-autolog: False
2025-07-31 11:13:03,414 - INFO - 商品项 25 当前元素无data-autolog，查找子元素...
2025-07-31 11:13:03,425 - INFO - 商品项 25 找到子元素，class: tfx-item
2025-07-31 11:13:03,428 - INFO - 商品项 25 直接获取到data-autolog: item_id=817497595601&path=search_goods_card&index=24&type=category
2025-07-31 11:13:03,428 - INFO - 商品项 25 data-autolog: item_id=817497595601&path=search_goods_card&index=24&type=category
2025-07-31 11:13:03,428 - INFO - 商品项 25 提取到item_id: 817497595601
2025-07-31 11:13:03,437 - INFO - 商品项 25 商品名称: 保税直发adidas Originals Superstar经典金标贝壳头休闲鞋FV3284
2025-07-31 11:13:03,449 - INFO - 找到商品项 26，检查元素信息...
2025-07-31 11:13:03,459 - INFO - 商品项 26 - 元素标签: div, class: , 有data-autolog: False
2025-07-31 11:13:03,459 - INFO - 商品项 26 当前元素无data-autolog，查找子元素...
2025-07-31 11:13:03,467 - INFO - 商品项 26 找到子元素，class: tfx-item
2025-07-31 11:13:03,470 - INFO - 商品项 26 直接获取到data-autolog: item_id=744673805135&path=search_goods_card&index=25&type=category
2025-07-31 11:13:03,470 - INFO - 商品项 26 data-autolog: item_id=744673805135&path=search_goods_card&index=25&type=category
2025-07-31 11:13:03,470 - INFO - 商品项 26 提取到item_id: 744673805135
2025-07-31 11:13:03,480 - INFO - 商品项 26 商品名称: 保税直发Adidas Superstar金标贝壳头经典黑白运动男女鞋 EG4959
2025-07-31 11:13:03,492 - INFO - 找到商品项 27，检查元素信息...
2025-07-31 11:13:03,501 - INFO - 商品项 27 - 元素标签: div, class: , 有data-autolog: False
2025-07-31 11:13:03,502 - INFO - 商品项 27 当前元素无data-autolog，查找子元素...
2025-07-31 11:13:03,508 - INFO - 商品项 27 找到子元素，class: tfx-item
2025-07-31 11:13:03,510 - INFO - 商品项 27 直接获取到data-autolog: item_id=695703727323&path=search_goods_card&index=26&type=category
2025-07-31 11:13:03,510 - INFO - 商品项 27 data-autolog: item_id=695703727323&path=search_goods_card&index=26&type=category
2025-07-31 11:13:03,511 - INFO - 商品项 27 提取到item_id: 695703727323
2025-07-31 11:13:03,519 - INFO - 商品项 27 商品名称: 保税直发 New Balance/NB正品男女款327系列复古运动休闲鞋MS327
2025-07-31 11:13:03,530 - INFO - 找到商品项 28，检查元素信息...
2025-07-31 11:13:03,538 - INFO - 商品项 28 - 元素标签: div, class: , 有data-autolog: False
2025-07-31 11:13:03,539 - INFO - 商品项 28 当前元素无data-autolog，查找子元素...
2025-07-31 11:13:03,546 - INFO - 商品项 28 找到子元素，class: tfx-item
2025-07-31 11:13:03,549 - INFO - 商品项 28 直接获取到data-autolog: item_id=724206375361&path=search_goods_card&index=27&type=category
2025-07-31 11:13:03,549 - INFO - 商品项 28 data-autolog: item_id=724206375361&path=search_goods_card&index=27&type=category
2025-07-31 11:13:03,549 - INFO - 商品项 28 提取到item_id: 724206375361
2025-07-31 11:13:03,557 - INFO - 商品项 28 商品名称: 现货NeilMed鼻腔清洗护理成人洗鼻器240ml瓶*2+250盐包
2025-07-31 11:13:03,569 - INFO - 找到商品项 29，检查元素信息...
2025-07-31 11:13:03,575 - INFO - 商品项 29 - 元素标签: div, class: , 有data-autolog: False
2025-07-31 11:13:03,576 - INFO - 商品项 29 当前元素无data-autolog，查找子元素...
2025-07-31 11:13:03,583 - INFO - 商品项 29 找到子元素，class: tfx-item
2025-07-31 11:13:03,586 - INFO - 商品项 29 直接获取到data-autolog: item_id=793486462051&path=search_goods_card&index=28&type=category
2025-07-31 11:13:03,586 - INFO - 商品项 29 data-autolog: item_id=793486462051&path=search_goods_card&index=28&type=category
2025-07-31 11:13:03,586 - INFO - 商品项 29 提取到item_id: 793486462051
2025-07-31 11:13:03,596 - INFO - 商品项 29 商品名称: 保税直发阿迪达斯Adidas Campus 00s男女同款黑白面包板鞋 HQ8708
2025-07-31 11:13:03,609 - INFO - 找到商品项 30，检查元素信息...
2025-07-31 11:13:03,617 - INFO - 商品项 30 - 元素标签: div, class: , 有data-autolog: False
2025-07-31 11:13:03,618 - INFO - 商品项 30 当前元素无data-autolog，查找子元素...
2025-07-31 11:13:03,626 - INFO - 商品项 30 找到子元素，class: tfx-item
2025-07-31 11:13:03,628 - INFO - 商品项 30 直接获取到data-autolog: item_id=798769340190&path=search_goods_card&index=29&type=category
2025-07-31 11:13:03,628 - INFO - 商品项 30 data-autolog: item_id=798769340190&path=search_goods_card&index=29&type=category
2025-07-31 11:13:03,629 - INFO - 商品项 30 提取到item_id: 798769340190
2025-07-31 11:13:03,638 - INFO - 商品项 30 商品名称: 保税直发Adidas/阿迪达斯女鞋SUPERSTAR贝壳头休闲板鞋EF5398
2025-07-31 11:13:03,649 - INFO - 找到商品项 31，检查元素信息...
2025-07-31 11:13:03,657 - INFO - 商品项 31 - 元素标签: div, class: , 有data-autolog: False
2025-07-31 11:13:03,657 - INFO - 商品项 31 当前元素无data-autolog，查找子元素...
2025-07-31 11:13:03,664 - INFO - 商品项 31 找到子元素，class: tfx-item
2025-07-31 11:13:03,666 - INFO - 商品项 31 直接获取到data-autolog: item_id=712432188388&path=search_goods_card&index=30&type=category
2025-07-31 11:13:03,667 - INFO - 商品项 31 data-autolog: item_id=712432188388&path=search_goods_card&index=30&type=category
2025-07-31 11:13:03,667 - INFO - 商品项 31 提取到item_id: 712432188388
2025-07-31 11:13:03,676 - INFO - 商品项 31 商品名称: 通用PUMA/彪马 Smash v2黑白运动休闲男女小白鞋帆布鞋365968-02
2025-07-31 11:13:03,687 - INFO - 找到商品项 32，检查元素信息...
2025-07-31 11:13:03,695 - INFO - 商品项 32 - 元素标签: div, class: , 有data-autolog: False
2025-07-31 11:13:03,695 - INFO - 商品项 32 当前元素无data-autolog，查找子元素...
2025-07-31 11:13:03,703 - INFO - 商品项 32 找到子元素，class: tfx-item
2025-07-31 11:13:03,706 - INFO - 商品项 32 直接获取到data-autolog: item_id=684735558124&path=search_goods_card&index=31&type=category
2025-07-31 11:13:03,706 - INFO - 商品项 32 data-autolog: item_id=684735558124&path=search_goods_card&index=31&type=category
2025-07-31 11:13:03,706 - INFO - 商品项 32 提取到item_id: 684735558124
2025-07-31 11:13:03,715 - INFO - 商品项 32 商品名称: FILA男士套头卫衣1559385/1551890【】
2025-07-31 11:13:03,726 - INFO - 找到商品项 33，检查元素信息...
2025-07-31 11:13:03,734 - INFO - 商品项 33 - 元素标签: div, class: , 有data-autolog: False
2025-07-31 11:13:03,734 - INFO - 商品项 33 当前元素无data-autolog，查找子元素...
2025-07-31 11:13:03,742 - INFO - 商品项 33 找到子元素，class: tfx-item
2025-07-31 11:13:03,745 - INFO - 商品项 33 直接获取到data-autolog: item_id=770161099349&path=search_goods_card&index=32&type=category
2025-07-31 11:13:03,745 - INFO - 商品项 33 data-autolog: item_id=770161099349&path=search_goods_card&index=32&type=category
2025-07-31 11:13:03,745 - INFO - 商品项 33 提取到item_id: 770161099349
2025-07-31 11:13:03,754 - INFO - 商品项 33 商品名称: 保税直发Adidas阿迪达斯 samba OG经典德训男女休闲鞋板鞋 B75807
2025-07-31 11:13:03,765 - INFO - 找到商品项 34，检查元素信息...
2025-07-31 11:13:03,774 - INFO - 商品项 34 - 元素标签: div, class: , 有data-autolog: False
2025-07-31 11:13:03,774 - INFO - 商品项 34 当前元素无data-autolog，查找子元素...
2025-07-31 11:13:03,782 - INFO - 商品项 34 找到子元素，class: tfx-item
2025-07-31 11:13:03,784 - INFO - 商品项 34 直接获取到data-autolog: item_id=627862966284&path=search_goods_card&index=33&type=category
2025-07-31 11:13:03,785 - INFO - 商品项 34 data-autolog: item_id=627862966284&path=search_goods_card&index=33&type=category
2025-07-31 11:13:03,785 - INFO - 商品项 34 提取到item_id: 627862966284
2025-07-31 11:13:03,793 - INFO - 商品项 34 商品名称: NeilMed 奈尔梅德 成人洗鼻壶含10包盐
2025-07-31 11:13:03,805 - INFO - 找到商品项 35，检查元素信息...
2025-07-31 11:13:03,823 - INFO - 商品项 35 - 元素标签: div, class: , 有data-autolog: False
2025-07-31 11:13:03,823 - INFO - 商品项 35 当前元素无data-autolog，查找子元素...
2025-07-31 11:13:03,834 - INFO - 商品项 35 找到子元素，class: tfx-item
2025-07-31 11:13:03,837 - INFO - 商品项 35 直接获取到data-autolog: item_id=766844772348&path=search_goods_card&index=34&type=category
2025-07-31 11:13:03,839 - INFO - 商品项 35 data-autolog: item_id=766844772348&path=search_goods_card&index=34&type=category
2025-07-31 11:13:03,839 - INFO - 商品项 35 提取到item_id: 766844772348
2025-07-31 11:13:03,847 - INFO - 商品项 35 商品名称: 保税直发Adidas/阿迪达斯女鞋SUPERSTAR贝壳头运动休闲板鞋EF5398
2025-07-31 11:13:03,857 - INFO - 找到商品项 36，检查元素信息...
2025-07-31 11:13:03,865 - INFO - 商品项 36 - 元素标签: div, class: , 有data-autolog: False
2025-07-31 11:13:03,865 - INFO - 商品项 36 当前元素无data-autolog，查找子元素...
2025-07-31 11:13:03,873 - INFO - 商品项 36 找到子元素，class: tfx-item
2025-07-31 11:13:03,876 - INFO - 商品项 36 直接获取到data-autolog: item_id=627238564910&path=search_goods_card&index=35&type=category
2025-07-31 11:13:03,876 - INFO - 商品项 36 data-autolog: item_id=627238564910&path=search_goods_card&index=35&type=category
2025-07-31 11:13:03,876 - INFO - 商品项 36 提取到item_id: 627238564910
2025-07-31 11:13:03,884 - INFO - 商品项 36 商品名称: NeilMed 奈尔梅德 儿童洗鼻壶含30包盐
2025-07-31 11:13:03,896 - INFO - 找到商品项 37，检查元素信息...
2025-07-31 11:13:03,904 - INFO - 商品项 37 - 元素标签: div, class: , 有data-autolog: False
2025-07-31 11:13:03,904 - INFO - 商品项 37 当前元素无data-autolog，查找子元素...
2025-07-31 11:13:03,911 - INFO - 商品项 37 找到子元素，class: tfx-item
2025-07-31 11:13:03,914 - INFO - 商品项 37 直接获取到data-autolog: item_id=758051943865&path=search_goods_card&index=36&type=category
2025-07-31 11:13:03,914 - INFO - 商品项 37 data-autolog: item_id=758051943865&path=search_goods_card&index=36&type=category
2025-07-31 11:13:03,914 - INFO - 商品项 37 提取到item_id: 758051943865
2025-07-31 11:13:03,923 - INFO - 商品项 37 商品名称: 保税直发adidas originals Superstar 男女防滑耐磨休闲鞋 HQ8750
2025-07-31 11:13:03,937 - INFO - 找到商品项 38，检查元素信息...
2025-07-31 11:13:03,945 - INFO - 商品项 38 - 元素标签: div, class: , 有data-autolog: False
2025-07-31 11:13:03,945 - INFO - 商品项 38 当前元素无data-autolog，查找子元素...
2025-07-31 11:13:03,952 - INFO - 商品项 38 找到子元素，class: tfx-item
2025-07-31 11:13:03,955 - INFO - 商品项 38 直接获取到data-autolog: item_id=627821770191&path=search_goods_card&index=37&type=category
2025-07-31 11:13:03,955 - INFO - 商品项 38 data-autolog: item_id=627821770191&path=search_goods_card&index=37&type=category
2025-07-31 11:13:03,955 - INFO - 商品项 38 提取到item_id: 627821770191
2025-07-31 11:13:03,962 - INFO - 商品项 38 商品名称: NeilMed 奈尔梅德 电动洗鼻器洗鼻壶套装（建议7岁或以上）
2025-07-31 11:13:03,974 - INFO - 找到商品项 39，检查元素信息...
2025-07-31 11:13:03,983 - INFO - 商品项 39 - 元素标签: div, class: , 有data-autolog: False
2025-07-31 11:13:03,983 - INFO - 商品项 39 当前元素无data-autolog，查找子元素...
2025-07-31 11:13:03,989 - INFO - 商品项 39 找到子元素，class: tfx-item
2025-07-31 11:13:03,992 - INFO - 商品项 39 直接获取到data-autolog: item_id=627228732806&path=search_goods_card&index=38&type=category
2025-07-31 11:13:03,992 - INFO - 商品项 39 data-autolog: item_id=627228732806&path=search_goods_card&index=38&type=category
2025-07-31 11:13:03,993 - INFO - 商品项 39 提取到item_id: 627228732806
2025-07-31 11:13:04,000 - INFO - 商品项 39 商品名称: NeilMed 奈尔梅德 儿童洗鼻壶海盐补充套装
2025-07-31 11:13:04,011 - INFO - 找到商品项 40，检查元素信息...
2025-07-31 11:13:04,019 - INFO - 商品项 40 - 元素标签: div, class: , 有data-autolog: False
2025-07-31 11:13:04,020 - INFO - 商品项 40 当前元素无data-autolog，查找子元素...
2025-07-31 11:13:04,027 - INFO - 商品项 40 找到子元素，class: tfx-item
2025-07-31 11:13:04,029 - INFO - 商品项 40 直接获取到data-autolog: item_id=796297220083&path=search_goods_card&index=39&type=category
2025-07-31 11:13:04,030 - INFO - 商品项 40 data-autolog: item_id=796297220083&path=search_goods_card&index=39&type=category
2025-07-31 11:13:04,030 - INFO - 商品项 40 提取到item_id: 796297220083
2025-07-31 11:13:04,038 - INFO - 商品项 40 商品名称: Takibi塔吉比电动充气床垫户外弹丝拉丝睡垫加厚露营防潮垫打地铺
2025-07-31 11:13:04,039 - INFO - 当前页面爬取完成，找到 40 个商品
2025-07-31 11:13:04,039 - INFO - 第 1 页找到 40 个商品，总计: 40
2025-07-31 11:13:04,047 - INFO - 未找到下一页按钮: Message: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@id='root']/div[2]/div/div/div/div[4]/div[2]/div/div[2]/button[2]"}
  (Session info: chrome=138.0.7204.169); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff60783e925+77845]
	GetHandleVerifier [0x0x7ff60783e980+77936]
	(No symbol) [0x0x7ff6075f9cda]
	(No symbol) [0x0x7ff6076506aa]
	(No symbol) [0x0x7ff60765095c]
	(No symbol) [0x0x7ff6076a3d07]
	(No symbol) [0x0x7ff60767890f]
	(No symbol) [0x0x7ff6076a0b07]
	(No symbol) [0x0x7ff6076786a3]
	(No symbol) [0x0x7ff607641791]
	(No symbol) [0x0x7ff607642523]
	GetHandleVerifier [0x0x7ff607b1683d+3059501]
	GetHandleVerifier [0x0x7ff607b10bfd+3035885]
	GetHandleVerifier [0x0x7ff607b303f0+3164896]
	GetHandleVerifier [0x0x7ff607858c2e+185118]
	GetHandleVerifier [0x0x7ff60786053f+216111]
	GetHandleVerifier [0x0x7ff6078472d4+113092]
	GetHandleVerifier [0x0x7ff607847489+113529]
	GetHandleVerifier [0x0x7ff60782e288+10616]
	BaseThreadInitThunk [0x0x7ffed339e8d7+23]
	RtlUserThreadStart [0x0x7ffed40fc34c+44]

2025-07-31 11:13:04,047 - INFO - 已到达最后一页或翻页失败
2025-07-31 11:13:04,047 - INFO - 爬取完成，共找到 40 个商品
2025-07-31 11:13:04,048 - INFO - 成功读取到 40 个商品（目标60个），开始铺货任务
2025-07-31 11:13:04,048 - INFO - 第四步：开始处理商品...
2025-07-31 11:13:04,053 - INFO - 已读取 40 个商品，目标成功铺货 3 个商品
2025-07-31 11:13:04,053 - INFO - 将按顺序处理商品
2025-07-31 11:13:04,053 - INFO - 当前批次大小: 13
2025-07-31 11:13:04,054 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=745745162451
2025-07-31 11:13:07,354 - INFO - 页面加载完成
2025-07-31 11:13:07,354 - INFO - 模拟阅读行为，停顿 1.7 秒
2025-07-31 11:13:10,058 - INFO - 开始第 1 次铺货尝试...
2025-07-31 11:13:10,058 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-31 11:13:10,058 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-31 11:13:10,067 - INFO - span innerHTML: 立即铺货
2025-07-31 11:13:10,091 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-31 11:13:10,091 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-31 11:13:10,103 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-31 11:13:11,160 - INFO - 尝试ActionChains点击...
2025-07-31 11:13:11,460 - INFO - ActionChains点击成功
2025-07-31 11:13:11,900 - INFO - 铺货按钮点击成功
2025-07-31 11:13:13,920 - INFO - 使用配置的XPath找到确认按钮
2025-07-31 11:13:13,938 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-31 11:13:15,103 - INFO - 尝试ActionChains点击...
2025-07-31 11:13:15,374 - INFO - ActionChains点击成功
2025-07-31 11:13:15,847 - INFO - 确认按钮点击成功
2025-07-31 11:13:17,847 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-31 11:13:17,854 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-31 11:13:17,855 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:13:18,864 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-31 11:13:18,864 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:13:19,873 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-31 11:13:19,874 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:13:20,883 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-31 11:13:20,883 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:13:21,892 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-07-31 11:13:21,892 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:13:22,900 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-07-31 11:13:22,900 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:13:23,908 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-07-31 11:13:23,908 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:13:24,916 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-07-31 11:13:24,916 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:13:25,924 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-07-31 11:13:25,925 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:13:26,934 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-07-31 11:13:26,934 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:13:27,935 - INFO - 等待超时，进行最后一次检查...
2025-07-31 11:13:27,942 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-07-31 11:13:27,942 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-07-31 11:13:27,942 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-07-31 11:13:27,942 - INFO - 检测是否出现滑块验证...
2025-07-31 11:13:27,950 - INFO - 在主文档中未找到滑块，检查iframe...
2025-07-31 11:13:27,957 - INFO - 找到 0 个iframe
2025-07-31 11:13:27,957 - INFO - 在所有iframe中都未检测到滑块验证
2025-07-31 11:13:27,957 - INFO - 未检测到滑块验证或处理失败
2025-07-31 11:13:27,957 - WARNING - 第 1 个商品处理失败（不计入成功数量）
2025-07-31 11:13:27,958 - INFO - 商品处理失败，立即处理下一个商品
2025-07-31 11:13:27,970 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=734534774113
2025-07-31 11:13:32,412 - INFO - 页面加载完成
2025-07-31 11:13:32,412 - INFO - 模拟阅读行为，停顿 2.6 秒
2025-07-31 11:13:36,572 - INFO - 开始第 1 次铺货尝试...
2025-07-31 11:13:36,572 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-31 11:13:36,572 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-31 11:13:36,578 - INFO - span innerHTML: 立即铺货
2025-07-31 11:13:36,594 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-31 11:13:36,594 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-31 11:13:36,607 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-31 11:13:38,106 - INFO - 尝试ActionChains点击...
2025-07-31 11:13:38,402 - INFO - ActionChains点击成功
2025-07-31 11:13:38,610 - INFO - 铺货按钮点击成功
2025-07-31 11:13:40,624 - INFO - 使用配置的XPath找到确认按钮
2025-07-31 11:13:40,643 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-31 11:13:41,674 - INFO - 尝试ActionChains点击...
2025-07-31 11:13:41,942 - INFO - ActionChains点击成功
2025-07-31 11:13:42,229 - INFO - 确认按钮点击成功
2025-07-31 11:13:44,230 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-31 11:13:44,238 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-31 11:13:44,238 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:13:45,244 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-31 11:13:45,245 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:13:46,254 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-31 11:13:46,254 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:13:47,264 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-31 11:13:47,264 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:13:48,272 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-07-31 11:13:48,272 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:13:49,280 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-07-31 11:13:49,280 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:13:50,289 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-07-31 11:13:50,289 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:13:51,296 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-07-31 11:13:51,297 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:13:52,305 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-07-31 11:13:52,305 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:13:53,313 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-07-31 11:13:53,313 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:13:54,314 - INFO - 等待超时，进行最后一次检查...
2025-07-31 11:13:54,321 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-07-31 11:13:54,321 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-07-31 11:13:54,321 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-07-31 11:13:54,322 - INFO - 检测是否出现滑块验证...
2025-07-31 11:13:54,327 - INFO - 在主文档中未找到滑块，检查iframe...
2025-07-31 11:13:54,332 - INFO - 找到 0 个iframe
2025-07-31 11:13:54,332 - INFO - 在所有iframe中都未检测到滑块验证
2025-07-31 11:13:54,333 - INFO - 未检测到滑块验证或处理失败
2025-07-31 11:13:54,333 - WARNING - 第 2 个商品处理失败（不计入成功数量）
2025-07-31 11:13:54,333 - INFO - 商品处理失败，立即处理下一个商品
2025-07-31 11:13:54,346 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=743822760544
2025-07-31 11:13:57,426 - INFO - 页面加载完成
2025-07-31 11:13:57,426 - INFO - 模拟阅读行为，停顿 2.1 秒
2025-07-31 11:14:00,435 - INFO - 开始第 1 次铺货尝试...
2025-07-31 11:14:00,435 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-31 11:14:00,436 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-31 11:14:00,442 - INFO - span innerHTML: 立即铺货
2025-07-31 11:14:00,460 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-31 11:14:00,461 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-31 11:14:00,475 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-31 11:14:01,923 - INFO - 尝试ActionChains点击...
2025-07-31 11:14:02,213 - INFO - ActionChains点击成功
2025-07-31 11:14:02,668 - INFO - 铺货按钮点击成功
2025-07-31 11:14:04,684 - INFO - 使用配置的XPath找到确认按钮
2025-07-31 11:14:04,700 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-31 11:14:05,938 - INFO - 尝试ActionChains点击...
2025-07-31 11:14:06,217 - INFO - ActionChains点击成功
2025-07-31 11:14:06,512 - INFO - 确认按钮点击成功
2025-07-31 11:14:08,513 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-31 11:14:08,521 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-31 11:14:08,521 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:14:09,529 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-31 11:14:09,529 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:14:10,537 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-31 11:14:10,537 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:14:11,547 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-31 11:14:11,547 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:14:12,559 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-07-31 11:14:12,559 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:14:13,569 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-07-31 11:14:13,569 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:14:14,577 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-07-31 11:14:14,577 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:14:15,586 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-07-31 11:14:15,586 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:14:16,593 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-07-31 11:14:16,593 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:14:17,602 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-07-31 11:14:17,602 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:14:18,604 - INFO - 等待超时，进行最后一次检查...
2025-07-31 11:14:18,610 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-07-31 11:14:18,611 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-07-31 11:14:18,611 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-07-31 11:14:18,611 - INFO - 检测是否出现滑块验证...
2025-07-31 11:14:18,616 - INFO - 在主文档中未找到滑块，检查iframe...
2025-07-31 11:14:18,620 - INFO - 找到 0 个iframe
2025-07-31 11:14:18,620 - INFO - 在所有iframe中都未检测到滑块验证
2025-07-31 11:14:18,620 - INFO - 未检测到滑块验证或处理失败
2025-07-31 11:14:18,622 - WARNING - 第 3 个商品处理失败（不计入成功数量）
2025-07-31 11:14:18,622 - INFO - 商品处理失败，立即处理下一个商品
2025-07-31 11:14:18,636 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=769056228826
2025-07-31 11:14:21,786 - INFO - 页面加载完成
2025-07-31 11:14:21,786 - INFO - 模拟阅读行为，停顿 2.9 秒
2025-07-31 11:14:25,762 - INFO - 开始第 1 次铺货尝试...
2025-07-31 11:14:25,762 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-31 11:14:25,762 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-31 11:14:25,768 - INFO - span innerHTML: 立即铺货
2025-07-31 11:14:25,785 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-31 11:14:25,786 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-31 11:14:25,799 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-31 11:14:27,001 - INFO - 尝试ActionChains点击...
2025-07-31 11:14:27,297 - INFO - ActionChains点击成功
2025-07-31 11:14:27,763 - INFO - 铺货按钮点击成功
2025-07-31 11:14:29,776 - INFO - 使用配置的XPath找到确认按钮
2025-07-31 11:14:29,789 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-31 11:14:30,921 - INFO - 尝试ActionChains点击...
2025-07-31 11:14:31,187 - INFO - ActionChains点击成功
2025-07-31 11:14:31,674 - INFO - 确认按钮点击成功
2025-07-31 11:14:33,675 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-31 11:14:33,682 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-31 11:14:33,682 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:14:34,692 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-31 11:14:34,692 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:14:35,704 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-31 11:14:35,704 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:14:36,713 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-31 11:14:36,713 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:14:37,721 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-07-31 11:14:37,722 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:14:38,731 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-07-31 11:14:38,731 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:14:39,741 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-07-31 11:14:39,741 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:14:40,749 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-07-31 11:14:40,750 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:14:41,759 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-07-31 11:14:41,759 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:14:42,766 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-07-31 11:14:42,766 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:14:43,767 - INFO - 等待超时，进行最后一次检查...
2025-07-31 11:14:43,775 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-07-31 11:14:43,775 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-07-31 11:14:43,775 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-07-31 11:14:43,776 - INFO - 检测是否出现滑块验证...
2025-07-31 11:14:43,781 - INFO - 在主文档中未找到滑块，检查iframe...
2025-07-31 11:14:43,786 - INFO - 找到 0 个iframe
2025-07-31 11:14:43,786 - INFO - 在所有iframe中都未检测到滑块验证
2025-07-31 11:14:43,786 - INFO - 未检测到滑块验证或处理失败
2025-07-31 11:14:43,786 - WARNING - 第 4 个商品处理失败（不计入成功数量）
2025-07-31 11:14:43,787 - INFO - 商品处理失败，立即处理下一个商品
2025-07-31 11:14:43,800 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=744687180064
2025-07-31 11:14:47,650 - INFO - 页面加载完成
2025-07-31 11:14:47,650 - INFO - 模拟阅读行为，停顿 2.4 秒
2025-07-31 11:14:50,766 - INFO - 开始第 1 次铺货尝试...
2025-07-31 11:14:50,766 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-31 11:14:50,766 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-31 11:14:50,774 - INFO - span innerHTML: 立即铺货
2025-07-31 11:14:50,792 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-31 11:14:50,792 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-31 11:14:50,808 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-31 11:14:51,896 - INFO - 尝试ActionChains点击...
2025-07-31 11:14:52,192 - INFO - ActionChains点击成功
2025-07-31 11:14:52,459 - INFO - 铺货按钮点击成功
2025-07-31 11:14:54,471 - INFO - 使用配置的XPath找到确认按钮
2025-07-31 11:14:54,483 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-31 11:14:55,730 - INFO - 尝试ActionChains点击...
2025-07-31 11:14:55,997 - INFO - ActionChains点击成功
2025-07-31 11:14:56,363 - INFO - 确认按钮点击成功
2025-07-31 11:14:58,363 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-31 11:14:58,370 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-31 11:14:58,370 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:14:59,377 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-31 11:14:59,377 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:15:00,388 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-31 11:15:00,388 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:15:01,397 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-31 11:15:01,398 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:15:02,407 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-07-31 11:15:02,408 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:15:03,416 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-07-31 11:15:03,416 - INFO - span仍为'立即铺货'，继续等待...
2025-07-31 11:15:04,426 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-07-31 11:15:04,426 - INFO - span仍为'立即铺货'，继续等待...
