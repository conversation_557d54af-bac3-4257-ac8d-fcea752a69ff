2025-07-27 23:06:50,993 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169cda]
	(No symbol) [0x0x7ff7cd171679]
	(No symbol) [0x0x7ff7cd174a61]
	(No symbol) [0x0x7ff7cd211e4b]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:06:50,993 - INFO - 防检测机制设置完成
2025-07-27 23:06:50,993 - INFO - 浏览器驱动初始化成功
2025-07-27 23:06:50,998 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:06:51,005 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:06:51,010 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:06:51,013 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:06:51,018 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:06:51,021 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:06:51,028 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:06:51,033 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:06:51,037 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:06:51,041 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:06:51,046 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:06:51,049 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:06:51,052 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:06:51,058 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:06:51,060 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:06:51,063 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:06:51,066 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:06:51,071 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:06:51,074 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:06:51,076 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:06:51,077 - INFO - Cookies已加载
2025-07-27 23:06:51,077 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?channel=global_zx&spm=a21ug5.29159167.6452766900.2ndview_zxmore_click
2025-07-27 23:06:51,077 - INFO - 添加额外延迟: 2.7秒
2025-07-27 23:06:59,373 - INFO - 当前User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-27 23:06:59,374 - INFO - 确认：使用桌面版User-Agent
2025-07-27 23:06:59,377 - INFO - 桌面版User-Agent设置完成: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-27 23:06:59,380 - INFO - 成功导航到目标页面
2025-07-27 23:06:59,380 - INFO - 登录成功！现在可以开始铺货任务
2025-07-27 23:07:02,518 - INFO - Cookies已保存
2025-07-27 23:07:04,809 - INFO - 浏览器已关闭
2025-07-27 23:07:06,197 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169cda]
	(No symbol) [0x0x7ff7cd171679]
	(No symbol) [0x0x7ff7cd174a61]
	(No symbol) [0x0x7ff7cd211e4b]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 23:07:06,198 - INFO - 防检测机制设置完成
2025-07-27 23:07:06,198 - INFO - 浏览器驱动初始化成功
2025-07-27 23:07:06,198 - INFO - 开始自动化铺货流程（有窗口模式）
2025-07-27 23:07:06,198 - INFO - 第一步：正在导航到目标页面...
2025-07-27 23:07:11,591 - INFO - 第一步：点击登录按钮
2025-07-27 23:07:11,631 - INFO - 找到登录按钮，使用选择器: //*[@id='login-form']/div[6]/button
2025-07-27 23:07:12,659 - INFO - 准备点击登录按钮: 标签=button, 文本='登录', 选择器=//*[@id='login-form']/div[6]/button
2025-07-27 23:07:12,675 - INFO - 准备点击元素: tag=button, text='登录', class='fm-button fm-submit password-l', id=''
2025-07-27 23:07:13,940 - INFO - 尝试ActionChains点击...
2025-07-27 23:07:14,340 - INFO - ActionChains点击成功
2025-07-27 23:07:14,769 - INFO - 登录按钮点击成功
2025-07-27 23:07:14,769 - INFO - 登录按钮点击成功
2025-07-27 23:07:17,770 - INFO - 第二步：获取账号信息...
2025-07-27 23:07:17,770 - INFO - 正在跳转到个人中心页面: https://jingya.taobao.com/?v=2
2025-07-27 23:07:42,582 - INFO - 尝试获取账号名称，使用XPath: //*[@id='workbench-user-tool-btn']/div/div[2]
2025-07-27 23:07:42,596 - INFO - 成功获取账号信息: tb997603987291:书生
2025-07-27 23:07:42,596 - INFO - 获取到账号名称: tb997603987291:书生
2025-07-27 23:07:42,597 - INFO - 第三步：开始爬取商品信息...
2025-07-27 23:07:42,597 - INFO - GUI界面已更新账号显示: tb997603987291:书生
2025-07-27 23:07:42,599 - INFO - 开始爬取商品信息...
2025-07-27 23:07:46,709 - INFO - 正在爬取第 1 页商品信息...
2025-07-27 23:07:48,718 - INFO - 找到商品容器，开始爬取商品...
2025-07-27 23:07:48,729 - INFO - 找到商品项 1，查找tfx-item元素...
2025-07-27 23:07:48,737 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:07:48,743 - INFO - 元素标签: div, class: tfx-item--top
2025-07-27 23:07:48,750 - INFO - 商品项 1 outerHTML长度: 199
2025-07-27 23:07:48,750 - INFO - 商品项 1 outerHTML预览: <div class="tfx-item--top"><span class="tfx-item__tag">已铺货</span><img src="https://img.alicdn.com/imgextra/i2/2212737241744/O1CN01WdunKC1Oknn2ougM0_!!2212737241744.jpg" class="tfx-item__image"></div>
2025-07-27 23:07:48,751 - INFO - 商品项 1 outerHTML中不包含data-autolog
2025-07-27 23:07:48,751 - INFO - 商品项 1 未找到data-autolog属性，跳过
2025-07-27 23:07:48,761 - INFO - 找到商品项 2，查找tfx-item元素...
2025-07-27 23:07:48,767 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:07:48,772 - INFO - 元素标签: div, class: tfx-item--top
2025-07-27 23:07:48,778 - INFO - 商品项 2 outerHTML长度: 199
2025-07-27 23:07:48,778 - INFO - 商品项 2 outerHTML预览: <div class="tfx-item--top"><span class="tfx-item__tag">已铺货</span><img src="https://img.alicdn.com/imgextra/i2/2212737241744/O1CN01gR51sI1OknoKLl7bC_!!2212737241744.jpg" class="tfx-item__image"></div>
2025-07-27 23:07:48,778 - INFO - 商品项 2 outerHTML中不包含data-autolog
2025-07-27 23:07:48,778 - INFO - 商品项 2 未找到data-autolog属性，跳过
2025-07-27 23:07:48,787 - INFO - 找到商品项 3，查找tfx-item元素...
2025-07-27 23:07:48,875 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:07:48,902 - INFO - 元素标签: div, class: tfx-item--top
2025-07-27 23:07:48,914 - INFO - 商品项 3 outerHTML长度: 199
2025-07-27 23:07:48,914 - INFO - 商品项 3 outerHTML预览: <div class="tfx-item--top"><span class="tfx-item__tag">已铺货</span><img src="https://img.alicdn.com/imgextra/i1/2217613031232/O1CN01Lx4rVo1KyIvafKzaf_!!2217613031232.jpg" class="tfx-item__image"></div>
2025-07-27 23:07:48,914 - INFO - 商品项 3 outerHTML中不包含data-autolog
2025-07-27 23:07:48,914 - INFO - 商品项 3 未找到data-autolog属性，跳过
2025-07-27 23:07:48,930 - INFO - 找到商品项 4，查找tfx-item元素...
2025-07-27 23:07:48,934 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:07:48,940 - INFO - 元素标签: div, class: tfx-item--top
2025-07-27 23:07:48,946 - INFO - 商品项 4 outerHTML长度: 199
2025-07-27 23:07:48,946 - INFO - 商品项 4 outerHTML预览: <div class="tfx-item--top"><span class="tfx-item__tag">已铺货</span><img src="https://img.alicdn.com/imgextra/i2/2212911559585/O1CN01sCJyOP2KfzIzOktDc_!!2212911559585.jpg" class="tfx-item__image"></div>
2025-07-27 23:07:48,947 - INFO - 商品项 4 outerHTML中不包含data-autolog
2025-07-27 23:07:48,947 - INFO - 商品项 4 未找到data-autolog属性，跳过
2025-07-27 23:07:48,959 - INFO - 找到商品项 5，查找tfx-item元素...
2025-07-27 23:07:48,964 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:07:48,971 - INFO - 元素标签: div, class: tfx-item--top
2025-07-27 23:07:48,979 - INFO - 商品项 5 outerHTML长度: 199
2025-07-27 23:07:48,979 - INFO - 商品项 5 outerHTML预览: <div class="tfx-item--top"><span class="tfx-item__tag">已铺货</span><img src="https://img.alicdn.com/imgextra/i1/2212737241744/O1CN01A0Owzq1Okne6yvGUS_!!2212737241744.jpg" class="tfx-item__image"></div>
2025-07-27 23:07:48,980 - INFO - 商品项 5 outerHTML中不包含data-autolog
2025-07-27 23:07:48,980 - INFO - 商品项 5 未找到data-autolog属性，跳过
2025-07-27 23:07:48,990 - INFO - 找到商品项 6，查找tfx-item元素...
2025-07-27 23:07:48,995 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:07:48,999 - INFO - 元素标签: div, class: tfx-item--top
2025-07-27 23:07:49,006 - INFO - 商品项 6 outerHTML长度: 199
2025-07-27 23:07:49,007 - INFO - 商品项 6 outerHTML预览: <div class="tfx-item--top"><span class="tfx-item__tag">已铺货</span><img src="https://img.alicdn.com/imgextra/i4/2212737241744/O1CN01bR2Snk1OknmD9CHo4_!!2212737241744.jpg" class="tfx-item__image"></div>
2025-07-27 23:07:49,007 - INFO - 商品项 6 outerHTML中不包含data-autolog
2025-07-27 23:07:49,007 - INFO - 商品项 6 未找到data-autolog属性，跳过
2025-07-27 23:07:49,017 - INFO - 找到商品项 7，查找tfx-item元素...
2025-07-27 23:07:49,022 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:07:49,026 - INFO - 元素标签: div, class: tfx-item--top
2025-07-27 23:07:49,033 - INFO - 商品项 7 outerHTML长度: 161
2025-07-27 23:07:49,033 - INFO - 商品项 7 outerHTML预览: <div class="tfx-item--top"><img src="https://img.alicdn.com/imgextra/i1/2212737241744/O1CN01VunOfw1OknnvT3KPN_!!2212737241744.jpg" class="tfx-item__image"></div>
2025-07-27 23:07:49,033 - INFO - 商品项 7 outerHTML中不包含data-autolog
2025-07-27 23:07:49,033 - INFO - 商品项 7 未找到data-autolog属性，跳过
2025-07-27 23:07:49,043 - INFO - 找到商品项 8，查找tfx-item元素...
2025-07-27 23:07:49,051 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:07:49,056 - INFO - 元素标签: div, class: tfx-item--top
2025-07-27 23:07:49,062 - INFO - 商品项 8 outerHTML长度: 161
2025-07-27 23:07:49,062 - INFO - 商品项 8 outerHTML预览: <div class="tfx-item--top"><img src="https://img.alicdn.com/imgextra/i4/2212737241744/O1CN01CyqR8Y1OknoMW7g3z_!!2212737241744.jpg" class="tfx-item__image"></div>
2025-07-27 23:07:49,062 - INFO - 商品项 8 outerHTML中不包含data-autolog
2025-07-27 23:07:49,062 - INFO - 商品项 8 未找到data-autolog属性，跳过
2025-07-27 23:07:49,073 - INFO - 找到商品项 9，查找tfx-item元素...
2025-07-27 23:07:49,077 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:07:49,082 - INFO - 元素标签: div, class: tfx-item--top
2025-07-27 23:07:49,089 - INFO - 商品项 9 outerHTML长度: 155
2025-07-27 23:07:49,089 - INFO - 商品项 9 outerHTML预览: <div class="tfx-item--top"><img src="https://img.alicdn.com/imgextra/i4/2828729903/O1CN01JF05eC2N1d7Er11Ou_!!2828729903.jpg" class="tfx-item__image"></div>
2025-07-27 23:07:49,089 - INFO - 商品项 9 outerHTML中不包含data-autolog
2025-07-27 23:07:49,089 - INFO - 商品项 9 未找到data-autolog属性，跳过
2025-07-27 23:07:49,101 - INFO - 找到商品项 10，查找tfx-item元素...
2025-07-27 23:07:49,106 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:07:49,111 - INFO - 元素标签: div, class: tfx-item--top
2025-07-27 23:07:49,117 - INFO - 商品项 10 outerHTML长度: 161
2025-07-27 23:07:49,117 - INFO - 商品项 10 outerHTML预览: <div class="tfx-item--top"><img src="https://img.alicdn.com/imgextra/i2/2213445491080/O1CN01owKBix1Jqgp6Cndk1_!!2213445491080.jpg" class="tfx-item__image"></div>
2025-07-27 23:07:49,117 - INFO - 商品项 10 outerHTML中不包含data-autolog
2025-07-27 23:07:49,118 - INFO - 商品项 10 未找到data-autolog属性，跳过
2025-07-27 23:07:49,128 - INFO - 找到商品项 11，查找tfx-item元素...
2025-07-27 23:07:49,133 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:07:49,140 - INFO - 元素标签: div, class: tfx-item--top
2025-07-27 23:07:49,147 - INFO - 商品项 11 outerHTML长度: 155
2025-07-27 23:07:49,147 - INFO - 商品项 11 outerHTML预览: <div class="tfx-item--top"><img src="https://img.alicdn.com/imgextra/i3/2828729903/O1CN01fU6LQq2N1dAOm2J5t_!!2828729903.jpg" class="tfx-item__image"></div>
2025-07-27 23:07:49,147 - INFO - 商品项 11 outerHTML中不包含data-autolog
2025-07-27 23:07:49,148 - INFO - 商品项 11 未找到data-autolog属性，跳过
2025-07-27 23:07:49,186 - INFO - 找到商品项 12，查找tfx-item元素...
2025-07-27 23:07:49,193 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:07:49,200 - INFO - 元素标签: div, class: tfx-item--top
2025-07-27 23:07:49,208 - INFO - 商品项 12 outerHTML长度: 161
2025-07-27 23:07:49,208 - INFO - 商品项 12 outerHTML预览: <div class="tfx-item--top"><img src="https://img.alicdn.com/imgextra/i4/2213942032975/O1CN01urTMXN1XqbILRRYE5_!!2213942032975.jpg" class="tfx-item__image"></div>
2025-07-27 23:07:49,208 - INFO - 商品项 12 outerHTML中不包含data-autolog
2025-07-27 23:07:49,208 - INFO - 商品项 12 未找到data-autolog属性，跳过
2025-07-27 23:07:49,220 - INFO - 找到商品项 13，查找tfx-item元素...
2025-07-27 23:07:49,226 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:07:49,234 - INFO - 元素标签: div, class: tfx-item--top
2025-07-27 23:07:49,239 - INFO - 商品项 13 outerHTML长度: 161
2025-07-27 23:07:49,241 - INFO - 商品项 13 outerHTML预览: <div class="tfx-item--top"><img src="https://img.alicdn.com/imgextra/i3/2213172345972/O1CN016IoV7Y1tzE41Gvvkb_!!2213172345972.jpg" class="tfx-item__image"></div>
2025-07-27 23:07:49,241 - INFO - 商品项 13 outerHTML中不包含data-autolog
2025-07-27 23:07:49,241 - INFO - 商品项 13 未找到data-autolog属性，跳过
2025-07-27 23:07:49,252 - INFO - 找到商品项 14，查找tfx-item元素...
2025-07-27 23:07:49,257 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:07:49,263 - INFO - 元素标签: div, class: tfx-item--top
2025-07-27 23:07:49,270 - INFO - 商品项 14 outerHTML长度: 161
2025-07-27 23:07:49,270 - INFO - 商品项 14 outerHTML预览: <div class="tfx-item--top"><img src="https://img.alicdn.com/imgextra/i1/2217613031232/O1CN01AMqhhP1KyIwMw3Xwa_!!2217613031232.png" class="tfx-item__image"></div>
2025-07-27 23:07:49,270 - INFO - 商品项 14 outerHTML中不包含data-autolog
2025-07-27 23:07:49,270 - INFO - 商品项 14 未找到data-autolog属性，跳过
2025-07-27 23:07:49,281 - INFO - 找到商品项 15，查找tfx-item元素...
2025-07-27 23:07:49,286 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:07:49,292 - INFO - 元素标签: div, class: tfx-item--top
2025-07-27 23:07:49,297 - INFO - 商品项 15 outerHTML长度: 161
2025-07-27 23:07:49,297 - INFO - 商品项 15 outerHTML预览: <div class="tfx-item--top"><img src="https://img.alicdn.com/imgextra/i3/2213942032975/O1CN01kfW5oA1XqbIsm5tLs_!!2213942032975.jpg" class="tfx-item__image"></div>
2025-07-27 23:07:49,297 - INFO - 商品项 15 outerHTML中不包含data-autolog
2025-07-27 23:07:49,297 - INFO - 商品项 15 未找到data-autolog属性，跳过
2025-07-27 23:07:49,308 - INFO - 找到商品项 16，查找tfx-item元素...
2025-07-27 23:07:49,314 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:07:49,320 - INFO - 元素标签: div, class: tfx-item--top
2025-07-27 23:07:49,327 - INFO - 商品项 16 outerHTML长度: 161
2025-07-27 23:07:49,328 - INFO - 商品项 16 outerHTML预览: <div class="tfx-item--top"><img src="https://img.alicdn.com/imgextra/i2/2208785401688/O1CN01DiN7he1OL9aREniKV_!!2208785401688.jpg" class="tfx-item__image"></div>
2025-07-27 23:07:49,328 - INFO - 商品项 16 outerHTML中不包含data-autolog
2025-07-27 23:07:49,328 - INFO - 商品项 16 未找到data-autolog属性，跳过
2025-07-27 23:07:49,340 - INFO - 找到商品项 17，查找tfx-item元素...
2025-07-27 23:07:49,344 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:07:49,349 - INFO - 元素标签: div, class: tfx-item--top
2025-07-27 23:07:49,356 - INFO - 商品项 17 outerHTML长度: 161
2025-07-27 23:07:49,356 - INFO - 商品项 17 outerHTML预览: <div class="tfx-item--top"><img src="https://img.alicdn.com/imgextra/i1/2213942032975/O1CN01OzCOsr1XqbIAThraf_!!2213942032975.jpg" class="tfx-item__image"></div>
2025-07-27 23:07:49,356 - INFO - 商品项 17 outerHTML中不包含data-autolog
2025-07-27 23:07:49,356 - INFO - 商品项 17 未找到data-autolog属性，跳过
2025-07-27 23:07:49,369 - INFO - 找到商品项 18，查找tfx-item元素...
2025-07-27 23:07:49,373 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:07:49,379 - INFO - 元素标签: div, class: tfx-item--top
2025-07-27 23:07:49,387 - INFO - 商品项 18 outerHTML长度: 161
2025-07-27 23:07:49,387 - INFO - 商品项 18 outerHTML预览: <div class="tfx-item--top"><img src="https://img.alicdn.com/imgextra/i3/2212737241744/O1CN01aWrZ0V1Oknbufxi3V_!!2212737241744.jpg" class="tfx-item__image"></div>
2025-07-27 23:07:49,388 - INFO - 商品项 18 outerHTML中不包含data-autolog
2025-07-27 23:07:49,388 - INFO - 商品项 18 未找到data-autolog属性，跳过
2025-07-27 23:07:49,405 - INFO - 找到商品项 19，查找tfx-item元素...
2025-07-27 23:07:49,410 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:07:49,416 - INFO - 元素标签: div, class: tfx-item--top
2025-07-27 23:07:49,422 - INFO - 商品项 19 outerHTML长度: 161
2025-07-27 23:07:49,422 - INFO - 商品项 19 outerHTML预览: <div class="tfx-item--top"><img src="https://img.alicdn.com/imgextra/i1/2212737241744/O1CN01HMuocx1OknmRi90mS_!!2212737241744.jpg" class="tfx-item__image"></div>
2025-07-27 23:07:49,422 - INFO - 商品项 19 outerHTML中不包含data-autolog
2025-07-27 23:07:49,422 - INFO - 商品项 19 未找到data-autolog属性，跳过
2025-07-27 23:07:49,434 - INFO - 找到商品项 20，查找tfx-item元素...
2025-07-27 23:07:49,438 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:07:49,444 - INFO - 元素标签: div, class: tfx-item--top
2025-07-27 23:07:49,450 - INFO - 商品项 20 outerHTML长度: 161
2025-07-27 23:07:49,450 - INFO - 商品项 20 outerHTML预览: <div class="tfx-item--top"><img src="https://img.alicdn.com/imgextra/i3/2212737241744/O1CN01Z1bZQz1OknWuj1g2M_!!2212737241744.jpg" class="tfx-item__image"></div>
2025-07-27 23:07:49,451 - INFO - 商品项 20 outerHTML中不包含data-autolog
2025-07-27 23:07:49,451 - INFO - 商品项 20 未找到data-autolog属性，跳过
2025-07-27 23:07:49,461 - INFO - 找到商品项 21，查找tfx-item元素...
2025-07-27 23:07:49,465 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:07:49,470 - INFO - 元素标签: div, class: tfx-item--top
2025-07-27 23:07:49,476 - INFO - 商品项 21 outerHTML长度: 161
2025-07-27 23:07:49,476 - INFO - 商品项 21 outerHTML预览: <div class="tfx-item--top"><img src="https://img.alicdn.com/imgextra/i4/2212737241744/O1CN01eIg8PB1OknX5evarQ_!!2212737241744.jpg" class="tfx-item__image"></div>
2025-07-27 23:07:49,476 - INFO - 商品项 21 outerHTML中不包含data-autolog
2025-07-27 23:07:49,476 - INFO - 商品项 21 未找到data-autolog属性，跳过
2025-07-27 23:07:49,486 - INFO - 找到商品项 22，查找tfx-item元素...
2025-07-27 23:07:49,489 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:07:49,496 - INFO - 元素标签: div, class: tfx-item--top
2025-07-27 23:07:49,501 - INFO - 商品项 22 outerHTML长度: 161
2025-07-27 23:07:49,501 - INFO - 商品项 22 outerHTML预览: <div class="tfx-item--top"><img src="https://img.alicdn.com/imgextra/i1/2210283494943/O1CN01glYVxF1mNwhRRNqqv_!!2210283494943.jpg" class="tfx-item__image"></div>
2025-07-27 23:07:49,501 - INFO - 商品项 22 outerHTML中不包含data-autolog
2025-07-27 23:07:49,501 - INFO - 商品项 22 未找到data-autolog属性，跳过
2025-07-27 23:07:49,511 - INFO - 找到商品项 23，查找tfx-item元素...
2025-07-27 23:07:49,515 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:07:49,520 - INFO - 元素标签: div, class: tfx-item--top
2025-07-27 23:07:49,527 - INFO - 商品项 23 outerHTML长度: 161
2025-07-27 23:07:49,527 - INFO - 商品项 23 outerHTML预览: <div class="tfx-item--top"><img src="https://img.alicdn.com/imgextra/i3/2213942032975/O1CN01jO84TC1XqbGFs1G2N_!!2213942032975.png" class="tfx-item__image"></div>
2025-07-27 23:07:49,528 - INFO - 商品项 23 outerHTML中不包含data-autolog
2025-07-27 23:07:49,528 - INFO - 商品项 23 未找到data-autolog属性，跳过
2025-07-27 23:07:49,538 - INFO - 找到商品项 24，查找tfx-item元素...
2025-07-27 23:07:49,543 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:07:49,547 - INFO - 元素标签: div, class: tfx-item--top
2025-07-27 23:07:49,552 - INFO - 商品项 24 outerHTML长度: 161
2025-07-27 23:07:49,553 - INFO - 商品项 24 outerHTML预览: <div class="tfx-item--top"><img src="https://img.alicdn.com/imgextra/i4/2212737241744/O1CN017d3S4Q1OknjuXV89R_!!2212737241744.jpg" class="tfx-item__image"></div>
2025-07-27 23:07:49,553 - INFO - 商品项 24 outerHTML中不包含data-autolog
2025-07-27 23:07:49,553 - INFO - 商品项 24 未找到data-autolog属性，跳过
2025-07-27 23:07:49,563 - INFO - 找到商品项 25，查找tfx-item元素...
2025-07-27 23:07:49,568 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:07:49,572 - INFO - 元素标签: div, class: tfx-item--top
2025-07-27 23:07:49,577 - INFO - 商品项 25 outerHTML长度: 155
2025-07-27 23:07:49,577 - INFO - 商品项 25 outerHTML预览: <div class="tfx-item--top"><img src="https://img.alicdn.com/imgextra/i4/2828729903/O1CN01JF05eC2N1d7Er11Ou_!!2828729903.jpg" class="tfx-item__image"></div>
2025-07-27 23:07:49,578 - INFO - 商品项 25 outerHTML中不包含data-autolog
2025-07-27 23:07:49,578 - INFO - 商品项 25 未找到data-autolog属性，跳过
2025-07-27 23:07:49,589 - INFO - 找到商品项 26，查找tfx-item元素...
2025-07-27 23:07:49,594 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:07:49,598 - INFO - 元素标签: div, class: tfx-item--top
2025-07-27 23:07:49,604 - INFO - 商品项 26 outerHTML长度: 161
2025-07-27 23:07:49,604 - INFO - 商品项 26 outerHTML预览: <div class="tfx-item--top"><img src="https://img.alicdn.com/imgextra/i1/2212737241744/O1CN01VAemee1OknoJma33o_!!2212737241744.jpg" class="tfx-item__image"></div>
2025-07-27 23:07:49,604 - INFO - 商品项 26 outerHTML中不包含data-autolog
2025-07-27 23:07:49,604 - INFO - 商品项 26 未找到data-autolog属性，跳过
2025-07-27 23:07:49,615 - INFO - 找到商品项 27，查找tfx-item元素...
2025-07-27 23:07:49,620 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:07:49,625 - INFO - 元素标签: div, class: tfx-item--top
2025-07-27 23:07:49,629 - INFO - 商品项 27 outerHTML长度: 161
2025-07-27 23:07:49,629 - INFO - 商品项 27 outerHTML预览: <div class="tfx-item--top"><img src="https://img.alicdn.com/imgextra/i4/2212737241744/O1CN01SXGAJ21OknnAIrdNp_!!2212737241744.jpg" class="tfx-item__image"></div>
2025-07-27 23:07:49,631 - INFO - 商品项 27 outerHTML中不包含data-autolog
2025-07-27 23:07:49,631 - INFO - 商品项 27 未找到data-autolog属性，跳过
2025-07-27 23:07:49,641 - INFO - 找到商品项 28，查找tfx-item元素...
2025-07-27 23:07:49,646 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:07:49,651 - INFO - 元素标签: div, class: tfx-item--top
2025-07-27 23:07:49,657 - INFO - 商品项 28 outerHTML长度: 155
2025-07-27 23:07:49,657 - INFO - 商品项 28 outerHTML预览: <div class="tfx-item--top"><img src="https://img.alicdn.com/imgextra/i2/2828729903/O1CN01aoFIj42N1dAPeVYeo_!!2828729903.jpg" class="tfx-item__image"></div>
2025-07-27 23:07:49,657 - INFO - 商品项 28 outerHTML中不包含data-autolog
2025-07-27 23:07:49,658 - INFO - 商品项 28 未找到data-autolog属性，跳过
2025-07-27 23:07:49,669 - INFO - 找到商品项 29，查找tfx-item元素...
2025-07-27 23:07:49,674 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:07:49,680 - INFO - 元素标签: div, class: tfx-item--top
2025-07-27 23:07:49,686 - INFO - 商品项 29 outerHTML长度: 161
2025-07-27 23:07:49,686 - INFO - 商品项 29 outerHTML预览: <div class="tfx-item--top"><img src="https://img.alicdn.com/imgextra/i1/2212911559585/O1CN01NXrUch2KfzIydbFHB_!!2212911559585.jpg" class="tfx-item__image"></div>
2025-07-27 23:07:49,686 - INFO - 商品项 29 outerHTML中不包含data-autolog
2025-07-27 23:07:49,686 - INFO - 商品项 29 未找到data-autolog属性，跳过
2025-07-27 23:07:49,697 - INFO - 找到商品项 30，查找tfx-item元素...
2025-07-27 23:07:49,701 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:07:49,706 - INFO - 元素标签: div, class: tfx-item--top
2025-07-27 23:07:49,713 - INFO - 商品项 30 outerHTML长度: 161
2025-07-27 23:07:49,713 - INFO - 商品项 30 outerHTML预览: <div class="tfx-item--top"><img src="https://img.alicdn.com/imgextra/i3/2212737241744/O1CN01aWrZ0V1Oknbufxi3V_!!2212737241744.jpg" class="tfx-item__image"></div>
2025-07-27 23:07:49,714 - INFO - 商品项 30 outerHTML中不包含data-autolog
2025-07-27 23:07:49,714 - INFO - 商品项 30 未找到data-autolog属性，跳过
2025-07-27 23:07:49,724 - INFO - 找到商品项 31，查找tfx-item元素...
2025-07-27 23:07:49,728 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:07:49,734 - INFO - 元素标签: div, class: tfx-item--top
2025-07-27 23:07:49,740 - INFO - 商品项 31 outerHTML长度: 161
2025-07-27 23:07:49,740 - INFO - 商品项 31 outerHTML预览: <div class="tfx-item--top"><img src="https://img.alicdn.com/imgextra/i1/2207530243200/O1CN01nUWu2Y1ZVeNRfdktR_!!2207530243200.jpg" class="tfx-item__image"></div>
2025-07-27 23:07:49,740 - INFO - 商品项 31 outerHTML中不包含data-autolog
2025-07-27 23:07:49,741 - INFO - 商品项 31 未找到data-autolog属性，跳过
2025-07-27 23:07:49,752 - INFO - 找到商品项 32，查找tfx-item元素...
2025-07-27 23:07:49,758 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:07:49,763 - INFO - 元素标签: div, class: tfx-item--top
2025-07-27 23:07:49,770 - INFO - 商品项 32 outerHTML长度: 161
2025-07-27 23:07:49,770 - INFO - 商品项 32 outerHTML预览: <div class="tfx-item--top"><img src="https://img.alicdn.com/imgextra/i2/2213942032975/O1CN01mzmgfL1XqbDJxoj7J_!!2213942032975.png" class="tfx-item__image"></div>
2025-07-27 23:07:49,770 - INFO - 商品项 32 outerHTML中不包含data-autolog
2025-07-27 23:07:49,770 - INFO - 商品项 32 未找到data-autolog属性，跳过
2025-07-27 23:07:49,781 - INFO - 找到商品项 33，查找tfx-item元素...
2025-07-27 23:07:49,785 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:07:49,790 - INFO - 元素标签: div, class: tfx-item--top
2025-07-27 23:07:49,796 - INFO - 商品项 33 outerHTML长度: 161
2025-07-27 23:07:49,796 - INFO - 商品项 33 outerHTML预览: <div class="tfx-item--top"><img src="https://img.alicdn.com/imgextra/i1/2213172345972/O1CN01uteaGs1tzEFlyhVyI_!!2213172345972.jpg" class="tfx-item__image"></div>
2025-07-27 23:07:49,796 - INFO - 商品项 33 outerHTML中不包含data-autolog
2025-07-27 23:07:49,797 - INFO - 商品项 33 未找到data-autolog属性，跳过
2025-07-27 23:07:49,807 - INFO - 找到商品项 34，查找tfx-item元素...
2025-07-27 23:07:49,811 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:07:49,816 - INFO - 元素标签: div, class: tfx-item--top
2025-07-27 23:07:49,822 - INFO - 商品项 34 outerHTML长度: 155
2025-07-27 23:07:49,822 - INFO - 商品项 34 outerHTML预览: <div class="tfx-item--top"><img src="https://img.alicdn.com/imgextra/i2/2828729903/O1CN01GsLznx2N1dC7kLvuq_!!2828729903.jpg" class="tfx-item__image"></div>
2025-07-27 23:07:49,822 - INFO - 商品项 34 outerHTML中不包含data-autolog
2025-07-27 23:07:49,822 - INFO - 商品项 34 未找到data-autolog属性，跳过
2025-07-27 23:07:49,833 - INFO - 找到商品项 35，查找tfx-item元素...
2025-07-27 23:07:49,838 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:07:49,843 - INFO - 元素标签: div, class: tfx-item--top
2025-07-27 23:07:49,849 - INFO - 商品项 35 outerHTML长度: 161
2025-07-27 23:07:49,849 - INFO - 商品项 35 outerHTML预览: <div class="tfx-item--top"><img src="https://img.alicdn.com/imgextra/i3/2213942032975/O1CN01FzaS9o1XqbKpzBHn0_!!2213942032975.jpg" class="tfx-item__image"></div>
2025-07-27 23:07:49,849 - INFO - 商品项 35 outerHTML中不包含data-autolog
2025-07-27 23:07:49,849 - INFO - 商品项 35 未找到data-autolog属性，跳过
2025-07-27 23:07:49,860 - INFO - 找到商品项 36，查找tfx-item元素...
2025-07-27 23:07:49,864 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:07:49,871 - INFO - 元素标签: div, class: tfx-item--top
2025-07-27 23:07:49,876 - INFO - 商品项 36 outerHTML长度: 161
2025-07-27 23:07:49,877 - INFO - 商品项 36 outerHTML预览: <div class="tfx-item--top"><img src="https://img.alicdn.com/imgextra/i4/2212737241744/O1CN01eIg8PB1OknX5evarQ_!!2212737241744.jpg" class="tfx-item__image"></div>
2025-07-27 23:07:49,877 - INFO - 商品项 36 outerHTML中不包含data-autolog
2025-07-27 23:07:49,877 - INFO - 商品项 36 未找到data-autolog属性，跳过
2025-07-27 23:07:49,888 - INFO - 找到商品项 37，查找tfx-item元素...
2025-07-27 23:07:49,891 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:07:49,897 - INFO - 元素标签: div, class: tfx-item--top
2025-07-27 23:07:49,903 - INFO - 商品项 37 outerHTML长度: 161
2025-07-27 23:07:49,903 - INFO - 商品项 37 outerHTML预览: <div class="tfx-item--top"><img src="https://img.alicdn.com/imgextra/i1/2209073569363/O1CN01Mduhbf2J2JMmfRHyi_!!2209073569363.jpg" class="tfx-item__image"></div>
2025-07-27 23:07:49,903 - INFO - 商品项 37 outerHTML中不包含data-autolog
2025-07-27 23:07:49,903 - INFO - 商品项 37 未找到data-autolog属性，跳过
2025-07-27 23:07:49,913 - INFO - 找到商品项 38，查找tfx-item元素...
2025-07-27 23:07:49,918 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:07:49,923 - INFO - 元素标签: div, class: tfx-item--top
2025-07-27 23:07:49,929 - INFO - 商品项 38 outerHTML长度: 161
2025-07-27 23:07:49,930 - INFO - 商品项 38 outerHTML预览: <div class="tfx-item--top"><img src="https://img.alicdn.com/imgextra/i2/2213942032975/O1CN01OP0QEl1XqbFLAYNRr_!!2213942032975.jpg" class="tfx-item__image"></div>
2025-07-27 23:07:49,930 - INFO - 商品项 38 outerHTML中不包含data-autolog
2025-07-27 23:07:49,930 - INFO - 商品项 38 未找到data-autolog属性，跳过
2025-07-27 23:07:49,940 - INFO - 找到商品项 39，查找tfx-item元素...
2025-07-27 23:07:49,945 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:07:49,950 - INFO - 元素标签: div, class: tfx-item--top
2025-07-27 23:07:49,955 - INFO - 商品项 39 outerHTML长度: 161
2025-07-27 23:07:49,956 - INFO - 商品项 39 outerHTML预览: <div class="tfx-item--top"><img src="https://img.alicdn.com/imgextra/i1/2213942032975/O1CN01k95nDl1XqbIDPSUxc_!!2213942032975.png" class="tfx-item__image"></div>
2025-07-27 23:07:49,956 - INFO - 商品项 39 outerHTML中不包含data-autolog
2025-07-27 23:07:49,956 - INFO - 商品项 39 未找到data-autolog属性，跳过
2025-07-27 23:07:49,967 - INFO - 找到商品项 40，查找tfx-item元素...
2025-07-27 23:07:49,971 - INFO - 使用选择器 .//div[contains(@class, 'tfx-item')] 找到tfx-item元素
2025-07-27 23:07:49,978 - INFO - 元素标签: div, class: tfx-item--top
2025-07-27 23:07:49,983 - INFO - 商品项 40 outerHTML长度: 161
2025-07-27 23:07:49,983 - INFO - 商品项 40 outerHTML预览: <div class="tfx-item--top"><img src="https://img.alicdn.com/imgextra/i1/2215961400429/O1CN01Y9lHw51F2WqMye7SN_!!2215961400429.jpg" class="tfx-item__image"></div>
2025-07-27 23:07:49,983 - INFO - 商品项 40 outerHTML中不包含data-autolog
2025-07-27 23:07:49,983 - INFO - 商品项 40 未找到data-autolog属性，跳过
2025-07-27 23:07:49,983 - INFO - 当前页面爬取完成，找到 0 个商品
2025-07-27 23:07:49,983 - INFO - 第 1 页未找到商品，停止爬取
2025-07-27 23:07:49,984 - INFO - 爬取完成，共找到 0 个商品
2025-07-27 23:07:49,984 - ERROR - 未能获取到商品信息，停止流程
