2025-07-28 23:16:13,570 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059cda]
	(No symbol) [0x0x7ff69a061679]
	(No symbol) [0x0x7ff69a064a61]
	(No symbol) [0x0x7ff69a101e4b]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:16:13,571 - INFO - 防检测机制设置完成
2025-07-28 23:16:13,571 - INFO - 浏览器驱动初始化成功
2025-07-28 23:16:13,577 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:16:13,581 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:16:13,586 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:16:13,590 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:16:13,594 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:16:13,604 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:16:13,607 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:16:13,610 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:16:13,613 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:16:13,617 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:16:13,621 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:16:13,624 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:16:13,627 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:16:13,630 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:16:13,633 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:16:13,636 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:16:13,638 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:16:13,641 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:16:13,645 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:16:13,648 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:16:13,648 - INFO - Cookies已加载
2025-07-28 23:16:13,648 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?channel=global_zx&spm=a21ug5.29159167.6452766900.2ndview_zxmore_click
2025-07-28 23:16:20,001 - INFO - 当前User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-28 23:16:20,001 - INFO - 确认：使用桌面版User-Agent
2025-07-28 23:16:20,006 - INFO - 桌面版User-Agent设置完成: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-28 23:16:20,008 - INFO - 成功导航到目标页面
2025-07-28 23:16:20,008 - INFO - 登录成功！现在可以开始铺货任务
2025-07-28 23:16:23,048 - INFO - Cookies已保存
2025-07-28 23:16:25,339 - INFO - 浏览器已关闭
2025-07-28 23:16:26,744 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059cda]
	(No symbol) [0x0x7ff69a061679]
	(No symbol) [0x0x7ff69a064a61]
	(No symbol) [0x0x7ff69a101e4b]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:16:26,744 - INFO - 防检测机制设置完成
2025-07-28 23:16:26,744 - INFO - 浏览器驱动初始化成功
2025-07-28 23:16:26,744 - INFO - 开始自动化铺货流程（有界面模式）
2025-07-28 23:16:26,744 - INFO - 第一步：正在导航到目标页面...
2025-07-28 23:16:29,873 - INFO - 第一步：点击登录按钮
2025-07-28 23:16:29,908 - INFO - 找到登录按钮，使用选择器: //*[@id='login-form']/div[6]/button
2025-07-28 23:16:30,939 - INFO - 准备点击登录按钮: 标签=button, 文本='登录', 选择器=//*[@id='login-form']/div[6]/button
2025-07-28 23:16:30,968 - INFO - 准备点击元素: tag=button, text='登录', class='fm-button fm-submit password-l', id=''
2025-07-28 23:16:32,054 - INFO - 尝试ActionChains点击...
2025-07-28 23:16:32,466 - INFO - ActionChains点击成功
2025-07-28 23:16:32,847 - INFO - 登录按钮点击成功
2025-07-28 23:16:32,847 - INFO - 登录按钮点击成功
2025-07-28 23:16:35,848 - INFO - 第二步：获取账号信息...
2025-07-28 23:16:35,848 - INFO - 正在跳转到个人中心页面: https://jingya.taobao.com/?v=2
2025-07-28 23:17:00,825 - INFO - 尝试获取账号名称，使用XPath: //*[@id='workbench-user-tool-btn']/div/div[2]
2025-07-28 23:17:00,835 - INFO - 成功获取账号信息: 卓祥22店:冰淇淋
2025-07-28 23:17:00,835 - INFO - 获取到账号名称: 卓祥22店:冰淇淋
2025-07-28 23:17:00,843 - INFO - GUI界面已更新账号显示: 卓祥22店:冰淇淋
2025-07-28 23:17:00,843 - INFO - 第三步：开始读取20个商品信息...
2025-07-28 23:17:00,847 - INFO - 开始爬取商品信息，最大数量: 20
2025-07-28 23:17:03,938 - INFO - 正在爬取第 1 页商品信息...
2025-07-28 23:17:05,946 - INFO - 找到商品容器，开始爬取商品，最大数量: 20
2025-07-28 23:17:05,958 - INFO - 找到商品项 1，检查元素信息...
2025-07-28 23:17:05,972 - INFO - 商品项 1 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:17:05,976 - INFO - 商品项 1 直接获取到data-autolog: item_id=681778175573&path=search_goods_card&type=search
2025-07-28 23:17:05,976 - INFO - 商品项 1 data-autolog: item_id=681778175573&path=search_goods_card&type=search
2025-07-28 23:17:05,976 - INFO - 商品项 1 提取到item_id: 681778175573
2025-07-28 23:17:05,987 - INFO - 商品项 1 商品名称: 保税直供 Dove多芬磨砂膏石榴籽乳木果风味身体磨砂膏298g
2025-07-28 23:17:06,000 - INFO - 找到商品项 2，检查元素信息...
2025-07-28 23:17:06,011 - INFO - 商品项 2 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:17:06,015 - INFO - 商品项 2 直接获取到data-autolog: item_id=690841537159&path=search_goods_card&index=1&type=search
2025-07-28 23:17:06,015 - INFO - 商品项 2 data-autolog: item_id=690841537159&path=search_goods_card&index=1&type=search
2025-07-28 23:17:06,015 - INFO - 商品项 2 提取到item_id: 690841537159
2025-07-28 23:17:06,024 - INFO - 商品项 2 商品名称: 保税直供 凡士林Vaseline烟酰胺身体乳400ml 725ml无压泵款200ml
2025-07-28 23:17:06,037 - INFO - 找到商品项 3，检查元素信息...
2025-07-28 23:17:06,046 - INFO - 商品项 3 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:17:06,049 - INFO - 商品项 3 直接获取到data-autolog: item_id=789447097664&path=search_goods_card&index=2&type=search
2025-07-28 23:17:06,049 - INFO - 商品项 3 data-autolog: item_id=789447097664&path=search_goods_card&index=2&type=search
2025-07-28 23:17:06,049 - INFO - 商品项 3 提取到item_id: 789447097664
2025-07-28 23:17:06,058 - INFO - 商品项 3 商品名称: 【甄选】珂润Curel泡沫洗面奶150ml
2025-07-28 23:17:06,070 - INFO - 找到商品项 4，检查元素信息...
2025-07-28 23:17:06,080 - INFO - 商品项 4 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:17:06,084 - INFO - 商品项 4 直接获取到data-autolog: item_id=694682843417&path=search_goods_card&index=3&type=search
2025-07-28 23:17:06,084 - INFO - 商品项 4 data-autolog: item_id=694682843417&path=search_goods_card&index=3&type=search
2025-07-28 23:17:06,085 - INFO - 商品项 4 提取到item_id: 694682843417
2025-07-28 23:17:06,093 - INFO - 商品项 4 商品名称: 【可开授权】日台混发Fino发膜美容液护发素 发膜230g
2025-07-28 23:17:06,111 - INFO - 找到商品项 5，检查元素信息...
2025-07-28 23:17:06,128 - INFO - 商品项 5 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:17:06,130 - INFO - 商品项 5 直接获取到data-autolog: item_id=681082809892&path=search_goods_card&index=4&type=search
2025-07-28 23:17:06,130 - INFO - 商品项 5 data-autolog: item_id=681082809892&path=search_goods_card&index=4&type=search
2025-07-28 23:17:06,130 - INFO - 商品项 5 提取到item_id: 681082809892
2025-07-28 23:17:06,138 - INFO - 商品项 5 商品名称: 保税直供 Dove多芬大白碗润肤身体乳300ml 【1//2//3罐】
2025-07-28 23:17:06,149 - INFO - 找到商品项 6，检查元素信息...
2025-07-28 23:17:06,212 - INFO - 商品项 6 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:17:06,216 - INFO - 商品项 6 直接获取到data-autolog: item_id=694675776881&path=search_goods_card&index=5&type=search
2025-07-28 23:17:06,216 - INFO - 商品项 6 data-autolog: item_id=694675776881&path=search_goods_card&index=5&type=search
2025-07-28 23:17:06,216 - INFO - 商品项 6 提取到item_id: 694675776881
2025-07-28 23:17:06,229 - INFO - 商品项 6 商品名称: 保税直供 Pantene潘婷护发素三分钟奇迹多效修护发膜级护发素
2025-07-28 23:17:06,243 - INFO - 找到商品项 7，检查元素信息...
2025-07-28 23:17:06,252 - INFO - 商品项 7 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:17:06,256 - INFO - 商品项 7 直接获取到data-autolog: item_id=680729304287&path=search_goods_card&index=6&type=search
2025-07-28 23:17:06,256 - INFO - 商品项 7 data-autolog: item_id=680729304287&path=search_goods_card&index=6&type=search
2025-07-28 23:17:06,256 - INFO - 商品项 7 提取到item_id: 680729304287
2025-07-28 23:17:06,266 - INFO - 商品项 7 商品名称: 保税直供 多芬dove空气感无硅油 洗发水480g 护发素480g护发临期
2025-07-28 23:17:06,278 - INFO - 找到商品项 8，检查元素信息...
2025-07-28 23:17:06,292 - INFO - 商品项 8 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:17:06,295 - INFO - 商品项 8 直接获取到data-autolog: item_id=710355107054&path=search_goods_card&index=7&type=search
2025-07-28 23:17:06,295 - INFO - 商品项 8 data-autolog: item_id=710355107054&path=search_goods_card&index=7&type=search
2025-07-28 23:17:06,295 - INFO - 商品项 8 提取到item_id: 710355107054
2025-07-28 23:17:06,304 - INFO - 商品项 8 商品名称: 保税直供 Dove多芬洗发水护发素空气感洗发水480g 【护发素临期
2025-07-28 23:17:06,314 - INFO - 找到商品项 9，检查元素信息...
2025-07-28 23:17:06,322 - INFO - 商品项 9 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:17:06,325 - INFO - 商品项 9 直接获取到data-autolog: item_id=740463658412&path=search_goods_card&index=8&type=search
2025-07-28 23:17:06,325 - INFO - 商品项 9 data-autolog: item_id=740463658412&path=search_goods_card&index=8&type=search
2025-07-28 23:17:06,325 - INFO - 商品项 9 提取到item_id: 740463658412
2025-07-28 23:17:06,335 - INFO - 商品项 9 商品名称: 【主推链接】花王cape空气感定型喷雾50g/180g 编码：3305300000
2025-07-28 23:17:06,348 - INFO - 找到商品项 10，检查元素信息...
2025-07-28 23:17:06,357 - INFO - 商品项 10 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:17:06,359 - INFO - 商品项 10 直接获取到data-autolog: item_id=821067902127&path=search_goods_card&index=9&type=search
2025-07-28 23:17:06,359 - INFO - 商品项 10 data-autolog: item_id=821067902127&path=search_goods_card&index=9&type=search
2025-07-28 23:17:06,359 - INFO - 商品项 10 提取到item_id: 821067902127
2025-07-28 23:17:06,367 - INFO - 商品项 10 商品名称: 丹碧丝TAMPAX卫生棉条长导管式内置式纯棉月经棉条棉棒游泳卫生巾
2025-07-28 23:17:06,378 - INFO - 找到商品项 11，检查元素信息...
2025-07-28 23:17:06,386 - INFO - 商品项 11 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:17:06,389 - INFO - 商品项 11 直接获取到data-autolog: item_id=789835300215&path=search_goods_card&index=10&type=search
2025-07-28 23:17:06,389 - INFO - 商品项 11 data-autolog: item_id=789835300215&path=search_goods_card&index=10&type=search
2025-07-28 23:17:06,389 - INFO - 商品项 11 提取到item_id: 789835300215
2025-07-28 23:17:06,398 - INFO - 商品项 11 商品名称: 【保税】雅漾喷雾爽肤水300ml
2025-07-28 23:17:06,410 - INFO - 找到商品项 12，检查元素信息...
2025-07-28 23:17:06,418 - INFO - 商品项 12 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:17:06,421 - INFO - 商品项 12 直接获取到data-autolog: item_id=843747605382&path=search_goods_card&index=11&type=search
2025-07-28 23:17:06,421 - INFO - 商品项 12 data-autolog: item_id=843747605382&path=search_goods_card&index=11&type=search
2025-07-28 23:17:06,421 - INFO - 商品项 12 提取到item_id: 843747605382
2025-07-28 23:17:06,431 - INFO - 商品项 12 商品名称: 倩碧紫胖子卸妆膏125ml/瓶
2025-07-28 23:17:06,442 - INFO - 找到商品项 13，检查元素信息...
2025-07-28 23:17:06,450 - INFO - 商品项 13 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:17:06,453 - INFO - 商品项 13 直接获取到data-autolog: item_id=675816539149&path=search_goods_card&index=12&type=search
2025-07-28 23:17:06,453 - INFO - 商品项 13 data-autolog: item_id=675816539149&path=search_goods_card&index=12&type=search
2025-07-28 23:17:06,453 - INFO - 商品项 13 提取到item_id: 675816539149
2025-07-28 23:17:06,462 - INFO - 商品项 13 商品名称: 新加坡Vicopyl维可保益生菌(Pylopass/拒绝幽门菌/抑口臭/调肠胃)
2025-07-28 23:17:06,474 - INFO - 找到商品项 14，检查元素信息...
2025-07-28 23:17:06,483 - INFO - 商品项 14 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:17:06,490 - INFO - 商品项 14 直接获取到data-autolog: item_id=846634805775&path=search_goods_card&index=13&type=search
2025-07-28 23:17:06,490 - INFO - 商品项 14 data-autolog: item_id=846634805775&path=search_goods_card&index=13&type=search
2025-07-28 23:17:06,490 - INFO - 商品项 14 提取到item_id: 846634805775
2025-07-28 23:17:06,525 - INFO - 商品项 14 商品名称: 【甄选】cow牛乳石碱原味/玫瑰花香 瓶装480ml
2025-07-28 23:17:06,535 - INFO - 找到商品项 15，检查元素信息...
2025-07-28 23:17:06,543 - INFO - 商品项 15 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:17:06,546 - INFO - 商品项 15 直接获取到data-autolog: item_id=852510575114&path=search_goods_card&index=14&type=search
2025-07-28 23:17:06,546 - INFO - 商品项 15 data-autolog: item_id=852510575114&path=search_goods_card&index=14&type=search
2025-07-28 23:17:06,546 - INFO - 商品项 15 提取到item_id: 852510575114
2025-07-28 23:17:06,555 - INFO - 商品项 15 商品名称: Nars 纳斯 红壳蜜粉饼16g（含粉扑）、Nars纳斯裸光蜜粉饼10g
2025-07-28 23:17:06,568 - INFO - 找到商品项 16，检查元素信息...
2025-07-28 23:17:06,575 - INFO - 商品项 16 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:17:06,579 - INFO - 商品项 16 直接获取到data-autolog: item_id=743442966826&path=search_goods_card&index=15&type=search
2025-07-28 23:17:06,579 - INFO - 商品项 16 data-autolog: item_id=743442966826&path=search_goods_card&index=15&type=search
2025-07-28 23:17:06,579 - INFO - 商品项 16 提取到item_id: 743442966826
2025-07-28 23:17:06,588 - INFO - 商品项 16 商品名称: ESI卡路里拜拜片白芸豆阻断片大餐救星膳食纤维身材热控管理
2025-07-28 23:17:06,622 - INFO - 找到商品项 17，检查元素信息...
2025-07-28 23:17:06,631 - INFO - 商品项 17 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:17:06,636 - INFO - 商品项 17 直接获取到data-autolog: item_id=848934676698&path=search_goods_card&index=16&type=search
2025-07-28 23:17:06,636 - INFO - 商品项 17 data-autolog: item_id=848934676698&path=search_goods_card&index=16&type=search
2025-07-28 23:17:06,636 - INFO - 商品项 17 提取到item_id: 848934676698
2025-07-28 23:17:06,644 - INFO - 商品项 17 商品名称: Estee Lauder 雅诗兰黛持妆粉底液30ml 2C0 /1w1/1C1
2025-07-28 23:17:06,656 - INFO - 找到商品项 18，检查元素信息...
2025-07-28 23:17:06,665 - INFO - 商品项 18 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:17:06,668 - INFO - 商品项 18 直接获取到data-autolog: item_id=693758717931&path=search_goods_card&index=17&type=search
2025-07-28 23:17:06,668 - INFO - 商品项 18 data-autolog: item_id=693758717931&path=search_goods_card&index=17&type=search
2025-07-28 23:17:06,668 - INFO - 商品项 18 提取到item_id: 693758717931
2025-07-28 23:17:06,678 - INFO - 商品项 18 商品名称: 保税直供 资生堂fino高效渗透发膜 230g （1/2/3罐）日版
2025-07-28 23:17:06,688 - INFO - 找到商品项 19，检查元素信息...
2025-07-28 23:17:06,698 - INFO - 商品项 19 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:17:06,701 - INFO - 商品项 19 直接获取到data-autolog: item_id=691142128229&path=search_goods_card&index=18&type=search
2025-07-28 23:17:06,701 - INFO - 商品项 19 data-autolog: item_id=691142128229&path=search_goods_card&index=18&type=search
2025-07-28 23:17:06,701 - INFO - 商品项 19 提取到item_id: 691142128229
2025-07-28 23:17:06,710 - INFO - 商品项 19 商品名称: 保税 日版Dove多芬洁面洗面奶新版 正装瓶150ml 补充袋125ml 甄选
2025-07-28 23:17:06,723 - INFO - 找到商品项 20，检查元素信息...
2025-07-28 23:17:06,732 - INFO - 商品项 20 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:17:06,735 - INFO - 商品项 20 直接获取到data-autolog: item_id=690739721578&path=search_goods_card&index=19&type=search
2025-07-28 23:17:06,735 - INFO - 商品项 20 data-autolog: item_id=690739721578&path=search_goods_card&index=19&type=search
2025-07-28 23:17:06,735 - INFO - 商品项 20 提取到item_id: 690739721578
2025-07-28 23:17:06,758 - INFO - 商品项 20 商品名称: 保税直供 SUNSILK夏士莲椰子洗发水//护发素450ml 泰产
2025-07-28 23:17:06,758 - INFO - 已爬取到 20 个商品，停止当前页面爬取
2025-07-28 23:17:06,758 - INFO - 当前页面爬取完成，找到 20 个商品
2025-07-28 23:17:06,758 - INFO - 第 1 页找到 20 个商品，总计: 20
2025-07-28 23:17:06,758 - INFO - 已达到最大商品数量 20，停止爬取
2025-07-28 23:17:06,758 - INFO - 爬取完成，共找到 20 个商品
2025-07-28 23:17:06,758 - INFO - 成功读取到 20 个商品（目标20个），开始铺货任务
2025-07-28 23:17:06,758 - INFO - 第四步：开始处理商品...
2025-07-28 23:17:06,772 - INFO - 已读取 20 个商品，目标成功铺货 5 个商品
2025-07-28 23:17:06,772 - INFO - 当前批次大小: 14
2025-07-28 23:17:06,773 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=681778175573
2025-07-28 23:17:09,873 - INFO - 页面加载完成
2025-07-28 23:17:09,873 - INFO - 模拟阅读行为，停顿 1.9 秒
2025-07-28 23:17:12,669 - INFO - 开始第 1 次铺货尝试...
2025-07-28 23:17:12,669 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:17:12,669 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:17:12,679 - INFO - span innerHTML: 立即铺货
2025-07-28 23:17:12,701 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-28 23:17:12,701 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-28 23:17:12,715 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-28 23:17:13,804 - INFO - 尝试ActionChains点击...
2025-07-28 23:17:14,099 - INFO - ActionChains点击成功
2025-07-28 23:17:14,434 - INFO - 铺货按钮点击成功
2025-07-28 23:17:16,448 - INFO - 使用配置的XPath找到确认按钮
2025-07-28 23:17:16,459 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-28 23:17:17,618 - INFO - 尝试ActionChains点击...
2025-07-28 23:17:17,892 - INFO - ActionChains点击成功
2025-07-28 23:17:18,310 - INFO - 确认按钮点击成功
2025-07-28 23:17:20,310 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-28 23:17:20,318 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-28 23:17:20,319 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:17:21,328 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-28 23:17:21,328 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:17:22,334 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-28 23:17:22,335 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:17:23,343 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-28 23:17:23,343 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:17:24,350 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-07-28 23:17:24,350 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:17:25,358 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-07-28 23:17:25,358 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:17:26,368 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-07-28 23:17:26,368 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:17:27,375 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-07-28 23:17:27,375 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:17:28,383 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-07-28 23:17:28,384 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:17:29,391 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-07-28 23:17:29,391 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:17:30,392 - INFO - 等待超时，进行最后一次检查...
2025-07-28 23:17:30,400 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-07-28 23:17:30,400 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-07-28 23:17:30,400 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-07-28 23:17:30,400 - INFO - 检测是否出现滑块验证...
2025-07-28 23:17:30,428 - INFO - 未检测到滑块验证
2025-07-28 23:17:30,428 - INFO - 未检测到滑块验证或处理失败
2025-07-28 23:17:30,428 - WARNING - 第 1 个商品处理失败（不计入成功数量）
2025-07-28 23:17:30,428 - INFO - 商品处理失败，立即处理下一个商品
2025-07-28 23:17:30,441 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=690841537159
2025-07-28 23:17:34,588 - INFO - 页面加载完成
2025-07-28 23:17:34,588 - INFO - 模拟阅读行为，停顿 2.2 秒
2025-07-28 23:17:37,456 - INFO - 开始第 1 次铺货尝试...
2025-07-28 23:17:37,456 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:17:37,456 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:17:37,463 - INFO - span innerHTML: 立即铺货
2025-07-28 23:17:37,484 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-28 23:17:37,484 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-28 23:17:37,501 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-28 23:17:38,771 - INFO - 尝试ActionChains点击...
2025-07-28 23:17:39,056 - INFO - ActionChains点击成功
2025-07-28 23:17:39,483 - INFO - 铺货按钮点击成功
2025-07-28 23:17:41,494 - INFO - 使用配置的XPath找到确认按钮
2025-07-28 23:17:41,507 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-28 23:17:42,597 - INFO - 尝试ActionChains点击...
2025-07-28 23:17:42,870 - INFO - ActionChains点击成功
2025-07-28 23:17:43,237 - INFO - 确认按钮点击成功
2025-07-28 23:17:45,238 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-28 23:17:45,245 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-28 23:17:45,246 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:17:46,273 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-28 23:17:46,273 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:17:47,282 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-28 23:17:47,283 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:17:48,290 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-28 23:17:48,291 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:17:49,298 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-07-28 23:17:49,298 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:17:50,306 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-07-28 23:17:50,306 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:17:51,321 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-07-28 23:17:51,322 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:17:52,329 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-07-28 23:17:52,329 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:17:53,337 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-07-28 23:17:53,337 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:17:54,346 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-07-28 23:17:54,346 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:17:55,347 - INFO - 等待超时，进行最后一次检查...
2025-07-28 23:17:55,354 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-07-28 23:17:55,354 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-07-28 23:17:55,354 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-07-28 23:17:55,354 - INFO - 检测是否出现滑块验证...
2025-07-28 23:17:55,362 - INFO - 未检测到滑块验证
2025-07-28 23:17:55,362 - INFO - 未检测到滑块验证或处理失败
2025-07-28 23:17:55,362 - WARNING - 第 2 个商品处理失败（不计入成功数量）
2025-07-28 23:17:55,363 - INFO - 商品处理失败，立即处理下一个商品
2025-07-28 23:17:55,376 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=789447097664
2025-07-28 23:17:59,460 - INFO - 页面加载完成
2025-07-28 23:17:59,460 - INFO - 模拟阅读行为，停顿 1.8 秒
2025-07-28 23:18:02,153 - INFO - 开始第 1 次铺货尝试...
2025-07-28 23:18:02,153 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:18:02,153 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:18:02,161 - INFO - span innerHTML: 立即铺货
2025-07-28 23:18:02,180 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-28 23:18:02,181 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-28 23:18:02,193 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-28 23:18:03,540 - INFO - 尝试ActionChains点击...
2025-07-28 23:18:03,833 - INFO - ActionChains点击成功
2025-07-28 23:18:04,243 - INFO - 铺货按钮点击成功
2025-07-28 23:18:06,257 - INFO - 使用配置的XPath找到确认按钮
2025-07-28 23:18:06,270 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-28 23:18:07,647 - INFO - 尝试ActionChains点击...
2025-07-28 23:18:07,914 - INFO - ActionChains点击成功
2025-07-28 23:18:08,184 - INFO - 确认按钮点击成功
2025-07-28 23:18:10,185 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-28 23:18:10,245 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-28 23:18:10,246 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:18:11,595 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-28 23:18:11,595 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:18:12,609 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-28 23:18:12,610 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:18:13,621 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-28 23:18:13,621 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:18:14,633 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-07-28 23:18:14,634 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:18:15,043 - INFO - 用户请求暂停自动化流程
2025-07-28 23:18:15,646 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-07-28 23:18:15,646 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:18:16,658 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-07-28 23:18:16,658 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:18:17,670 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-07-28 23:18:17,671 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:18:18,683 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-07-28 23:18:18,683 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:18:19,697 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-07-28 23:18:19,698 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:18:20,699 - INFO - 等待超时，进行最后一次检查...
2025-07-28 23:18:20,712 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-07-28 23:18:20,713 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-07-28 23:18:20,713 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-07-28 23:18:20,713 - INFO - 检测是否出现滑块验证...
2025-07-28 23:18:20,723 - INFO - 未检测到滑块验证
2025-07-28 23:18:20,723 - INFO - 未检测到滑块验证或处理失败
2025-07-28 23:18:20,723 - WARNING - 第 3 个商品处理失败（不计入成功数量）
2025-07-28 23:18:20,723 - INFO - 商品处理失败，立即处理下一个商品
2025-07-28 23:23:40,604 - INFO - Cookies已保存
2025-07-28 23:23:40,936 - INFO - 用户停止了流程
2025-07-28 23:23:40,936 - INFO - 自动化流程已停止
2025-07-28 23:23:42,972 - INFO - 浏览器已关闭
