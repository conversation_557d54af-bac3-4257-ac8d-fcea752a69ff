2025-07-28 23:32:17,511 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059cda]
	(No symbol) [0x0x7ff69a061679]
	(No symbol) [0x0x7ff69a064a61]
	(No symbol) [0x0x7ff69a101e4b]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:32:17,512 - INFO - 防检测机制设置完成
2025-07-28 23:32:17,512 - INFO - 浏览器驱动初始化成功
2025-07-28 23:32:17,518 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:32:17,522 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:32:17,527 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:32:17,531 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:32:17,536 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:32:17,540 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:32:17,546 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:32:17,551 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:32:17,554 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:32:17,559 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:32:17,562 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:32:17,566 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:32:17,569 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:32:17,575 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:32:17,578 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:32:17,581 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:32:17,584 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:32:17,588 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:32:17,591 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:32:17,593 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:32:17,594 - INFO - Cookies已加载
2025-07-28 23:32:17,594 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?channel=global_zx&spm=a21ug5.29159167.6452766900.2ndview_zxmore_click
2025-07-28 23:32:23,115 - INFO - 当前User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-28 23:32:23,115 - INFO - 确认：使用桌面版User-Agent
2025-07-28 23:32:23,119 - INFO - 桌面版User-Agent设置完成: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-28 23:32:23,125 - INFO - 成功导航到目标页面
2025-07-28 23:32:23,126 - INFO - 登录成功！现在可以开始铺货任务
2025-07-28 23:32:26,633 - INFO - Cookies已保存
2025-07-28 23:32:28,902 - INFO - 浏览器已关闭
2025-07-28 23:32:30,260 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059cda]
	(No symbol) [0x0x7ff69a061679]
	(No symbol) [0x0x7ff69a064a61]
	(No symbol) [0x0x7ff69a101e4b]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-28 23:32:30,260 - INFO - 防检测机制设置完成
2025-07-28 23:32:30,260 - INFO - 浏览器驱动初始化成功
2025-07-28 23:32:30,260 - INFO - 开始自动化铺货流程（有界面模式）
2025-07-28 23:32:30,260 - INFO - 第一步：正在导航到目标页面...
2025-07-28 23:32:33,976 - INFO - 第一步：点击登录按钮
2025-07-28 23:32:34,002 - INFO - 找到登录按钮，使用选择器: //*[@id='login-form']/div[6]/button
2025-07-28 23:32:35,027 - INFO - 准备点击登录按钮: 标签=button, 文本='登录', 选择器=//*[@id='login-form']/div[6]/button
2025-07-28 23:32:35,041 - INFO - 准备点击元素: tag=button, text='登录', class='fm-button fm-submit password-l', id=''
2025-07-28 23:32:36,130 - INFO - 尝试ActionChains点击...
2025-07-28 23:32:36,539 - INFO - ActionChains点击成功
2025-07-28 23:32:37,011 - INFO - 登录按钮点击成功
2025-07-28 23:32:37,011 - INFO - 登录按钮点击成功
2025-07-28 23:32:40,012 - INFO - 第二步：获取账号信息...
2025-07-28 23:32:40,013 - INFO - 正在跳转到个人中心页面: https://jingya.taobao.com/?v=2
2025-07-28 23:32:58,636 - INFO - 尝试获取账号名称，使用XPath: //*[@id='workbench-user-tool-btn']/div/div[2]
2025-07-28 23:33:08,816 - WARNING - 等待元素超时: //*[@id='workbench-user-tool-btn']/div/div[2]
2025-07-28 23:33:08,816 - WARNING - 未找到账号信息元素
2025-07-28 23:33:08,816 - WARNING - 未能获取账号名称
2025-07-28 23:33:08,817 - INFO - 第三步：开始读取20个商品信息...
2025-07-28 23:33:08,818 - INFO - 开始爬取商品信息，最大数量: 20
2025-07-28 23:33:13,040 - INFO - 正在爬取第 1 页商品信息...
2025-07-28 23:33:15,047 - INFO - 找到商品容器，开始爬取商品，最大数量: 20
2025-07-28 23:33:15,059 - INFO - 找到商品项 1，检查元素信息...
2025-07-28 23:33:15,071 - INFO - 商品项 1 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:33:15,074 - INFO - 商品项 1 直接获取到data-autolog: item_id=681778175573&path=search_goods_card&type=search
2025-07-28 23:33:15,075 - INFO - 商品项 1 data-autolog: item_id=681778175573&path=search_goods_card&type=search
2025-07-28 23:33:15,075 - INFO - 商品项 1 提取到item_id: 681778175573
2025-07-28 23:33:15,088 - INFO - 商品项 1 商品名称: 保税直供 Dove多芬磨砂膏石榴籽乳木果风味身体磨砂膏298g
2025-07-28 23:33:15,101 - INFO - 找到商品项 2，检查元素信息...
2025-07-28 23:33:15,112 - INFO - 商品项 2 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:33:15,115 - INFO - 商品项 2 直接获取到data-autolog: item_id=690841537159&path=search_goods_card&index=1&type=search
2025-07-28 23:33:15,116 - INFO - 商品项 2 data-autolog: item_id=690841537159&path=search_goods_card&index=1&type=search
2025-07-28 23:33:15,116 - INFO - 商品项 2 提取到item_id: 690841537159
2025-07-28 23:33:15,124 - INFO - 商品项 2 商品名称: 保税直供 凡士林Vaseline烟酰胺身体乳400ml 725ml无压泵款200ml
2025-07-28 23:33:15,136 - INFO - 找到商品项 3，检查元素信息...
2025-07-28 23:33:19,207 - INFO - 商品项 3 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:33:19,210 - INFO - 商品项 3 直接获取到data-autolog: item_id=789447097664&path=search_goods_card&index=2&type=search
2025-07-28 23:33:19,211 - INFO - 商品项 3 data-autolog: item_id=789447097664&path=search_goods_card&index=2&type=search
2025-07-28 23:33:19,211 - INFO - 商品项 3 提取到item_id: 789447097664
2025-07-28 23:33:19,242 - INFO - 商品项 3 商品名称: 【甄选】珂润Curel泡沫洗面奶150ml
2025-07-28 23:33:19,252 - INFO - 找到商品项 4，检查元素信息...
2025-07-28 23:33:19,260 - INFO - 商品项 4 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:33:19,263 - INFO - 商品项 4 直接获取到data-autolog: item_id=694682843417&path=search_goods_card&index=3&type=search
2025-07-28 23:33:19,263 - INFO - 商品项 4 data-autolog: item_id=694682843417&path=search_goods_card&index=3&type=search
2025-07-28 23:33:19,263 - INFO - 商品项 4 提取到item_id: 694682843417
2025-07-28 23:33:19,273 - INFO - 商品项 4 商品名称: 【可开授权】日台混发Fino发膜美容液护发素 发膜230g
2025-07-28 23:33:19,283 - INFO - 找到商品项 5，检查元素信息...
2025-07-28 23:33:19,291 - INFO - 商品项 5 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:33:19,294 - INFO - 商品项 5 直接获取到data-autolog: item_id=681082809892&path=search_goods_card&index=4&type=search
2025-07-28 23:33:19,295 - INFO - 商品项 5 data-autolog: item_id=681082809892&path=search_goods_card&index=4&type=search
2025-07-28 23:33:19,295 - INFO - 商品项 5 提取到item_id: 681082809892
2025-07-28 23:33:19,303 - INFO - 商品项 5 商品名称: 保税直供 Dove多芬大白碗润肤身体乳300ml 【1//2//3罐】
2025-07-28 23:33:19,314 - INFO - 找到商品项 6，检查元素信息...
2025-07-28 23:33:19,324 - INFO - 商品项 6 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:33:19,326 - INFO - 商品项 6 直接获取到data-autolog: item_id=694675776881&path=search_goods_card&index=5&type=search
2025-07-28 23:33:19,327 - INFO - 商品项 6 data-autolog: item_id=694675776881&path=search_goods_card&index=5&type=search
2025-07-28 23:33:19,327 - INFO - 商品项 6 提取到item_id: 694675776881
2025-07-28 23:33:19,335 - INFO - 商品项 6 商品名称: 保税直供 Pantene潘婷护发素三分钟奇迹多效修护发膜级护发素
2025-07-28 23:33:19,346 - INFO - 找到商品项 7，检查元素信息...
2025-07-28 23:33:19,354 - INFO - 商品项 7 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:33:19,358 - INFO - 商品项 7 直接获取到data-autolog: item_id=680729304287&path=search_goods_card&index=6&type=search
2025-07-28 23:33:19,358 - INFO - 商品项 7 data-autolog: item_id=680729304287&path=search_goods_card&index=6&type=search
2025-07-28 23:33:19,358 - INFO - 商品项 7 提取到item_id: 680729304287
2025-07-28 23:33:19,383 - INFO - 商品项 7 商品名称: 保税直供 多芬dove空气感无硅油 洗发水480g 护发素480g护发临期
2025-07-28 23:33:19,395 - INFO - 找到商品项 8，检查元素信息...
2025-07-28 23:33:19,403 - INFO - 商品项 8 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:33:19,406 - INFO - 商品项 8 直接获取到data-autolog: item_id=710355107054&path=search_goods_card&index=7&type=search
2025-07-28 23:33:19,406 - INFO - 商品项 8 data-autolog: item_id=710355107054&path=search_goods_card&index=7&type=search
2025-07-28 23:33:19,406 - INFO - 商品项 8 提取到item_id: 710355107054
2025-07-28 23:33:19,415 - INFO - 商品项 8 商品名称: 保税直供 Dove多芬洗发水护发素空气感洗发水480g 【护发素临期
2025-07-28 23:33:19,425 - INFO - 找到商品项 9，检查元素信息...
2025-07-28 23:33:19,434 - INFO - 商品项 9 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:33:19,437 - INFO - 商品项 9 直接获取到data-autolog: item_id=740463658412&path=search_goods_card&index=8&type=search
2025-07-28 23:33:19,437 - INFO - 商品项 9 data-autolog: item_id=740463658412&path=search_goods_card&index=8&type=search
2025-07-28 23:33:19,437 - INFO - 商品项 9 提取到item_id: 740463658412
2025-07-28 23:33:19,445 - INFO - 商品项 9 商品名称: 【主推链接】花王cape空气感定型喷雾50g/180g 编码：3305300000
2025-07-28 23:33:19,457 - INFO - 找到商品项 10，检查元素信息...
2025-07-28 23:33:19,464 - INFO - 商品项 10 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:33:19,466 - INFO - 商品项 10 直接获取到data-autolog: item_id=821067902127&path=search_goods_card&index=9&type=search
2025-07-28 23:33:19,466 - INFO - 商品项 10 data-autolog: item_id=821067902127&path=search_goods_card&index=9&type=search
2025-07-28 23:33:19,467 - INFO - 商品项 10 提取到item_id: 821067902127
2025-07-28 23:33:19,476 - INFO - 商品项 10 商品名称: 丹碧丝TAMPAX卫生棉条长导管式内置式纯棉月经棉条棉棒游泳卫生巾
2025-07-28 23:33:19,487 - INFO - 找到商品项 11，检查元素信息...
2025-07-28 23:33:19,495 - INFO - 商品项 11 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:33:19,498 - INFO - 商品项 11 直接获取到data-autolog: item_id=789835300215&path=search_goods_card&index=10&type=search
2025-07-28 23:33:19,499 - INFO - 商品项 11 data-autolog: item_id=789835300215&path=search_goods_card&index=10&type=search
2025-07-28 23:33:19,499 - INFO - 商品项 11 提取到item_id: 789835300215
2025-07-28 23:33:19,507 - INFO - 商品项 11 商品名称: 【保税】雅漾喷雾爽肤水300ml
2025-07-28 23:33:19,518 - INFO - 找到商品项 12，检查元素信息...
2025-07-28 23:33:19,526 - INFO - 商品项 12 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:33:19,528 - INFO - 商品项 12 直接获取到data-autolog: item_id=843747605382&path=search_goods_card&index=11&type=search
2025-07-28 23:33:19,528 - INFO - 商品项 12 data-autolog: item_id=843747605382&path=search_goods_card&index=11&type=search
2025-07-28 23:33:19,528 - INFO - 商品项 12 提取到item_id: 843747605382
2025-07-28 23:33:19,537 - INFO - 商品项 12 商品名称: 倩碧紫胖子卸妆膏125ml/瓶
2025-07-28 23:33:19,549 - INFO - 找到商品项 13，检查元素信息...
2025-07-28 23:33:19,556 - INFO - 商品项 13 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:33:19,558 - INFO - 商品项 13 直接获取到data-autolog: item_id=675816539149&path=search_goods_card&index=12&type=search
2025-07-28 23:33:19,560 - INFO - 商品项 13 data-autolog: item_id=675816539149&path=search_goods_card&index=12&type=search
2025-07-28 23:33:19,560 - INFO - 商品项 13 提取到item_id: 675816539149
2025-07-28 23:33:19,569 - INFO - 商品项 13 商品名称: 新加坡Vicopyl维可保益生菌(Pylopass/拒绝幽门菌/抑口臭/调肠胃)
2025-07-28 23:33:19,580 - INFO - 找到商品项 14，检查元素信息...
2025-07-28 23:33:19,588 - INFO - 商品项 14 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:33:19,591 - INFO - 商品项 14 直接获取到data-autolog: item_id=846634805775&path=search_goods_card&index=13&type=search
2025-07-28 23:33:19,592 - INFO - 商品项 14 data-autolog: item_id=846634805775&path=search_goods_card&index=13&type=search
2025-07-28 23:33:19,592 - INFO - 商品项 14 提取到item_id: 846634805775
2025-07-28 23:33:19,601 - INFO - 商品项 14 商品名称: 【甄选】cow牛乳石碱原味/玫瑰花香 瓶装480ml
2025-07-28 23:33:19,614 - INFO - 找到商品项 15，检查元素信息...
2025-07-28 23:33:19,622 - INFO - 商品项 15 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:33:19,626 - INFO - 商品项 15 直接获取到data-autolog: item_id=852510575114&path=search_goods_card&index=14&type=search
2025-07-28 23:33:19,627 - INFO - 商品项 15 data-autolog: item_id=852510575114&path=search_goods_card&index=14&type=search
2025-07-28 23:33:19,628 - INFO - 商品项 15 提取到item_id: 852510575114
2025-07-28 23:33:19,638 - INFO - 商品项 15 商品名称: Nars 纳斯 红壳蜜粉饼16g（含粉扑）、Nars纳斯裸光蜜粉饼10g
2025-07-28 23:33:19,649 - INFO - 找到商品项 16，检查元素信息...
2025-07-28 23:33:19,661 - INFO - 商品项 16 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:33:19,664 - INFO - 商品项 16 直接获取到data-autolog: item_id=743442966826&path=search_goods_card&index=15&type=search
2025-07-28 23:33:19,664 - INFO - 商品项 16 data-autolog: item_id=743442966826&path=search_goods_card&index=15&type=search
2025-07-28 23:33:19,665 - INFO - 商品项 16 提取到item_id: 743442966826
2025-07-28 23:33:19,676 - INFO - 商品项 16 商品名称: ESI卡路里拜拜片白芸豆阻断片大餐救星膳食纤维身材热控管理
2025-07-28 23:33:19,688 - INFO - 找到商品项 17，检查元素信息...
2025-07-28 23:33:19,700 - INFO - 商品项 17 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:33:19,702 - INFO - 商品项 17 直接获取到data-autolog: item_id=848934676698&path=search_goods_card&index=16&type=search
2025-07-28 23:33:19,703 - INFO - 商品项 17 data-autolog: item_id=848934676698&path=search_goods_card&index=16&type=search
2025-07-28 23:33:19,703 - INFO - 商品项 17 提取到item_id: 848934676698
2025-07-28 23:33:19,728 - INFO - 商品项 17 商品名称: Estee Lauder 雅诗兰黛持妆粉底液30ml 2C0 /1w1/1C1
2025-07-28 23:33:19,741 - INFO - 找到商品项 18，检查元素信息...
2025-07-28 23:33:19,750 - INFO - 商品项 18 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:33:19,754 - INFO - 商品项 18 直接获取到data-autolog: item_id=693758717931&path=search_goods_card&index=17&type=search
2025-07-28 23:33:19,755 - INFO - 商品项 18 data-autolog: item_id=693758717931&path=search_goods_card&index=17&type=search
2025-07-28 23:33:19,755 - INFO - 商品项 18 提取到item_id: 693758717931
2025-07-28 23:33:19,766 - INFO - 商品项 18 商品名称: 保税直供 资生堂fino高效渗透发膜 230g （1/2/3罐）日版
2025-07-28 23:33:19,779 - INFO - 找到商品项 19，检查元素信息...
2025-07-28 23:33:19,790 - INFO - 商品项 19 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:33:19,793 - INFO - 商品项 19 直接获取到data-autolog: item_id=691142128229&path=search_goods_card&index=18&type=search
2025-07-28 23:33:19,793 - INFO - 商品项 19 data-autolog: item_id=691142128229&path=search_goods_card&index=18&type=search
2025-07-28 23:33:19,793 - INFO - 商品项 19 提取到item_id: 691142128229
2025-07-28 23:33:19,803 - INFO - 商品项 19 商品名称: 保税 日版Dove多芬洁面洗面奶新版 正装瓶150ml 补充袋125ml 甄选
2025-07-28 23:33:19,814 - INFO - 找到商品项 20，检查元素信息...
2025-07-28 23:33:19,823 - INFO - 商品项 20 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-28 23:33:19,826 - INFO - 商品项 20 直接获取到data-autolog: item_id=690739721578&path=search_goods_card&index=19&type=search
2025-07-28 23:33:19,826 - INFO - 商品项 20 data-autolog: item_id=690739721578&path=search_goods_card&index=19&type=search
2025-07-28 23:33:19,826 - INFO - 商品项 20 提取到item_id: 690739721578
2025-07-28 23:33:19,836 - INFO - 商品项 20 商品名称: 保税直供 SUNSILK夏士莲椰子洗发水//护发素450ml 泰产
2025-07-28 23:33:19,836 - INFO - 已爬取到 20 个商品，停止当前页面爬取
2025-07-28 23:33:19,836 - INFO - 当前页面爬取完成，找到 20 个商品
2025-07-28 23:33:19,837 - INFO - 第 1 页找到 20 个商品，总计: 20
2025-07-28 23:33:19,837 - INFO - 已达到最大商品数量 20，停止爬取
2025-07-28 23:33:19,837 - INFO - 爬取完成，共找到 20 个商品
2025-07-28 23:33:19,837 - INFO - 成功读取到 20 个商品（目标20个），开始铺货任务
2025-07-28 23:33:19,837 - INFO - 第四步：开始处理商品...
2025-07-28 23:33:19,850 - INFO - 已读取 20 个商品，目标成功铺货 3 个商品
2025-07-28 23:33:19,850 - INFO - 当前批次大小: 14
2025-07-28 23:33:19,852 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=681778175573
2025-07-28 23:33:23,513 - INFO - 页面加载完成
2025-07-28 23:33:23,513 - INFO - 模拟阅读行为，停顿 1.5 秒
2025-07-28 23:33:26,309 - INFO - 开始第 1 次铺货尝试...
2025-07-28 23:33:26,309 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:33:26,309 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:33:26,318 - INFO - span innerHTML: 立即铺货
2025-07-28 23:33:26,337 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-28 23:33:26,337 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-28 23:33:26,349 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-28 23:33:27,372 - INFO - 尝试ActionChains点击...
2025-07-28 23:33:27,657 - INFO - ActionChains点击成功
2025-07-28 23:33:28,112 - INFO - 铺货按钮点击成功
2025-07-28 23:33:30,127 - INFO - 使用配置的XPath找到确认按钮
2025-07-28 23:33:30,139 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-28 23:33:31,302 - INFO - 尝试ActionChains点击...
2025-07-28 23:33:31,572 - INFO - ActionChains点击成功
2025-07-28 23:33:31,959 - INFO - 确认按钮点击成功
2025-07-28 23:33:33,960 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-28 23:33:33,970 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-28 23:33:33,970 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:33:34,978 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-28 23:33:34,978 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:33:35,986 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-28 23:33:35,986 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:33:36,994 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-28 23:33:36,994 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:33:38,004 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-07-28 23:33:38,004 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:33:39,012 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-07-28 23:33:39,012 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:33:40,023 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-07-28 23:33:40,024 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:33:41,032 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-07-28 23:33:41,032 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:33:42,040 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-07-28 23:33:42,040 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:33:43,048 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-07-28 23:33:43,048 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:33:44,050 - INFO - 等待超时，进行最后一次检查...
2025-07-28 23:33:44,058 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-07-28 23:33:44,060 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-07-28 23:33:44,060 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-07-28 23:33:44,060 - INFO - 检测是否出现滑块验证...
2025-07-28 23:33:44,068 - INFO - 在主文档中未找到滑块，检查iframe...
2025-07-28 23:33:44,081 - INFO - 找到 1 个iframe
2025-07-28 23:33:44,082 - INFO - 检查第 1 个iframe...
2025-07-28 23:33:44,106 - INFO - 在第 1 个iframe中检测到滑块验证，开始处理...
2025-07-28 23:33:44,106 - INFO - 开始处理滑块验证...
2025-07-28 23:33:44,118 - INFO - 滑块容器宽度: 300px
2025-07-28 23:33:44,119 - INFO - 计算拖拽距离: 285px
2025-07-28 23:33:44,119 - INFO - 尝试一次性拖拽到底...
2025-07-28 23:33:47,318 - INFO - 一次性拖拽成功，滑块已消失
2025-07-28 23:33:47,320 - INFO - 滑块验证处理成功，准备重试铺货
2025-07-28 23:33:49,320 - INFO - 开始第 2 次铺货尝试...
2025-07-28 23:33:49,320 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:33:49,320 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:33:49,328 - INFO - span innerHTML: 立即铺货
2025-07-28 23:33:59,528 - WARNING - 等待元素可点击超时: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:33:59,528 - WARNING - 使用配置的XPath未找到添加商品按钮
2025-07-28 23:33:59,528 - ERROR - 未找到铺货按钮
2025-07-28 23:33:59,528 - WARNING - 第 1 个商品处理失败（不计入成功数量）
2025-07-28 23:33:59,528 - INFO - 商品处理失败，立即处理下一个商品
2025-07-28 23:33:59,541 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=690841537159
2025-07-28 23:34:02,658 - INFO - 页面加载完成
2025-07-28 23:34:02,658 - INFO - 模拟阅读行为，停顿 2.2 秒
2025-07-28 23:34:05,932 - INFO - 开始第 1 次铺货尝试...
2025-07-28 23:34:05,932 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:34:05,932 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:34:05,939 - INFO - span innerHTML: 立即铺货
2025-07-28 23:34:05,977 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-28 23:34:05,977 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-28 23:34:05,990 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-28 23:34:07,328 - INFO - 尝试ActionChains点击...
2025-07-28 23:34:07,613 - INFO - ActionChains点击成功
2025-07-28 23:34:07,956 - INFO - 铺货按钮点击成功
2025-07-28 23:34:09,968 - INFO - 使用配置的XPath找到确认按钮
2025-07-28 23:34:09,982 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-28 23:34:11,164 - INFO - 尝试ActionChains点击...
2025-07-28 23:34:11,436 - INFO - ActionChains点击成功
2025-07-28 23:34:11,764 - INFO - 确认按钮点击成功
2025-07-28 23:34:13,765 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-28 23:34:13,774 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-28 23:34:13,774 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:34:14,783 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-28 23:34:14,784 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:34:15,792 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-28 23:34:15,793 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:34:16,800 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-28 23:34:16,800 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:34:17,808 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-07-28 23:34:17,808 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:34:18,817 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-07-28 23:34:18,817 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:34:19,825 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-07-28 23:34:19,825 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:34:20,835 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-07-28 23:34:20,836 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:34:21,845 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-07-28 23:34:21,845 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:34:22,852 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-07-28 23:34:22,852 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:34:23,853 - INFO - 等待超时，进行最后一次检查...
2025-07-28 23:34:23,888 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-07-28 23:34:23,888 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-07-28 23:34:23,889 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-07-28 23:34:23,889 - INFO - 检测是否出现滑块验证...
2025-07-28 23:34:23,894 - INFO - 在主文档中未找到滑块，检查iframe...
2025-07-28 23:34:23,916 - INFO - 找到 1 个iframe
2025-07-28 23:34:23,916 - INFO - 检查第 1 个iframe...
2025-07-28 23:34:23,935 - INFO - 在第 1 个iframe中检测到滑块验证，开始处理...
2025-07-28 23:34:23,936 - INFO - 开始处理滑块验证...
2025-07-28 23:34:23,945 - INFO - 滑块容器宽度: 300px
2025-07-28 23:34:23,945 - INFO - 计算拖拽距离: 285px
2025-07-28 23:34:23,946 - INFO - 尝试一次性拖拽到底...
2025-07-28 23:34:27,091 - INFO - 一次性拖拽成功，滑块已消失
2025-07-28 23:34:27,092 - INFO - 滑块验证处理成功，准备重试铺货
2025-07-28 23:34:29,092 - INFO - 开始第 2 次铺货尝试...
2025-07-28 23:34:29,092 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:34:29,092 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:34:29,102 - INFO - span innerHTML: 立即铺货
2025-07-28 23:34:39,281 - WARNING - 等待元素可点击超时: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:34:39,281 - WARNING - 使用配置的XPath未找到添加商品按钮
2025-07-28 23:34:39,282 - ERROR - 未找到铺货按钮
2025-07-28 23:34:39,282 - WARNING - 第 2 个商品处理失败（不计入成功数量）
2025-07-28 23:34:39,282 - INFO - 商品处理失败，立即处理下一个商品
2025-07-28 23:34:39,294 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=789447097664
2025-07-28 23:34:43,253 - INFO - 页面加载完成
2025-07-28 23:34:43,253 - INFO - 模拟阅读行为，停顿 2.7 秒
2025-07-28 23:34:47,146 - INFO - 开始第 1 次铺货尝试...
2025-07-28 23:34:47,146 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:34:47,146 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:34:47,153 - INFO - span innerHTML: 立即铺货
2025-07-28 23:34:47,174 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-28 23:34:47,174 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-28 23:34:47,188 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-28 23:34:48,656 - INFO - 尝试ActionChains点击...
2025-07-28 23:34:48,942 - INFO - ActionChains点击成功
2025-07-28 23:34:49,239 - INFO - 铺货按钮点击成功
2025-07-28 23:34:51,255 - INFO - 使用配置的XPath找到确认按钮
2025-07-28 23:34:51,272 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-28 23:34:52,555 - INFO - 尝试ActionChains点击...
2025-07-28 23:34:52,824 - INFO - ActionChains点击成功
2025-07-28 23:34:53,188 - INFO - 确认按钮点击成功
2025-07-28 23:34:55,188 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-28 23:34:55,197 - INFO - 第1次检查 - span innerHTML: 立即铺货
2025-07-28 23:34:55,197 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:34:56,206 - INFO - 第2次检查 - span innerHTML: 立即铺货
2025-07-28 23:34:56,206 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:34:57,214 - INFO - 第3次检查 - span innerHTML: 立即铺货
2025-07-28 23:34:57,214 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:34:58,223 - INFO - 第4次检查 - span innerHTML: 立即铺货
2025-07-28 23:34:58,223 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:34:59,231 - INFO - 第5次检查 - span innerHTML: 立即铺货
2025-07-28 23:34:59,232 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:35:00,240 - INFO - 第6次检查 - span innerHTML: 立即铺货
2025-07-28 23:35:00,241 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:35:01,248 - INFO - 第7次检查 - span innerHTML: 立即铺货
2025-07-28 23:35:01,248 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:35:02,257 - INFO - 第8次检查 - span innerHTML: 立即铺货
2025-07-28 23:35:02,257 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:35:03,266 - INFO - 第9次检查 - span innerHTML: 立即铺货
2025-07-28 23:35:03,266 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:35:04,274 - INFO - 第10次检查 - span innerHTML: 立即铺货
2025-07-28 23:35:04,274 - INFO - span仍为'立即铺货'，继续等待...
2025-07-28 23:35:05,276 - INFO - 等待超时，进行最后一次检查...
2025-07-28 23:35:05,282 - INFO - 最终检查 - span innerHTML: 立即铺货
2025-07-28 23:35:05,282 - INFO - 最终验证失败：span未变为'已铺货'，当前为'立即铺货'
2025-07-28 23:35:05,282 - INFO - 铺货验证失败，检查是否出现滑块验证
2025-07-28 23:35:05,282 - INFO - 检测是否出现滑块验证...
2025-07-28 23:35:05,292 - INFO - 在主文档中未找到滑块，检查iframe...
2025-07-28 23:35:05,299 - INFO - 找到 1 个iframe
2025-07-28 23:35:05,299 - INFO - 检查第 1 个iframe...
2025-07-28 23:35:05,320 - INFO - 在第 1 个iframe中检测到滑块验证，开始处理...
2025-07-28 23:35:05,320 - INFO - 开始处理滑块验证...
2025-07-28 23:35:05,335 - INFO - 滑块容器宽度: 300px
2025-07-28 23:35:05,335 - INFO - 计算拖拽距离: 285px
2025-07-28 23:35:05,335 - INFO - 尝试一次性拖拽到底...
2025-07-28 23:35:08,491 - INFO - 一次性拖拽成功，滑块已消失
2025-07-28 23:35:08,494 - INFO - 滑块验证处理成功，准备重试铺货
2025-07-28 23:35:10,494 - INFO - 开始第 2 次铺货尝试...
2025-07-28 23:35:10,494 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-28 23:35:10,494 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-28 23:35:10,502 - INFO - span innerHTML: 立即铺货
2025-07-28 23:35:10,534 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-28 23:35:10,534 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-28 23:35:10,549 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
