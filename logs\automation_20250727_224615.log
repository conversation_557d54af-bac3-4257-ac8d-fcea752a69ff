2025-07-27 22:46:25,114 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169cda]
	(No symbol) [0x0x7ff7cd171679]
	(No symbol) [0x0x7ff7cd174a61]
	(No symbol) [0x0x7ff7cd211e4b]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:46:25,115 - INFO - 防检测机制设置完成
2025-07-27 22:46:25,115 - INFO - 浏览器驱动初始化成功
2025-07-27 22:46:25,120 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:46:25,124 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:46:25,130 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:46:25,133 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:46:25,138 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:46:25,146 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:46:25,149 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:46:25,154 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:46:25,162 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:46:25,170 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:46:25,175 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:46:25,181 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:46:25,185 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:46:25,187 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:46:25,193 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:46:25,196 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:46:25,200 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:46:25,203 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:46:25,207 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169b0c]
	(No symbol) [0x0x7ff7cd225b46]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:46:25,207 - INFO - Cookies已加载
2025-07-27 22:46:25,207 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?channel=global_zx&spm=a21ug5.29159167.6452766900.2ndview_zxmore_click
2025-07-27 22:46:25,208 - INFO - 添加额外延迟: 1.6秒
2025-07-27 22:46:30,881 - INFO - 当前User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-27 22:46:30,882 - INFO - 确认：使用桌面版User-Agent
2025-07-27 22:46:30,886 - INFO - 桌面版User-Agent设置完成: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-27 22:46:33,398 - INFO - 成功导航到目标页面
2025-07-27 22:46:33,399 - INFO - 登录成功！现在可以开始铺货任务
2025-07-27 22:46:42,132 - INFO - Cookies已保存
2025-07-27 22:46:44,387 - INFO - 浏览器已关闭
2025-07-27 22:46:45,753 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff7cd3ae925+77845]
	GetHandleVerifier [0x0x7ff7cd3ae980+77936]
	(No symbol) [0x0x7ff7cd169cda]
	(No symbol) [0x0x7ff7cd171679]
	(No symbol) [0x0x7ff7cd174a61]
	(No symbol) [0x0x7ff7cd211e4b]
	(No symbol) [0x0x7ff7cd1e88ca]
	(No symbol) [0x0x7ff7cd210b07]
	(No symbol) [0x0x7ff7cd1e86a3]
	(No symbol) [0x0x7ff7cd1b1791]
	(No symbol) [0x0x7ff7cd1b2523]
	GetHandleVerifier [0x0x7ff7cd68683d+3059501]
	GetHandleVerifier [0x0x7ff7cd680bfd+3035885]
	GetHandleVerifier [0x0x7ff7cd6a03f0+3164896]
	GetHandleVerifier [0x0x7ff7cd3c8c2e+185118]
	GetHandleVerifier [0x0x7ff7cd3d053f+216111]
	GetHandleVerifier [0x0x7ff7cd3b72d4+113092]
	GetHandleVerifier [0x0x7ff7cd3b7489+113529]
	GetHandleVerifier [0x0x7ff7cd39e288+10616]
	BaseThreadInitThunk [0x0x7ff98a02e8d7+23]
	RtlUserThreadStart [0x0x7ff98b13c34c+44]

2025-07-27 22:46:45,753 - INFO - 防检测机制设置完成
2025-07-27 22:46:45,753 - INFO - 浏览器驱动初始化成功
2025-07-27 22:46:45,753 - INFO - 开始自动化铺货流程（有窗口模式）
2025-07-27 22:46:45,753 - INFO - 第一步：正在导航到目标页面...
2025-07-27 22:46:49,579 - INFO - 第一步：点击登录按钮
2025-07-27 22:46:49,640 - INFO - 找到登录按钮，使用选择器: //*[@id='login-form']/div[6]/button
2025-07-27 22:46:50,665 - INFO - 准备点击登录按钮: 标签=button, 文本='登录', 选择器=//*[@id='login-form']/div[6]/button
2025-07-27 22:46:50,683 - INFO - 准备点击元素: tag=button, text='登录', class='fm-button fm-submit password-l', id=''
2025-07-27 22:46:52,099 - INFO - 尝试ActionChains点击...
2025-07-27 22:46:52,514 - INFO - ActionChains点击成功
2025-07-27 22:46:52,754 - INFO - 登录按钮点击成功
2025-07-27 22:46:52,754 - INFO - 登录按钮点击成功
2025-07-27 22:46:55,755 - INFO - 第二步：获取账号信息...
2025-07-27 22:46:55,755 - INFO - 正在跳转到个人中心页面: https://jingya.taobao.com/?v=2
2025-07-27 22:47:20,635 - INFO - 尝试获取账号名称，使用XPath: //*[@id='workbench-user-tool-btn']/div/div[2]
2025-07-27 22:47:20,648 - INFO - 成功获取账号信息: tb997603987291:书生
2025-07-27 22:47:20,649 - INFO - 获取到账号名称: tb997603987291:书生
2025-07-27 22:47:20,650 - INFO - 第三步：开始爬取商品信息...
2025-07-27 22:47:20,650 - INFO - GUI界面已更新账号显示: tb997603987291:书生
2025-07-27 22:47:20,651 - INFO - 开始爬取商品信息...
2025-07-27 22:47:24,825 - INFO - 正在爬取第 1 页商品信息...
2025-07-27 22:47:31,771 - INFO - 第 1 页未找到商品，停止爬取
2025-07-27 22:47:31,771 - INFO - 爬取完成，共找到 0 个商品
2025-07-27 22:47:31,771 - ERROR - 未能获取到商品信息，停止流程
