2025-07-29 00:41:05,371 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059cda]
	(No symbol) [0x0x7ff69a061679]
	(No symbol) [0x0x7ff69a064a61]
	(No symbol) [0x0x7ff69a101e4b]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:41:05,371 - INFO - 防检测机制设置完成
2025-07-29 00:41:05,372 - INFO - 浏览器驱动初始化成功
2025-07-29 00:41:05,377 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:41:05,381 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:41:05,385 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:41:05,388 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:41:05,394 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:41:05,398 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:41:05,400 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:41:05,407 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:41:05,413 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:41:05,420 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:41:05,425 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:41:05,433 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:41:05,437 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:41:05,440 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:41:05,444 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:41:05,448 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:41:05,450 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:41:05,454 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:41:05,456 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:41:05,459 - WARNING - 添加cookie失败: Message: invalid cookie domain
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a115b46]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:41:05,460 - INFO - Cookies已加载
2025-07-29 00:41:05,460 - INFO - 正在导航到目标页面: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?channel=global_zx&spm=a21ug5.29159167.6452766900.2ndview_zxmore_click
2025-07-29 00:41:10,076 - INFO - 当前User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-29 00:41:10,077 - INFO - 确认：使用桌面版User-Agent
2025-07-29 00:41:10,084 - INFO - 桌面版User-Agent设置完成: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-29 00:41:11,080 - WARNING - 随机鼠标移动失败: Message: move target out of bounds
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059b0c]
	(No symbol) [0x0x7ff69a10be53]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:41:12,900 - INFO - 成功导航到目标页面
2025-07-29 00:41:12,901 - INFO - 登录成功！现在可以开始铺货任务
2025-07-29 00:41:15,207 - INFO - Cookies已保存
2025-07-29 00:41:17,475 - INFO - 浏览器已关闭
2025-07-29 00:41:17,476 - INFO - 以无界面(headless)模式启动Chrome，仅添加headless、no-sandbox、disable-dev-shm-usage参数
2025-07-29 00:41:18,861 - WARNING - 设置User-Agent失败: Message: javascript error: Cannot redefine property: userAgent
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff69a29e925+77845]
	GetHandleVerifier [0x0x7ff69a29e980+77936]
	(No symbol) [0x0x7ff69a059cda]
	(No symbol) [0x0x7ff69a061679]
	(No symbol) [0x0x7ff69a064a61]
	(No symbol) [0x0x7ff69a101e4b]
	(No symbol) [0x0x7ff69a0d88ca]
	(No symbol) [0x0x7ff69a100b07]
	(No symbol) [0x0x7ff69a0d86a3]
	(No symbol) [0x0x7ff69a0a1791]
	(No symbol) [0x0x7ff69a0a2523]
	GetHandleVerifier [0x0x7ff69a57683d+3059501]
	GetHandleVerifier [0x0x7ff69a570bfd+3035885]
	GetHandleVerifier [0x0x7ff69a5903f0+3164896]
	GetHandleVerifier [0x0x7ff69a2b8c2e+185118]
	GetHandleVerifier [0x0x7ff69a2c053f+216111]
	GetHandleVerifier [0x0x7ff69a2a72d4+113092]
	GetHandleVerifier [0x0x7ff69a2a7489+113529]
	GetHandleVerifier [0x0x7ff69a28e288+10616]
	BaseThreadInitThunk [0x0x7ff9c5dae8d7+23]
	RtlUserThreadStart [0x0x7ff9c801c34c+44]

2025-07-29 00:41:18,861 - INFO - 防检测机制设置完成
2025-07-29 00:41:18,861 - INFO - 浏览器驱动初始化成功
2025-07-29 00:41:18,861 - INFO - 开始自动化铺货流程（无头模式）
2025-07-29 00:41:18,861 - INFO - 第一步：正在导航到目标页面...
2025-07-29 00:41:22,621 - INFO - 第一步：点击登录按钮
2025-07-29 00:41:22,643 - INFO - 找到登录按钮，使用选择器: //*[@id='login-form']/div[6]/button
2025-07-29 00:41:23,683 - INFO - 准备点击登录按钮: 标签=button, 文本='登录', 选择器=//*[@id='login-form']/div[6]/button
2025-07-29 00:41:23,697 - INFO - 准备点击元素: tag=button, text='登录', class='fm-button fm-submit password-l', id=''
2025-07-29 00:41:25,028 - INFO - 尝试ActionChains点击...
2025-07-29 00:41:25,439 - INFO - ActionChains点击成功
2025-07-29 00:41:25,807 - INFO - 登录按钮点击成功
2025-07-29 00:41:25,807 - INFO - 登录按钮点击成功
2025-07-29 00:41:28,808 - INFO - 第二步：获取账号信息...
2025-07-29 00:41:28,808 - INFO - 正在跳转到个人中心页面: https://jingya.taobao.com/?v=2
2025-07-29 00:41:53,603 - INFO - 尝试获取账号名称，使用XPath: //*[@id='workbench-user-tool-btn']/div/div[2]
2025-07-29 00:41:53,616 - INFO - 成功获取账号信息: 卓祥22店:冰淇淋
2025-07-29 00:41:53,616 - INFO - 获取到账号名称: 卓祥22店:冰淇淋
2025-07-29 00:41:53,617 - INFO - GUI界面已更新账号显示: 卓祥22店:冰淇淋
2025-07-29 00:41:53,617 - INFO - 第三步：开始读取20个商品信息...
2025-07-29 00:41:53,618 - INFO - 开始爬取商品信息，最大数量: 20
2025-07-29 00:41:56,732 - INFO - 正在爬取第 1 页商品信息...
2025-07-29 00:41:58,740 - INFO - 找到商品容器，开始爬取商品，最大数量: 20
2025-07-29 00:41:58,750 - INFO - 找到商品项 1，检查元素信息...
2025-07-29 00:41:58,760 - INFO - 商品项 1 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:41:58,762 - INFO - 商品项 1 直接获取到data-autolog: item_id=681778175573&path=search_goods_card&type=search
2025-07-29 00:41:58,762 - INFO - 商品项 1 data-autolog: item_id=681778175573&path=search_goods_card&type=search
2025-07-29 00:41:58,762 - INFO - 商品项 1 提取到item_id: 681778175573
2025-07-29 00:41:58,775 - INFO - 商品项 1 商品名称: 保税直供 Dove多芬磨砂膏石榴籽乳木果风味身体磨砂膏298g
2025-07-29 00:41:58,787 - INFO - 找到商品项 2，检查元素信息...
2025-07-29 00:41:58,796 - INFO - 商品项 2 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:41:58,799 - INFO - 商品项 2 直接获取到data-autolog: item_id=690841537159&path=search_goods_card&index=1&type=search
2025-07-29 00:41:58,799 - INFO - 商品项 2 data-autolog: item_id=690841537159&path=search_goods_card&index=1&type=search
2025-07-29 00:41:58,799 - INFO - 商品项 2 提取到item_id: 690841537159
2025-07-29 00:41:58,806 - INFO - 商品项 2 商品名称: 保税直供 凡士林Vaseline烟酰胺身体乳400ml 725ml无压泵款200ml
2025-07-29 00:41:58,818 - INFO - 找到商品项 3，检查元素信息...
2025-07-29 00:41:58,825 - INFO - 商品项 3 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:41:58,829 - INFO - 商品项 3 直接获取到data-autolog: item_id=789447097664&path=search_goods_card&index=2&type=search
2025-07-29 00:41:58,829 - INFO - 商品项 3 data-autolog: item_id=789447097664&path=search_goods_card&index=2&type=search
2025-07-29 00:41:58,829 - INFO - 商品项 3 提取到item_id: 789447097664
2025-07-29 00:41:58,837 - INFO - 商品项 3 商品名称: 【甄选】珂润Curel泡沫洗面奶150ml
2025-07-29 00:41:58,850 - INFO - 找到商品项 4，检查元素信息...
2025-07-29 00:41:58,857 - INFO - 商品项 4 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:41:58,860 - INFO - 商品项 4 直接获取到data-autolog: item_id=694682843417&path=search_goods_card&index=3&type=search
2025-07-29 00:41:58,860 - INFO - 商品项 4 data-autolog: item_id=694682843417&path=search_goods_card&index=3&type=search
2025-07-29 00:41:58,860 - INFO - 商品项 4 提取到item_id: 694682843417
2025-07-29 00:41:58,870 - INFO - 商品项 4 商品名称: 【可开授权】日台混发Fino发膜美容液护发素 发膜230g
2025-07-29 00:41:58,899 - INFO - 找到商品项 5，检查元素信息...
2025-07-29 00:41:58,907 - INFO - 商品项 5 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:41:58,909 - INFO - 商品项 5 直接获取到data-autolog: item_id=681082809892&path=search_goods_card&index=4&type=search
2025-07-29 00:41:58,910 - INFO - 商品项 5 data-autolog: item_id=681082809892&path=search_goods_card&index=4&type=search
2025-07-29 00:41:58,910 - INFO - 商品项 5 提取到item_id: 681082809892
2025-07-29 00:41:58,918 - INFO - 商品项 5 商品名称: 保税直供 Dove多芬大白碗润肤身体乳300ml 【1//2//3罐】
2025-07-29 00:41:58,991 - INFO - 找到商品项 6，检查元素信息...
2025-07-29 00:41:59,009 - INFO - 商品项 6 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:41:59,013 - INFO - 商品项 6 直接获取到data-autolog: item_id=694675776881&path=search_goods_card&index=5&type=search
2025-07-29 00:41:59,013 - INFO - 商品项 6 data-autolog: item_id=694675776881&path=search_goods_card&index=5&type=search
2025-07-29 00:41:59,013 - INFO - 商品项 6 提取到item_id: 694675776881
2025-07-29 00:41:59,022 - INFO - 商品项 6 商品名称: 保税直供 Pantene潘婷护发素三分钟奇迹多效修护发膜级护发素
2025-07-29 00:41:59,036 - INFO - 找到商品项 7，检查元素信息...
2025-07-29 00:41:59,046 - INFO - 商品项 7 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:41:59,050 - INFO - 商品项 7 直接获取到data-autolog: item_id=680729304287&path=search_goods_card&index=6&type=search
2025-07-29 00:41:59,050 - INFO - 商品项 7 data-autolog: item_id=680729304287&path=search_goods_card&index=6&type=search
2025-07-29 00:41:59,050 - INFO - 商品项 7 提取到item_id: 680729304287
2025-07-29 00:41:59,060 - INFO - 商品项 7 商品名称: 保税直供 多芬dove空气感无硅油 洗发水480g 护发素480g护发临期
2025-07-29 00:41:59,070 - INFO - 找到商品项 8，检查元素信息...
2025-07-29 00:41:59,078 - INFO - 商品项 8 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:41:59,080 - INFO - 商品项 8 直接获取到data-autolog: item_id=710355107054&path=search_goods_card&index=7&type=search
2025-07-29 00:41:59,080 - INFO - 商品项 8 data-autolog: item_id=710355107054&path=search_goods_card&index=7&type=search
2025-07-29 00:41:59,080 - INFO - 商品项 8 提取到item_id: 710355107054
2025-07-29 00:41:59,090 - INFO - 商品项 8 商品名称: 保税直供 Dove多芬洗发水护发素空气感洗发水480g 【护发素临期
2025-07-29 00:41:59,104 - INFO - 找到商品项 9，检查元素信息...
2025-07-29 00:41:59,111 - INFO - 商品项 9 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:41:59,114 - INFO - 商品项 9 直接获取到data-autolog: item_id=740463658412&path=search_goods_card&index=8&type=search
2025-07-29 00:41:59,114 - INFO - 商品项 9 data-autolog: item_id=740463658412&path=search_goods_card&index=8&type=search
2025-07-29 00:41:59,115 - INFO - 商品项 9 提取到item_id: 740463658412
2025-07-29 00:41:59,124 - INFO - 商品项 9 商品名称: 【主推链接】花王cape空气感定型喷雾50g/180g 编码：3305300000
2025-07-29 00:41:59,137 - INFO - 找到商品项 10，检查元素信息...
2025-07-29 00:41:59,146 - INFO - 商品项 10 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:41:59,148 - INFO - 商品项 10 直接获取到data-autolog: item_id=821067902127&path=search_goods_card&index=9&type=search
2025-07-29 00:41:59,149 - INFO - 商品项 10 data-autolog: item_id=821067902127&path=search_goods_card&index=9&type=search
2025-07-29 00:41:59,149 - INFO - 商品项 10 提取到item_id: 821067902127
2025-07-29 00:41:59,174 - INFO - 商品项 10 商品名称: 丹碧丝TAMPAX卫生棉条长导管式内置式纯棉月经棉条棉棒游泳卫生巾
2025-07-29 00:41:59,185 - INFO - 找到商品项 11，检查元素信息...
2025-07-29 00:41:59,192 - INFO - 商品项 11 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:41:59,195 - INFO - 商品项 11 直接获取到data-autolog: item_id=789835300215&path=search_goods_card&index=10&type=search
2025-07-29 00:41:59,195 - INFO - 商品项 11 data-autolog: item_id=789835300215&path=search_goods_card&index=10&type=search
2025-07-29 00:41:59,195 - INFO - 商品项 11 提取到item_id: 789835300215
2025-07-29 00:41:59,204 - INFO - 商品项 11 商品名称: 【保税】雅漾喷雾爽肤水300ml
2025-07-29 00:41:59,216 - INFO - 找到商品项 12，检查元素信息...
2025-07-29 00:41:59,224 - INFO - 商品项 12 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:41:59,226 - INFO - 商品项 12 直接获取到data-autolog: item_id=843747605382&path=search_goods_card&index=11&type=search
2025-07-29 00:41:59,227 - INFO - 商品项 12 data-autolog: item_id=843747605382&path=search_goods_card&index=11&type=search
2025-07-29 00:41:59,227 - INFO - 商品项 12 提取到item_id: 843747605382
2025-07-29 00:41:59,236 - INFO - 商品项 12 商品名称: 倩碧紫胖子卸妆膏125ml/瓶
2025-07-29 00:41:59,245 - INFO - 找到商品项 13，检查元素信息...
2025-07-29 00:41:59,255 - INFO - 商品项 13 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:41:59,259 - INFO - 商品项 13 直接获取到data-autolog: item_id=675816539149&path=search_goods_card&index=12&type=search
2025-07-29 00:41:59,259 - INFO - 商品项 13 data-autolog: item_id=675816539149&path=search_goods_card&index=12&type=search
2025-07-29 00:41:59,259 - INFO - 商品项 13 提取到item_id: 675816539149
2025-07-29 00:41:59,285 - INFO - 商品项 13 商品名称: 新加坡Vicopyl维可保益生菌(Pylopass/拒绝幽门菌/抑口臭/调肠胃)
2025-07-29 00:41:59,318 - INFO - 找到商品项 14，检查元素信息...
2025-07-29 00:41:59,326 - INFO - 商品项 14 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:41:59,329 - INFO - 商品项 14 直接获取到data-autolog: item_id=846634805775&path=search_goods_card&index=13&type=search
2025-07-29 00:41:59,330 - INFO - 商品项 14 data-autolog: item_id=846634805775&path=search_goods_card&index=13&type=search
2025-07-29 00:41:59,330 - INFO - 商品项 14 提取到item_id: 846634805775
2025-07-29 00:41:59,337 - INFO - 商品项 14 商品名称: 【甄选】cow牛乳石碱原味/玫瑰花香 瓶装480ml
2025-07-29 00:41:59,350 - INFO - 找到商品项 15，检查元素信息...
2025-07-29 00:41:59,359 - INFO - 商品项 15 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:41:59,361 - INFO - 商品项 15 直接获取到data-autolog: item_id=852510575114&path=search_goods_card&index=14&type=search
2025-07-29 00:41:59,362 - INFO - 商品项 15 data-autolog: item_id=852510575114&path=search_goods_card&index=14&type=search
2025-07-29 00:41:59,362 - INFO - 商品项 15 提取到item_id: 852510575114
2025-07-29 00:41:59,370 - INFO - 商品项 15 商品名称: Nars 纳斯 红壳蜜粉饼16g（含粉扑）、Nars纳斯裸光蜜粉饼10g
2025-07-29 00:41:59,381 - INFO - 找到商品项 16，检查元素信息...
2025-07-29 00:41:59,389 - INFO - 商品项 16 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:41:59,392 - INFO - 商品项 16 直接获取到data-autolog: item_id=743442966826&path=search_goods_card&index=15&type=search
2025-07-29 00:41:59,392 - INFO - 商品项 16 data-autolog: item_id=743442966826&path=search_goods_card&index=15&type=search
2025-07-29 00:41:59,392 - INFO - 商品项 16 提取到item_id: 743442966826
2025-07-29 00:41:59,401 - INFO - 商品项 16 商品名称: ESI卡路里拜拜片白芸豆阻断片大餐救星膳食纤维身材热控管理
2025-07-29 00:41:59,413 - INFO - 找到商品项 17，检查元素信息...
2025-07-29 00:41:59,422 - INFO - 商品项 17 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:41:59,425 - INFO - 商品项 17 直接获取到data-autolog: item_id=848934676698&path=search_goods_card&index=16&type=search
2025-07-29 00:41:59,425 - INFO - 商品项 17 data-autolog: item_id=848934676698&path=search_goods_card&index=16&type=search
2025-07-29 00:41:59,426 - INFO - 商品项 17 提取到item_id: 848934676698
2025-07-29 00:41:59,433 - INFO - 商品项 17 商品名称: Estee Lauder 雅诗兰黛持妆粉底液30ml 2C0 /1w1/1C1
2025-07-29 00:41:59,445 - INFO - 找到商品项 18，检查元素信息...
2025-07-29 00:41:59,454 - INFO - 商品项 18 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:41:59,457 - INFO - 商品项 18 直接获取到data-autolog: item_id=693758717931&path=search_goods_card&index=17&type=search
2025-07-29 00:41:59,457 - INFO - 商品项 18 data-autolog: item_id=693758717931&path=search_goods_card&index=17&type=search
2025-07-29 00:41:59,457 - INFO - 商品项 18 提取到item_id: 693758717931
2025-07-29 00:41:59,482 - INFO - 商品项 18 商品名称: 保税直供 资生堂fino高效渗透发膜 230g （1/2/3罐）日版
2025-07-29 00:41:59,494 - INFO - 找到商品项 19，检查元素信息...
2025-07-29 00:41:59,503 - INFO - 商品项 19 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:41:59,506 - INFO - 商品项 19 直接获取到data-autolog: item_id=691142128229&path=search_goods_card&index=18&type=search
2025-07-29 00:41:59,506 - INFO - 商品项 19 data-autolog: item_id=691142128229&path=search_goods_card&index=18&type=search
2025-07-29 00:41:59,506 - INFO - 商品项 19 提取到item_id: 691142128229
2025-07-29 00:41:59,530 - INFO - 商品项 19 商品名称: 保税 日版Dove多芬洁面洗面奶新版 正装瓶150ml 补充袋125ml 甄选
2025-07-29 00:41:59,541 - INFO - 找到商品项 20，检查元素信息...
2025-07-29 00:41:59,551 - INFO - 商品项 20 - 元素标签: div, class: tfx-item, 有data-autolog: True
2025-07-29 00:41:59,554 - INFO - 商品项 20 直接获取到data-autolog: item_id=690739721578&path=search_goods_card&index=19&type=search
2025-07-29 00:41:59,554 - INFO - 商品项 20 data-autolog: item_id=690739721578&path=search_goods_card&index=19&type=search
2025-07-29 00:41:59,555 - INFO - 商品项 20 提取到item_id: 690739721578
2025-07-29 00:41:59,564 - INFO - 商品项 20 商品名称: 保税直供 SUNSILK夏士莲椰子洗发水//护发素450ml 泰产
2025-07-29 00:41:59,564 - INFO - 已爬取到 20 个商品，停止当前页面爬取
2025-07-29 00:41:59,564 - INFO - 当前页面爬取完成，找到 20 个商品
2025-07-29 00:41:59,565 - INFO - 第 1 页找到 20 个商品，总计: 20
2025-07-29 00:41:59,565 - INFO - 已达到最大商品数量 20，停止爬取
2025-07-29 00:41:59,565 - INFO - 爬取完成，共找到 20 个商品
2025-07-29 00:41:59,565 - INFO - 成功读取到 20 个商品（目标20个），开始铺货任务
2025-07-29 00:41:59,565 - INFO - 第四步：开始处理商品...
2025-07-29 00:41:59,576 - INFO - 已读取 20 个商品，目标成功铺货 3 个商品
2025-07-29 00:41:59,576 - INFO - 商品处理顺序已随机化
2025-07-29 00:41:59,576 - INFO - 当前批次大小: 12
2025-07-29 00:41:59,577 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=694682843417
2025-07-29 00:42:02,720 - INFO - 页面加载完成
2025-07-29 00:42:02,721 - INFO - 模拟阅读行为，停顿 1.3 秒
2025-07-29 00:42:05,370 - INFO - 开始第 1 次铺货尝试...
2025-07-29 00:42:05,370 - INFO - 尝试使用配置的XPath: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button
2025-07-29 00:42:05,370 - INFO - 尝试点击按钮之前检查span: //*[@id="root"]/div[3]/div/div/div/div[1]/div/div/div[2]/div[3]/div[6]/button/span
2025-07-29 00:42:05,380 - INFO - span innerHTML: 立即铺货
2025-07-29 00:42:05,401 - INFO - 找到按钮，文本内容: '立即铺货'
2025-07-29 00:42:05,402 - INFO - 使用配置的XPath找到添加商品按钮
2025-07-29 00:42:05,415 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-large next-btn-p', id=''
2025-07-29 00:42:06,510 - INFO - 尝试ActionChains点击...
2025-07-29 00:42:06,797 - INFO - ActionChains点击成功
2025-07-29 00:42:07,175 - INFO - 铺货按钮点击成功
2025-07-29 00:42:09,189 - INFO - 使用配置的XPath找到确认按钮
2025-07-29 00:42:09,201 - INFO - 准备点击元素: tag=button, text='立即铺货', class='next-btn next-medium next-btn-', id=''
2025-07-29 00:42:10,477 - INFO - 尝试ActionChains点击...
2025-07-29 00:42:10,754 - INFO - ActionChains点击成功
2025-07-29 00:42:11,019 - INFO - 确认按钮点击成功
2025-07-29 00:42:13,020 - INFO - 验证铺货是否成功，检查span元素状态...
2025-07-29 00:42:13,027 - INFO - 第1次检查 - span innerHTML: 已铺货
2025-07-29 00:42:13,027 - INFO - 验证成功：span已变为'已铺货'，商品铺货成功
2025-07-29 00:42:13,027 - INFO - 第 1 个商品处理成功 (总成功: 1/3, 批次进度: 1/12)
2025-07-29 00:42:13,027 - INFO - 铺货成功，等待 4.6 秒后处理下一个商品
2025-07-29 00:42:17,655 - INFO - 正在访问商品详情页面: https://jingya-x.taobao.com/wow/z/tfx/static/itemDetail?spm=a21ug5.28857937.1319808900.search_goods_card_tfx_item_0&supplierItemId=852510575114
2025-07-29 00:42:21,236 - INFO - 页面加载完成
2025-07-29 00:42:21,236 - INFO - 模拟阅读行为，停顿 1.9 秒
